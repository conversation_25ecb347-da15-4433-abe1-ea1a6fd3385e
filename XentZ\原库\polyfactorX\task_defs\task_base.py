import os
import importlib
from dataclasses import dataclass, field, asdict
from typing import List, Tuple
from loguru import logger
from datafeed.dataloader import Featherloader
from config import DATA_DIR_FACTOR, DATA_DIR_CSVS, DATA_DIR_FEATHER
# from engine.algos import *
from jzal_pro.cls_common import BaseObj
from loguru import logger
# logger.add("logs/myapp_{time}.log", level="INFO", format="{time} - {name} - {level} - {message}", rotation="1 week", compression="zip")

@dataclass
class Task(BaseObj):
    name:str = ''
    start_date: str = '20100101'
    end_date: str = None
    commission: int = 0.01  # 万1,有作用
    slippage: int = 0.0001  # 有作用
    stamp_duty = 0  # 单边印花税,etf=0
    init_cash = 10 * 10000
    benchmark: str = '510300.SH'
    data_path: str = 'etfs'
    symbols: List[str] = field(default_factory=list)
    columns: List[str] = field(default_factory=list)
    feature_names: list = field(default_factory=list)
    feature_exprs: list = field(default_factory=list)

    def load_datas(self):  # 父类: 自动加载品种数据和特征数据
        #if self.columns is None:
        #path = DATA_DIR_CSVS.joinpath(self.data_path).resolve()
        #folder = '/*'
        #loader = Duckdbloader(path=path, symbols=self.symbols, columns=self.columns,
        #                    start_date=self.start_date, end_date=self.end_date, folder=folder,)
        # fields.append('close/shift(close,1)-1')
        # names.append('return_0')
        path = DATA_DIR_FEATHER.joinpath(self.data_path).resolve()
        loader = Featherloader(path=path, symbols=self.symbols, columns=self.columns,
                            start_date=self.start_date, end_date=self.end_date)
        dfs = loader.load(fields=self.feature_exprs, names=self.feature_names)
        dfs.dropna(inplace=True)  # 对结果影响非常重要! 会删掉一些新品种的数据
        return dfs
    def to_toml(self, name):
        import toml
        from config import DATA_DIR
        with open(DATA_DIR.joinpath('tasks').joinpath(name+'.toml'), "w", encoding='UTF-8') as f:
            toml.dump(asdict(self),f)    
    # def log(self, txt: str, level: str = "INFO"):
    #     # 根据传入的日志级别选择相应的 Loguru logger 方法
    #     if level.upper() == "DEBUG":
    #         logger.debug('%s: %s' % (self.__class__.__name__, txt))
    #     elif level.upper() == "INFO":
    #         logger.info('%s: %s' % (self.__class__.__name__, txt))
    #     elif level.upper() == "WARNING":
    #         logger.warning('%s: %s' % (self.__class__.__name__, txt))
    #     elif level.upper() == "ERROR":
    #         logger.error('%s: %s' % (self.__class__.__name__, txt))
    #     elif level.upper() == "CRITICAL":
    #         logger.critical('%s: %s' % (self.__class__.__name__, txt))
    #     else:
    #         raise ValueError(f"Unsupported log level: {level}")
@dataclass
class TaskFactor(Task):
    job_num: int = 2  # 8, 设置并行核数 设置为-1，将会调用电脑所有进程
    freq: str = '15' # '15'min, 周期频率
    n_days: int = 5
    split_dt: str = '20161230'
    a_symbol: str = None # type: ignore
    a_fcts_file: str = None # type: ignore
    def load_a_symbol_fcts_direct(self): # (code, fcts) mapping
        dfs = pd.DataFrame()
        sym_path = DATA_DIR_CSVS.joinpath('etfs').joinpath(self.a_symbol+'_'+self.freq+'.xlsx').resolve() # 需重构
        fct_path = DATA_DIR_FACTOR.joinpath(self.a_fcts_file).resolve()
        from jzal_pro.utils.df_utils import gen_etime_close_df_divd_time
        original_df = gen_etime_close_df_divd_time(sym_path, self.start_date, self.end_date)
        original_df.set_index('etime', inplace=True)
        ''' original_df >>
                                tdate   close
        etime
        2005-02-23 09:45:00  2005-02-23  0.6433
        '''
        fct_df = pd.read_csv(fct_path, index_col=0)  # 读取因子表格
        fct_df.index = pd.to_datetime(fct_df.index)
        fct_df = fct_df.loc[(fct_df.index >= self.start_date) & (fct_df.index <= self.end_date)]
        ''' fct_df >>
                            factor   fct_new   fct_abs
        timestamp
        2005-02-23 09:45:00  0.000000  0.000000  0.000000
        '''
        a_df = pd.DataFrame()
        if original_df.index.equals(fct_df.index):
            a_df = pd.concat([original_df, fct_df], axis=1)
            a_df.index.name = 'date'
        ''' dfs >> 与btq_jzal数据格式统一
                                tdate   close    factor   fct_new   fct_abs
        date
        2005-02-23 09:45:00  2005-02-23  0.6433  0.000000  0.000000  0.000000       
        '''
        return a_df, fct_df.columns # 返回原数据+因子数据的df 和 因子名字们
    
@dataclass
class TaskAlgo(Task):
    
    algo_period: str = 'RunDaily'
    algo_period_days: int = 20

    # 仓位分配，默认等权
    algo_weight: str = 'WeightEqually'
    algo_weight_fix: list = field(default_factory=list)  # 当WeightFix时需要
    algo_weight_target_vol: float = 0.07
    algo_weight_target_vol_exclude: list = field(default_factory=list)  # TargetVol时需要

    # 规则信号
    rules_buy: List[str] = field(default_factory=list)
    at_least_buy: int = 1
    rules_sell: List[str] = field(default_factory=list)
    at_least_sell: int = 1

    # 机器模型
    model_name: str = 'model.pk'

    # 排序
    order_by: str = 'order_by'
    topK: int = 1
    dropN: int = 0
    b_ascending: bool = False
    def __str__(self):
        return self.name

    def _parse_period(self):
        module = importlib.import_module('engine.algos')
        if self.algo_period == 'RunDays':
            algo_period = getattr(module, self.algo_period)(self.algo_period_days)
        else:
            if self.algo_period in ['RunWeekly', 'RunOnce', 'RunMonthly', 'RunQuarterly', 'RunYearly']:
                algo_period = getattr(module, self.algo_period)()
            else:
                algo_period = RunAlways()
        return algo_period

    def _parse_weights(self):
        module = importlib.import_module('engine.algos')
        if self.algo_weight == 'WeightEqually':
            return WeightEqually()
        if self.algo_weight == 'WeightFix':
            if len(self.symbols) != len(self.algo_weight_fix):
                logger.error('固定权重 != symbols数 ')
                return
            return WeightFix(self.algo_weight_fix)
        if self.algo_weight == 'WeightERC':
            return WeightERC()
        if self.algo_weight == 'TargetVol':
            return TargetVol(target_volatility=self.algo_weight_target_vol, exclude=self.algo_weight_target_vol_exclude)
        logger.error('{}不存在'.format(self.algo_weight))
        return None

    def get_algos(self):
        pass


@dataclass
class TaskAssetsAllocation(TaskAlgo):  # 资产配置模板
    def get_algos(self):
        return [
            self._parse_period(),
            SelectAll(),
            self._parse_weights(),
            Rebalance()
        ]


@dataclass
class TaskRolling(TaskAlgo):  # 轮动策略模板
    def get_algos(self):
        return [
            RunAlways(),
            V2SelectBySignal(rules_buy=self.rules_buy,
                           buy_at_least_count=self.at_least_buy,
                           rules_sell=self.rules_sell,
                           sell_at_least_count=self.at_least_sell,
                           b_before_rank = True
                           ),
            V2SelectTopK(factor_name=self.order_by, K=self.topK, drop_top_n=self.dropN,
                       b_ascending=self.b_ascending),
            self._parse_weights(),
            Rebalance()
        ]
'''
@dataclass
class TaskRolling_Model(TaskAlgo):  # 轮动策略模板
    def get_algos(self):
        return [
            RunAlways(),
            SelectBySignal(rules_buy=self.rules_buy,
                           buy_at_least_count=self.at_least_buy,
                           rules_sell=self.rules_sell,
                           sell_at_least_count=self.at_least_sell
                           ),
            SelectByModel(model_name=self.model_name,feature_names=self.fct_names),
            SelectTopK(factor_name=self.order_by, K=self.topK, drop_top_n=self.dropN,
                       b_ascending=self.b_ascending),
            self._parse_weights(),
            Rebalance()
        ]
'''
@dataclass
class TaskPickTime(TaskAlgo):  # 择时策略模板
    def get_algos(self):
        return [
            RunAlways(),
            V2SelectBySignal(rules_buy=self.rules_buy,
                           buy_at_least_count=self.at_least_buy,
                           rules_sell=self.rules_sell,
                           sell_at_least_count=self.at_least_sell,
                           b_before_rank = True
                           ),
            WeightEqually(),
            Rebalance()
        ]

