
from collections import namedtuple
import toml
from config import CONFIG_DIR
''' 因子投研'''
# ==== 基本参数配置
PAIRS    = ['510050.SH_15','510050.SH_30'] #内置pair全集
DAY_BARS = [16, 8  ]  # 对应的每日bar个数
ANN_DAYS = [252,252]  # 对应的年化天数
config_path = CONFIG_DIR / 'task_mining.toml'
T_DELAY = 1 # TODO:启动
TRADE_ON_OPEN = True
LABEL_SINGLE = ['label_1'] # 单label的模型训练
LABEL_MULTIPLE = ['label_1', 'label_2', 'label_3', 'label_4'] # 多label的模型训练
# ==== 算子检测后排除
EXPR_NAME_EXCLUDED = ['b_aroon_14','b_kdj_k','b_kdj_d','b_vwap_336','b_vwap_512',
                      'KUP','K_UP2','KLOW','KL_OW2','K_SFT2','K_MID2',
                      'VOLUME1','VOLUME2','VOLUME3','VOLUME4','CORR5','CORD5',
                      'CNTP5','CNTP10','CNTN5','CNTN10','CNTD5','SUMP5','SUMN5']
# ==== GP参数设置
GP_MODE = 'live' # 'test'(调试) or 'live'(真挖)
GP_LOOP = 1 # 6000 for weekend
MODEL_ALGO = 'linear'  # 或 'rf', 'xgb', 'linear', 'lgb'
# ==== L1因子筛选
L1_STEP1_ALGO = 'sic' # 'sr' - select by sharp; 'sic' - select by sic
# ==== norm计算参数设置
NormParams = namedtuple('NormParams', ['window', 'clip_num', 'log_method','MA_type','is_demean', 'algo_norm', 'algo_clip'])

# ma_type: 0不平滑; 1为ma平滑 | is_demean: True去均值,False不去均值 | algo_norm: 0为zscore, 1为除以L2norm, 2为MinMax, 3为鲁棒, 4为累计概率, 5为分位数norm(适合非线性模型,lightGBM)
# algo_clip: 0为不先clip, 1为先mad_clip
if MODEL_ALGO == 'linear':
    # ==== 线性模型配置如下
    NORM_PARAMS_DEFAULT = NormParams(window=2000, clip_num=6, log_method=0, MA_type=0, is_demean=True, algo_norm=0, algo_clip=1)
    NORM_PARAMS_LABEL = NormParams(window=2000, clip_num=2, log_method=0, MA_type=0, is_demean=False, algo_norm=0, algo_clip=0)
    NORM_PARAMS_POS = NormParams(window=2000, clip_num=2, log_method=0, MA_type=1, is_demean=False, algo_norm=0, algo_clip=0)
else: # 分位数norm擅长处理非线性模型拟合
    # ==== 分位数norm(因子值+LABEL), 适合非线性模型, 如lightGBM
    NORM_PARAMS_DEFAULT = NormParams(window=2000, clip_num=6, log_method=0, MA_type=0, is_demean=False, algo_norm=5, algo_clip=0) # 非线性模型,因子值
    NORM_PARAMS_LABEL = NormParams(window=2000, clip_num=2, log_method=0, MA_type=0, is_demean=False, algo_norm=5, algo_clip=0)
    NORM_PARAMS_POS = NormParams(window=2000, clip_num=2, log_method=0, MA_type=1, is_demean=False, algo_norm=5, algo_clip=0)

# ==== GP参数设置
if GP_MODE == 'live':
    GP_PARAMS = {
        # 基础参数
        'population_size': 20000,  # 一次生成因子的数量，初始10w; 测试500
        'hall_of_fame': 1000,     # >=n_components, 测试100
        'n_components': 600,      # 最终输出多少个因子？从相关性角度选了,其他都是metric角度, 测试100
        'tournament_size': 1000,  # 3000, 测试200
        'generations': 2,         # 非常非常非常重要！！！--进化多少轮次？3也就顶天了,一般2
        'init_depth': (1, 3),     # 第二重要的一个部位，控制我们公式的一个深度
        'parsimony_coefficient': 0.0005,  # Xv: 0.005 or None
        'const_range': None,      # (-1, 1), critical
    
        # 变异概率参数
        'p_crossover': 0.8,
        'p_subtree_mutation': 0.02,
        'p_hoist_mutation': 0.02,
        'p_point_mutation': 0.02,
        'p_point_replace': 0.5,   # 不参与概率大于1的检查
        
        # 其他参数
        'verbose': 1,
        'random_state': None
        # 'corrcoef_threshold': 0.8,  # 改进版新增
    }
elif GP_MODE == 'test':
    GP_PARAMS = {
        # 基础参数
        'population_size': 500,  # 一次生成因子的数量，初始10w; 测试500
        'hall_of_fame': 100,     # >=n_components, 测试100
        'n_components': 100,     # 最终输出多少个因子？从相关性角度选了,其他都是metric角度, 测试100
        'tournament_size': 200,  # 3000, 测试200
        'generations': 2,        # 非常非常非常重要！！！--进化多少轮次？3也就顶天了,一般2
        'init_depth': (1, 3),    # 第二重要的一个部位，控制我们公式的一个深度
        'parsimony_coefficient': 0.0005,  # Xv: 0.005 or None
        'const_range': None,     # (-1, 1), critical
    
        # 变异概率参数
        'p_crossover': 0.8,
        'p_subtree_mutation': 0.02,
        'p_hoist_mutation': 0.02,
        'p_point_mutation': 0.02,
        'p_point_replace': 0.5,   # 不参与概率大于1的检查
        
        # 其他参数
        'verbose': 1,
        'random_state': 42
        # 'corrcoef_threshold': 0.8,  # 改进版新增
    }

# ==== 绩效计算参数设置
config = toml.load(str(config_path))
# 
global_fee_rate = config.get('global', {}).get('fee_rate', 0.0021)
global_free_rate = config.get('global', {}).get('free_rate', 0.03)
#
pair_params = {}
pairs_config = config.get('pairs', {})
for pair, params in pairs_config.items():
    pair_params[pair] = {
        'fee_rate': params.get('fee_rate', global_fee_rate),
        'free_rate': params.get('free_rate', global_free_rate)
    }
#
MetricParams = namedtuple('MetricParams', ['day_bars', 'ann_days', 'fixed_return', 'fee_rate', 'weights', 'is_ret_open'])
METRIC_PARAMS_DEFAULT = {
    pair: MetricParams(
        day_bars=day_bar,
        ann_days=ann_day,
        fixed_return=pair_params.get(pair, {}).get('free_rate', global_free_rate),
        fee_rate=pair_params.get(pair, {}).get('fee_rate', global_fee_rate),
        weights=None,
        is_ret_open=TRADE_ON_OPEN
    )
    for pair, day_bar, ann_day in zip(PAIRS, DAY_BARS, ANN_DAYS)
}

''' 其他环境 '''
YEARS = ['2020', '2021', '2022', '2023', '2024']
# INTERVALS = ["1m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "8h", "12h", "1d", "3d", "1w", "1mo"]
INTERVALS = ["30m"]
# DAILY_INTERVALS = ["1m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "8h", "12h", "1d"]
DAILY_INTERVALS = ["30m"]
MONTHS = list(range(1,13))
MAX_DAYS = 35
BASE_URL = 'https://data.binance.vision/'