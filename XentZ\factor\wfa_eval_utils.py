#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WFA验证核心算法库 - 最终优化版本
采用过程式编程风格，注重执行效率和逻辑严谨性
移除所有降级处理，使用项目封装的日志系统
"""

import numpy as np
import pandas as pd
from dataclasses import dataclass
from typing import Dict, List, Tuple, Callable
from datetime import datetime

# 项目内部导入
from common.cls_base import BaseObj
from factor.factor_perf import rank_1d, FactorPerf


@dataclass
class WFAParams:
    """WFA验证参数配置，支持多周期数据"""
    training_window: int = 750      # 训练窗口大小(约3年)
    testing_window: int = 60        # 测试窗口大小(约3个月)
    step_size: int = 60             # 步进大小，等于测试窗口避免重叠
    tanh_k: float = 5.0             # S型映射参数，平衡噪声过滤与信号响应
    hold_n: int = 1                 # 持有期(交易日)
    gap: int = 1                    # 时序验证间隔，避免信息泄露
    min_periods: int = 50           # 最小有效样本数
    correlation_method: str = "spearman"  # 相关性计算方法

    def __post_init__(self):
        """参数验证"""
        if self.training_window < 10:
            raise ValueError("训练窗口过小，至少需要10个周期")
        if self.testing_window < 2:
            raise ValueError("测试窗口过小，至少需要2个周期")
        if self.tanh_k <= 0:
            raise ValueError("tanh_k参数必须大于0")

    @staticmethod
    def get_annualization_factor(data_freq: str) -> int:
        """根据数据周期获取年化因子"""
        annualization_map = {
            'D': 252,   # 日线：252个交易日/年
            'W': 52,    # 周线：52周/年
            'M': 12,    # 月线：12个月/年
            'Q': 4,     # 季线：4个季度/年
            'Y': 1      # 年线：1年/年
        }
        return annualization_map.get(data_freq.upper(), 252)

    @classmethod
    def create_for_frequency(cls, data_freq: str, base_config: dict = None):
        """根据数据周期创建优化的WFA参数

        Args:
            data_freq: 数据周期，从dataset配置中获取
            base_config: 基础配置字典，从toml文件中读取
        """
        # 预设的最优参数配置
        optimal_configs = {
            'D': {  # 日线数据
                'training_window': 750,
                'testing_window': 60,
                'step_size': 60,
                'min_periods': 50
            },
            'W': {  # 周线数据
                'training_window': 150,
                'testing_window': 12,
                'step_size': 12,
                'min_periods': 10
            },
            'M': {  # 月线数据
                'training_window': 36,
                'testing_window': 6,
                'step_size': 6,
                'min_periods': 3
            },
            'Q': {  # 季线数据
                'training_window': 12,
                'testing_window': 2,
                'step_size': 2,
                'min_periods': 2
            },
            'Y': {  # 年线数据
                'training_window': 5,
                'testing_window': 1,
                'step_size': 1,
                'min_periods': 1
            }
        }

        # 获取对应周期的最优配置
        freq_key = data_freq.upper()
        config = optimal_configs.get(freq_key, optimal_configs['D']).copy()

        # 如果提供了基础配置，则覆盖默认值
        if base_config:
            config.update(base_config)

        return cls(**config)


@dataclass
class WFAResult:
    """WFA验证结果"""
    factor_id: str                  # 因子ID
    symbol: str                     # 标的代码
    pnl_series: pd.Series          # PnL序列
    position_series: pd.Series     # 仓位序列
    metrics: Dict[str, float]      # 绩效指标
    test_periods: List[Dict]       # 测试期详情
    pass_status: str               # 通过状态: L3_PASSED/L3_FAILED
    fail_reasons: List[str]        # 失败原因列表
    
    def __post_init__(self):
        """数据验证"""
        if self.pnl_series is None or len(self.pnl_series) == 0:
            raise ValueError("PnL序列不能为空")
        if self.pass_status not in ['L3_PASSED', 'L3_FAILED', 'PENDING']:
            raise ValueError("通过状态必须是L3_PASSED、L3_FAILED或PENDING")


class WFAValidator(BaseObj):
    """WFA验证器，使用项目封装的日志系统"""
    
    def __init__(self):
        super().__init__()
        self.log("WFA验证器初始化完成", "INFO")
    
    def calculate_ecdf_mapping(self, factor_values: pd.Series) -> Callable:
        """学习因子值的经验累积分布函数(ECDF)"""
        self.log(f"开始学习ECDF，训练样本数: {len(factor_values)}", "DEBUG")
        
        # 数据清洗：移除缺失值
        clean_values = factor_values.dropna()
        if len(clean_values) < 10:
            raise ValueError(f"训练数据不足，至少需要10个有效值，当前仅有{len(clean_values)}个")
        
        # 构建经验分布：排序后的因子值作为分位点
        sorted_values = np.sort(clean_values.values)
        n = len(sorted_values)
        
        self.log(f"ECDF构建完成，分位点数量: {n}", "DEBUG")
        
        def mapping_func(new_values: pd.Series) -> pd.Series:
            """将新的因子值映射为百分位排名[0,1]"""
            if len(new_values) == 0:
                return pd.Series(dtype=float)
            
            # 使用searchsorted进行高效查找
            ranks = np.searchsorted(sorted_values, new_values.values, side='right')
            percentiles = ranks / n
            
            # 边界处理：确保在[0.001, 0.999]范围内，避免极端值
            percentiles = np.clip(percentiles, 0.001, 0.999)
            
            return pd.Series(percentiles, index=new_values.index)
        
        return mapping_func
    
    def apply_tanh_position_mapping(self, percentiles: pd.Series, 
                                   tanh_k: float = 5.0,
                                   direction: int = 1) -> pd.Series:
        """使用tanh函数进行S型仓位映射"""
        self.log(f"执行S型仓位映射，tanh_k={tanh_k}, direction={direction}", "DEBUG")
        
        if len(percentiles) == 0:
            return pd.Series(dtype=float)
        
        # 核心映射公式：tanh(k * (percentile - 0.5)) * direction
        base_positions = np.tanh(tanh_k * (percentiles - 0.5))
        final_positions = base_positions * direction
        
        # 安全边界：确保仓位在合理范围内
        final_positions = np.clip(final_positions, -1.0, 1.0)
        
        self.log(f"仓位映射完成，范围: [{final_positions.min():.3f}, {final_positions.max():.3f}]", "DEBUG")
        
        return pd.Series(final_positions, index=percentiles.index)
    
    def calculate_spearman_correlation(self, factor_values: pd.Series,
                                     returns: pd.Series) -> float:
        """计算Spearman相关性，使用numba加速"""
        self.log("开始计算Spearman相关性", "DEBUG")
        
        # 数据对齐和清洗
        aligned_data = pd.concat([factor_values, returns], axis=1).dropna()
        if len(aligned_data) < 10:
            self.log(f"有效数据点不足: {len(aligned_data)}", "WARNING")
            return 0.0
        
        # 使用numba加速的排名计算
        factor_ranks = rank_1d(aligned_data.iloc[:, 0].values.astype(np.float32))
        return_ranks = rank_1d(aligned_data.iloc[:, 1].values.astype(np.float32))
        
        # 计算Pearson相关系数（对排名数据等价于Spearman）
        correlation = np.corrcoef(factor_ranks, return_ranks)[0, 1]
        
        if np.isnan(correlation):
            self.log("相关性计算结果为NaN", "WARNING")
            return 0.0
            
        self.log(f"Spearman相关性: {correlation:.4f}", "DEBUG")
        return correlation
    
    def run_wfa_validation(self, factor_data: pd.Series,
                          price_data: pd.Series,
                          wfa_params: WFAParams,
                          data_freq: str = "D") -> WFAResult:
        """WFA验证主流程"""
        self.log(f"开始WFA验证: 训练窗口={wfa_params.training_window}, 测试窗口={wfa_params.testing_window}", "INFO")
        
        start_time = datetime.now()
        
        # 1. 数据预处理和对齐
        self.log("执行数据预处理和对齐", "DEBUG")
        aligned_data = pd.concat([factor_data, price_data], axis=1).dropna()
        
        min_required_length = wfa_params.training_window + wfa_params.testing_window
        if len(aligned_data) < min_required_length:
            raise ValueError(f"数据长度不足以进行WFA验证，需要至少{min_required_length}个数据点，当前仅有{len(aligned_data)}个")
        
        factor_aligned = aligned_data.iloc[:, 0]
        price_aligned = aligned_data.iloc[:, 1]
        
        # 计算收益率序列：考虑持有期和间隔
        returns = price_aligned.pct_change(wfa_params.hold_n).shift(-wfa_params.hold_n - wfa_params.gap + 1)
        
        self.log(f"数据对齐完成，有效数据点: {len(factor_aligned)}", "DEBUG")
        
        # 2. 滚动窗口验证
        self.log("开始滚动窗口验证", "DEBUG")
        pnl_segments = []
        position_segments = []
        test_periods = []
        
        start_idx = 0
        total_windows = 0
        successful_windows = 0
        
        while start_idx + wfa_params.training_window + wfa_params.testing_window <= len(factor_aligned):
            total_windows += 1
            
            # 窗口数据切分
            train_end = start_idx + wfa_params.training_window
            test_end = train_end + wfa_params.testing_window
            
            factor_train = factor_aligned.iloc[start_idx:train_end]
            factor_test = factor_aligned.iloc[train_end:test_end]
            returns_train = returns.iloc[start_idx:train_end]
            returns_test = returns.iloc[train_end:test_end]
            
            self.log(f"处理窗口 {total_windows}: 训练期 {factor_train.index[0]} - {factor_train.index[-1]}", "DEBUG")
            
            # 训练阶段：学习分布和方向
            ecdf_mapping = self.calculate_ecdf_mapping(factor_train)
            correlation = self.calculate_spearman_correlation(factor_train, returns_train)
            direction = 1 if correlation > 0 else -1
            
            # 测试阶段：仓位映射和PnL计算
            test_percentiles = ecdf_mapping(factor_test)
            test_positions = self.apply_tanh_position_mapping(test_percentiles, wfa_params.tanh_k, direction)
            
            # 计算测试期PnL
            test_pnl = test_positions * returns_test
            test_pnl = test_pnl.dropna()
            
            if len(test_pnl) >= wfa_params.min_periods:
                pnl_segments.append(test_pnl)
                position_segments.append(test_positions)
                successful_windows += 1
                
                # 记录测试期信息
                test_periods.append({
                    'window_id': total_windows,
                    'train_start': factor_train.index[0],
                    'train_end': factor_train.index[-1],
                    'test_start': factor_test.index[0],
                    'test_end': factor_test.index[-1],
                    'direction': direction,
                    'correlation': float(correlation),
                    'test_pnl_mean': float(test_pnl.mean()),
                    'test_pnl_std': float(test_pnl.std()),
                    'test_periods_count': len(test_pnl)
                })
                
                self.log(f"窗口 {total_windows} 成功，方向={direction}, 相关性={correlation:.4f}", "DEBUG")
            else:
                self.log(f"窗口 {total_windows} 有效数据点不足: {len(test_pnl)}", "WARNING")
            
            # 滚动前进
            start_idx += wfa_params.step_size
        
        # 3. 验证结果
        if not pnl_segments:
            raise ValueError("没有有效的测试期结果，WFA验证失败")
        
        self.log(f"滚动窗口验证完成: 总窗口={total_windows}, 成功窗口={successful_windows}", "INFO")
        
        # 4. 拼接PnL序列
        full_pnl_series = pd.concat(pnl_segments).sort_index()
        full_position_series = pd.concat(position_segments).sort_index()
        
        # 5. 计算绩效指标
        self.log("计算整体绩效指标", "DEBUG")
        annualization_factor = WFAParams.get_annualization_factor(data_freq)
        metrics = FactorPerf.calc_single_asset_metrics(full_pnl_series, annualization_factor)
        
        # 6. 构造结果对象
        result = WFAResult(
            factor_id=factor_data.name or 'unknown',
            symbol='unknown',  # 需要从外部传入
            pnl_series=full_pnl_series,
            position_series=full_position_series,
            metrics=metrics,
            test_periods=test_periods,
            pass_status='PENDING',  # 需要后续判定
            fail_reasons=[]
        )
        
        elapsed_time = (datetime.now() - start_time).total_seconds()
        self.log(f"WFA验证完成: 耗时={elapsed_time:.2f}秒, PnL长度={len(full_pnl_series)}, 夏普比率={metrics.get('sharpe_ratio', 0):.3f}", "INFO")
        
        return result

    def check_wfa_criteria(self, metrics: Dict[str, float], criteria_params: Dict[str, float]) -> Tuple[str, List[str]]:
        """检查WFA是否通过统一标准"""
        self.log("开始检查WFA通过标准", "DEBUG")

        fail_reasons = []

        # 主要标准检查（必须全部满足）
        if metrics.get('sharpe_ratio', 0) < criteria_params.get('min_sharpe', 0.5):
            fail_reasons.append(f"夏普比率过低: {metrics.get('sharpe_ratio', 0):.3f} < {criteria_params.get('min_sharpe', 0.5)}")

        if metrics.get('max_drawdown', 1) > criteria_params.get('max_mdd', 0.30):
            fail_reasons.append(f"最大回撤过大: {metrics.get('max_drawdown', 1):.2%} > {criteria_params.get('max_mdd', 0.30):.2%}")

        if metrics.get('win_rate', 0) < criteria_params.get('min_win_rate', 0.55):
            fail_reasons.append(f"胜率过低: {metrics.get('win_rate', 0):.2%} < {criteria_params.get('min_win_rate', 0.55):.2%}")

        # 补充标准检查（可选）
        if 'min_calmar' in criteria_params and metrics.get('calmar_ratio', 0) < criteria_params['min_calmar']:
            fail_reasons.append(f"卡玛比率过低: {metrics.get('calmar_ratio', 0):.3f} < {criteria_params['min_calmar']}")

        # 判定结果
        if len(fail_reasons) == 0:
            self.log("WFA验证通过所有标准", "INFO")
            return 'L3_PASSED', []
        else:
            self.log(f"WFA验证未通过，失败原因数: {len(fail_reasons)}", "INFO")
            return 'L3_FAILED', fail_reasons

    def validate_wfa_inputs(self, factor_data: pd.Series, price_data: pd.Series, wfa_params: WFAParams) -> None:
        """验证WFA输入数据和参数的有效性"""
        # 检查数据有效性
        if factor_data is None or len(factor_data) == 0:
            raise ValueError("因子数据不能为空")

        if price_data is None or len(price_data) == 0:
            raise ValueError("价格数据不能为空")

        # 检查数据长度
        min_required_length = wfa_params.training_window + wfa_params.testing_window
        if len(factor_data) < min_required_length:
            raise ValueError(f"因子数据长度不足: {len(factor_data)} < {min_required_length}")

        if len(price_data) < min_required_length:
            raise ValueError(f"价格数据长度不足: {len(price_data)} < {min_required_length}")

        # 检查数据质量
        factor_valid_ratio = factor_data.notna().mean()
        if factor_valid_ratio < 0.8:
            raise ValueError(f"因子数据缺失过多: {factor_valid_ratio:.2%} < 80%")

        price_valid_ratio = price_data.notna().mean()
        if price_valid_ratio < 0.8:
            raise ValueError(f"价格数据缺失过多: {price_valid_ratio:.2%} < 80%")

        self.log(f"输入验证通过: 因子数据{len(factor_data)}点, 价格数据{len(price_data)}点", "DEBUG")


# 全局实例，供外部直接调用
wfa_validator = WFAValidator()


# 便捷函数，保持向后兼容
def run_wfa_validation(factor_data: pd.Series, price_data: pd.Series, wfa_params: WFAParams, data_freq: str = "D") -> WFAResult:
    """便捷函数：执行WFA验证"""
    return wfa_validator.run_wfa_validation(factor_data, price_data, wfa_params, data_freq)


def check_wfa_criteria(metrics: Dict[str, float], criteria_params: Dict[str, float]) -> Tuple[str, List[str]]:
    """便捷函数：检查WFA通过标准"""
    return wfa_validator.check_wfa_criteria(metrics, criteria_params)


def validate_wfa_inputs(factor_data: pd.Series, price_data: pd.Series, wfa_params: WFAParams) -> None:
    """便捷函数：验证WFA输入"""
    return wfa_validator.validate_wfa_inputs(factor_data, price_data, wfa_params)


def load_wfa_config_from_toml(config_path: str = "config/tasks/ts_l3_wfa.toml") -> Tuple[WFAParams, str, Dict]:
    """
    从toml配置文件加载WFA配置

    Args:
        config_path: 配置文件路径

    Returns:
        (wfa_params, data_freq, criteria_params): WFA参数、数据周期、通过标准
    """
    try:
        from dynaconf import Dynaconf

        # 加载配置文件
        config = Dynaconf(settings_files=[config_path])

        # 获取数据周期信息（从dataset配置中读取）
        data_freq = config.get('dataset', {}).get('freq', 'D')

        # 获取WFA基础配置
        wfa_config = config.get('wfa', {})

        # 根据数据周期创建优化的WFA参数
        wfa_params = WFAParams.create_for_frequency(data_freq, wfa_config)

        # 获取通过标准配置
        criteria_params = config.get('criteria', {})

        return wfa_params, data_freq, criteria_params

    except Exception as e:
        # 如果配置加载失败，使用默认配置
        print(f"配置加载失败，使用默认配置: {str(e)}")
        default_params = WFAParams()
        default_criteria = {
            'min_sharpe': 0.5,
            'max_mdd': 0.30,
            'min_win_rate': 0.55
        }
        return default_params, "D", default_criteria
