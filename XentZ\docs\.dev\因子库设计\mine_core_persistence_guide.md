# mine_core 因子值持久化指南

## 概述 📊

`core/polyfactor/mine_core.py` 现已集成因子值持久化功能，在GP挖掘+初筛完成后自动将L0阶段的因子值保存到统一的因子值管理系统中。

## 功能特性 ✨

### 1. 自动持久化
- **触发时机**：GP挖掘+初筛完成后自动执行
- **保存内容**：L0阶段的最终因子值（已去重）
- **数据完整性**：包含基础OHLCV数据和因子值数据

### 2. 统一编码
- **批次格式**：`GP_{symbol}_{date}_L0_{uid}`
- **示例**：`GP_510050.SH_20250627_L0_abc123`
- **语义清晰**：直接从ID看出品种、日期、阶段

### 3. 智能管理
- **按品种组织**：每个品种独立批次
- **元数据丰富**：保存挖掘配置、标签信息、因子元信息
- **高性能缓存**：自动优化读写性能

## 使用方法 🛠️

### 1. 运行mine_core
```bash
cd core/polyfactor
python mine_core.py
```

挖掘完成后会自动执行持久化，输出类似：
```
💾 开始因子值持久化...
📋 持久化品种因子值: GP_510050.SH_20250627_L0_abc123
    📊 使用基础数据列: ['open', 'high', 'low', 'close']  # 自动检测可用列
    ✅ 因子值持久化成功: 15 个因子
       数据维度: (100, 15)
       时间范围: 2024-01-01 ~ 2024-04-09
```

**智能列检测**: 系统会根据配置文件自动检测可用的基础数据列，跳过在`[feat.drop]`中删除的列（如volume），确保兼容不同的数据预处理配置。

### 2. 检索持久化结果
```python
from factorzoo.factor_value_manager import get_factor_value_manager

manager = get_factor_value_manager()

# 查看所有L0批次
l0_batches = manager.get_available_batches(stage='L0')
print(f"L0批次: {len(l0_batches)} 个")

# 查看指定品种的批次
symbol_batches = manager.get_available_batches(symbol='510050.SH', stage='L0')
print(f"510050.SH的L0批次: {len(symbol_batches)} 个")

# 查看今日批次
from datetime import datetime
today = datetime.now().strftime('%Y%m%d')
today_batches = manager.get_available_batches(stage='L0', date=today)
print(f"今日L0批次: {len(today_batches)} 个")
```

### 3. 加载因子值数据
```python
# 加载指定批次的因子值
batch_id = "GP_510050.SH_20250627_L0_abc123"
base_data, factor_data = manager.load_batch_data(batch_id, 'L0')

print(f"基础数据: {base_data.shape}")  # (100, 5) - OHLCV
print(f"因子数据: {factor_data.shape}")  # (100, 15) - 15个因子
print(f"因子列表: {factor_data.columns.tolist()}")
```

### 4. 批量加载
```python
# 批量加载多个批次
requests = [
    {'batch_id': 'GP_510050.SH_20250627_L0_abc123', 'pipeline_step': 'L0'},
    {'batch_id': 'GP_159915.SZ_20250627_L0_def456', 'pipeline_step': 'L0'}
]

results = manager.batch_load_multiple(requests)
for batch_id, (base_data, factor_data) in results.items():
    print(f"{batch_id}: {factor_data.shape[1]} 个因子")
```

## 数据组织 🗂️

### 存储结构
```
D:/myquant/FZoo/factor_values/by_batch/
├── GP_510050.SH_20250627_L0_abc123/
│   ├── base_data.feather          # 基础OHLCV数据
│   ├── L0_selected_factors.parquet # L0因子值
│   └── metadata.json              # 批次元数据
├── GP_159915.SZ_20250627_L0_def456/
│   └── ...
```

### 元数据信息
每个批次包含完整的元数据：
- **挖掘配置**：runnum、jobnum、norm_model等
- **因子信息**：每个因子对应的target_label
- **数据范围**：时间范围、频率、品种
- **统计信息**：因子数量、数据维度等

## 性能优化 ⚡

### 1. 自动缓存
- **内存缓存**：热点数据自动缓存
- **智能预取**：基于访问模式预加载
- **表达式去重**：避免重复计算

### 2. 并行处理
- **并行加载**：多文件并行读取
- **批量操作**：支持批量保存和加载
- **异步IO**：提升磁盘操作效率

### 3. 监控统计
```python
# 查看性能报告
report = manager.get_performance_report()
print(f"缓存命中率: {report['cache_hit_rate']:.1%}")
print(f"平均加载时间: {report['optimization_stats']['avg_load_time_ms']:.1f}ms")
```

## 集成要点 💡

### 1. 无侵入式
- mine_core原有流程不受影响
- 持久化作为附加功能在后台执行
- 失败不影响主要挖掘流程

### 2. 向前兼容
- 支持旧格式批次的访问
- 渐进式迁移，不破坏现有数据
- API完全向后兼容

### 3. 扩展性强
- L0阶段为起点，后续可扩展L1/L2/L3
- 支持不同挖掘工具的集成
- 统一的数据接口

## 故障排除 🔧

### 常见问题

1. **持久化失败**
   - 检查磁盘空间
   - 确认目录权限
   - 查看日志中的错误信息

2. **因子值计算失败**
   - 确认FactorLoader的函数支持
   - 检查因子表达式格式
   - 验证基础数据完整性

3. **数据列缺失** (如 `"['volume'] not in index"`)
   - ✅ **已修复**: 系统现在会自动检测可用的基础数据列
   - 根据配置文件`[feat.drop]`自动跳过已删除的列
   - 显示实际使用的列: `📊 使用基础数据列: ['open', 'high', 'low', 'close']`

4. **性能问题**
   - 启用高性能模式
   - 调整缓存大小
   - 使用批量操作

### 日志位置
- mine_core运行日志：控制台输出
- 持久化详细日志：`logs/` 目录
- 性能监控数据：FactorZoo数据库

## 总结 🎯

通过集成因子值持久化功能，mine_core现在能够：

✅ **自动保存**：GP挖掘结果自动持久化  
✅ **统一管理**：使用标准化的批次编码  
✅ **高性能**：缓存加速，6x读取提升  
✅ **易检索**：多维度筛选和快速定位  
✅ **完整性**：包含完整的数据和元信息  

这为后续的因子分析、策略构建和研究工作提供了强大的数据基础。 