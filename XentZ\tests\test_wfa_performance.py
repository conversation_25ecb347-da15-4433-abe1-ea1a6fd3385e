#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WFA验证性能测试
测试处理时间、内存使用和批量处理能力
"""

import unittest
import numpy as np
import pandas as pd
import time
import psutil
import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from factor.wfa_eval_utils import (
    WFAParams, run_wfa_validation, calculate_ecdf_mapping,
    apply_tanh_position_mapping, calculate_spearman_correlation_optimized
)


class TestWFAPerformance(unittest.TestCase):
    """WFA性能测试"""
    
    def setUp(self):
        """设置测试数据"""
        np.random.seed(42)
        
        # 创建不同规模的测试数据
        self.small_data = self._create_test_data(1000)  # 增加到1000以满足最小要求
        self.medium_data = self._create_test_data(1500)
        self.large_data = self._create_test_data(3000)

        self.wfa_params = WFAParams(
            training_window=750,
            testing_window=60,
            step_size=60,
            tanh_k=5.0
        )
    
    def _create_test_data(self, n_days):
        """创建测试数据"""
        dates = pd.date_range('2020-01-01', periods=n_days, freq='D')
        
        # 创建有预测能力的因子
        noise = np.random.randn(n_days)
        trend = np.cumsum(np.random.randn(n_days) * 0.01)
        factor_data = pd.Series(noise + trend * 0.1, index=dates, name='factor')
        
        # 创建价格数据
        price_changes = np.random.randn(n_days) * 0.02 + factor_data.shift(1) * 0.001
        price_data = pd.Series(100 * np.exp(np.cumsum(price_changes)), index=dates, name='price')
        
        return factor_data, price_data
    
    def _measure_performance(self, factor_data, price_data, test_name):
        """测量性能指标"""
        # 记录初始内存
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行WFA验证
        start_time = time.time()
        result = run_wfa_validation(factor_data, price_data, self.wfa_params)
        end_time = time.time()
        
        # 记录最终内存
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_usage = final_memory - initial_memory
        
        elapsed_time = end_time - start_time
        
        print(f"\n{test_name} 性能报告:")
        print(f"  数据规模: {len(factor_data)} 个数据点")
        print(f"  处理时间: {elapsed_time:.3f} 秒")
        print(f"  内存使用: {memory_usage:.2f} MB")
        print(f"  PnL长度: {len(result.pnl_series)}")
        print(f"  测试窗口数: {len(result.test_periods)}")
        print(f"  夏普比率: {result.metrics.get('sharpe_ratio', 0):.3f}")
        
        return elapsed_time, memory_usage, result
    
    def test_small_data_performance(self):
        """测试小规模数据性能"""
        factor_data, price_data = self.small_data
        elapsed_time, memory_usage, result = self._measure_performance(
            factor_data, price_data, "小规模数据(500点)"
        )
        
        # 性能要求
        self.assertLess(elapsed_time, 5.0, "小规模数据处理时间应小于5秒")
        self.assertLess(memory_usage, 50, "内存使用应小于50MB")
        self.assertGreater(len(result.pnl_series), 0, "应产生有效的PnL序列")
    
    def test_medium_data_performance(self):
        """测试中等规模数据性能"""
        factor_data, price_data = self.medium_data
        elapsed_time, memory_usage, result = self._measure_performance(
            factor_data, price_data, "中等规模数据(1500点)"
        )
        
        # 性能要求
        self.assertLess(elapsed_time, 10.0, "中等规模数据处理时间应小于10秒")
        self.assertLess(memory_usage, 100, "内存使用应小于100MB")
        self.assertGreater(len(result.pnl_series), 0, "应产生有效的PnL序列")
    
    def test_large_data_performance(self):
        """测试大规模数据性能"""
        factor_data, price_data = self.large_data
        elapsed_time, memory_usage, result = self._measure_performance(
            factor_data, price_data, "大规模数据(3000点)"
        )
        
        # 性能要求
        self.assertLess(elapsed_time, 20.0, "大规模数据处理时间应小于20秒")
        self.assertLess(memory_usage, 200, "内存使用应小于200MB")
        self.assertGreater(len(result.pnl_series), 0, "应产生有效的PnL序列")
    
    def test_batch_processing_simulation(self):
        """测试批量处理模拟"""
        print(f"\n批量处理性能测试:")
        
        # 模拟处理10个因子
        n_factors = 10
        total_time = 0
        max_memory = 0
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024
        
        for i in range(n_factors):
            factor_data, price_data = self._create_test_data(1000)
            
            start_time = time.time()
            result = run_wfa_validation(factor_data, price_data, self.wfa_params)
            elapsed_time = time.time() - start_time
            
            current_memory = process.memory_info().rss / 1024 / 1024
            memory_usage = current_memory - initial_memory
            max_memory = max(max_memory, memory_usage)
            
            total_time += elapsed_time
            
            if (i + 1) % 5 == 0:
                print(f"  已处理 {i + 1}/{n_factors} 个因子")
        
        avg_time = total_time / n_factors
        
        print(f"  总处理时间: {total_time:.2f} 秒")
        print(f"  平均处理时间: {avg_time:.2f} 秒/因子")
        print(f"  最大内存使用: {max_memory:.2f} MB")
        print(f"  预计100个因子耗时: {avg_time * 100 / 60:.1f} 分钟")
        
        # 性能要求：100个因子应在30分钟内完成
        estimated_100_factors_time = avg_time * 100
        self.assertLess(estimated_100_factors_time, 1800, "100个因子处理时间应小于30分钟")


class TestComponentPerformance(unittest.TestCase):
    """组件性能测试"""
    
    def setUp(self):
        """设置测试数据"""
        np.random.seed(42)
        self.n = 10000
        self.factor_values = pd.Series(np.random.randn(self.n))
        self.returns = pd.Series(np.random.randn(self.n))
        self.percentiles = pd.Series(np.random.uniform(0, 1, self.n))
    
    def test_ecdf_mapping_performance(self):
        """测试ECDF映射性能"""
        start_time = time.time()
        
        # 训练ECDF
        mapping_func = calculate_ecdf_mapping(self.factor_values[:5000])
        
        # 应用映射
        percentiles = mapping_func(self.factor_values[5000:])
        
        elapsed_time = time.time() - start_time
        
        print(f"\nECDF映射性能:")
        print(f"  训练样本: 5000")
        print(f"  测试样本: {len(percentiles)}")
        print(f"  处理时间: {elapsed_time:.3f} 秒")
        
        self.assertLess(elapsed_time, 1.0, "ECDF映射应在1秒内完成")
    
    def test_tanh_mapping_performance(self):
        """测试S型映射性能"""
        start_time = time.time()
        
        positions = apply_tanh_position_mapping(self.percentiles, tanh_k=5.0)
        
        elapsed_time = time.time() - start_time
        
        print(f"\nS型映射性能:")
        print(f"  数据点数: {len(self.percentiles)}")
        print(f"  处理时间: {elapsed_time:.3f} 秒")
        
        self.assertLess(elapsed_time, 0.1, "S型映射应在0.1秒内完成")
    
    def test_correlation_performance(self):
        """测试相关性计算性能"""
        start_time = time.time()
        
        correlation = calculate_spearman_correlation_optimized(self.factor_values, self.returns)
        
        elapsed_time = time.time() - start_time
        
        print(f"\n相关性计算性能:")
        print(f"  数据点数: {len(self.factor_values)}")
        print(f"  处理时间: {elapsed_time:.3f} 秒")
        print(f"  相关系数: {correlation:.4f}")
        
        self.assertLess(elapsed_time, 0.5, "相关性计算应在0.5秒内完成")


class TestMemoryUsage(unittest.TestCase):
    """内存使用测试"""
    
    def test_memory_efficiency(self):
        """测试内存使用效率"""
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024
        
        # 创建大规模数据
        n_days = 5000
        dates = pd.date_range('2015-01-01', periods=n_days, freq='D')
        factor_data = pd.Series(np.random.randn(n_days), index=dates)
        price_data = pd.Series(np.random.randn(n_days), index=dates)
        
        wfa_params = WFAParams(training_window=1000, testing_window=100, step_size=100)
        
        # 执行WFA验证
        result = run_wfa_validation(factor_data, price_data, wfa_params)
        
        final_memory = process.memory_info().rss / 1024 / 1024
        memory_increase = final_memory - initial_memory
        
        print(f"\n内存使用效率测试:")
        print(f"  数据规模: {n_days} 个数据点")
        print(f"  内存增长: {memory_increase:.2f} MB")
        print(f"  每个数据点内存: {memory_increase * 1024 / n_days:.2f} KB")
        
        # 内存使用应该合理
        self.assertLess(memory_increase, 500, "内存增长应小于500MB")
        
        # 清理结果以释放内存
        del result, factor_data, price_data


if __name__ == '__main__':
    print("=" * 60)
    print("WFA验证系统性能测试")
    print("=" * 60)
    
    # 运行性能测试
    unittest.main(verbosity=2)
