# 单ETF时序数据集配置
# 用于定义单个或多个ETF作为研究和回测目标时的数据加载、切分和预处理方式。

[dataset]
name = "Single_ETF_TimeSeries"
desc = "主要用于单标的或少数几个ETF的特征工程、因子挖掘和策略回测"

# ============================== 数据源定义 ============================== #
[data_source]
symbols = ["510050.SH"] # 默认用于快速测试的标的
# 备选标的池, 可在具体任务中引用或覆盖:
# symbols_pool = ["510050.SH", "510300.SH", "000016.SH", "159915.SZ", "159934.SZ", "510880.SH", "513100.SH", "513520.SH"]
freq = "D"

[data_source.hku_loader]
# 数据复权方式: "EQUAL_FORWARD", "EQUAL_BACKWARD", None
recover = "EQUAL_BACKWARD"

# ============================== 数据切分 ============================== #
[time_split]
# 定义不同阶段的数据范围, 若不指定end_date, 则默认取到最新
train_start_date = "2020-01-01"
train_end_date = "2023-01-01"

target_start_date = "2023-01-02"
target_end_date = "2024-01-01"