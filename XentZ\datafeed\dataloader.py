import pandas as pd
from tqdm import tqdm
from loguru import logger
from datetime import datetime

from datafeed.expr_funcs.expr import calc_expr
from common.cls_base import BaseObj

class DataLoader(BaseObj):
    def __init__(self):
        pass
    
    @staticmethod
    def get_col_pivot_df(df_all, col='close', start_date='20100101', 
                   end_date=datetime.now().strftime('%Y%m%d')):
        '''
        从df_all中获取指定列的数据,返回透视图, 适合多品种的同列对比
        '''
        if df_all.index.name != 'datetime':
            logger.error('df_all的index必须为datetime')
            return None
        if col not in df_all.columns:
            logger.error(f'{col}列不存在')
            return None
        df_close = df_all.pivot_table(values=col, index=df_all.index, columns='symbol', dropna=False)
        df_close.ffill(inplace=True)
        df_close = df_close[start_date:]
        df_close = df_close[:end_date]
        if type(df_close) is pd.Series:
            df_close = df_close.to_frame()
        # df_close.fillna(0, inplace=True)
        df_close.dropna(inplace=True)
        return df_close

if __name__ == '__main__':
    import pandas as pd
    from datafeed.dataloader import CSVDataloader

    df = CSVDataloader.get_df(set_index=True, symbols=['510300.SH', '159915.SZ'])
    print(df)
    expr = 'r_rsrs(high,low,18,600)'
    df = CSVDataloader.calc_expr(df, [expr],
                                 ['rsrs'])
    print(df)
