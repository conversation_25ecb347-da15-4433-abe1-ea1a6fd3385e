from time import time
import numpy as np
import pandas as pd
from numpy.typing import NDArray
from typing import Union

# 第一个 mad_clip 函数
def mad_clip_v1(df: Union[NDArray, pd.DataFrame], k: int = 3, axis=0):
    if isinstance(df, pd.DataFrame):
        df = df.values
    # if df.ndim > 1:
    #     if df.shape[1] == 1:
    #         df = df.ravel()
    #     else:
    #         raise ValueError("mad_clip 函数只支持一维数组作为输入")   
    if axis == 0 or axis == 1:
        med = np.median(df[np.isfinite(df)], axis=axis)
        mad = np.median(np.abs(df[np.isfinite(df)] - med), axis=axis)
    else:
        raise ValueError("轴方向（axis）必须是0或1")
    
    mad = np.where(mad == 0, np.nan, mad)
    
    if axis == 0:
        lower_bound = med - k * 1.4826 * mad
        upper_bound = med + k * mad
    elif axis == 1:
        lower_bound = med[:, np.newaxis] - k * 1.4826 * mad[:, np.newaxis]
        upper_bound = med[:, np.newaxis] + k * mad[:, np.newaxis]

    lower_bound = np.clip(lower_bound, -np.inf, np.inf)
    upper_bound = np.clip(upper_bound, -np.inf, np.inf)

    return np.clip(df, lower_bound, upper_bound)

# 第二个 mad_clip 函数 (改为 axis=0)
def mad_clip_v2(df: Union[NDArray, pd.DataFrame], k: int = 3, axis=0):
    ''' 使用 MAD 3 倍截断法去极值
    Args:
    df: 输入数据，要求索引为日期，资产名为列，单元格值为因子的宽表
    k: 截断倍数。
    axis:截断方向
    '''
    med = np.median(df, axis=axis).reshape(-1, df.shape[1])
    mad = np.median(np.abs(df - med), axis=axis)
    return np.clip(df, med.flatten() - k * 1.4826 * mad, med.flatten() + k * mad)

# 测试数据生成
np.random.seed(42)
data = np.random.randn(1000000, 5)  # 生成100行5列的随机数据

# 转换为 DataFrame
df = pd.DataFrame(data, columns=['A', 'B', 'C', 'D', 'E'])

# 调用第一个函数 (axis=0)
start = time()
result_v1 = mad_clip_v1(df, k=3, axis=0)
end = time()
print('v1: ', end - start)

# 调用第二个函数 (axis=0)
start = time()
result_v2 = mad_clip_v2(df, k=3, axis=0)
end = time()
print('v2: ', end - start)

# 比较两个结果是否一致
are_equal = np.allclose(result_v1, result_v2, equal_nan=True)

# print("结果是否一致:", are_equal)
# print("V1结果:\n", result_v1[:5])  # 打印前5行
# print("V2结果:\n", result_v2[:5])  # 打印前5行
