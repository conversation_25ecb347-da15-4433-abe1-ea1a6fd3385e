#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FactorZoo 并行加载和异步IO优化模块
提供多线程并行加载、异步IO、批量处理等性能优化功能
"""

import asyncio
import threading
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Callable, Any, Tuple, Union
from pathlib import Path
import queue
from dataclasses import dataclass
from .config_cache import get_performance_config


@dataclass
class LoadTask:
    """加载任务定义"""
    task_id: str
    file_path: Path
    load_function: Callable
    kwargs: Dict
    priority: int = 0


@dataclass
class LoadResult:
    """加载结果"""
    task_id: str
    success: bool
    data: Optional[Any]
    error: Optional[str]
    load_time: float
    file_size: int


class ParallelFileLoader:
    """并行文件加载器"""
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.config = get_performance_config()
        self._stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'total_time': 0.0,
            'total_bytes': 0
        }
    
    def load_files_parallel(self, tasks: List[LoadTask]) -> List[LoadResult]:
        """并行加载多个文件"""
        if not tasks:
            return []
        
        start_time = time.time()
        results = []
        
        # 按优先级排序任务
        tasks.sort(key=lambda x: x.priority, reverse=True)
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_task = {}
            for task in tasks:
                future = executor.submit(self._execute_load_task, task)
                future_to_task[future] = task
            
            # 收集结果
            for future in as_completed(future_to_task):
                task = future_to_task[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    if result.success:
                        self._stats['completed_tasks'] += 1
                        self._stats['total_bytes'] += result.file_size
                    else:
                        self._stats['failed_tasks'] += 1
                        
                except Exception as e:
                    # 创建错误结果
                    error_result = LoadResult(
                        task_id=task.task_id,
                        success=False,
                        data=None,
                        error=str(e),
                        load_time=0.0,
                        file_size=0
                    )
                    results.append(error_result)
                    self._stats['failed_tasks'] += 1
        
        self._stats['total_tasks'] += len(tasks)
        self._stats['total_time'] += time.time() - start_time
        
        return results
    
    def _execute_load_task(self, task: LoadTask) -> LoadResult:
        """执行单个加载任务"""
        start_time = time.time()
        
        try:
            # 获取文件大小
            file_size = task.file_path.stat().st_size if task.file_path.exists() else 0
            
            # 执行加载函数
            data = task.load_function(task.file_path, **task.kwargs)
            
            load_time = time.time() - start_time
            
            return LoadResult(
                task_id=task.task_id,
                success=True,
                data=data,
                error=None,
                load_time=load_time,
                file_size=file_size
            )
            
        except Exception as e:
            load_time = time.time() - start_time
            
            return LoadResult(
                task_id=task.task_id,
                success=False,
                data=None,
                error=str(e),
                load_time=load_time,
                file_size=0
            )
    
    def get_stats(self) -> Dict:
        """获取加载统计"""
        total_tasks = self._stats['total_tasks']
        if total_tasks == 0:
            return self._stats
        
        return {
            **self._stats,
            'success_rate': self._stats['completed_tasks'] / total_tasks,
            'avg_load_time': self._stats['total_time'] / total_tasks,
            'avg_file_size': self._stats['total_bytes'] / total_tasks if total_tasks > 0 else 0,
            'throughput_mb_per_sec': self._stats['total_bytes'] / (1024*1024) / self._stats['total_time'] if self._stats['total_time'] > 0 else 0
        }


class AsyncIOManager:
    """异步IO管理器"""
    
    def __init__(self, buffer_size: int = 8192):
        self.buffer_size = buffer_size
        
    async def read_file_async(self, file_path: Path) -> bytes:
        """异步读取文件"""
        # 简化实现，使用线程池执行同步IO
        loop = asyncio.get_event_loop()
        content = await loop.run_in_executor(None, self._read_file_sync, file_path)
        return content
    
    def _read_file_sync(self, file_path: Path) -> bytes:
        """同步读取文件"""
        with open(file_path, 'rb') as f:
            return f.read()
    
    async def write_file_async(self, file_path: Path, data: bytes):
        """异步写入文件"""
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, self._write_file_sync, file_path, data)
    
    def _write_file_sync(self, file_path: Path, data: bytes):
        """同步写入文件"""
        with open(file_path, 'wb') as f:
            f.write(data)
    
    async def read_multiple_files(self, file_paths: List[Path]) -> List[Tuple[Path, bytes]]:
        """异步读取多个文件"""
        tasks = []
        for file_path in file_paths:
            task = asyncio.create_task(self.read_file_async(file_path))
            tasks.append((file_path, task))
        
        results = []
        for file_path, task in tasks:
            try:
                content = await task
                results.append((file_path, content))
            except Exception as e:
                print(f"读取文件 {file_path} 失败: {e}")
                results.append((file_path, b''))
        
        return results


class BatchProcessor:
    """批量处理器"""
    
    def __init__(self, batch_size: int = 10, max_queue_size: int = 100):
        self.batch_size = batch_size
        self.max_queue_size = max_queue_size
        self._input_queue = queue.Queue(maxsize=max_queue_size)
        self._result_queue = queue.Queue()
        self._processing = False
        self._worker_thread = None
    
    def start_processing(self, process_function: Callable[[List], List]):
        """开始批量处理"""
        if self._processing:
            return
        
        self._processing = True
        self._worker_thread = threading.Thread(
            target=self._batch_worker,
            args=(process_function,),
            daemon=True
        )
        self._worker_thread.start()
    
    def add_item(self, item: Any) -> bool:
        """添加待处理项"""
        try:
            self._input_queue.put(item, timeout=1.0)
            return True
        except queue.Full:
            return False
    
    def get_result(self, timeout: float = 1.0) -> Optional[Any]:
        """获取处理结果"""
        try:
            return self._result_queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def stop_processing(self):
        """停止批量处理"""
        self._processing = False
        if self._worker_thread:
            self._worker_thread.join(timeout=5.0)
    
    def _batch_worker(self, process_function: Callable[[List], List]):
        """批量处理工作线程"""
        batch = []
        
        while self._processing:
            try:
                # 收集一批数据
                while len(batch) < self.batch_size:
                    try:
                        item = self._input_queue.get(timeout=0.1)
                        batch.append(item)
                    except queue.Empty:
                        break
                
                # 如果有数据就处理
                if batch:
                    try:
                        results = process_function(batch)
                        for result in results:
                            self._result_queue.put(result)
                    except Exception as e:
                        # 处理失败，放入错误结果
                        for item in batch:
                            self._result_queue.put(('error', item, str(e)))
                    
                    batch.clear()
                else:
                    time.sleep(0.01)  # 避免空转
                    
            except Exception as e:
                print(f"批量处理工作线程出错: {e}")
                time.sleep(0.1)


class SmartPrefetcher:
    """智能预取器"""
    
    def __init__(self, cache_size: int = 50, prediction_window: int = 10):
        self.cache_size = cache_size
        self.prediction_window = prediction_window
        self._access_history = []
        self._prefetch_cache = {}
        self._access_patterns = {}
        self._lock = threading.RLock()
    
    def record_access(self, item_key: str):
        """记录访问历史"""
        with self._lock:
            current_time = time.time()
            self._access_history.append((item_key, current_time))
            
            # 保持历史记录在合理大小
            if len(self._access_history) > 1000:
                self._access_history = self._access_history[-500:]
            
            # 更新访问模式
            self._update_access_patterns()
    
    def predict_next_accesses(self) -> List[str]:
        """预测下一步可能访问的项目"""
        with self._lock:
            if len(self._access_history) < 3:
                return []
            
            # 简单的模式匹配预测
            recent_accesses = [item for item, _ in self._access_history[-self.prediction_window:]]
            
            predicted_items = []
            for pattern, next_items in self._access_patterns.items():
                if self._matches_pattern(recent_accesses, pattern):
                    predicted_items.extend(next_items[:3])  # 预测前3个
            
            # 去重并限制数量
            unique_predictions = list(set(predicted_items))[:10]
            return unique_predictions
    
    def _update_access_patterns(self):
        """更新访问模式"""
        if len(self._access_history) < 4:
            return
        
        # 分析最近的访问序列
        recent_items = [item for item, _ in self._access_history[-20:]]
        
        # 构建简单的2-gram模式
        for i in range(len(recent_items) - 2):
            pattern = tuple(recent_items[i:i+2])
            next_item = recent_items[i+2]
            
            if pattern not in self._access_patterns:
                self._access_patterns[pattern] = []
            
            if next_item not in self._access_patterns[pattern]:
                self._access_patterns[pattern].append(next_item)
    
    def _matches_pattern(self, recent_accesses: List[str], pattern: tuple) -> bool:
        """检查最近访问是否匹配模式"""
        if len(recent_accesses) < len(pattern):
            return False
        
        return tuple(recent_accesses[-len(pattern):]) == pattern
    
    def should_prefetch(self, item_key: str) -> bool:
        """判断是否应该预取某个项目"""
        predictions = self.predict_next_accesses()
        return item_key in predictions


class PerformanceParallelManager:
    """性能并行管理器主类"""
    
    def __init__(self):
        self.config = get_performance_config()
        
        # 初始化组件
        self.file_loader = ParallelFileLoader(
            max_workers=self.config.parallel.max_worker_threads
        )
        
        self.async_manager = AsyncIOManager(
            buffer_size=self.config.parallel.io_buffer_size
        )
        
        self.batch_processor = BatchProcessor(
            batch_size=self.config.parallel.batch_size
        )
        
        self.prefetcher = SmartPrefetcher()
        
    def load_factor_files_parallel(self, file_paths: List[Path], 
                                  load_function: Callable = pd.read_parquet) -> Dict[Path, Any]:
        """并行加载因子文件"""
        # 创建加载任务
        tasks = []
        for i, file_path in enumerate(file_paths):
            task = LoadTask(
                task_id=f"load_{i}",
                file_path=file_path,
                load_function=load_function,
                kwargs={},
                priority=1
            )
            tasks.append(task)
        
        # 执行并行加载
        results = self.file_loader.load_files_parallel(tasks)
        
        # 整理结果
        result_dict = {}
        for result in results:
            if result.success:
                # 根据task_id找到对应的文件路径
                task_index = int(result.task_id.split('_')[1])
                result_dict[file_paths[task_index]] = result.data
            else:
                print(f"加载任务 {result.task_id} 失败: {result.error}")
        
        return result_dict
    
    async def load_factor_files_async(self, file_paths: List[Path]) -> Dict[Path, pd.DataFrame]:
        """异步加载因子文件"""
        result_dict = {}
        
        # 批量异步读取文件
        file_contents = await self.async_manager.read_multiple_files(file_paths)
        
        # 解析数据
        for file_path, content in file_contents:
            if content:
                try:
                    # 根据文件扩展名选择解析方法
                    if file_path.suffix == '.parquet':
                        import io
                        df = pd.read_parquet(io.BytesIO(content))
                    elif file_path.suffix == '.feather':
                        import io
                        df = pd.read_feather(io.BytesIO(content))
                    else:
                        df = pd.DataFrame()
                    
                    result_dict[file_path] = df
                except Exception as e:
                    print(f"解析文件 {file_path} 失败: {e}")
        
        return result_dict
    
    def start_batch_processing(self, process_function: Callable[[List], List]):
        """启动批量处理"""
        if self.config.parallel.enable_batch_processing:
            self.batch_processor.start_processing(process_function)
    
    def stop_batch_processing(self):
        """停止批量处理"""
        self.batch_processor.stop_processing()
    
    def optimize_loading_strategy(self, file_paths: List[Path]) -> str:
        """根据文件特征优化加载策略"""
        total_size = sum(p.stat().st_size for p in file_paths if p.exists())
        file_count = len(file_paths)
        
        # 根据文件数量和大小选择策略
        if file_count <= 3:
            return "sequential"  # 文件少，顺序加载
        elif total_size > 1024 * 1024 * 1024:  # 超过1GB
            return "async"  # 大文件，异步加载
        else:
            return "parallel"  # 中等规模，并行加载
    
    def get_comprehensive_stats(self) -> Dict:
        """获取综合性能统计"""
        return {
            'file_loader_stats': self.file_loader.get_stats(),
            'config': {
                'max_workers': self.file_loader.max_workers,
                'batch_size': self.batch_processor.batch_size,
                'buffer_size': self.async_manager.buffer_size
            },
            'parallel_config': self.config.get_config_summary()['parallel']
        }


# 全局并行管理器实例
_global_parallel_manager = None
_parallel_lock = threading.Lock()


def get_parallel_manager() -> PerformanceParallelManager:
    """获取全局并行管理器实例"""
    global _global_parallel_manager
    
    if _global_parallel_manager is None:
        with _parallel_lock:
            if _global_parallel_manager is None:
                _global_parallel_manager = PerformanceParallelManager()
    
    return _global_parallel_manager


# 便捷函数
def load_files_parallel(file_paths: List[Path], 
                       load_function: Callable = pd.read_parquet) -> Dict[Path, Any]:
    """便捷的并行文件加载函数"""
    manager = get_parallel_manager()
    return manager.load_factor_files_parallel(file_paths, load_function)


async def load_files_async(file_paths: List[Path]) -> Dict[Path, pd.DataFrame]:
    """便捷的异步文件加载函数"""
    manager = get_parallel_manager()
    return await manager.load_factor_files_async(file_paths)


if __name__ == "__main__":
    # 并行加载演示
    print("🚀 并行加载和异步IO演示")
    print("=" * 50)
    
    manager = get_parallel_manager()
    
    # 创建一些测试文件路径（假设存在）
    test_files = [
        Path("test_factor1.parquet"),
        Path("test_factor2.parquet"),  
        Path("test_factor3.parquet")
    ]
    
    print(f"📊 加载策略建议: {manager.optimize_loading_strategy(test_files)}")
    
    # 显示配置信息
    print("\n⚙️  当前并行配置:")
    stats = manager.get_comprehensive_stats()
    
    for key, value in stats['config'].items():
        print(f"  {key}: {value}")
    
    print("\n💡 性能优化建议:")
    print("  1. 根据CPU核心数调整max_workers")
    print("  2. 根据内存大小调整batch_size")
    print("  3. 对于大文件优先使用异步IO")
    print("  4. 启用智能预取提升缓存命中率") 