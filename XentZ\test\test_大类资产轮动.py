from hikyuu.interactive import *
from hikyuu import *
import matplotlib.pyplot as plt

# =============== 策略参数设置 ===============
# ETF列表
etf_codes = ["sz159915", "sz159934", "sh510880", "sh513100", "sh513520"]
# etf_codes = ["sz159915", "sz159934"]
# 回测起始日期
start_date = Datetime(20200101)
end_date = None
query = Query(start_date, end_date)
# 初始资金
initial_cash = 100000
# 调仓周期
rebalance_days = 5
# ROCP计算周期
rocp_days = 20
# 选择的TopN数量
top_n = 1

# =============== 配置数据 ===============
options = {
    "stock_list": etf_codes,
    "ktype_list": ['day'],
    "load_history_finance": False,
    "load_weight": False,
    "start_spot": False
}
load_hikyuu(**options)    

# 创建用于回测的证券列表
stks = tuple([sm[code] for code in options["stock_list"]])
# =============== 创建交易系统 ===============
# 创建模拟交易账户
tm = crtTM(date=start_date,
           init_cash=initial_cash, 
           cost_func=TC_FixedA2017())

# 创建简单系统策略
sys = SYS_Simple()
sys.tm = tm
sys.mm = MM_FixedPercent(0.9)  # 使用90%的资金进行购买
sys.sg = SG_Cycle()
sys.set_param("buy_delay", False)

# =============== 创建组合 ===============
rocp = ROCP(CLOSE, rocp_days)
se = SE_MultiFactor([REF(rocp,1)], topn=top_n, 
                    ref_stk = sm['sz159915'],
                    mode="MF_EqualWeight")
se.add_stock_list(stks,sys)

# 使用等权重的资产分配策略
af = AF_EqualWeight()
# 创建投资组合（传入股票列表）
pf = PF_Simple(tm=tm, af=af, se=se, adjust_cycle=rebalance_days,
                    adjust_mode="query", delay_to_trading_day=True)
# =============== 执行回测 ===============
# 启动回测（只传入query参数）
pf.run(query)

# =============== 分析结果 ===============
# 获取绩效数据
perf = Performance()
pf.performance()
plt.show()
# # 设置结束日期为最后一个交易日
# latest_date = tm.get_trade_list()[-1].datetime if tm.get_trade_list() else query.end_date
# perf.statistics(tm, latest_date)

# # 打印绩效统计数据
# print("\n============= 策略绩效 =============")
# print(f"总收益率: {perf['总收益率']:.2f}%")
# print(f"年化收益率: {perf['年化收益率']:.2f}%")
# print(f"夏普比率: {perf['夏普比率']:.4f}")
# print(f"最大回撤: {perf['最大回撤']:.2f}%")
# print(f"最长回撤时间: {perf['最长回撤时间']}交易日")
# print(f"赢利交易比例: {perf['赢利交易比例%']:.2f}%")
# print(f"交易次数: {len(tm.get_trade_list())}")

# # =============== 绘制图表 ===============
# # 创建子图
# ax1, ax2 = create_figure(2)

# # 净值曲线
# tm.plot_funds(axes=ax1, legend_on=True, text_on=True)
# ax1.set_title("ETF轮动策略净值曲线")

# # 绘制持仓变化
# # 统计每个ETF的持仓日期
# holding_data = {code: [] for code in etf_codes}
# dates = []

# # 获取投资组合的持仓记录
# history = pf.get_history_positions()
# for date, pos_map in history.items():
#     dates.append(date)
#     # 记录每个ETF是否持有
#     for code in etf_codes:
#         stk = sm[code]
#         if stk in pos_map:
#             holding_data[code].append(1)
#         else:
#             holding_data[code].append(0)

# # 绘制持仓变化
# for code in etf_codes:
#     if dates and holding_data[code]:  # 确保有数据
#         ax2.plot(dates, holding_data[code], drawstyle='steps-post', label=code)

# ax2.set_title("ETF持仓变化")
# ax2.legend(loc='upper left')
# ax2.set_ylim(-0.1, 1.1)
# ax2.set_yticks([0, 1])
# ax2.set_yticklabels(['未持有', '持有'])
# ax2.grid(True)

# plt.tight_layout()
# plt.show()

# # 打印交易记录
# print("\n============= 交易记录 =============")
# for record in tm.get_trade_list():
#     print(record)






