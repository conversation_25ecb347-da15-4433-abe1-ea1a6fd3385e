#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FactorZoo数据查看器
支持Feather、Parquet文件的快速查看和基本编辑
"""

import pandas as pd
import os
import sys
from pathlib import Path
from typing import Optional

class DataViewer:
    """轻量级数据文件查看器"""
    
    def __init__(self):
        self.current_df = None
        self.current_file = None
    
    def load_file(self, file_path: str) -> bool:
        """加载数据文件"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                print(f"❌ 文件不存在: {file_path}")
                return False
            
            print(f"📁 加载文件: {file_path.name}")
            
            # 根据文件扩展名选择读取方法
            if file_path.suffix.lower() == '.feather':
                self.current_df = pd.read_feather(file_path)
            elif file_path.suffix.lower() == '.parquet':
                self.current_df = pd.read_parquet(file_path)
            elif file_path.suffix.lower() == '.csv':
                self.current_df = pd.read_csv(file_path, index_col=0)
            else:
                print(f"❌ 不支持的文件格式: {file_path.suffix}")
                return False
            
            self.current_file = file_path
            print(f"✅ 加载成功! 数据形状: {self.current_df.shape}")
            return True
            
        except Exception as e:
            print(f"❌ 加载失败: {str(e)}")
            return False
    
    def show_info(self):
        """显示数据基本信息"""
        if self.current_df is None:
            print("⚠️  请先加载文件")
            return
        
        print("\n" + "="*60)
        print("📊 数据文件信息")
        print("="*60)
        print(f"文件路径: {self.current_file}")
        print(f"数据形状: {self.current_df.shape}")
        print(f"内存使用: {self.current_df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
        
        if hasattr(self.current_df.index, 'name') and self.current_df.index.name:
            print(f"索引名称: {self.current_df.index.name}")
        
        print(f"列名数量: {len(self.current_df.columns)}")
        print(f"列名预览: {list(self.current_df.columns[:5])}")
        if len(self.current_df.columns) > 5:
            print(f"         ... 及其他 {len(self.current_df.columns) - 5} 列")
        
        print(f"\n数据类型统计:")
        dtype_counts = self.current_df.dtypes.value_counts()
        for dtype, count in dtype_counts.items():
            print(f"  {dtype}: {count} 列")
        
        print(f"\n缺失值统计:")
        null_counts = self.current_df.isnull().sum()
        if null_counts.sum() == 0:
            print("  ✅ 无缺失值")
        else:
            null_cols = null_counts[null_counts > 0]
            for col, count in null_cols.head(5).items():
                print(f"  {col}: {count} 个缺失值")
        
        print(f"\n时间范围:")
        if pd.api.types.is_datetime64_any_dtype(self.current_df.index):
            print(f"  开始时间: {self.current_df.index.min()}")
            print(f"  结束时间: {self.current_df.index.max()}")
        elif 'datetime' in self.current_df.columns:
            dt_col = self.current_df['datetime']
            if pd.api.types.is_datetime64_any_dtype(dt_col):
                print(f"  开始时间: {dt_col.min()}")
                print(f"  结束时间: {dt_col.max()}")
        else:
            print("  ⚠️  未检测到时间列")
    
    def show_head(self, n: int = 10):
        """显示前n行数据"""
        if self.current_df is None:
            print("⚠️  请先加载文件")
            return
        
        print(f"\n📋 前 {n} 行数据:")
        print("-" * 80)
        
        # 如果列数太多，只显示前几列
        if len(self.current_df.columns) > 10:
            display_df = self.current_df.iloc[:n, :8]
            print(display_df.to_string())
            print(f"\n... 及其他 {len(self.current_df.columns) - 8} 列")
        else:
            print(self.current_df.head(n).to_string())
    
    def show_tail(self, n: int = 10):
        """显示后n行数据"""
        if self.current_df is None:
            print("⚠️  请先加载文件")
            return
        
        print(f"\n📋 后 {n} 行数据:")
        print("-" * 80)
        
        if len(self.current_df.columns) > 10:
            display_df = self.current_df.iloc[-n:, :8]
            print(display_df.to_string())
            print(f"\n... 及其他 {len(self.current_df.columns) - 8} 列")
        else:
            print(self.current_df.tail(n).to_string())
    
    def show_columns(self):
        """显示所有列名"""
        if self.current_df is None:
            print("⚠️  请先加载文件")
            return
        
        print(f"\n📋 所有列名 (共 {len(self.current_df.columns)} 列):")
        print("-" * 60)
        
        for i, col in enumerate(self.current_df.columns, 1):
            dtype = self.current_df[col].dtype
            null_count = self.current_df[col].isnull().sum()
            print(f"{i:3d}. {col:<30} | {str(dtype):<12} | 缺失: {null_count}")
    
    def describe_column(self, column_name: str):
        """详细描述指定列"""
        if self.current_df is None:
            print("⚠️  请先加载文件")
            return
        
        if column_name not in self.current_df.columns:
            print(f"❌ 列 '{column_name}' 不存在")
            return
        
        col = self.current_df[column_name]
        print(f"\n📊 列 '{column_name}' 详细信息:")
        print("-" * 50)
        print(f"数据类型: {col.dtype}")
        print(f"非空值数量: {col.count()}")
        print(f"缺失值数量: {col.isnull().sum()}")
        print(f"唯一值数量: {col.nunique()}")
        
        if pd.api.types.is_numeric_dtype(col):
            print(f"\n数值统计:")
            print(col.describe().to_string())
        else:
            print(f"\n频率统计 (前10项):")
            print(col.value_counts().head(10).to_string())
    
    def export_to_csv(self, output_path: Optional[str] = None):
        """导出为CSV文件"""
        if self.current_df is None:
            print("⚠️  请先加载文件")
            return
        
        if output_path is None:
            output_path = str(self.current_file).replace(self.current_file.suffix, '.csv')
        
        try:
            self.current_df.to_csv(output_path, index=True, encoding='utf-8-sig')
            print(f"✅ 导出成功: {output_path}")
        except Exception as e:
            print(f"❌ 导出失败: {str(e)}")
    
    def interactive_mode(self):
        """交互模式"""
        print("\n🔧 进入交互模式 (输入 'help' 查看帮助, 'quit' 退出)")
        
        while True:
            try:
                cmd = input("\n> ").strip().lower()
                
                if cmd == 'quit' or cmd == 'exit':
                    print("👋 退出交互模式")
                    break
                elif cmd == 'help':
                    self._show_help()
                elif cmd == 'info':
                    self.show_info()
                elif cmd.startswith('head'):
                    n = 10
                    if ' ' in cmd:
                        try:
                            n = int(cmd.split()[1])
                        except:
                            pass
                    self.show_head(n)
                elif cmd.startswith('tail'):
                    n = 10
                    if ' ' in cmd:
                        try:
                            n = int(cmd.split()[1])
                        except:
                            pass
                    self.show_tail(n)
                elif cmd == 'columns':
                    self.show_columns()
                elif cmd.startswith('desc '):
                    col_name = cmd[5:].strip()
                    self.describe_column(col_name)
                elif cmd == 'export':
                    self.export_to_csv()
                elif cmd.startswith('load '):
                    file_path = cmd[5:].strip()
                    self.load_file(file_path)
                else:
                    print("❌ 未知命令，输入 'help' 查看帮助")
                    
            except KeyboardInterrupt:
                print("\n👋 退出交互模式")
                break
            except Exception as e:
                print(f"❌ 命令执行出错: {str(e)}")
    
    def _show_help(self):
        """显示帮助信息"""
        help_text = """
📚 可用命令:
  info          - 显示数据基本信息
  head [n]      - 显示前n行 (默认10行)
  tail [n]      - 显示后n行 (默认10行)
  columns       - 显示所有列名
  desc <列名>   - 详细描述指定列
  export        - 导出为CSV文件
  load <文件>   - 加载新的数据文件
  help          - 显示此帮助
  quit/exit     - 退出交互模式
        """
        print(help_text)

def main():
    """主函数"""
    print("🪶 FactorZoo数据查看器")
    print("=" * 50)
    
    viewer = DataViewer()
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        if viewer.load_file(file_path):
            viewer.show_info()
            viewer.show_head(5)
            
            # 询问是否进入交互模式
            choice = input("\n是否进入交互模式? (y/n): ").strip().lower()
            if choice in ['y', 'yes', '是']:
                viewer.interactive_mode()
    else:
        print("用法:")
        print("  python data_viewer.py <文件路径>")
        print("\n支持的文件格式: .feather, .parquet, .csv")
        print("\n示例:")
        print("  python data_viewer.py base_data.feather")
        print("  python data_viewer.py L0_selected_factors.parquet")

if __name__ == "__main__":
    main() 