# FactorZoo 批次管理文件查看功能说明

## 功能概述

我们已经成功为 FactorZoo 批次管理工具增加了文件内容查看功能。现在在"4. 查看批次详细文件"选项中，除了显示文件列表外，还可以交互式地查看具体文件的内容。

## 主要功能特性

### 1. 文件列表显示增强
- 为每个文件添加了编号（1, 2, 3...）
- 支持不同文件类型的图标显示：
  - 🪶 `.feather` 文件
  - 📦 `.parquet` 文件  
  - 📄 `.json` 文件
  - 📋 其他文件类型

### 2. 交互式文件内容查看
在显示文件列表后，用户可以：
- 输入文件编号选择要查看的文件
- 自定义显示的行数（默认10行）
- 输入 0 退出文件查看

### 3. 多种文件格式支持

#### DataFrame文件（.feather/.parquet）
- 显示数据维度（行数、列数）
- 显示列名列表
- 显示前N行数据（格式化输出）
- 显示每列的数据类型
- 显示数值列的基本统计信息
- **返回完整的DataFrame对象**

#### JSON文件（.json）
- 格式化显示JSON内容
- 支持中文显示

#### 文本文件（其他格式）
- 显示文件总行数
- 显示前N行内容，带行号

### 4. DataFrame数据的深度分析
对于DataFrame文件，提供额外的分析选项：
- **选项1**: 查看更多行数据
- **选项2**: 查看详细列信息（.info()）
- **选项3**: 查看数据范围（最小值、最大值）

## 使用方法

### 基本步骤
1. 运行批次管理工具：
   ```bash
   python factorzoo/scripts/批次管理.py
   ```

2. 选择"4. 查看批次详细文件"

3. 输入要查看的批次ID

4. 查看文件列表后，输入文件编号查看内容

### 交互示例
```
📁 批次文件详情: GP_SH510050_20250627_L0_035766
================================================================================
 1. 🪶 base_data.feather                            0.03 MB
 2. 📦 L0_selected_factors_factors.parquet          0.01 MB
 3. 📄 metadata.json                                0.00 MB
--------------------------------------------------------------------------------
📊 总计: 3 个文件, 0.04 MB

💡 查看文件内容:
输入文件编号查看内容，输入0退出

请选择文件 (1-3, 0=退出): 1
显示多少行? (默认10行): 5

📄 文件内容: base_data.feather
================================================================================
📊 数据维度: (1045, 5)
📋 列名: ['datetime', 'open', 'high', 'low', 'close']

🔍 前 5 行数据:
--------------------------------------------------------------------------------
   datetime     open     high      low    close
0  2021-01-04  3.1400  3.1670  3.1200  3.1670
1  2021-01-05  3.1720  3.1890  3.1360  3.1410
2  2021-01-06  3.1470  3.1730  3.1340  3.1590
3  2021-01-07  3.1650  3.1720  3.1420  3.1490
4  2021-01-08  3.1520  3.1800  3.1450  3.1740

... 共 1045 行，仅显示前 5 行

🔧 更多操作:
1. 查看更多行
2. 查看列信息
3. 查看数据范围
0. 返回文件列表
```

## 技术实现

### 文件修改
1. **factorzoo/scripts/batch_tools.py**
   - 修改 `show_batch_files()` 方法：添加文件编号，返回文件列表
   - 新增 `show_file_content()` 方法：处理不同格式文件的内容显示

2. **factorzoo/scripts/批次管理.py**
   - 增强选项4的处理逻辑：添加文件内容查看的交互循环
   - 添加DataFrame深度分析功能

### 核心功能
- 自动检测文件格式并选择合适的读取方法
- pandas配置优化显示效果
- 错误处理和用户友好的提示信息
- 内存使用优化（只加载必要的数据）

## 支持的数据分析功能

### 基本信息
- 数据维度和形状
- 列名和数据类型
- 缺失值统计

### 统计分析
- 数值列的描述性统计
- 最小值、最大值范围
- 均值和标准差

### 数据预览
- 可配置的行数显示
- 格式化的表格输出
- 列宽度自适应

## 优势特点

1. **用户友好**: 直观的交互界面，编号选择文件
2. **功能全面**: 支持多种文件格式，提供深度分析
3. **性能优化**: 只加载需要显示的数据行数
4. **错误处理**: 完善的异常捕获和用户提示
5. **扩展性强**: 易于添加新的文件格式支持

## 使用场景

- **数据验证**: 快速查看批次中的数据文件内容
- **问题排查**: 检查数据文件的格式和内容是否正确
- **数据探索**: 了解因子数据的分布和特征
- **配置检查**: 查看批次的元数据和配置信息

这个功能极大地提升了 FactorZoo 的易用性，让用户可以方便地检查和验证批次数据，无需编写额外的代码或使用其他工具。 