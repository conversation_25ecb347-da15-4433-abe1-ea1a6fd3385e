import streamlit as st
from pathlib import Path
import sys

# Add project root to path
root = Path(__file__).parent.parent
if str(root) not in sys.path:
    sys.path.append(str(root))

def main():
    """主页面"""
    st.set_page_config(
        page_title="因子挖掘机",
        page_icon="📊",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 初始化页面状态
    if 'current_page' not in st.session_state:
        st.session_state.current_page = "Home"
    
    st.title("欢迎使用因子挖掘机")
    st.markdown("""
    请按顺序完成以下步骤：
    
    1. 任务配置 - 设置基本参数和选择品种
    2. 特征管理 - 管理和编辑算子
    3. GP挖掘 - 执行因子挖掘任务
    """)
    
    # 开始按钮
    if st.button("开始配置", use_container_width=True):
        st.session_state.current_page = "任务配置"
        st.switch_page("pages/1_任务配置.py")

if __name__ == "__main__":
    main() 