import pandas as pd
from .expr_funcs import *

def expr_transform(df, expr):
    # close/shift(close,5) -1
    for col in df.columns:
        # expr = expr.replace("(" + col, '(df[["symbol","{}"]]'.format(col))
        expr = expr.replace(col, 'df["{}"]'.format(col))
    return expr


def calc_expr(df: pd.DataFrame, expr: str):
    if expr in list(df.columns):
        return df[expr]    
    expr = expr_transform(df, expr)
    try:
        se = eval(expr)
        # 此处可以添加测试代码
        if expr == 'ta_ad(CNTD20, QTLD10, VSUMN_5, JZ004_98)':
            print(se)
            pd.Series(se).to_csv('se.csv')
            
        return se
    except:
        import traceback
        traceback.print_exc()
        raise NameError('{}——eval异常'.format(expr))
    # shift(close,1) -> shift(df['close'],1)
    return None
