import os
import sys
from pathlib import Path

def main():
    """启动GUI应用"""
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    
    # 确保项目根目录在Python路径中
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    # 设置环境变量，确保子进程也能找到模块
    os.environ["PYTHONPATH"] = str(project_root)
    
    # 获取app文件的路径
    app_path = Path(__file__).parent / "Home.py"
    
    if not app_path.exists():
        print(f"错误：找不到GUI应用文件: {app_path}")
        sys.exit(1)
    
    # 构建并执行streamlit运行命令
    cmd = f"set PYTHONPATH={project_root} && streamlit run {app_path}" if os.name == "nt" else f"PYTHONPATH={project_root} streamlit run {app_path}"
    
    print(f"启动因子挖掘工作台...")
    print(f"运行命令: {cmd}")
    
    # 执行streamlit命令
    os.system(cmd)

if __name__ == "__main__":
    main() 