import numpy as np
import pandas as pd

def corr_pro(left, right, periods=20):
    left = pd.Series(left)
    right = pd.Series(right)
    right.index = left.index # 否则(se,np)这种情况出错
    res = left.rolling(window=periods).corr(right)
    # left.rolling(window=periods).apply(func=func,right)
    # 考虑到没波动的情况
    res.loc[
        np.isclose(left.rolling(periods, min_periods=1).std(), 0, atol=2e-05)
        | np.isclose(right.rolling(periods, min_periods=1).std(), 0, atol=2e-05)
        ] = np.nan
    
    return pd.Series(res, index=left.index)

def pair(left, right, func, *args, **kwargs):
    return func(left, right, *args, **kwargs)


def _corr(left, right, N):
    return pd.Series(left).rolling(window=N).corr(pd.Series(right))


def corr(se_left, se_right, N):
    return pair(se_left, se_right, _corr, N)


def correlation(se_left, se_right, N):  # 与上面的corr函数等价
    return corr(se_left, se_right, N)

if __name__ == "__main__":
    # 创建示例数据
    np.random.seed(0)  # 设置随机种子以便结果可复现
    date_range = pd.date_range(start='2023-01-01', end='2023-01-31', freq='D')
    left_series = pd.Series(np.random.randn(len(date_range)), index=date_range)
    right_series = pd.Series(np.random.randn(len(date_range)), index=date_range)

    # 应用ts_corr函数
    corr_result = ts_corr(left_series, right_series, periods=7)

    # 打印结果的前几行和后几行查看
    print("Correlation Result:")
    print(corr_result.head())
    print("\n...")
    print(corr_result.tail())

    # 可视化结果（可选）
    import matplotlib.pyplot as plt

    plt.figure(figsize=(10, 6))
    corr_result.plot()
    plt.title('Rolling Correlation between Two Time Series')
    plt.ylabel('Correlation')
    plt.show()