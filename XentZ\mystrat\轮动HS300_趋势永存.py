#!/usr/bin/env python
# -*- coding: utf-8 -*-

from hikyuu import *
from hikyuu.interactive import *
import datetime
import matplotlib.pyplot as plt

if __name__ == "__main__":
    # =============== 1. 策略参数设置 ===============
    start_date = Datetime(20100101)
    end_date = Datetime(20250509)
    initial_cash = 1000000
    risk_factor = 0.01  # 风险因子
    top_percent = 0.2    # 选择前20%
    # cost_func = get_part(f"mystar.other.tc_etf")
    cost_func = TC_FixedA2017()
    
    # =============== 2. 系统初始化 ===============
    # 使用沪深300成分股作为股票池
    block = sm.get_block("指数板块","沪深300")
    stks = tuple([s for s in block])
    # stks = [s for s in sm if s.type == constant.STOCKTYPE_ETF]
    stock_list = [stock.market_code for stock in stks]
    options = {
        "stock_list": stock_list,
        "ktype_list": ["day"],
    }
    load_hikyuu(**options)

    # =============== 3. 创建模拟交易账户 ===============
    tm = crtTM(date=start_date, init_cash=initial_cash, cost_func=cost_func)
    
    # =============== 4. 创建趋势评分指标 ===============
    query = Query(start_date, end_date, 
                  ktype=Query.DAY, recover_type=Query.BACKWARD)
    ind_se = get_part('mystar.ind.斜率年化乘R2',period=90)
    
    # =============== 5. 创建市场环境判断(EV) ===============
    # 使用指数200日均线判断市场环境
    market_index = sm['sh000300']  # 指数
    market_index_k = market_index.get_kdata(query)
    ind_ev = (CLOSE() > MA(CLOSE(),200))
    market_ev = EV_Bool(CONTEXT(ind_ev(market_index_k)))
    # =============== 6. 创建系统条件判断(CN) ===============
    # 价格在100日均线上方
    price_above_ma100 = CLOSE() > MA(CLOSE(), 100)
    # 无大缺口
    daily_returns = ABS(CLOSE() / REF(CLOSE(), 1) - 1)
    no_large_gap = HHV(daily_returns, 90) < 0.15
    
    # 组合条件
    cn1 = CN_Bool(price_above_ma100)
    cn2 = CN_Bool(no_large_gap)
    cn = cn1 + cn2
    # =============== 7. 创建信号指示器(SG) ===============
    # 买入信号：满足条件 (ev + cn)
    # 卖出信号：价格跌破100日均线
    ind_sg = CLOSE() < MA(CLOSE(), 100)
    sg = SG_Sell(ind_sg)
    
    # =============== 8. 创建止损策略(ST) ===============
    # 使用固定百分比止损
    st = ST_FixedPercent(0.05)  # 10%止损
    
    # =============== 9. 创建资金管理策略(MM) ===============
    # 基于ATR的头寸管理
    # mm = MM_FixedPercent(0.1)  # 使用20日ATR计算风险
    mm = get_part('mystar.mm.fixed_atrperc_funds', 
                  risk_percent=risk_factor,
                  atr_multiplier=1,
                  atr_period=20)
    
    # =============== 10. 创建完整交易系统 ===============
    sys = SYS_Simple(tm=tm, sg=sg, mm=mm, ev=market_ev, cn=cn, st=st)
    sys.name = "趋势永存"
    sys.set_param("ev_open_position",True)
    sys.set_param("cn_open_position",True)
    # =============== 11. 创建选择器(SE) ===============
    # 选择趋势评分前20%的股票
    se = SE_MultiFactor([ind_se], topn=int(top_percent * 300), 
                        ref_stk = sm['sh000300'],
                        mode="MF_EqualWeight")
    se.add_stock_list(stks, sys)
    # =============== 12. 创建资产分配器(AF) ===============
    # 使用等权重分配资金
    af = AF_EqualWeight()
    
    # =============== 13. 创建投资组合(PF) ===============
    # pf = PF_Simple(tm=tm, se=se, af=af, adjust_cycle=7,
    #                 adjust_mode="query", delay_to_trading_day=True)
    pf = PF_WithoutAF(tm=tm, se=se, adjust_cycle=7,
                adjust_mode="query", delay_to_trading_day=True)
    
    # pf.set_param("trace", True)
    pf.run(query)
    pf.tm.tocsv(os.path.dirname(os.path.abspath(__file__)))
    pf.performance()
    plt.show()
    exit()
    
    # =============== 14. 创建自定义策略实现双周头寸再平衡 ===============
    # 使用crtEV创建自定义市场环境判断
    def is_biweekly_wednesday(market, query, *args):
        """判断是否为双周的周三"""
        # 获取当前日期
        current_date = query.datetime
        
        # 检查是否周三
        if current_date.dayOfWeek() != 3:
            return False
            
        # 使用静态变量记录周数
        if not hasattr(is_biweekly_wednesday, "week_count"):
            is_biweekly_wednesday.week_count = 0
            
        # 只有周三才增加计数
        is_biweekly_wednesday.week_count += 1
        
        # 每两周返回True
        return is_biweekly_wednesday.week_count % 2 == 0
    
    # 创建双周环境判断
    biweekly_ev = crtEV(is_biweekly_wednesday, name="双周周三环境")
    
    # =============== 15. 创建双周头寸再平衡系统 ===============
    # 创建自定义信号指示器，用于双周头寸再平衡
    def biweekly_rebalance_sg(stock, query, *args):
        """双周头寸再平衡信号"""
        # 获取当前持仓
        pos = args[0].get_position(stock)
        if pos is None or pos.number <= 0:
            return (0, 0)  # 无持仓，不需要再平衡
            
        # 计算期望头寸
        kdata = stock.get_kdata(Query(-30))
        if len(kdata) < 20:
            return (0, 0)
            
        atr = ATR(kdata, 20)[-1]
        if atr <= 0:
            return (0, 0)
            
        expected_size = int(args[0].current_cash * risk_factor / atr)
        current_size = pos.number
        
        # 如果差异超过10%，则调整
        if abs(current_size - expected_size) / current_size > 0.1:
            if current_size > expected_size:
                # 减仓
                return (0, current_size - expected_size)
            else:
                # 加仓
                return (expected_size - current_size, 0)
                
        return (0, 0)
    
    # 创建自定义信号指示器
    rebalance_sg = crtSG(biweekly_rebalance_sg, params={'tm': tm}, name="双周再平衡信号")
    
    # 创建专门用于头寸再平衡的交易系统
    rebalance_sys = SYS_Simple(tm=tm, sg=rebalance_sg, mm=MM_Nothing(), ev=biweekly_ev)
    rebalance_sys.name = "双周头寸再平衡"
    
    # 添加到投资组合
    pf.add_stock_sys(rebalance_sys)
    
    # =============== 16. 运行回测 ===============
    # 创建查询条件
    query = Query(start_date, end_date)
    
    # 运行投资组合
    pf.run(query)
    
    # =============== 17. 结果分析 ===============
    # 计算绩效指标
    per = Performance()
    per.statistics(tm, end_date)
    
    # 输出结果
    print("=== 趋势永存策略回测结果 ===")
    print(f"年化收益率: {per.annual_return * 100:.2f}%")
    print(f"最大回撤: {per.max_drawdown * 100:.2f}%")
    print(f"夏普比率: {per.sharpe:.2f}")
    print(f"交易次数: {per.trade_count}")
    print(f"胜率: {per.win_rate * 100:.2f}%")
    
    # 保存交易记录
    tm.tocsv("趋势永存策略")
    
    # 绘制权益曲线
    tm.plot()