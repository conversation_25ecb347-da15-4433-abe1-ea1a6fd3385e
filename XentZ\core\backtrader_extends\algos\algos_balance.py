import numpy as np
import pandas as pd

from .algo_base import Algo
from loguru import logger


class Rebalance(Algo):

    def __init__(self):
        super(Rebalance, self).__init__()

    def __call__(self, target):
        if "weights" not in target.temp:
            return True

        target_weights = target.temp["weights"]
        # print(targets)
        if type(target_weights) is pd.Series:
            targets = target_weights.to_dict()

        # 要清仓的, 仅判断持有多单的情况 -- by JZAL
        symbols,_ = target.get_current_holding_symbols()
        for s in symbols:
            if s not in target_weights.keys():
                # logger.info('清空：{}'.format(s))
                # logger.info('{}平仓：{}'.format(target.now, s))
                target.order = target.close(s)

        # 如果调仓就1个，且当前有持仓就1个，二者还相等，那不执行
        if len(target_weights.keys()) == 1 and list(target_weights.keys())[0] in symbols and len(symbols) == 1:
            return True

            # 目标仓位里，分成两部分： 目标仓位小于当前仓位的，要卖一部分，其余的，都是买入，含新增的（curr_w=0）
            # to_sell / to_buy
        to_sell = []
        to_buy = []

        for s in target_weights.keys():
            # 取当前仓位比例
            mv = target.broker.getvalue()
            curr_w = target.get_symbol_mv(s) / mv
            if target_weights[s] < curr_w:
                to_sell.append(s)
            else:
                to_buy.append(s)
                
        for s in to_sell:
            w = target_weights[s]
            target.order = target.order_target_percent(s, w * 0.95)

        for s in to_buy:
            w = target_weights[s]
            target.order = target.order_target_percent(s, w * 0.9)
        # 这里还有一个逻辑，就是仓位变少的（卖出）要选执行，然后才是买入，否则现金会不够多。
        # for symbol, w in target_weights.items():
        #     logger.info('{}调仓：{},{}'.format(target.now, symbol, w))
        return True