import pandas as pd
import numpy as np
from itertools import chain, combinations, product
from scipy.stats import norm

# 假设df是你的大宽表DataFrame，其中列是因子，行是日期
df = ...

# 计算单因子的Sharpe ratio
sharpe_ratios = df.apply(lambda x: x.mean() / x.std(), axis=0)

# 分层，每10%为一层，同时处理不足10%的情况
num_layers = 10
layer_size = len(sharpe_ratios) // num_layers
layers = {}
for i in range(num_layers):
    start = i * layer_size
    end = (i + 1) * layer_size if i < num_layers - 1 else len(sharpe_ratios)
    layers[i] = sharpe_ratios.nlargest(end).index[start:end]

# 计算层内方差
layer_vars = {layer: df.loc[:, factors].var().mean() for layer, factors in layers.items()}

# 计算最优分配样本数，这里简化假设所有层的抽样成本相同
total_samples = int(len(sharpe_ratios) * 0.1)  # 总共抽取10%的样本
samples_per_layer = {
    layer: max(1, int(total_samples * (layer_var / sum(layer_vars.values()))))
           for layer, layer_var in layer_vars.items()
}
# 调整，确保总和不超过total_samples
adjustment = total_samples - sum(samples_per_layer.values())
if adjustment != 0:
    # 如果总和不匹配，从方差最大的层开始减少样本数
    for layer in sorted(samples_per_layer, key=lambda k: layer_vars[k], reverse=True):
        if adjustment == 0:
            break
        samples_per_layer[layer] -= 1
        adjustment += 1

# 随机抽取（不放回），考虑层内因子个数不足的情况
np.random.seed(42)  # 为了可重复性

# 抽取因子
selected_factors = {}
for layer_id, factors in layers.items():
    num_to_select = min(samples_per_layer[layer_id], len(factors))
    selected_factors[layer_id] = np.random.choice(factors, size=num_to_select, replace=False)

# 生成组合：支持跨层组合和同层组合
all_combinations = []
for layer_id, factors in selected_factors.items():
    all_combinations.extend(combinations(factors, r) for r in range(1, len(factors) + 1))

# 交叉组合
cross_layer_combinations = list(product(*selected_factors.values()))
cross_layer_combinations = [chain.from_iterable(c) for c in cross_layer_combinations]

# 将所有组合合并
all_combinations.extend(combinations(c, r) for c in cross_layer_combinations for r in range(1, len(c) + 1))

# 转换为列表
all_combinations = [list(c) for c in all_combinations]

# 评估组合性能
# 假设evaluate_model_performance是你现有的模型评估函数
results = []
for combo in all_combinations:
    df_subset = df[combo]
    performance = evaluate_model_performance(df_subset)
    results.append((combo, performance))

# 选择最佳组合
best_results = sorted(results, key=lambda x: x[1], reverse=True)[:n]

# 输出最佳组合
for result in best_results:
    print(f"组合: {result[0]}, 性能评分: {result[1]}")