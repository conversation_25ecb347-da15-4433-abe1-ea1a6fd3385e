''' 因子筛选 或 显示的 工具集 '''
from tkinter import Y
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import math
from datetime import date
from scipy.stats import skew, kurtosis, gaussian_kde, norm
import statsmodels.api as sm
from statsmodels.regression.quantile_regression import QuantReg
from enums import NORM_PARAMS_LABEL
from .perf_utils import calc_rolling_sr_RC


def plot_fctval(df: pd.DataFrame, compr_col: list = ['ret'], 
               compr_show_cum: bool = False, savefig: str = 'combined'):
    df = df.copy()
    columns = df.columns.tolist()
    # 移除label_col中的列，剩下的应该是因子列
    factor_cols = [col for col in columns if col not in compr_col]
    cum_factor_cols = {f'cum_{col}': df[col].cumsum() for col in factor_cols} # 对因子列进行累加
    if compr_show_cum: # 对compr_col中的列进行累加
        cum_compr_cols = {f'cum_{lcol}': df[lcol].cumsum() for lcol in compr_col}
        # 使用pd.concat一次性添加所有新的累积和列
        df = pd.concat([df, pd.DataFrame(cum_compr_cols), pd.DataFrame(cum_factor_cols)], axis=1)
        real_compr_cols = [f'cum_{col}' for col in columns if col in compr_col]
    else:
        # 使用pd.concat一次性添加所有新的累积和列
        df = pd.concat([df, pd.DataFrame(cum_factor_cols)], axis=1)
        real_compr_cols = [col for col in columns if col in compr_col] 
        
    # 固定子图行列数为6x4
    num_rows = 6
    num_cols = 4
    # 由于每列数据绘制两次，所以实际上的子图数量加倍
    plots_per_fig = num_rows * num_cols
    
    # 计算需要多少个图
    num_plots = len(columns)  # 每列数据现在占用了两个子图的位置
    num_figs = (num_plots + plots_per_fig - 1) // plots_per_fig  # 向上取整
    
    for fig_idx in range(num_figs):
        start_col = fig_idx * plots_per_fig
        end_col = min(start_col + plots_per_fig, num_plots)
        fig, axs = plt.subplots(nrows=num_rows, ncols=num_cols, 
                                figsize=(10 * num_cols, 3 * num_rows), 
                                squeeze=False)
        
        for i in range(start_col, end_col):
            col = columns[i//2]
            plot_type = i % 2
            row = (i - start_col) // (num_cols)
            col_index = (i - start_col) % (num_cols)
            ax1 = axs[row][col_index]
            ax2 = ax1.twinx()
            
            if plot_type == 0:
                sample_order = range(len(df))
                ax1.plot(sample_order, df[col], 'b-', label=f'Factor: {i+1}')
                ax2.plot(sample_order, df[real_compr_cols], 'r-', label=', '.join(real_compr_cols))
                ax1.set_ylabel(f'Factor: {i+1}', color='g', fontsize=10, fontweight='bold')
                ax1.tick_params(axis='y', colors='g',labelsize=12)
                ax1.legend(loc='upper left')
                ax2.set_ylabel(', '.join(real_compr_cols), color='g',fontsize=10, fontweight='bold')
                ax2.tick_params(axis='y', colors='g',labelsize=12)
                ax2.legend(loc='upper right') 
                # axs[row][col_index].set_title(col if len(col) <= 32 else f'Column: {i+1}', fontsize=15, fontweight='bold')
            elif plot_type == 1:
                '''=================== 绘制直方图和密度曲线 ================ '''
                counts, bins, patches = ax1.hist(df[col], bins=50, alpha=0.75, density=True)
                ax1.plot(bins, gaussian_kde(df[col])(bins), color='k', linewidth=2)
                
                ''' ===================== 绘制箱型图 ===================== '''
                bp = ax2.boxplot(df[col], vert=False)
                # 加粗箱体的线条
                for box in bp['boxes']:
                    box.set(linewidth=2)  # 设置箱体线条宽度
                # 加粗中位数线
                for median in bp['medians']:
                    median.set(linewidth=2)
                # 加粗须触线和端点
                for whisker in bp['whiskers']:
                    whisker.set(linewidth=2)
                for cap in bp['caps']:
                    cap.set(linewidth=2)
                
                ''' ===================== 添加均值和置信区间的线 ===================== '''
                mean = df[col].mean()
                std_err = df[col].std() / np.sqrt(len(df[col]))
                conf_interval = norm.interval(0.95, loc=mean, scale=std_err)
                ax1.axvline(mean, color='r', linestyle='--', label=f'Mean: {mean:.2f}')
                ax1.fill_between(bins, 0, gaussian_kde(df[col])(bins), alpha=0.2)
                
                # 添加统计信息
                ax1.text(0.95, 0.95, f'Mean: {mean:.2f}\nCI: [{conf_interval[0]:.2f}, {conf_interval[1]:.2f}]',
                            verticalalignment='top', horizontalalignment='right',
                            transform=ax1.transAxes, color='red', fontsize=15, fontweight='bold')
                
                ax1.text(0.05, 0.9, f'Count: {len(df[col])}',
                            transform=ax1.transAxes, fontsize=15,fontweight='bold')
                ax1.text(0.05, 0.8, f'Skew: {df[col].skew():.2f}',
                            transform=ax1.transAxes, fontsize=15,fontweight='bold')
                ax1.text(0.05, 0.7, f'Kurt: {df[col].kurt():.2f}',
                            transform=ax1.transAxes, fontsize=15,fontweight='bold')
            # 设置子图标题
            axs[row][col_index].set_title(col if len(col) <= 32 else f'Column: {i+1}', fontsize=15, fontweight='bold')
            
        # 隐藏多余的子图
        for i in range(end_col - start_col, plots_per_fig):
            row = i // num_cols
            col_index = i % num_cols
            if row < num_rows:
                axs[row][col_index].axis('off')
        # 调整子图间距
        plt.tight_layout()
        # 保存图像
        plt.savefig(f'fctval_{savefig}_{fig_idx + 1}.png')
        plt.close()

def plot_single_combo_stats( combo_ret_se: pd.Series,  # day-level
                        combo_cost_se: pd.Series, # day-level
                        combo_ret_se_r: pd.Series, # day_level, roll
                        combo_cost_se_r: pd.Series, # day-level, roll
                        combo_ret_se_out_r: pd.Series, # day-level, roll
                        combo_cost_se_out_r: pd.Series, # day-level, roll
                        benchmark: pd.Series, # day-level
                        df_ratios_in: pd.DataFrame,  # 结果矩阵,非时序
                        df_ratios_out: pd.DataFrame, # 结果矩阵,非时序
                        df_ratios_all: pd.DataFrame, # 结果矩阵,非时序
                        df_ratios_all_r: pd.DataFrame, # 结果矩阵,非时序
                        df_ratios_out_r: pd.DataFrame, # 结果矩阵,非时序
                        boundary_day: date,
                        output # 输出的文件路径和文件名信息
                      ):
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.weight'] = 'bold'
    fig = plt.figure(figsize=(20, 20))  # 增加高度以容纳新的子图
    fig.suptitle(combo_ret_se.name, fontsize=24, fontweight='bold', wrap=True)
    gs = fig.add_gridspec(3, 2, width_ratios=[1, 1], height_ratios=[1, 1, 1])
    
    ax1 = fig.add_subplot(gs[0, 0])  # 子图1在左上
    ax2 = fig.add_subplot(gs[1, 0])  # 子图2在左下
    ax4 = fig.add_subplot(gs[2, 0])  # 新的子图4在左下
    ax3 = fig.add_subplot(gs[0:1, 1])  # 子图3在右侧
    ax5 = fig.add_subplot(gs[2, 1])  # 新的子图5在右下

    # 子图1: 累计日度百分比收益率, 累计日度成本百分比率, 累计bech_df
    cumulative_ret = combo_ret_se.cumsum()
    cumulative_ret_r = combo_ret_se_r.cumsum()
    cumulative_cost = combo_cost_se.cumsum()
    cumulative_cost_r = combo_cost_se_r.cumsum()
    cumulative_ret_out_r = combo_ret_se_out_r.cumsum()
    cumulative_bench = benchmark.cumsum()
    boundary_day = pd.Timestamp(boundary_day)

   # 绘制每条线并添加标签
    ax1.plot(cumulative_ret.index, cumulative_ret, label='累计百分比收益率', color='blue')
    ax1.plot(cumulative_ret_r.index, cumulative_ret_r, label='累计百分比收益率(滚动)', color='red')
    ax1.plot(cumulative_cost.index, cumulative_cost, label='累计百分比成本率', color='green', linestyle='--')
    ax1.plot(cumulative_cost_r.index, cumulative_cost_r, label='累计百分比成本率(滚动)', color='cyan', linestyle='--')
    ax1.plot(cumulative_bench.index, cumulative_bench, label='原始价格收益百分比', color='magenta')
    ax1.plot(cumulative_ret_out_r.index, cumulative_ret_out_r, label='累计百分比收益率(样本外滚动)', 
             color='orange',linewidth=4)
    ax1.axvline(x=boundary_day, color='black', linestyle='--', label='分界线')
    ax1.axhline(y=0, color='black', linestyle='-', label='Zero Line')  # 添加0轴
    # 在cumulative_cost_r曲线的最右侧末端标注"r"
    last_index_cost_r = cumulative_cost_r.index[-1]
    last_value_cost_r = cumulative_cost_r[-1]
    ax1.annotate('r', (last_index_cost_r, last_value_cost_r), 
                 textcoords="offset points", xytext=(5,0), ha='center', weight='bold',color='red')

    # 在cumulative_ret_r曲线的最右侧末端标注"r"
    last_index_ret_r = cumulative_ret_r.index[-1]
    last_value_ret_r = cumulative_ret_r[-1]
    ax1.annotate('r', (last_index_ret_r, last_value_ret_r), 
                 textcoords="offset points", xytext=(5,0), ha='center', weight='bold', color='red')
    
    # 在cumulative_ret_r曲线的最右侧末端标注"r"
    last_index_ret_out_r = cumulative_ret_out_r.index[-1]
    last_value_ret_out_r = cumulative_ret_out_r[-1]
    ax1.annotate('r', (last_index_ret_out_r, last_value_ret_out_r), 
                 textcoords="offset points", xytext=(5,0), ha='center', weight='bold', color='red')
    # 添加图例
    ax1.legend()
    # 设置标题和标签
    ax1.set_title('累计百分比收益率/成本率/原始价格收益率')
    ax1.set_xlabel('日期')
    ax1.set_ylabel('百分比累计曲线')
    # 添加网格
    ax1.grid(True)
    # 设置刻度参数
    ax1.tick_params(axis='both', which='major', labelsize=14, width=2, length=6)

    # 子图2: 3个月rolling sharpe
    rolling_sharpe = calc_rolling_sr_RC(combo_ret_se, combo_cost_se,
                                        rolling_days=21*3, min_period=30,
                                        day_bars=1,ann_days=252,fixed_return=0.03)
    median_sharpe = rolling_sharpe.median().values[0]

    ax2.plot(rolling_sharpe.index, rolling_sharpe, label='3个月滚动年夏普')
    ax2.axhline(y=median_sharpe, color='r', linestyle='--', linewidth=2, label='滚动夏普中位数')
    ax2.axhline(y=0, color='k', linestyle='-', label='Zero Line')
    ax2.legend()
    ax2.set_title('3个月滚动年夏普')
    ax2.set_xlabel('Date')
    ax2.set_ylabel('夏普率')
    ax2.grid(True)
    ax2.tick_params(axis='both', which='major', labelsize=14, width=2, length=6)

    # 新的子图4: 3个月rolling sharpe for combo_ret_se_r
    rolling_sharpe_r = calc_rolling_sr_RC(combo_ret_se_r, combo_cost_se_r,
                                        rolling_days=21*3, min_period=30,
                                        day_bars=1, ann_days=252, fixed_return=0.03)
    median_sharpe_r = rolling_sharpe_r.median().values[0]

    ax4.plot(rolling_sharpe_r.index, rolling_sharpe_r, label='3个月滚动年夏普(滚动)')
    ax4.axhline(y=median_sharpe_r, color='r', linestyle='--', linewidth=2, label='滚动夏普中位数')
    ax4.axhline(y=0, color='k', linestyle='-', label='Zero Line')
    ax4.legend()
    ax4.set_title('3个月滚动年夏普(滚动)')
    ax4.set_xlabel('Date')
    ax4.set_ylabel('夏普率')
    ax4.grid(True)
    ax4.tick_params(axis='both', which='major', labelsize=14, width=2, length=6)

    # 新的子图5: 3个月rolling sharpe for combo_ret_se_out_r
    rolling_sharpe_out_r = calc_rolling_sr_RC(combo_ret_se_out_r, combo_cost_se_out_r,
                                            rolling_days=21*3, min_period=30,
                                            day_bars=1, ann_days=252, fixed_return=0.03)
    median_sharpe_out_r = rolling_sharpe_out_r.median().values[0]

    ax5.plot(rolling_sharpe_out_r.index, rolling_sharpe_out_r, label='3个月滚动年夏普(样本外滚动)')
    ax5.axhline(y=median_sharpe_out_r, color='r', linestyle='--', linewidth=2, label='滚动夏普中位数')
    ax5.axhline(y=0, color='k', linestyle='-', label='Zero Line')
    ax5.legend()
    ax5.set_title('3个月滚动年夏普(样本外滚动)')
    ax5.set_xlabel('Date')
    ax5.set_ylabel('夏普率')
    ax5.grid(True)
    ax5.tick_params(axis='both', which='major', labelsize=14, width=2, length=6)

    # 子图3: 因子绩效指标数据报告
    metrics = ['sr', 'cost_sr', 'tot_ret', 'ann_ret', 'ann_std', 'mdd', 'avg_dd', 
               'monthly_skew', 'lower_tail', 'upper_tail', 'profit_factor', 'calmar', 
               'trade_times', 'win_rate', 'saw_score']
    formatted_metrics = []
    combo_name = combo_ret_se.name
    expected_length = len(metrics) + 2  # 包含 'bgn_mdd' 和 'end_mdd'

    for df_ratios in [df_ratios_in, df_ratios_out, df_ratios_all, df_ratios_all_r, df_ratios_out_r]:
        if combo_name in df_ratios.index:
            row = df_ratios.loc[combo_name]
            try:
                formatted_row = [format(row[metric], '.2f') if metric not in ['tot_ret', 'ann_ret', 'ann_std', 'mdd', 'avg_dd', 'win_rate', 'saw_score'] else format(row[metric] * 100, '.1f')+'%' if metric not in ['saw_score'] else format(row[metric]* 100, '.1f') for metric in metrics]
            except KeyError as e:
                print(f"Debug: Metric {e} not found in row for {combo_name}")
                formatted_row = ['N/A' for metric in metrics]  # 如果出错，填充'N/A'
            formatted_row += [row.get('bgn_mdd', 'N/A'), row.get('end_mdd', 'N/A')]
            
            # 检查行的长度是否匹配预期长度
            if len(formatted_row) != expected_length:
                print(f"Debug: Length mismatch for {combo_name} in {df_ratios}")
                formatted_row = formatted_row[:expected_length]  # 截断或填充以匹配预期长度
                if len(formatted_row) < expected_length:
                    formatted_row += ['N/A'] * (expected_length - len(formatted_row))
            
            formatted_metrics.append(formatted_row)
        else:
            print(f"Debug: {combo_name} not in df_ratios")  # 添加调试信息

    # 创建 DataFrame
    table_data = pd.DataFrame(formatted_metrics, columns=metrics + ['bgn_mdd', 'end_mdd'], index=['内(滚动)', '外(pred)', '全(内+外)', '全(滚动)', '外(滚动)'])
    table_data['bgn_mdd'] = table_data['bgn_mdd'].apply(lambda x: x.strftime('%Y-%m-%d') if isinstance(x, pd.Timestamp) else x)
    table_data['end_mdd'] = table_data['end_mdd'].apply(lambda x: x.strftime('%Y-%m-%d') if isinstance(x, pd.Timestamp) else x)

    # 转置 DataFrame 以适应表格
    table_data = table_data.T
    # 绘制表格并放置在右侧上方位置
    table = ax3.table(cellText=table_data.values,
                    rowLabels=table_data.index,
                    colLabels=table_data.columns,
                    cellLoc='center',
                    loc='upper right',
                    colWidths=[0.1]*len(table_data.columns))

    # 设置表格的宽度为总宽度的一半
    table.auto_set_column_width(col=list(range(len(table_data.columns))))
    table.auto_set_font_size(False)
    table.set_fontsize(18)
    table.scale(1.5, 1.5)  # 放大表格

    # 设置表格字体变粗变大
    for key, cell in table.get_celld().items():
        cell.set_text_props(weight='bold', fontsize=18)

    # 颜色编码（可选）
    for i, metric in enumerate(metrics):
        if metric in ['sr', 'cost_sr', 'profit_factor', 'calmar', 'win_rate', 'saw_score']:
            for j in range(len(table_data.columns)):
                cell_key = (i + 1, j)
                if cell_key in table.get_celld():
                    cell = table[cell_key]
                    cell.set_facecolor('#eafff5')  # 设定一个浅绿色背景色
                    cell.set_text_props(weight='bold', color='black')
        elif metric in ['mdd', 'avg_dd', 'lower_tail', 'upper_tail']:
            for j in range(len(table_data.columns)):
                cell_key = (i + 1, j)
                if cell_key in table.get_celld():
                    cell = table[cell_key]
                    cell.set_facecolor('#ffe5e5')  # 设定一个浅红色背景色
                    cell.set_text_props(weight='bold', color='black')
        else:
            for j in range(len(table_data.columns)):
                cell_key = (i + 1, j)
                if cell_key in table.get_celld():
                    cell = table[cell_key]
                    cell.set_facecolor('#ffffff')  # 默认白色背景

    # 去除纵轴和横轴刻度
    ax3.grid(visible=False)
    ax3.get_yaxis().set_ticks([])
    ax3.get_xaxis().set_ticks([])
    ax3.set_title('该组合的绩效指标数据报告')
    
    plt.tight_layout()
    plt.savefig(output)
    plt.close()

def plot_single_factor_stats( fct_ret_se: pd.Series,  # day-level
                        fct_cost_se: pd.Series, # day-level
                        benchmark: pd.Series, # day-level
                        df_ratios_in: pd.DataFrame,  # 结果矩阵,非时序
                        df_ratios_out: pd.DataFrame, # 结果矩阵,非时序
                        df_ratios_all: pd.DataFrame, # 结果矩阵,非时序
                        boundary_day: date,
                        output # 输出的文件路径和文件名信息
                      ):
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.weight'] = 'bold'
    # fig, axes = plt.subplots(2, 1, figsize=(15, 15))
    fig = plt.figure(figsize=(20, 15))
    fig.suptitle(fct_ret_se.name, fontsize=24, fontweight='bold', wrap=True)
    gs = fig.add_gridspec(2, 2, width_ratios=[1, 1], height_ratios=[1, 1])
    
    ax1 = fig.add_subplot(gs[0, 0])  # 子图1在左上
    ax2 = fig.add_subplot(gs[1, 0])  # 子图2在左下
    ax3 = fig.add_subplot(gs[:, 1])  # 子图3在右侧

    # 子图1: 累计日度百分比收益率, 累计日度成本百分比率, 累计bech_df
    cumulative_ret = fct_ret_se.cumsum()
    cumulative_cost = fct_cost_se.cumsum()
    cumulative_bench = benchmark.cumsum()
    boundary_day = pd.Timestamp(boundary_day)

    ax1.plot(cumulative_ret.index, cumulative_ret, label='累计百分比收益率')
    ax1.plot(cumulative_cost.index, cumulative_cost, label='累计百分比成本率')
    ax1.plot(cumulative_bench.index, cumulative_bench, label='原始价格收益百分比')
    ax1.axvline(x=boundary_day, color='r', linestyle='--', label='分界线')
    ax1.axhline(y=0, color='k', linestyle='-', label='Zero Line')  # 添加0轴
    ax1.legend()
    ax1.set_title('累计百分比收益率/成本率/原始价格收益率')
    ax1.set_xlabel('Date')
    ax1.set_ylabel('百分比累计曲线')
    ax1.grid(True)
    ax1.tick_params(axis='both', which='major', labelsize=14, width=2, length=6)

    # 子图2: 3个月rolling sharpe
    rolling_sharpe = calc_rolling_sr_RC(fct_ret_se, fct_cost_se,
                                        rolling_days=21*3, min_period=30,
                                        day_bars=1,ann_days=252,fixed_return=0.03)
    median_sharpe = rolling_sharpe.median().values[0]

    ax2.plot(rolling_sharpe.index, rolling_sharpe, label='3个月滚动年夏普')
    ax2.axhline(y=median_sharpe, color='r', linestyle='--', label='滚动夏普中位数')
    ax2.axhline(y=0, color='k', linestyle='-', label='Zero Line')
    ax2.legend()
    ax2.set_title('3个月滚动年夏普')
    ax2.set_xlabel('Date')
    ax2.set_ylabel('夏普率')
    ax2.grid(True)
    ax2.tick_params(axis='both', which='major', labelsize=14, width=2, length=6)

    # 子图3: 因子绩效指标数据报告
    metrics = ['sr', 'cost_sr', 'tot_ret', 'ann_ret', 'ann_std', 'mdd', 'avg_dd', 
               'monthly_skew', 'lower_tail', 'upper_tail', 'profit_factor', 'calmar', 
               'trade_times', 'win_rate', 'saw_score', 'topsis_score']
    formatted_metrics = []
    factor_name = fct_ret_se.name
    formatted_metrics = []
    factor_name = fct_ret_se.name
    expected_length = len(metrics) + 2  # 包含 'bgn_mdd' 和 'end_mdd'

    for df_ratios in [df_ratios_in, df_ratios_out, df_ratios_all]:
        if factor_name in df_ratios.index:
            row = df_ratios.loc[factor_name]
            try:
                formatted_row = [format(row[metric], '.2f') if metric not in ['tot_ret', 'ann_ret', 'ann_std', 'mdd', 'avg_dd', 'win_rate', 'saw_score', 'topsis_score'] else format(row[metric] * 100, '.1f')+'%' if metric not in ['saw_score', 'topsis_score'] else format(row[metric]* 100, '.1f') for metric in metrics]
            except KeyError as e:
                print(f"Debug: Metric {e} not found in row for {factor_name}")
                formatted_row = ['N/A' for metric in metrics]  # 如果出错，填充'N/A'
            formatted_row += [row.get('bgn_mdd', 'N/A'), row.get('end_mdd', 'N/A')]
            
            # 检查行的长度是否匹配预期长度
            if len(formatted_row) != expected_length:
                print(f"Debug: Length mismatch for {factor_name} in {df_ratios}")
                formatted_row = formatted_row[:expected_length]  # 截断或填充以匹配预期长度
                if len(formatted_row) < expected_length:
                    formatted_row += ['N/A'] * (expected_length - len(formatted_row))
            
            formatted_metrics.append(formatted_row)
        else:
            print(f"Debug: {factor_name} not in df_ratios")  # 添加调试信息

    # 创建 DataFrame
    table_data = pd.DataFrame(formatted_metrics, columns=metrics + ['bgn_mdd', 'end_mdd'], index=['样本内', '样本外', '全数据'])
    table_data['bgn_mdd'] = table_data['bgn_mdd'].apply(lambda x: x.strftime('%Y-%m-%d') if isinstance(x, pd.Timestamp) else x)
    table_data['end_mdd'] = table_data['end_mdd'].apply(lambda x: x.strftime('%Y-%m-%d') if isinstance(x, pd.Timestamp) else x)

    # 转置 DataFrame 以适应表格
    table_data = table_data.T
    # 绘制表格并放置在右侧上方位置
    table = ax3.table(cellText=table_data.values,
                    rowLabels=table_data.index,
                    colLabels=table_data.columns,
                    cellLoc='center',
                    loc='upper right',
                    colWidths=[0.1]*len(table_data.columns))

    table.auto_set_font_size(False)
    table.set_fontsize(18)
    table.scale(2, 2)

    # 设置表格的宽度为总宽度的一半
    table.auto_set_column_width(col=list(range(len(table_data.columns))))
    table.auto_set_font_size(False)
    table.scale(1.5, 1.5)  # 放大表格

    # 设置表格字体变粗变大
    for key, cell in table.get_celld().items():
        cell.set_text_props(weight='bold', fontsize=18)

    # 颜色编码（可选）
    for i, metric in enumerate(metrics):
        if metric in ['sr', 'cost_sr', 'profit_factor', 'calmar', 'win_rate', 'saw_score', 'topsis_score']:
            for j in range(len(table_data.columns)):
                cell_key = (i + 1, j)
                if cell_key in table.get_celld():
                    cell = table[cell_key]
                    cell.set_facecolor('#eafff5')  # 设定一个浅绿色背景色
                    cell.set_text_props(weight='bold', color='black')
        elif metric in ['mdd', 'avg_dd', 'lower_tail', 'upper_tail']:
            for j in range(len(table_data.columns)):
                cell_key = (i + 1, j)
                if cell_key in table.get_celld():
                    cell = table[cell_key]
                    cell.set_facecolor('#ffe5e5')  # 设定一个浅红色背景色
                    cell.set_text_props(weight='bold', color='black')
        else:
            for j in range(len(table_data.columns)):
                cell_key = (i + 1, j)
                if cell_key in table.get_celld():
                    cell = table[cell_key]
                    cell.set_facecolor('#ffffff')  # 默认白色背景

    # 去除纵轴和横轴刻度
    ax3.get_yaxis().set_ticks([])
    ax3.get_xaxis().set_ticks([])
    ax3.set_title('单因子绩效指标数据报告')
    
    plt.tight_layout()
    plt.savefig(output)
    plt.close()

def plot_eq6x4(df: pd.DataFrame, compr_col: list = ['ret'], 
               compr_show_cum: bool = False, save_path: str = None):
    '''
    根据df[compr_col] 和 df因子列 绘制 曲线图
    compr_col: 右轴做比较的列,比如close, ret这些
    compr_show_cum: 是否cum后显示, 比如ret这类
    其他因子列: 因子值变化曲线(不累计)
    '''
    if save_path is None:
        return
    df = df.copy()
    # 获取DataFrame的列名
    columns = df.columns.tolist()
    # 移除label_col中的列，剩下的应该是因子列
    factor_cols = [col for col in columns if col not in compr_col]
    cum_factor_cols = {f'cum_{col}': df[col].cumsum() for col in factor_cols} # 对因子列进行累加
    if compr_show_cum: # 对compr_col中的列进行累加
        cum_compr_cols = {f'cum_{lcol}': df[lcol].cumsum() for lcol in compr_col}
        # 使用pd.concat一次性添加所有新的累积和列
        df = pd.concat([df, pd.DataFrame(cum_compr_cols), pd.DataFrame(cum_factor_cols)], axis=1)
        real_compr_cols = [f'cum_{col}' for col in columns if col in compr_col]
    else:
        # 使用pd.concat一次性添加所有新的累积和列
        df = pd.concat([df, pd.DataFrame(cum_factor_cols)], axis=1)
        real_compr_cols = [col for col in columns if col in compr_col]
        
    # 固定子图行列数为6x4
    num_rows = 6
    num_cols = 4
    plots_per_fig = num_rows * num_cols

    # 计算需要多少个图
    num_plots = len(factor_cols)
    num_figs = (num_plots + plots_per_fig - 1) // plots_per_fig  # 向上取整

    for fig_idx in range(num_figs):
        # 创建画布，根据固定的行数和列数调整figsize
        fig, axs = plt.subplots(nrows=num_rows, ncols=num_cols, 
                                figsize=(10 * num_cols, 3 * num_rows), 
                                sharex=True, squeeze=False)

        start_col = fig_idx * plots_per_fig
        end_col = min(start_col + plots_per_fig, num_plots)

        # 遍历因子列并绘制
        for i in range(start_col, end_col):
            col = factor_cols[i]
            row = (i - start_col) // num_cols
            col_index = (i - start_col) % num_cols
            ax1 = axs[row][col_index]
            # 创建新的左侧轴用于显示累积因子值
            # ax1_2 = axs[row][col_index].twinx()
            # ax1_2.spines['left'].set_position(('outward', 60))
            # ax1_2.spines['left'].set_color('purple')
            # ax1_2.tick_params(axis='y', colors='purple')
            # 创建右侧轴
            ax2 = axs[row][col_index].twinx()
            # 使用采样顺序
            sample_order = range(len(df))
            # 绘制因子列和其累积曲线
            ax1.plot(sample_order, df[col], 'b-', label=f'Factor: {i+1}')
            # ax1_2.plot(sample_order, df[f'cum_{col}'], 'b-', label=f'Cum Factor: {i+1}')
            
            # 绘制close及其累积曲线
            ax2.plot(sample_order, df[real_compr_cols], 'r-', label=', '.join(real_compr_cols))
            # 调整右轴的刻度范围以适配close和累积close的区间
            # ax2.set_ylim(df[real_compr_cols].min(), df[real_compr_cols].max())
            
            ax1.set_ylabel(f'Factor: {i+1}', color='g', fontsize=10, fontweight='bold')
            ax1.tick_params(axis='y', colors='g',labelsize=12)
            ax1.legend(loc='upper left')
            # ax1_2.set_ylabel(f'Cum Factor: {i+1}', color='b')
            # ax1_2.legend(loc='upper left')
            ax2.set_ylabel(', '.join(real_compr_cols), color='g',fontsize=10, fontweight='bold')
            ax2.tick_params(axis='y', colors='g',labelsize=12)
            ax2.legend(loc='upper right')
            # 设置子图标题
            max_length = 64
            prefix_length = (max_length - 3) // 2  # 3 for '...'
            suffix_length = max_length - 3 - prefix_length
            axs[row][col_index].set_title(
                f'factor {3*(i+1)-2}-{3*(i+1)}:  {col}' if len(col) <= max_length 
                else f'factor {3*(i+1)-2}-{3*(i+1)}:  {col[:prefix_length]}...{col[-suffix_length:]}', 
                fontsize=15, 
                fontweight='bold'
            )

        # 如果因子数量少于24个，则隐藏多余的子图
        # for i in range(end_col - start_col, plots_per_fig):
        #     # row = i // num_cols
        #     row = (end_col - start_col + i) // num_cols
        #     col_index = (end_col - start_col + i) % num_cols
        #     # col_index = i % num_cols
        #     axs[row][col_index].axis('off')
        for i in range(end_col - start_col, plots_per_fig):
            row = i // num_cols
            col_index = i % num_cols
            if row < num_rows:
                axs[row][col_index].axis('off')
        # 设置共享X轴的标签
        fig.text(0.5, 0.04, 'Sample Order', ha='center')
        # 调整子图间距
        plt.tight_layout()
        # 保存图表
        plt.savefig(save_path+'_eq{}.png'.format(fig_idx + 1))
        plt.close()

def plot_hist6x4(df, save_path: str = None):
    """
    绘制DataFrame中指定列的直方图，并显示数据量、偏斜度和峰度，
    按照6x4子图格式布局，并保存图像。
    """
    if save_path is None:
        return
    df = df.copy()
    if isinstance(df, pd.Series):
        df = pd.DataFrame(df)
    print(df)
    # 计算需要的子图总数和实际的行数与列数
    col_list = df.columns.tolist()
    n = len(col_list)
    num_rows = 6
    num_cols = 4
    plots_per_fig = num_rows * num_cols
    num_figs = (n + plots_per_fig - 1) // plots_per_fig
    
    for fig_idx in range(num_figs):
        start_col = fig_idx * plots_per_fig
        end_col = min(start_col + plots_per_fig, n)
        
        fig, axs = plt.subplots(nrows=num_rows, ncols=num_cols,
                                figsize=(10 * num_cols, 3 * num_rows),
                                squeeze=False)
        
        for i in range(start_col, end_col):
            col = col_list[i]
            row = (i - start_col) // num_cols
            col_idx = (i - start_col) % num_cols
            ax_hist = axs[row][col_idx]
            ax_box = ax_hist.twinx()
            
            '''=================== 绘制直方图和密度曲线 ================ '''
            try:
                density = gaussian_kde(df[col])
            except np.linalg.LinAlgError:
                print(f'np.linalg.LinAlgError: column_{i+1} - {col}')
            counts, bins, patches = ax_hist.hist(df[col], bins=50, alpha=0.75, density=True)
            if density:
                ax_hist.plot(bins, density(bins), color='k', linewidth=2)
            
            ''' ===================== 绘制箱型图 ===================== '''
            bp = ax_box.boxplot(df[col], vert=False)
            # 加粗箱体的线条
            for box in bp['boxes']:
                box.set(linewidth=2)  # 设置箱体线条宽度
            # 加粗中位数线
            for median in bp['medians']:
                median.set(linewidth=2)
            # 加粗须触线和端点
            for whisker in bp['whiskers']:
                whisker.set(linewidth=2)
            for cap in bp['caps']:
                cap.set(linewidth=2)
            
            ''' ===================== 添加均值和置信区间的线 ===================== '''
            mean = df[col].mean()
            std_err = df[col].std() / np.sqrt(len(df[col]))
            conf_interval = norm.interval(0.95, loc=mean, scale=std_err)
            ax_hist.axvline(mean, color='r', linestyle='--', label=f'Mean: {mean:.2f}')
            if density:
                ax_hist.fill_between(bins, 0, density(bins), alpha=0.2)
            
            # 添加统计信息
            ax_hist.text(0.95, 0.95, f'Mean: {mean:.2f}\nCI: [{conf_interval[0]:.2f}, {conf_interval[1]:.2f}]',
                         verticalalignment='top', horizontalalignment='right',
                         transform=ax_hist.transAxes, color='red', fontsize=15, fontweight='bold')
            
            ax_hist.text(0.05, 0.9, f'Count: {len(df[col])}',
                         transform=ax_hist.transAxes, fontsize=15,fontweight='bold')
            ax_hist.text(0.05, 0.8, f'Skew: {df[col].skew():.2f}',
                         transform=ax_hist.transAxes, fontsize=15,fontweight='bold')
            ax_hist.text(0.05, 0.7, f'Kurt: {df[col].kurt():.2f}',
                         transform=ax_hist.transAxes, fontsize=15,fontweight='bold')
            
            # 设置子图标题
            max_length = 64
            prefix_length = (max_length - 3) // 2  # 3 for '...'
            suffix_length = max_length - 3 - prefix_length
            ax_hist.set_title(
                f'factor {3*(i+1)-2}-{3*(i+1)}: {col}' if len(col) <= max_length 
                else f'factor {3*(i+1)-2}-{3*(i+1)}: {col[:prefix_length]}...{col[-suffix_length:]}', 
                fontsize=15, 
                fontweight='bold'
            )
            # title_position = (0.5, 0.1)  # 这里的元组定义了标题的水平和垂直位置（相对于子图），可以根据需要调整
            # ax_hist.text(title_position[0], title_position[1], col, 
            #             horizontalalignment='center', verticalalignment='bottom', 
            #             transform=ax_hist.transAxes, fontsize=15, fontweight='bold')   
                
            # for ax in [ax_hist, ax_box]:
            #     ax.tick_params(axis='both', which='major', labelsize=15)  # 减小坐标轴数字字体大小
            
            # 隐藏多余的子图
            for i in range(end_col - start_col, plots_per_fig):
                row = i // num_cols
                col_idx = i % num_cols
                if row < num_rows:
                    axs[row][col_idx].axis('off')
        # 调整子图间距
        plt.tight_layout()
        # 保存图像
        plt.savefig(save_path+'_hist{}.png'.format(fig_idx + 1))
        plt.close()

def plot_hist(df, col_list: list, incl_y: bool = False):
    """
    绘制DataFrame中指定列的直方图，并显示数据量、偏斜度和峰度。
    """
    # TODO: 分位数回归图
    df = df.copy()
    df.info()
    if not incl_y:
        # 用于分位数回归的y
        df['y'] = df['close'].shift(-1) / df['close'] - 1   # puppy: 用未来t期收益率, 此处可norm也可不norm (TODO:尝试norm)
        import jzal_pro.utils.expr_utils as eu
        df['y'] = eu.norm(df['y'], params=NORM_PARAMS_LABEL)  # (1000,2,0)相对优,TODO:进一步测
        df['y'] = df['y'].astype(np.float64).fillna(0)
        col_list.insert(0, 'y') 
    
    n = len(col_list)
    cols = math.ceil(math.sqrt(n))
    rows = math.ceil(n / cols)

    
    fig, axs = plt.subplots(rows, cols, figsize=(10*cols, 8*rows), squeeze=False) # 创建一个画布和子图网格
    fig.subplots_adjust(top=0.9, wspace=0.4, hspace=0.6)  # 调整子图间距
    # 移动图例到图表外侧
    plt.legend(bbox_to_anchor=(1.05, 0.5), loc='center left', borderaxespad=0.)
    # 绘制每个直方图并计算统计数据
    for i, col in enumerate(col_list):
        # 计算子图的行和列索引
        row = i // cols
        col_idx = i % cols
        ax_hist = axs[row, col_idx]  # 创建或获取绘图轴
        ax_box = ax_hist.twinx()  # 创建用于箱线图的轴
        # 绘制直方图
        counts, bins, patches = ax_hist.hist(df[col], bins=20, alpha=0.75, density=True)
        # 绘制密度曲线
        ax_hist.plot(bins, gaussian_kde(df[col])(bins), color='k')
        # 在另一个轴上绘制箱线图
        ax_box.boxplot(df[col], vert=False)
        ax_box.tick_params(labelbottom=False, labeltop=False)
        # 计算均值和置信区间
        mean = df[col].mean()
        std_err = df[col].std() / np.sqrt(len(df[col]))
        conf_interval = norm.interval(0.95, loc=mean, scale=std_err)        
        # 添加均值和置信区间
        ax_hist.axvline(mean, color='r', linestyle='--', label=f'Mean: {mean:.2f}')
        ax_hist.fill_between(bins, 0, gaussian_kde(df[col])(bins), alpha=0.2)
        ax_hist.text(0.95, 0.95, f'Mean: {mean:.2f}\nCI: [{conf_interval[0]:.2f}, {conf_interval[1]:.2f}]',
                     verticalalignment='top', horizontalalignment='right',
                     transform=ax_hist.transAxes, color='red', fontsize=7,fontweight='bold')        
        # 计算数据量、偏斜度和峰度
        data_count = len(df[col])
        # skewness = skew(df[col], bias=False)
        skewness = df[col].skew()
        # kurt = kurtosis(df[col], fisher=False, bias=False) # 算的是超额kurt, 亮建议统一用pandas的函数
        kurt = df[col].kurt()
        # 在直方图上添加统计信息文本
        ax_hist.text(0.05, 0.9, f'Count: {data_count}',
                               transform=ax_hist.transAxes,fontsize=7,fontweight='bold')
        ax_hist.text(0.05, 0.8, f'Skew: {skewness:.2f}',
                               transform=ax_hist.transAxes,fontsize=7,fontweight='bold')
        ax_hist.text(0.05, 0.7, f'Kurt: {kurt:.2f}',
                               transform=ax_hist.transAxes,fontsize=7,fontweight='bold')
        # # 绘制分位数回归线
        # for quantile in [0.25, 0.5, 0.75]:  # 选择几个分位数
        #     # 拟合分位数回归模型
        #     model = QuantReg(y, df[[col]], q=quantile).fit()
        #     # 预测分位数回归线
        #     predictions = model.predict(df[[col]])
        #     # 绘制回归线
        #     ax_hist.plot(df[col], predictions, linestyle='--', label=f'Q{int(quantile*100)} Reg')
            
        # # 设置子图的标题
        # ax_hist.set_title(col, fontsize=5)
        # 在子图中央偏下位置添加加粗的标题
        title_position = (0.5, 0.1)  # 这里的元组定义了标题的水平和垂直位置（相对于子图），可以根据需要调整
        ax_hist.text(title_position[0], title_position[1], col, 
                    horizontalalignment='center', verticalalignment='bottom', 
                    transform=ax_hist.transAxes, fontsize=7, fontweight='bold')   
             
        for ax in [ax_hist, ax_box]:
            ax.tick_params(axis='both', which='major', labelsize=5)  # 减小坐标轴数字字体大小
    # 隐藏多余的子图
    for i in range(n, rows * cols):
        row = i // cols
        col_idx = i % cols
        axs[row, col_idx].axis('off')
    # 调整子图之间的间距
    plt.tight_layout()
    # plt.legend()
    # 显示图形
    plt.show()
    
# def corr()