import numpy as np
import pandas as pd
from scipy.stats import percentileofscore
import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent.parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))

def _flatten(x):
    if isinstance(x, pd.Series):
        x1 = x.to_numpy().flatten()
    elif isinstance(x, np.ndarray):
        x1 = x.flatten()
    return x1

def rolling(se, N, func):
    ind = getattr(se.rolling(window=N), func)()
    return ind

def sma(se:pd.Series, N) :  # 不用norm 会在dataloader里统一处理 
    x = (np.nan_to_num(se.rolling(window=N, min_periods=int(N / 2)).mean()))
    res = pd.Series(x, index=se.index)
    return res

def sum(se: pd.Series, N):
    return rolling(se, N, 'sum')


def max(se, N):
    return rolling(se, N, 'max').fillna(0)


def min(se, N):
    return rolling(se, N, 'min').fillna(0)


def std(se, N):
    return rolling(se, N, 'std')


def avg(se, N):
    se = rolling(se, N, 'mean')
    return se


def mean(se, N):
    return avg(se, N)

def idxmax(se, N=18, min_periods=2): # 性能提升200倍!
    se_flat = _flatten(se)
    # 检查 se 的长度是否足够
    if len(se_flat) < N:
        N = len(se_flat)
    result = np.full(len(se_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(se_flat, window_shape=N)
    # 计算最大值的位置
    idxmaxs = np.nanargmax(x, axis=1)
    # 填充结果，确保窗口正确对齐
    result[N-1:] = idxmaxs
    # 处理 min_periods 的情况
    for i in range(min_periods, N):
        x_min_periods = se_flat[:i]
        # x_min_periods = x_min_periods[~np.isnan(x_min_periods)]
        if len(x_min_periods) > 0:
            idxmaxs = np.nanargmax(x_min_periods)  # 在当前有效窗口内找到最大值的索引
            result[i-1] = idxmaxs
    
    return pd.Series(result, index=se.index)

def idxmin(se, N=18, min_periods=2): # 性能提升200倍!
    se_flat = _flatten(se)
    # 检查 se 的长度是否足够
    if len(se_flat) < N:
        N = len(se_flat)
    result = np.full(len(se_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(se_flat, window_shape=N)
    idxmins = np.nanargmin(x, axis=1)
    # 填充结果，确保窗口正确对齐
    result[N-1:] = idxmins 
    # 处理 min_periods 的情况
    for i in range(min_periods, N):
        x_min_periods = se_flat[:i]
        # x_min_periods = x_min_periods[~np.isnan(x_min_periods)]
        if len(x_min_periods) > 0:
            idxmins = np.nanargmin(x_min_periods)  # 在当前有效窗口内找到最大值的索引
            result[i-1] = idxmins
    
    return pd.Series(result, index=se.index)

def rank(se, N=5): # 性能提升1.5倍
    arry = _flatten(se)
    if len(arry) < N:
        N = len(arry)
    result = np.full(len(arry), np.nan)
    windows = np.lib.stride_tricks.sliding_window_view(arry, window_shape=N)
    # 计算每个窗口的百分位数
    scores = windows[:, -1]
    ranks = np.array([percentileofscore(window, score) / len(window) for window, score in zip(windows, scores)])
    result[N-1:] = ranks
    
    return pd.Series(result, index=se.index)


def _zscore(se):  # 可能不再用到
    try:
        se.dropna(inplace=True)
        value = (se.iloc[-1] - se.mean()) / se.std()
        if type(value) is not np.float64:
            print(value)
        return value
    except:
        return -1

def zscore(se: pd.Series, N=5, min_periods=2): # 性能提升700倍
    arry = _flatten(se)
    if len(arry) < N:
        N = len(arry)
        return -1
    result = np.full(len(arry), np.nan)
    # 使用 sliding_window_view 创建滑动窗口
    windows = np.lib.stride_tricks.sliding_window_view(arry, window_shape=N)
    # 计算每个窗口的均值和标准差
    means = np.nanmean(windows, axis=1)
    stds = np.nanstd(windows, axis=1, ddof=1)
    zscores = np.zeros_like(means)
    mask = stds != 0
    zscores[mask] = (windows[mask, -1] - means[mask]) / stds[mask]
    zscores[~mask] = -1
    result[N-1:] = zscores   
    # 处理 min_periods 的情况
    for i in range(min_periods, N):
        x_min_periods = arry[:i]
        if len(x_min_periods) > 0:
            mean_value = np.nanmean(x_min_periods)
            std_value = np.nanstd(x_min_periods, ddof=1)
            if std_value != 0:
                zscore_value = (x_min_periods[-1] - mean_value) / std_value
            else:
                zscore_value = -1
            result[i-1] = zscore_value
    
    return pd.Series(result, index=se.index)

def quantile(se, N, qscore):
    return se.rolling(N, min_periods=1).quantile(qscore)


def bias(se, N=5):
    arry = _flatten(se)
    if len(arry) < N:
        N = len(arry)
    result = np.full(len(arry), np.nan)
    # 使用 sliding_window_view 创建滑动窗口
    windows = np.lib.stride_tricks.sliding_window_view(arry, window_shape=N)
    means = np.nanmean(windows, axis=1)
    biases = windows / means[:, np.newaxis]
    result[N-1:] = biases[:, -1]
    
    return pd.Series(result, index=se.index)


def _qcut(se: pd.Series, quantiles, N):
    if len(se) < 3:
        return
    return pd.qcut(se, quantiles, labels=range(0, N), duplicates='drop').astype('float')

def qcut(se: pd.Series, N):
    quantiles = [step / 100 for step in range(0, 100, int(100 / N))]
    if len(quantiles) <= N:
        quantiles.append(1)
    labels = se.rolling(N).apply(
        lambda sub_se: _qcut(sub_se, quantiles, N))
    return labels

def orv(volume: pd.Series, n_days: int) -> pd.Series:
    ''' 每日首bar的成交量量比, 参数是n_days, 不是n_bars '''
    daily_first_bar_volume = volume.groupby(volume.index.date).first()
    # 计算过去 N 天内每天第一个 bar 的成交量总和，不包含当天
    rolling_sum_volume = daily_first_bar_volume.rolling(window=n_days).sum().shift(1)
    # 计算量比
    orv_ratio = daily_first_bar_volume / rolling_sum_volume
    # 将量比重新对齐到原始 DataFrame 的日期索引
    orv_ratio = orv_ratio.reindex(volume.index, method='bfill')
    return orv_ratio

def orv_index(volume: pd.Series, n_days: int, compr_ratio: float) -> pd.Series:
    ''' 每日首bar的成交量量比, compr_ratio为被比较的倍数 '''
    orv_ratio = orv(volume, n_days)
    result = (orv_ratio >= compr_ratio).astype(int)
    return result

if __name__ == "__main__":
    start_date = '2023-01-01'
    end_date = '2024-01-10'
    freq = '15min'

    def generate_data(start_date, end_date, freq='15min'):
        dates = pd.date_range(start=start_date, end=end_date, freq=freq)
        data = {
            'open': np.random.uniform(100, 110, size=len(dates)),
            'high': np.random.uniform(105, 115, size=len(dates)),
            'low': np.random.uniform(95, 105, size=len(dates)),
            'close': np.random.uniform(100, 110, size=len(dates)),
            'volume': np.random.uniform(1000, 5000, size=len(dates)).astype(int)
        }
        df = pd.DataFrame(data, index=dates)
        return df

    df = generate_data(start_date, end_date, freq)

    # 计算 ORV 指标
    n_days = 5
    compr_ratio = 1.1

    # 使用函数计算 ORV 指标并转换为二进制结果
    orv_result = orv_index(df['volume'], n_days, compr_ratio)

    # 添加结果到原始 DataFrame
    df['orv_index'] = orv_result

    # 显示结果
    print(df)
    df.to_csv('orv.csv')