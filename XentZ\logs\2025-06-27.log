2025-06-27 10:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:20 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:20 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:20 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:20 | INFO     | ResMonitor.end_timer: F_GP_GP_20250627_102018_216673_000001_TEST_001 took 0.00 seconds (00:00:00)
2025-06-27 10:20 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_20250627_102018_216673_000001_TEST_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 10:20 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_20250627_102018_216673_000001_TEST_001 memory delta: -0.35MB
2025-06-27 10:20 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_20250627_102018_216673_000001_TEST_001 completed successfully
2025-06-27 10:20 | INFO     | ResMonitor.end_timer: GP_MINING_TEST_GP_20250627_102018_216673 took 0.06 seconds (00:00:00)
2025-06-27 10:20 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_TEST_GP_20250627_102018_216673 CPU usage: 0.00s user, 0.03s system
2025-06-27 10:20 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_TEST_GP_20250627_102018_216673 memory delta: 0.77MB
2025-06-27 10:20 | INFO     | MonitorContext.__exit__: GP_MINING_TEST_GP_20250627_102018_216673 completed successfully
2025-06-27 10:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:41 | INFO     | ResMonitor.end_timer: TEST_GP_MINING_001 took 0.10 seconds (00:00:00)
2025-06-27 10:41 | INFO     | ResMonitor.end_timer_cpu: TEST_GP_MINING_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 10:41 | INFO     | ResMonitor.end_memory_monitor: TEST_GP_MINING_001 memory delta: 0.10MB
2025-06-27 10:41 | INFO     | FactorMonitorContext.__exit__: TEST_GP_MINING_001 completed successfully
2025-06-27 10:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:41 | INFO     | ResMonitor.end_timer: TEST_FACTOR_CALCULATION_001 took 0.18 seconds (00:00:00)
2025-06-27 10:41 | INFO     | ResMonitor.end_timer_cpu: TEST_FACTOR_CALCULATION_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 10:41 | INFO     | ResMonitor.end_memory_monitor: TEST_FACTOR_CALCULATION_001 memory delta: 0.08MB
2025-06-27 10:41 | INFO     | FactorMonitorContext.__exit__: TEST_FACTOR_CALCULATION_001 completed successfully
2025-06-27 10:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:41 | INFO     | ResMonitor.end_timer: TEST_SELECT_BY_SKEW_001 took 0.10 seconds (00:00:00)
2025-06-27 10:41 | INFO     | ResMonitor.end_timer_cpu: TEST_SELECT_BY_SKEW_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 10:41 | INFO     | ResMonitor.end_memory_monitor: TEST_SELECT_BY_SKEW_001 memory delta: 0.08MB
2025-06-27 10:41 | INFO     | FactorMonitorContext.__exit__: TEST_SELECT_BY_SKEW_001 completed successfully
2025-06-27 10:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:41 | INFO     | ResMonitor.end_timer: TEST_SELECT_BY_KURT_001 took 0.10 seconds (00:00:00)
2025-06-27 10:41 | INFO     | ResMonitor.end_timer_cpu: TEST_SELECT_BY_KURT_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 10:41 | INFO     | ResMonitor.end_memory_monitor: TEST_SELECT_BY_KURT_001 memory delta: 0.08MB
2025-06-27 10:41 | INFO     | FactorMonitorContext.__exit__: TEST_SELECT_BY_KURT_001 completed successfully
2025-06-27 10:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:41 | INFO     | ResMonitor.end_timer: TEST_SELECT_BY_SIC_001 took 0.10 seconds (00:00:00)
2025-06-27 10:41 | INFO     | ResMonitor.end_timer_cpu: TEST_SELECT_BY_SIC_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 10:41 | INFO     | ResMonitor.end_memory_monitor: TEST_SELECT_BY_SIC_001 memory delta: 0.08MB
2025-06-27 10:41 | INFO     | FactorMonitorContext.__exit__: TEST_SELECT_BY_SIC_001 completed successfully
2025-06-27 10:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:41 | INFO     | ResMonitor.end_timer: TEST_SELECT_BY_CORR_001 took 0.10 seconds (00:00:00)
2025-06-27 10:41 | INFO     | ResMonitor.end_timer_cpu: TEST_SELECT_BY_CORR_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 10:41 | INFO     | ResMonitor.end_memory_monitor: TEST_SELECT_BY_CORR_001 memory delta: 0.09MB
2025-06-27 10:41 | INFO     | FactorMonitorContext.__exit__: TEST_SELECT_BY_CORR_001 completed successfully
2025-06-27 10:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:41 | INFO     | ResMonitor.end_timer: TEST_REG_F_001 took 0.01 seconds (00:00:00)
2025-06-27 10:41 | INFO     | ResMonitor.end_timer_cpu: TEST_REG_F_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 10:41 | INFO     | ResMonitor.end_memory_monitor: TEST_REG_F_001 memory delta: 0.32MB
2025-06-27 10:41 | INFO     | FactorMonitorContext.__exit__: TEST_REG_F_001 completed successfully
2025-06-27 10:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:41 | INFO     | ResMonitor.end_timer: TEST_REG_F_002 took 0.01 seconds (00:00:00)
2025-06-27 10:41 | INFO     | ResMonitor.end_timer_cpu: TEST_REG_F_002 CPU usage: 0.00s user, 0.00s system
2025-06-27 10:41 | INFO     | ResMonitor.end_memory_monitor: TEST_REG_F_002 memory delta: 0.14MB
2025-06-27 10:41 | INFO     | FactorMonitorContext.__exit__: TEST_REG_F_002 completed successfully
2025-06-27 10:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:41 | INFO     | ResMonitor.end_timer: TEST_REG_F_003 took 0.00 seconds (00:00:00)
2025-06-27 10:41 | INFO     | ResMonitor.end_timer_cpu: TEST_REG_F_003 CPU usage: 0.00s user, 0.02s system
2025-06-27 10:41 | INFO     | ResMonitor.end_memory_monitor: TEST_REG_F_003 memory delta: 0.34MB
2025-06-27 10:41 | INFO     | FactorMonitorContext.__exit__: TEST_REG_F_003 completed successfully
2025-06-27 10:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:41 | INFO     | ResMonitor.end_timer: GP_MINE_TEST.SH_test took 0.50 seconds (00:00:00)
2025-06-27 10:41 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST.SH_test CPU usage: 0.00s user, 0.00s system
2025-06-27 10:41 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST.SH_test memory delta: 0.01MB
2025-06-27 10:41 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST.SH_test completed successfully
2025-06-27 10:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:41 | INFO     | ResMonitor.end_timer: CALC_TEST.SH_test took 0.20 seconds (00:00:00)
2025-06-27 10:41 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST.SH_test CPU usage: 0.00s user, 0.00s system
2025-06-27 10:41 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST.SH_test memory delta: 0.00MB
2025-06-27 10:41 | INFO     | FactorMonitorContext.__exit__: CALC_TEST.SH_test completed successfully
2025-06-27 10:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:41 | INFO     | ResMonitor.end_timer: SKEW_TEST.SH_test took 0.10 seconds (00:00:00)
2025-06-27 10:41 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST.SH_test CPU usage: 0.00s user, 0.00s system
2025-06-27 10:41 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST.SH_test memory delta: 0.00MB
2025-06-27 10:41 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST.SH_test completed successfully
2025-06-27 10:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:41 | INFO     | ResMonitor.end_timer: CORR_TEST.SH_test took 0.10 seconds (00:00:00)
2025-06-27 10:41 | INFO     | ResMonitor.end_timer_cpu: CORR_TEST.SH_test CPU usage: 0.00s user, 0.00s system
2025-06-27 10:41 | INFO     | ResMonitor.end_memory_monitor: CORR_TEST.SH_test memory delta: 0.00MB
2025-06-27 10:41 | INFO     | FactorMonitorContext.__exit__: CORR_TEST.SH_test completed successfully
2025-06-27 10:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:42 | INFO     | ResMonitor.end_timer: TEST_GP_MINING_001 took 0.11 seconds (00:00:00)
2025-06-27 10:42 | INFO     | ResMonitor.end_timer_cpu: TEST_GP_MINING_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 10:42 | INFO     | ResMonitor.end_memory_monitor: TEST_GP_MINING_001 memory delta: 0.09MB
2025-06-27 10:42 | INFO     | FactorMonitorContext.__exit__: TEST_GP_MINING_001 completed successfully
2025-06-27 10:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:42 | INFO     | ResMonitor.end_timer: TEST_FACTOR_CALCULATION_001 took 0.10 seconds (00:00:00)
2025-06-27 10:42 | INFO     | ResMonitor.end_timer_cpu: TEST_FACTOR_CALCULATION_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 10:42 | INFO     | ResMonitor.end_memory_monitor: TEST_FACTOR_CALCULATION_001 memory delta: 0.09MB
2025-06-27 10:42 | INFO     | FactorMonitorContext.__exit__: TEST_FACTOR_CALCULATION_001 completed successfully
2025-06-27 10:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:42 | INFO     | ResMonitor.end_timer: TEST_SELECT_BY_SKEW_001 took 0.10 seconds (00:00:00)
2025-06-27 10:42 | INFO     | ResMonitor.end_timer_cpu: TEST_SELECT_BY_SKEW_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 10:42 | INFO     | ResMonitor.end_memory_monitor: TEST_SELECT_BY_SKEW_001 memory delta: 0.08MB
2025-06-27 10:42 | INFO     | FactorMonitorContext.__exit__: TEST_SELECT_BY_SKEW_001 completed successfully
2025-06-27 10:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:42 | INFO     | ResMonitor.end_timer: TEST_SELECT_BY_KURT_001 took 0.10 seconds (00:00:00)
2025-06-27 10:42 | INFO     | ResMonitor.end_timer_cpu: TEST_SELECT_BY_KURT_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 10:42 | INFO     | ResMonitor.end_memory_monitor: TEST_SELECT_BY_KURT_001 memory delta: 0.09MB
2025-06-27 10:42 | INFO     | FactorMonitorContext.__exit__: TEST_SELECT_BY_KURT_001 completed successfully
2025-06-27 10:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:42 | INFO     | ResMonitor.end_timer: TEST_SELECT_BY_SIC_001 took 0.10 seconds (00:00:00)
2025-06-27 10:42 | INFO     | ResMonitor.end_timer_cpu: TEST_SELECT_BY_SIC_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 10:42 | INFO     | ResMonitor.end_memory_monitor: TEST_SELECT_BY_SIC_001 memory delta: 0.08MB
2025-06-27 10:42 | INFO     | FactorMonitorContext.__exit__: TEST_SELECT_BY_SIC_001 completed successfully
2025-06-27 10:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:42 | INFO     | ResMonitor.end_timer: TEST_SELECT_BY_CORR_001 took 0.10 seconds (00:00:00)
2025-06-27 10:42 | INFO     | ResMonitor.end_timer_cpu: TEST_SELECT_BY_CORR_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 10:42 | INFO     | ResMonitor.end_memory_monitor: TEST_SELECT_BY_CORR_001 memory delta: 0.09MB
2025-06-27 10:42 | INFO     | FactorMonitorContext.__exit__: TEST_SELECT_BY_CORR_001 completed successfully
2025-06-27 10:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:42 | INFO     | ResMonitor.end_timer: GP_MINE_TEST.SH_test took 0.50 seconds (00:00:00)
2025-06-27 10:42 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST.SH_test CPU usage: 0.00s user, 0.00s system
2025-06-27 10:42 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST.SH_test memory delta: 0.00MB
2025-06-27 10:42 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST.SH_test completed successfully
2025-06-27 10:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:42 | INFO     | ResMonitor.end_timer: CALC_TEST.SH_test took 0.20 seconds (00:00:00)
2025-06-27 10:42 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST.SH_test CPU usage: 0.00s user, 0.00s system
2025-06-27 10:42 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST.SH_test memory delta: 0.00MB
2025-06-27 10:42 | INFO     | FactorMonitorContext.__exit__: CALC_TEST.SH_test completed successfully
2025-06-27 10:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:42 | INFO     | ResMonitor.end_timer: SKEW_TEST.SH_test took 0.10 seconds (00:00:00)
2025-06-27 10:42 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST.SH_test CPU usage: 0.00s user, 0.00s system
2025-06-27 10:42 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST.SH_test memory delta: 0.00MB
2025-06-27 10:42 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST.SH_test completed successfully
2025-06-27 10:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:42 | INFO     | ResMonitor.end_timer: CORR_TEST.SH_test took 0.10 seconds (00:00:00)
2025-06-27 10:42 | INFO     | ResMonitor.end_timer_cpu: CORR_TEST.SH_test CPU usage: 0.00s user, 0.00s system
2025-06-27 10:42 | INFO     | ResMonitor.end_memory_monitor: CORR_TEST.SH_test memory delta: 0.01MB
2025-06-27 10:42 | INFO     | FactorMonitorContext.__exit__: CORR_TEST.SH_test completed successfully
2025-06-27 10:43 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 10:46 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 11:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:01 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:01 | INFO     | ResMonitor.end_timer: test_basic took 0.10 seconds (00:00:00)
2025-06-27 12:01 | INFO     | ResMonitor.end_timer_cpu: test_basic CPU usage: 0.00s user, 0.00s system
2025-06-27 12:01 | INFO     | ResMonitor.end_memory_monitor: test_basic memory delta: 0.07MB
2025-06-27 12:01 | INFO     | MonitorContext.__exit__: test_basic completed successfully
2025-06-27 12:01 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:01 | INFO     | ResMonitor.end_timer: test_factor_monitoring took 0.10 seconds (00:00:00)
2025-06-27 12:01 | INFO     | ResMonitor.end_timer_cpu: test_factor_monitoring CPU usage: 0.00s user, 0.00s system
2025-06-27 12:01 | INFO     | ResMonitor.end_memory_monitor: test_factor_monitoring memory delta: 0.00MB
2025-06-27 12:01 | INFO     | FactorMonitorContext.__exit__: test_factor_monitoring completed successfully
2025-06-27 12:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:06 | INFO     | ResMonitor.end_timer: test_basic took 0.10 seconds (00:00:00)
2025-06-27 12:06 | INFO     | ResMonitor.end_timer_cpu: test_basic CPU usage: 0.00s user, 0.00s system
2025-06-27 12:06 | INFO     | ResMonitor.end_memory_monitor: test_basic memory delta: 0.05MB
2025-06-27 12:06 | INFO     | MonitorContext.__exit__: test_basic completed successfully
2025-06-27 12:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:06 | INFO     | ResMonitor.end_timer: test_factor_monitoring took 0.10 seconds (00:00:00)
2025-06-27 12:06 | INFO     | ResMonitor.end_timer_cpu: test_factor_monitoring CPU usage: 0.00s user, 0.00s system
2025-06-27 12:06 | INFO     | ResMonitor.end_memory_monitor: test_factor_monitoring memory delta: 0.00MB
2025-06-27 12:06 | INFO     | FactorMonitorContext.__exit__: test_factor_monitoring completed successfully
2025-06-27 12:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:06 | INFO     | ResMonitor.end_timer: F_TEST_GP_TEST_1750997170328240_001 took 0.01 seconds (00:00:00)
2025-06-27 12:06 | INFO     | ResMonitor.end_timer_cpu: F_TEST_GP_TEST_1750997170328240_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 12:06 | INFO     | ResMonitor.end_memory_monitor: F_TEST_GP_TEST_1750997170328240_001 memory delta: 0.55MB
2025-06-27 12:06 | INFO     | FactorMonitorContext.__exit__: F_TEST_GP_TEST_1750997170328240_001 completed successfully
2025-06-27 12:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:06 | INFO     | ResMonitor.end_timer: F_TEST_GP_TEST_1750997170328240_002 took 0.01 seconds (00:00:00)
2025-06-27 12:06 | INFO     | ResMonitor.end_timer_cpu: F_TEST_GP_TEST_1750997170328240_002 CPU usage: 0.00s user, 0.02s system
2025-06-27 12:06 | INFO     | ResMonitor.end_memory_monitor: F_TEST_GP_TEST_1750997170328240_002 memory delta: 0.20MB
2025-06-27 12:06 | INFO     | FactorMonitorContext.__exit__: F_TEST_GP_TEST_1750997170328240_002 completed successfully
2025-06-27 12:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:06 | INFO     | ResMonitor.end_timer: F_TEST_GP_TEST_1750997170328240_003 took 0.01 seconds (00:00:00)
2025-06-27 12:06 | INFO     | ResMonitor.end_timer_cpu: F_TEST_GP_TEST_1750997170328240_003 CPU usage: 0.00s user, 0.00s system
2025-06-27 12:06 | INFO     | ResMonitor.end_memory_monitor: F_TEST_GP_TEST_1750997170328240_003 memory delta: 0.18MB
2025-06-27 12:06 | INFO     | FactorMonitorContext.__exit__: F_TEST_GP_TEST_1750997170328240_003 completed successfully
2025-06-27 12:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:06 | INFO     | ResMonitor.end_timer: quick_test took 0.01 seconds (00:00:00)
2025-06-27 12:06 | INFO     | ResMonitor.end_timer_cpu: quick_test CPU usage: 0.00s user, 0.00s system
2025-06-27 12:06 | INFO     | ResMonitor.end_memory_monitor: quick_test memory delta: 0.11MB
2025-06-27 12:06 | INFO     | FactorMonitorContext.__exit__: quick_test completed successfully
2025-06-27 12:07 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:07 | INFO     | ResMonitor.end_timer: quick_test took 0.01 seconds (00:00:00)
2025-06-27 12:07 | INFO     | ResMonitor.end_timer_cpu: quick_test CPU usage: 0.00s user, 0.00s system
2025-06-27 12:07 | INFO     | ResMonitor.end_memory_monitor: quick_test memory delta: 0.09MB
2025-06-27 12:07 | INFO     | FactorMonitorContext.__exit__: quick_test completed successfully
2025-06-27 12:08 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:08 | INFO     | ResMonitor.end_timer: quick_test took 0.01 seconds (00:00:00)
2025-06-27 12:08 | INFO     | ResMonitor.end_timer_cpu: quick_test CPU usage: 0.00s user, 0.00s system
2025-06-27 12:08 | INFO     | ResMonitor.end_memory_monitor: quick_test memory delta: 0.08MB
2025-06-27 12:08 | INFO     | FactorMonitorContext.__exit__: quick_test completed successfully
2025-06-27 12:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:09 | INFO     | ResMonitor.end_timer: GP_MINING_TEST took 0.10 seconds (00:00:00)
2025-06-27 12:09 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_TEST CPU usage: 0.00s user, 0.00s system
2025-06-27 12:09 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_TEST memory delta: 0.07MB
2025-06-27 12:09 | INFO     | FactorMonitorContext.__exit__: GP_MINING_TEST completed successfully
2025-06-27 12:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:09 | INFO     | ResMonitor.end_timer: F_TEST_GP_TEST_20250627_120940_001 took 0.01 seconds (00:00:00)
2025-06-27 12:09 | INFO     | ResMonitor.end_timer_cpu: F_TEST_GP_TEST_20250627_120940_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 12:09 | INFO     | ResMonitor.end_memory_monitor: F_TEST_GP_TEST_20250627_120940_001 memory delta: 0.59MB
2025-06-27 12:09 | INFO     | FactorMonitorContext.__exit__: F_TEST_GP_TEST_20250627_120940_001 completed successfully
2025-06-27 12:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:09 | INFO     | ResMonitor.end_timer: F_TEST_GP_TEST_20250627_120940_002 took 0.02 seconds (00:00:00)
2025-06-27 12:09 | INFO     | ResMonitor.end_timer_cpu: F_TEST_GP_TEST_20250627_120940_002 CPU usage: 0.00s user, 0.02s system
2025-06-27 12:09 | INFO     | ResMonitor.end_memory_monitor: F_TEST_GP_TEST_20250627_120940_002 memory delta: 0.00MB
2025-06-27 12:09 | INFO     | FactorMonitorContext.__exit__: F_TEST_GP_TEST_20250627_120940_002 completed successfully
2025-06-27 12:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:09 | INFO     | ResMonitor.end_timer: F_TEST_GP_TEST_20250627_120940_003 took 0.01 seconds (00:00:00)
2025-06-27 12:09 | INFO     | ResMonitor.end_timer_cpu: F_TEST_GP_TEST_20250627_120940_003 CPU usage: 0.00s user, 0.00s system
2025-06-27 12:09 | INFO     | ResMonitor.end_memory_monitor: F_TEST_GP_TEST_20250627_120940_003 memory delta: 0.00MB
2025-06-27 12:09 | INFO     | FactorMonitorContext.__exit__: F_TEST_GP_TEST_20250627_120940_003 completed successfully
2025-06-27 12:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:10 | INFO     | ResMonitor.end_timer: GP_MINING_TEST took 0.10 seconds (00:00:00)
2025-06-27 12:10 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_TEST CPU usage: 0.02s user, 0.00s system
2025-06-27 12:10 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_TEST memory delta: 0.03MB
2025-06-27 12:10 | INFO     | FactorMonitorContext.__exit__: GP_MINING_TEST completed successfully
2025-06-27 12:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:10 | INFO     | ResMonitor.end_timer: F_TEST_GP_TEST_20250627_121038_001 took 0.00 seconds (00:00:00)
2025-06-27 12:10 | INFO     | ResMonitor.end_timer_cpu: F_TEST_GP_TEST_20250627_121038_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 12:10 | INFO     | ResMonitor.end_memory_monitor: F_TEST_GP_TEST_20250627_121038_001 memory delta: 0.69MB
2025-06-27 12:10 | INFO     | FactorMonitorContext.__exit__: F_TEST_GP_TEST_20250627_121038_001 completed successfully
2025-06-27 12:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:10 | INFO     | ResMonitor.end_timer: F_TEST_GP_TEST_20250627_121038_002 took 0.00 seconds (00:00:00)
2025-06-27 12:10 | INFO     | ResMonitor.end_timer_cpu: F_TEST_GP_TEST_20250627_121038_002 CPU usage: 0.00s user, 0.00s system
2025-06-27 12:10 | INFO     | ResMonitor.end_memory_monitor: F_TEST_GP_TEST_20250627_121038_002 memory delta: 0.35MB
2025-06-27 12:10 | INFO     | FactorMonitorContext.__exit__: F_TEST_GP_TEST_20250627_121038_002 completed successfully
2025-06-27 12:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:10 | INFO     | ResMonitor.end_timer: F_TEST_GP_TEST_20250627_121038_003 took 0.00 seconds (00:00:00)
2025-06-27 12:10 | INFO     | ResMonitor.end_timer_cpu: F_TEST_GP_TEST_20250627_121038_003 CPU usage: 0.00s user, 0.00s system
2025-06-27 12:10 | INFO     | ResMonitor.end_memory_monitor: F_TEST_GP_TEST_20250627_121038_003 memory delta: 0.26MB
2025-06-27 12:10 | INFO     | FactorMonitorContext.__exit__: F_TEST_GP_TEST_20250627_121038_003 completed successfully
2025-06-27 12:20 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:20 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 12:20 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 12:20 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 12:20 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 12:20 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 12:20 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(4): ['open', 'high', 'low', 'close']
2025-06-27 12:20 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['volume', 'amount']
2025-06-27 12:20 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 12:20 | DEBUG    | BaseObj.normalize_df_all:   标签列(2): ['label_1d', 'label_3d']
2025-06-27 12:20 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(1): 1个
2025-06-27 12:20 | INFO     | ResMonitor.end_timer: GP_MINING_GP_20250627_122058_539940 took 0.10 seconds (00:00:00)
2025-06-27 12:20 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_20250627_122058_539940 CPU usage: 0.05s user, 0.05s system
2025-06-27 12:20 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_20250627_122058_539940 memory delta: 1.69MB
2025-06-27 12:20 | ERROR    | MonitorContext.__exit__: GP_MINING_GP_20250627_122058_539940 failed with error: "None of ['symbol'] are in the columns"
2025-06-27 12:22 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:22 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 12:22 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 12:22 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 12:22 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 12:22 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 12:22 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(4): ['open', 'high', 'low', 'close']
2025-06-27 12:22 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['volume', 'amount']
2025-06-27 12:22 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 12:22 | DEBUG    | BaseObj.normalize_df_all:   标签列(2): ['label_1d', 'label_3d']
2025-06-27 12:22 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(1): 1个
2025-06-27 12:22 | INFO     | ResMonitor.end_timer: GP_MINING_GP_20250627_122253_646805 took 0.15 seconds (00:00:00)
2025-06-27 12:22 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_20250627_122253_646805 CPU usage: 0.09s user, 0.11s system
2025-06-27 12:22 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_20250627_122253_646805 memory delta: 7.05MB
2025-06-27 12:22 | ERROR    | MonitorContext.__exit__: GP_MINING_GP_20250627_122253_646805 failed with error: "None of ['symbol'] are in the columns"
2025-06-27 12:23 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 12:24 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 12:25 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:25 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 12:25 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 12:25 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 12:25 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 12:25 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 12:25 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(4): ['open', 'high', 'low', 'close']
2025-06-27 12:25 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['volume', 'amount']
2025-06-27 12:25 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 12:25 | DEBUG    | BaseObj.normalize_df_all:   标签列(2): ['label_1d', 'label_3d']
2025-06-27 12:25 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(1): 1个
2025-06-27 12:25 | INFO     | ResMonitor.end_timer: GP_MINING_GP_20250627_122555_984252 took 0.06 seconds (00:00:00)
2025-06-27 12:25 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_20250627_122555_984252 CPU usage: 0.05s user, 0.03s system
2025-06-27 12:25 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_20250627_122555_984252 memory delta: 1.38MB
2025-06-27 12:25 | ERROR    | MonitorContext.__exit__: GP_MINING_GP_20250627_122555_984252 failed with error: "None of ['symbol'] are in the columns"
2025-06-27 12:27 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all: 调试信息:
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   all_columns: ['symbol', 'datetime', 'open', 'high', 'low', 'close', 'volume', 'amount', 'label_1d', 'label_3d']
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   symbol_col: 'symbol'
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   symbol_col in all_columns: True
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   base2keep: ['open', 'close', 'high', 'low']
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   cols_base2keep: ['symbol', 'open', 'high', 'low', 'close']
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['volume', 'amount']
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   标签列(2): ['label_1d', 'label_3d']
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(1): 1个
2025-06-27 12:27 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all: 调试信息:
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   all_columns: ['symbol', 'datetime', 'open', 'high', 'low', 'close', 'volume', 'amount', 'label_1d', 'label_3d']
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   symbol_col: 'symbol'
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   symbol_col in all_columns: True
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   base2keep: ['open', 'close', 'high', 'low']
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   cols_base2keep: ['symbol', 'open', 'high', 'low', 'close']
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['volume', 'amount']
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   标签列(2): ['label_1d', 'label_3d']
2025-06-27 12:27 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(1): 1个
2025-06-27 12:28 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 12:28 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 12:28 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 12:28 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 12:28 | DEBUG    | BaseObj.normalize_df_all: 调试信息:
2025-06-27 12:28 | DEBUG    | BaseObj.normalize_df_all:   all_columns: ['symbol', 'datetime', 'open', 'high', 'low', 'close', 'volume', 'amount', 'label_1d', 'label_3d']
2025-06-27 12:28 | DEBUG    | BaseObj.normalize_df_all:   symbol_col: 'symbol'
2025-06-27 12:28 | DEBUG    | BaseObj.normalize_df_all:   symbol_col in all_columns: True
2025-06-27 12:28 | DEBUG    | BaseObj.normalize_df_all:   base2keep: ['open', 'close', 'high', 'low']
2025-06-27 12:28 | DEBUG    | BaseObj.normalize_df_all:   cols_base2keep: ['symbol', 'open', 'high', 'low', 'close']
2025-06-27 12:28 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 12:28 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-27 12:28 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['volume', 'amount']
2025-06-27 12:28 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 12:28 | DEBUG    | BaseObj.normalize_df_all:   标签列(2): ['label_1d', 'label_3d']
2025-06-27 12:28 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(1): 1个
2025-06-27 12:29 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all: 调试信息:
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all:   all_columns: ['symbol', 'datetime', 'open', 'high', 'low', 'close', 'volume', 'amount', 'label_1d', 'label_3d']
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all:   symbol_col: 'symbol'
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all:   symbol_col in all_columns: True
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all:   base2keep: ['open', 'close', 'high', 'low']
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all:   cols_base2keep: ['symbol', 'open', 'high', 'low', 'close']
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['volume', 'amount']
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all:   标签列(2): ['label_1d', 'label_3d']
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(1): 1个
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 12 (基础7+归一化2+保持0+标签2+筛选后特征1)
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 12
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-27 12:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:29 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(4): ['open', 'high', 'low', 'close']
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['volume', 'amount']
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all:   标签列(2): ['label_1d', 'label_3d']
2025-06-27 12:29 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(1): 1个
2025-06-27 12:29 | INFO     | ResMonitor.end_timer: GP_MINING_GP_20250627_122951_938705 took 0.10 seconds (00:00:00)
2025-06-27 12:29 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_20250627_122951_938705 CPU usage: 0.09s user, 0.02s system
2025-06-27 12:29 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_20250627_122951_938705 memory delta: 5.77MB
2025-06-27 12:29 | ERROR    | MonitorContext.__exit__: GP_MINING_GP_20250627_122951_938705 failed with error: "None of ['symbol'] are in the columns"
2025-06-27 12:31 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 12:31 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 12:31 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 12:31 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 12:31 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 12:31 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-27 12:31 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['volume', 'amount']
2025-06-27 12:31 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 12:31 | DEBUG    | BaseObj.normalize_df_all:   标签列(2): ['label_1d', 'label_3d']
2025-06-27 12:31 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(1): 1个
2025-06-27 12:31 | DEBUG    | BaseObj.normalize_df_all: 调试: fill_missing前 symbol列存在: True
2025-06-27 12:31 | DEBUG    | BaseObj.normalize_df_all: 调试: fill_missing前 列名: ['symbol', 'datetime', 'open', 'high', 'low', 'close', 'volume', 'amount', 'label_1d', 'label_3d']
2025-06-27 12:31 | DEBUG    | BaseObj.normalize_df_all: 调试: fill_missing后 symbol列存在: True
2025-06-27 12:31 | DEBUG    | BaseObj.normalize_df_all: 调试: fill_missing后 列名: ['datetime', 'open', 'high', 'low', 'close', 'volume', 'amount', 'label_1d', 'label_3d', 'symbol']
2025-06-27 12:31 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-27 12:31 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 12 (基础7+归一化2+保持0+标签2+筛选后特征1)
2025-06-27 12:31 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 12
2025-06-27 12:31 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-27 12:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:34 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 12:34 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 12:34 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 12:34 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 12:34 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 12:34 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-27 12:34 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['volume', 'amount']
2025-06-27 12:34 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 12:34 | DEBUG    | BaseObj.normalize_df_all:   标签列(2): ['label_1d', 'label_3d']
2025-06-27 12:34 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(1): 1个
2025-06-27 12:34 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-27 12:34 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 12 (基础7+归一化2+保持0+标签2+筛选后特征1)
2025-06-27 12:34 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 12
2025-06-27 12:34 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-27 12:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:34 | INFO     | FctsGPMiner.mine: start mining on TEST1.SH...
2025-06-27 12:34 | INFO     | ResMonitor.end_timer: GP_MINE_TEST1.SH_label_1d_R01 took 12.97 seconds (00:00:12)
2025-06-27 12:34 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST1.SH_label_1d_R01 CPU usage: 6.19s user, 5.31s system
2025-06-27 12:34 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST1.SH_label_1d_R01 memory delta: 60.19MB
2025-06-27 12:34 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST1.SH_label_1d_R01 completed successfully
2025-06-27 12:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:34 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:34 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:34 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:34 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:34 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:34 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:34 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:34 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:34 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:34 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:34 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:34 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:34 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:34 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:34 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:34 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:34 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:34 | INFO     | ResMonitor.end_timer: CALC_TEST1.SH_label_1d_R01 took 0.21 seconds (00:00:00)
2025-06-27 12:34 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST1.SH_label_1d_R01 CPU usage: 0.28s user, 0.12s system
2025-06-27 12:34 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST1.SH_label_1d_R01 memory delta: 8.06MB
2025-06-27 12:34 | INFO     | FactorMonitorContext.__exit__: CALC_TEST1.SH_label_1d_R01 completed successfully
2025-06-27 12:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:34 | INFO     | ResMonitor.end_timer: SKEW_TEST1.SH_label_1d_R01 took 0.09 seconds (00:00:00)
2025-06-27 12:34 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST1.SH_label_1d_R01 CPU usage: 0.17s user, 0.02s system
2025-06-27 12:34 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST1.SH_label_1d_R01 memory delta: 5.33MB
2025-06-27 12:34 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST1.SH_label_1d_R01 completed successfully
2025-06-27 12:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:34 | INFO     | ResMonitor.end_timer: KURT_TEST1.SH_label_1d_R01 took 0.02 seconds (00:00:00)
2025-06-27 12:34 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST1.SH_label_1d_R01 CPU usage: 0.03s user, 0.00s system
2025-06-27 12:34 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST1.SH_label_1d_R01 memory delta: -1.22MB
2025-06-27 12:34 | INFO     | FactorMonitorContext.__exit__: KURT_TEST1.SH_label_1d_R01 completed successfully
2025-06-27 12:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: METRIC_TEST1.SH_label_1d_R01 took 48.45 seconds (00:00:48)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST1.SH_label_1d_R01 CPU usage: 41.52s user, 25.97s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST1.SH_label_1d_R01 memory delta: 761.21MB
2025-06-27 12:35 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST1.SH_label_1d_R01 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FctsGPMiner.mine: start mining on TEST1.SH...
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: GP_MINE_TEST1.SH_label_1d_R02 took 2.31 seconds (00:00:02)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST1.SH_label_1d_R02 CPU usage: 0.94s user, 0.02s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST1.SH_label_1d_R02 memory delta: 8.70MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST1.SH_label_1d_R02 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: CALC_TEST1.SH_label_1d_R02 took 0.16 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST1.SH_label_1d_R02 CPU usage: 0.12s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST1.SH_label_1d_R02 memory delta: 0.24MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: CALC_TEST1.SH_label_1d_R02 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: SKEW_TEST1.SH_label_1d_R02 took 0.07 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST1.SH_label_1d_R02 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST1.SH_label_1d_R02 memory delta: 0.11MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST1.SH_label_1d_R02 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: KURT_TEST1.SH_label_1d_R02 took 0.02 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST1.SH_label_1d_R02 CPU usage: 0.02s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST1.SH_label_1d_R02 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: KURT_TEST1.SH_label_1d_R02 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: METRIC_TEST1.SH_label_1d_R02 took 0.09 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST1.SH_label_1d_R02 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST1.SH_label_1d_R02 memory delta: 0.61MB
2025-06-27 12:35 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST1.SH_label_1d_R02 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FctsGPMiner.mine: start mining on TEST1.SH...
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: GP_MINE_TEST1.SH_label_1d_R03 took 2.17 seconds (00:00:02)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST1.SH_label_1d_R03 CPU usage: 0.94s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST1.SH_label_1d_R03 memory delta: 1.20MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST1.SH_label_1d_R03 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: CALC_TEST1.SH_label_1d_R03 took 0.19 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST1.SH_label_1d_R03 CPU usage: 0.09s user, 0.05s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST1.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: CALC_TEST1.SH_label_1d_R03 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: SKEW_TEST1.SH_label_1d_R03 took 0.06 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST1.SH_label_1d_R03 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST1.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST1.SH_label_1d_R03 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: KURT_TEST1.SH_label_1d_R03 took 0.02 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST1.SH_label_1d_R03 CPU usage: 0.02s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST1.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: KURT_TEST1.SH_label_1d_R03 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: METRIC_TEST1.SH_label_1d_R03 took 0.08 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST1.SH_label_1d_R03 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST1.SH_label_1d_R03 memory delta: 0.02MB
2025-06-27 12:35 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST1.SH_label_1d_R03 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FctsGPMiner.mine: start mining on TEST1.SH...
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: GP_MINE_TEST1.SH_label_3d_R01 took 2.11 seconds (00:00:02)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST1.SH_label_3d_R01 CPU usage: 0.92s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST1.SH_label_3d_R01 memory delta: 0.04MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST1.SH_label_3d_R01 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: CALC_TEST1.SH_label_3d_R01 took 0.14 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST1.SH_label_3d_R01 CPU usage: 0.11s user, 0.02s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST1.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: CALC_TEST1.SH_label_3d_R01 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: SKEW_TEST1.SH_label_3d_R01 took 0.06 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST1.SH_label_3d_R01 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST1.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST1.SH_label_3d_R01 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: KURT_TEST1.SH_label_3d_R01 took 0.02 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST1.SH_label_3d_R01 CPU usage: 0.02s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST1.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: KURT_TEST1.SH_label_3d_R01 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: METRIC_TEST1.SH_label_3d_R01 took 0.08 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST1.SH_label_3d_R01 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST1.SH_label_3d_R01 memory delta: 0.01MB
2025-06-27 12:35 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST1.SH_label_3d_R01 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FctsGPMiner.mine: start mining on TEST1.SH...
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: GP_MINE_TEST1.SH_label_3d_R02 took 2.19 seconds (00:00:02)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST1.SH_label_3d_R02 CPU usage: 0.94s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST1.SH_label_3d_R02 memory delta: 0.07MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST1.SH_label_3d_R02 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: CALC_TEST1.SH_label_3d_R02 took 0.14 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST1.SH_label_3d_R02 CPU usage: 0.09s user, 0.03s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST1.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: CALC_TEST1.SH_label_3d_R02 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: SKEW_TEST1.SH_label_3d_R02 took 0.06 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST1.SH_label_3d_R02 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST1.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST1.SH_label_3d_R02 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: KURT_TEST1.SH_label_3d_R02 took 0.02 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST1.SH_label_3d_R02 CPU usage: 0.02s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST1.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: KURT_TEST1.SH_label_3d_R02 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: METRIC_TEST1.SH_label_3d_R02 took 0.08 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST1.SH_label_3d_R02 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST1.SH_label_3d_R02 memory delta: 0.01MB
2025-06-27 12:35 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST1.SH_label_3d_R02 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FctsGPMiner.mine: start mining on TEST1.SH...
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: GP_MINE_TEST1.SH_label_3d_R03 took 2.12 seconds (00:00:02)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST1.SH_label_3d_R03 CPU usage: 0.89s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST1.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST1.SH_label_3d_R03 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: CALC_TEST1.SH_label_3d_R03 took 0.15 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST1.SH_label_3d_R03 CPU usage: 0.11s user, 0.02s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST1.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: CALC_TEST1.SH_label_3d_R03 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: SKEW_TEST1.SH_label_3d_R03 took 0.05 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST1.SH_label_3d_R03 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST1.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST1.SH_label_3d_R03 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: KURT_TEST1.SH_label_3d_R03 took 0.02 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST1.SH_label_3d_R03 CPU usage: 0.02s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST1.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: KURT_TEST1.SH_label_3d_R03 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: METRIC_TEST1.SH_label_3d_R03 took 0.08 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST1.SH_label_3d_R03 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST1.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:35 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST1.SH_label_3d_R03 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FctsGPMiner.mine: start mining on TEST2.SH...
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: GP_MINE_TEST2.SH_label_1d_R01 took 2.28 seconds (00:00:02)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST2.SH_label_1d_R01 CPU usage: 0.92s user, 0.02s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST2.SH_label_1d_R01 memory delta: 0.07MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST2.SH_label_1d_R01 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: CALC_TEST2.SH_label_1d_R01 took 0.15 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST2.SH_label_1d_R01 CPU usage: 0.12s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST2.SH_label_1d_R01 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: CALC_TEST2.SH_label_1d_R01 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: SKEW_TEST2.SH_label_1d_R01 took 0.06 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST2.SH_label_1d_R01 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST2.SH_label_1d_R01 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST2.SH_label_1d_R01 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: KURT_TEST2.SH_label_1d_R01 took 0.03 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST2.SH_label_1d_R01 CPU usage: 0.00s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST2.SH_label_1d_R01 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: KURT_TEST2.SH_label_1d_R01 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: METRIC_TEST2.SH_label_1d_R01 took 0.11 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST2.SH_label_1d_R01 CPU usage: 0.11s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST2.SH_label_1d_R01 memory delta: 0.00MB
2025-06-27 12:35 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST2.SH_label_1d_R01 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FctsGPMiner.mine: start mining on TEST2.SH...
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: GP_MINE_TEST2.SH_label_1d_R02 took 2.18 seconds (00:00:02)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST2.SH_label_1d_R02 CPU usage: 0.91s user, 0.02s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST2.SH_label_1d_R02 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST2.SH_label_1d_R02 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: CALC_TEST2.SH_label_1d_R02 took 0.16 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST2.SH_label_1d_R02 CPU usage: 0.11s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST2.SH_label_1d_R02 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: CALC_TEST2.SH_label_1d_R02 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: SKEW_TEST2.SH_label_1d_R02 took 0.06 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST2.SH_label_1d_R02 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST2.SH_label_1d_R02 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST2.SH_label_1d_R02 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: KURT_TEST2.SH_label_1d_R02 took 0.03 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST2.SH_label_1d_R02 CPU usage: 0.02s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST2.SH_label_1d_R02 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: KURT_TEST2.SH_label_1d_R02 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: METRIC_TEST2.SH_label_1d_R02 took 0.11 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST2.SH_label_1d_R02 CPU usage: 0.11s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST2.SH_label_1d_R02 memory delta: 0.00MB
2025-06-27 12:35 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST2.SH_label_1d_R02 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FctsGPMiner.mine: start mining on TEST2.SH...
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: GP_MINE_TEST2.SH_label_1d_R03 took 2.18 seconds (00:00:02)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST2.SH_label_1d_R03 CPU usage: 0.91s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST2.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST2.SH_label_1d_R03 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: CALC_TEST2.SH_label_1d_R03 took 0.76 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST2.SH_label_1d_R03 CPU usage: 0.11s user, 0.02s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST2.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: CALC_TEST2.SH_label_1d_R03 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: SKEW_TEST2.SH_label_1d_R03 took 0.09 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST2.SH_label_1d_R03 CPU usage: 0.05s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST2.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST2.SH_label_1d_R03 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: KURT_TEST2.SH_label_1d_R03 took 0.02 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST2.SH_label_1d_R03 CPU usage: 0.03s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST2.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: KURT_TEST2.SH_label_1d_R03 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: METRIC_TEST2.SH_label_1d_R03 took 0.14 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST2.SH_label_1d_R03 CPU usage: 0.14s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST2.SH_label_1d_R03 memory delta: 0.01MB
2025-06-27 12:35 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST2.SH_label_1d_R03 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FctsGPMiner.mine: start mining on TEST2.SH...
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: GP_MINE_TEST2.SH_label_3d_R01 took 2.28 seconds (00:00:02)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST2.SH_label_3d_R01 CPU usage: 0.92s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST2.SH_label_3d_R01 memory delta: 0.09MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST2.SH_label_3d_R01 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: CALC_TEST2.SH_label_3d_R01 took 0.50 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST2.SH_label_3d_R01 CPU usage: 0.12s user, 0.03s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST2.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: CALC_TEST2.SH_label_3d_R01 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: SKEW_TEST2.SH_label_3d_R01 took 0.06 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST2.SH_label_3d_R01 CPU usage: 0.05s user, 0.02s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST2.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST2.SH_label_3d_R01 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: KURT_TEST2.SH_label_3d_R01 took 0.04 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST2.SH_label_3d_R01 CPU usage: 0.05s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST2.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: KURT_TEST2.SH_label_3d_R01 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: METRIC_TEST2.SH_label_3d_R01 took 0.12 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST2.SH_label_3d_R01 CPU usage: 0.12s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST2.SH_label_3d_R01 memory delta: 0.01MB
2025-06-27 12:35 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST2.SH_label_3d_R01 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FctsGPMiner.mine: start mining on TEST2.SH...
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: GP_MINE_TEST2.SH_label_3d_R02 took 2.47 seconds (00:00:02)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST2.SH_label_3d_R02 CPU usage: 1.12s user, 0.02s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST2.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST2.SH_label_3d_R02 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: CALC_TEST2.SH_label_3d_R02 took 0.16 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST2.SH_label_3d_R02 CPU usage: 0.09s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST2.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: CALC_TEST2.SH_label_3d_R02 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: SKEW_TEST2.SH_label_3d_R02 took 0.06 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST2.SH_label_3d_R02 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST2.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST2.SH_label_3d_R02 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: KURT_TEST2.SH_label_3d_R02 took 0.03 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST2.SH_label_3d_R02 CPU usage: 0.03s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST2.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: KURT_TEST2.SH_label_3d_R02 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: METRIC_TEST2.SH_label_3d_R02 took 0.12 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST2.SH_label_3d_R02 CPU usage: 0.12s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST2.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:35 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST2.SH_label_3d_R02 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FctsGPMiner.mine: start mining on TEST2.SH...
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: GP_MINE_TEST2.SH_label_3d_R03 took 2.19 seconds (00:00:02)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST2.SH_label_3d_R03 CPU usage: 0.86s user, 0.02s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST2.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST2.SH_label_3d_R03 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:35 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:35 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: CALC_TEST2.SH_label_3d_R03 took 0.16 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST2.SH_label_3d_R03 CPU usage: 0.12s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST2.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: CALC_TEST2.SH_label_3d_R03 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: SKEW_TEST2.SH_label_3d_R03 took 0.06 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST2.SH_label_3d_R03 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST2.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST2.SH_label_3d_R03 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: KURT_TEST2.SH_label_3d_R03 took 0.03 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST2.SH_label_3d_R03 CPU usage: 0.03s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST2.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:35 | INFO     | FactorMonitorContext.__exit__: KURT_TEST2.SH_label_3d_R03 completed successfully
2025-06-27 12:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: METRIC_TEST2.SH_label_3d_R03 took 0.10 seconds (00:00:00)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST2.SH_label_3d_R03 CPU usage: 0.09s user, 0.00s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST2.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:35 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST2.SH_label_3d_R03 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:35 | INFO     | ResMonitor.end_timer: GP_MINING_GP_20250627_123417_876686 took 92.29 seconds (00:01:32)
2025-06-27 12:35 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_20250627_123417_876686 CPU usage: 62.22s user, 31.98s system
2025-06-27 12:35 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_20250627_123417_876686 memory delta: 863.16MB
2025-06-27 12:35 | INFO     | MonitorContext.__exit__: GP_MINING_GP_20250627_123417_876686 completed successfully
2025-06-27 12:37 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:37 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 12:37 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 12:37 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 12:37 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 12:37 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 12:37 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-27 12:37 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['volume', 'amount']
2025-06-27 12:37 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 12:37 | DEBUG    | BaseObj.normalize_df_all:   标签列(2): ['label_1d', 'label_3d']
2025-06-27 12:37 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(1): 1个
2025-06-27 12:37 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-27 12:37 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 12 (基础7+归一化2+保持0+标签2+筛选后特征1)
2025-06-27 12:37 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 12
2025-06-27 12:37 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-27 12:37 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:37 | INFO     | FctsGPMiner.mine: start mining on TEST1.SH...
2025-06-27 12:37 | INFO     | ResMonitor.end_timer: GP_MINE_TEST1.SH_label_1d_R01 took 8.73 seconds (00:00:08)
2025-06-27 12:37 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST1.SH_label_1d_R01 CPU usage: 5.27s user, 3.67s system
2025-06-27 12:37 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST1.SH_label_1d_R01 memory delta: 321.64MB
2025-06-27 12:37 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST1.SH_label_1d_R01 completed successfully
2025-06-27 12:37 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:37 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:37 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:37 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:37 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:37 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:37 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:37 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:37 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:37 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:37 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:37 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:37 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:37 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:37 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:37 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:37 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:37 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:37 | INFO     | ResMonitor.end_timer: CALC_TEST1.SH_label_1d_R01 took 0.23 seconds (00:00:00)
2025-06-27 12:37 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST1.SH_label_1d_R01 CPU usage: 0.28s user, 0.12s system
2025-06-27 12:37 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST1.SH_label_1d_R01 memory delta: 8.20MB
2025-06-27 12:37 | INFO     | FactorMonitorContext.__exit__: CALC_TEST1.SH_label_1d_R01 completed successfully
2025-06-27 12:37 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:37 | INFO     | ResMonitor.end_timer: SKEW_TEST1.SH_label_1d_R01 took 0.08 seconds (00:00:00)
2025-06-27 12:37 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST1.SH_label_1d_R01 CPU usage: 0.11s user, 0.05s system
2025-06-27 12:37 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST1.SH_label_1d_R01 memory delta: 3.33MB
2025-06-27 12:37 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST1.SH_label_1d_R01 completed successfully
2025-06-27 12:37 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:37 | INFO     | ResMonitor.end_timer: KURT_TEST1.SH_label_1d_R01 took 0.02 seconds (00:00:00)
2025-06-27 12:37 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST1.SH_label_1d_R01 CPU usage: 0.05s user, 0.02s system
2025-06-27 12:37 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST1.SH_label_1d_R01 memory delta: -0.85MB
2025-06-27 12:37 | INFO     | FactorMonitorContext.__exit__: KURT_TEST1.SH_label_1d_R01 completed successfully
2025-06-27 12:37 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: METRIC_TEST1.SH_label_1d_R01 took 43.32 seconds (00:00:43)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST1.SH_label_1d_R01 CPU usage: 44.23s user, 22.94s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST1.SH_label_1d_R01 memory delta: 1102.27MB
2025-06-27 12:38 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST1.SH_label_1d_R01 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | FctsGPMiner.mine: start mining on TEST1.SH...
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: GP_MINE_TEST1.SH_label_1d_R02 took 2.13 seconds (00:00:02)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST1.SH_label_1d_R02 CPU usage: 0.89s user, 0.03s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST1.SH_label_1d_R02 memory delta: 4.71MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST1.SH_label_1d_R02 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:38 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: CALC_TEST1.SH_label_1d_R02 took 0.20 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST1.SH_label_1d_R02 CPU usage: 0.16s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST1.SH_label_1d_R02 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: CALC_TEST1.SH_label_1d_R02 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: SKEW_TEST1.SH_label_1d_R02 took 0.07 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST1.SH_label_1d_R02 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST1.SH_label_1d_R02 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST1.SH_label_1d_R02 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: KURT_TEST1.SH_label_1d_R02 took 0.02 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST1.SH_label_1d_R02 CPU usage: 0.02s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST1.SH_label_1d_R02 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: KURT_TEST1.SH_label_1d_R02 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: METRIC_TEST1.SH_label_1d_R02 took 0.09 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST1.SH_label_1d_R02 CPU usage: 0.09s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST1.SH_label_1d_R02 memory delta: 0.02MB
2025-06-27 12:38 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST1.SH_label_1d_R02 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | FctsGPMiner.mine: start mining on TEST1.SH...
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: GP_MINE_TEST1.SH_label_1d_R03 took 2.35 seconds (00:00:02)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST1.SH_label_1d_R03 CPU usage: 0.97s user, 0.03s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST1.SH_label_1d_R03 memory delta: -16.70MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST1.SH_label_1d_R03 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:38 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: CALC_TEST1.SH_label_1d_R03 took 0.15 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST1.SH_label_1d_R03 CPU usage: 0.11s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST1.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: CALC_TEST1.SH_label_1d_R03 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: SKEW_TEST1.SH_label_1d_R03 took 0.06 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST1.SH_label_1d_R03 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST1.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST1.SH_label_1d_R03 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: KURT_TEST1.SH_label_1d_R03 took 0.02 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST1.SH_label_1d_R03 CPU usage: 0.02s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST1.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: KURT_TEST1.SH_label_1d_R03 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: METRIC_TEST1.SH_label_1d_R03 took 0.08 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST1.SH_label_1d_R03 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST1.SH_label_1d_R03 memory delta: 0.02MB
2025-06-27 12:38 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST1.SH_label_1d_R03 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | FctsGPMiner.mine: start mining on TEST1.SH...
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: GP_MINE_TEST1.SH_label_3d_R01 took 2.37 seconds (00:00:02)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST1.SH_label_3d_R01 CPU usage: 0.91s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST1.SH_label_3d_R01 memory delta: 0.02MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST1.SH_label_3d_R01 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:38 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: CALC_TEST1.SH_label_3d_R01 took 0.15 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST1.SH_label_3d_R01 CPU usage: 0.09s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST1.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: CALC_TEST1.SH_label_3d_R01 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: SKEW_TEST1.SH_label_3d_R01 took 0.06 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST1.SH_label_3d_R01 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST1.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST1.SH_label_3d_R01 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: KURT_TEST1.SH_label_3d_R01 took 0.02 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST1.SH_label_3d_R01 CPU usage: 0.03s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST1.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: KURT_TEST1.SH_label_3d_R01 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: METRIC_TEST1.SH_label_3d_R01 took 0.07 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST1.SH_label_3d_R01 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST1.SH_label_3d_R01 memory delta: 0.01MB
2025-06-27 12:38 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST1.SH_label_3d_R01 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | FctsGPMiner.mine: start mining on TEST1.SH...
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: GP_MINE_TEST1.SH_label_3d_R02 took 2.15 seconds (00:00:02)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST1.SH_label_3d_R02 CPU usage: 0.92s user, 0.02s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST1.SH_label_3d_R02 memory delta: 0.90MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST1.SH_label_3d_R02 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:38 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: CALC_TEST1.SH_label_3d_R02 took 0.15 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST1.SH_label_3d_R02 CPU usage: 0.14s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST1.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: CALC_TEST1.SH_label_3d_R02 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: SKEW_TEST1.SH_label_3d_R02 took 0.06 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST1.SH_label_3d_R02 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST1.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST1.SH_label_3d_R02 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: KURT_TEST1.SH_label_3d_R02 took 0.02 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST1.SH_label_3d_R02 CPU usage: 0.02s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST1.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: KURT_TEST1.SH_label_3d_R02 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: METRIC_TEST1.SH_label_3d_R02 took 0.08 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST1.SH_label_3d_R02 CPU usage: 0.12s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST1.SH_label_3d_R02 memory delta: 0.02MB
2025-06-27 12:38 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST1.SH_label_3d_R02 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | FctsGPMiner.mine: start mining on TEST1.SH...
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: GP_MINE_TEST1.SH_label_3d_R03 took 2.26 seconds (00:00:02)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST1.SH_label_3d_R03 CPU usage: 0.97s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST1.SH_label_3d_R03 memory delta: 0.02MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST1.SH_label_3d_R03 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:38 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: CALC_TEST1.SH_label_3d_R03 took 0.15 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST1.SH_label_3d_R03 CPU usage: 0.11s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST1.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: CALC_TEST1.SH_label_3d_R03 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: SKEW_TEST1.SH_label_3d_R03 took 0.06 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST1.SH_label_3d_R03 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST1.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST1.SH_label_3d_R03 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: KURT_TEST1.SH_label_3d_R03 took 0.02 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST1.SH_label_3d_R03 CPU usage: 0.02s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST1.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: KURT_TEST1.SH_label_3d_R03 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: METRIC_TEST1.SH_label_3d_R03 took 0.08 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST1.SH_label_3d_R03 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST1.SH_label_3d_R03 memory delta: 0.02MB
2025-06-27 12:38 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST1.SH_label_3d_R03 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | FctsGPMiner.mine: start mining on TEST2.SH...
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: GP_MINE_TEST2.SH_label_1d_R01 took 2.27 seconds (00:00:02)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST2.SH_label_1d_R01 CPU usage: 0.88s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST2.SH_label_1d_R01 memory delta: 0.04MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST2.SH_label_1d_R01 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:38 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: CALC_TEST2.SH_label_1d_R01 took 0.15 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST2.SH_label_1d_R01 CPU usage: 0.14s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST2.SH_label_1d_R01 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: CALC_TEST2.SH_label_1d_R01 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: SKEW_TEST2.SH_label_1d_R01 took 0.06 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST2.SH_label_1d_R01 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST2.SH_label_1d_R01 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST2.SH_label_1d_R01 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: KURT_TEST2.SH_label_1d_R01 took 0.03 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST2.SH_label_1d_R01 CPU usage: 0.03s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST2.SH_label_1d_R01 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: KURT_TEST2.SH_label_1d_R01 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: METRIC_TEST2.SH_label_1d_R01 took 0.11 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST2.SH_label_1d_R01 CPU usage: 0.11s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST2.SH_label_1d_R01 memory delta: 0.01MB
2025-06-27 12:38 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST2.SH_label_1d_R01 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | FctsGPMiner.mine: start mining on TEST2.SH...
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: GP_MINE_TEST2.SH_label_1d_R02 took 2.50 seconds (00:00:02)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST2.SH_label_1d_R02 CPU usage: 0.97s user, 0.05s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST2.SH_label_1d_R02 memory delta: -71.22MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST2.SH_label_1d_R02 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:38 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: CALC_TEST2.SH_label_1d_R02 took 0.14 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST2.SH_label_1d_R02 CPU usage: 0.05s user, 0.02s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST2.SH_label_1d_R02 memory delta: 0.01MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: CALC_TEST2.SH_label_1d_R02 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: SKEW_TEST2.SH_label_1d_R02 took 0.06 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST2.SH_label_1d_R02 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST2.SH_label_1d_R02 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST2.SH_label_1d_R02 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: KURT_TEST2.SH_label_1d_R02 took 0.03 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST2.SH_label_1d_R02 CPU usage: 0.03s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST2.SH_label_1d_R02 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: KURT_TEST2.SH_label_1d_R02 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: METRIC_TEST2.SH_label_1d_R02 took 0.11 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST2.SH_label_1d_R02 CPU usage: 0.11s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST2.SH_label_1d_R02 memory delta: 0.01MB
2025-06-27 12:38 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST2.SH_label_1d_R02 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | FctsGPMiner.mine: start mining on TEST2.SH...
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: GP_MINE_TEST2.SH_label_1d_R03 took 2.22 seconds (00:00:02)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST2.SH_label_1d_R03 CPU usage: 0.83s user, 0.03s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST2.SH_label_1d_R03 memory delta: 0.01MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST2.SH_label_1d_R03 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:38 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:38 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: CALC_TEST2.SH_label_1d_R03 took 0.15 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST2.SH_label_1d_R03 CPU usage: 0.06s user, 0.03s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST2.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: CALC_TEST2.SH_label_1d_R03 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: SKEW_TEST2.SH_label_1d_R03 took 0.06 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST2.SH_label_1d_R03 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST2.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST2.SH_label_1d_R03 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: KURT_TEST2.SH_label_1d_R03 took 0.03 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST2.SH_label_1d_R03 CPU usage: 0.03s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST2.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:38 | INFO     | FactorMonitorContext.__exit__: KURT_TEST2.SH_label_1d_R03 completed successfully
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | ResMonitor.end_timer: METRIC_TEST2.SH_label_1d_R03 took 0.12 seconds (00:00:00)
2025-06-27 12:38 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST2.SH_label_1d_R03 CPU usage: 0.12s user, 0.00s system
2025-06-27 12:38 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST2.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:38 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST2.SH_label_1d_R03 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:38 | INFO     | FctsGPMiner.mine: start mining on TEST2.SH...
2025-06-27 12:39 | INFO     | ResMonitor.end_timer: GP_MINE_TEST2.SH_label_3d_R01 took 2.44 seconds (00:00:02)
2025-06-27 12:39 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST2.SH_label_3d_R01 CPU usage: 0.92s user, 0.02s system
2025-06-27 12:39 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST2.SH_label_3d_R01 memory delta: 0.01MB
2025-06-27 12:39 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST2.SH_label_3d_R01 completed successfully
2025-06-27 12:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:39 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:39 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:39 | INFO     | ResMonitor.end_timer: CALC_TEST2.SH_label_3d_R01 took 0.15 seconds (00:00:00)
2025-06-27 12:39 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST2.SH_label_3d_R01 CPU usage: 0.14s user, 0.02s system
2025-06-27 12:39 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST2.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:39 | INFO     | FactorMonitorContext.__exit__: CALC_TEST2.SH_label_3d_R01 completed successfully
2025-06-27 12:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:39 | INFO     | ResMonitor.end_timer: SKEW_TEST2.SH_label_3d_R01 took 0.06 seconds (00:00:00)
2025-06-27 12:39 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST2.SH_label_3d_R01 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:39 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST2.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:39 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST2.SH_label_3d_R01 completed successfully
2025-06-27 12:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:39 | INFO     | ResMonitor.end_timer: KURT_TEST2.SH_label_3d_R01 took 0.03 seconds (00:00:00)
2025-06-27 12:39 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST2.SH_label_3d_R01 CPU usage: 0.03s user, 0.00s system
2025-06-27 12:39 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST2.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:39 | INFO     | FactorMonitorContext.__exit__: KURT_TEST2.SH_label_3d_R01 completed successfully
2025-06-27 12:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:39 | INFO     | ResMonitor.end_timer: METRIC_TEST2.SH_label_3d_R01 took 0.11 seconds (00:00:00)
2025-06-27 12:39 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST2.SH_label_3d_R01 CPU usage: 0.09s user, 0.00s system
2025-06-27 12:39 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST2.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:39 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST2.SH_label_3d_R01 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:39 | INFO     | FctsGPMiner.mine: start mining on TEST2.SH...
2025-06-27 12:39 | INFO     | ResMonitor.end_timer: GP_MINE_TEST2.SH_label_3d_R02 took 2.46 seconds (00:00:02)
2025-06-27 12:39 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST2.SH_label_3d_R02 CPU usage: 1.16s user, 0.00s system
2025-06-27 12:39 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST2.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:39 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST2.SH_label_3d_R02 completed successfully
2025-06-27 12:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:39 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:39 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:39 | INFO     | ResMonitor.end_timer: CALC_TEST2.SH_label_3d_R02 took 0.15 seconds (00:00:00)
2025-06-27 12:39 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST2.SH_label_3d_R02 CPU usage: 0.11s user, 0.02s system
2025-06-27 12:39 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST2.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:39 | INFO     | FactorMonitorContext.__exit__: CALC_TEST2.SH_label_3d_R02 completed successfully
2025-06-27 12:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:39 | INFO     | ResMonitor.end_timer: SKEW_TEST2.SH_label_3d_R02 took 0.08 seconds (00:00:00)
2025-06-27 12:39 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST2.SH_label_3d_R02 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:39 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST2.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:39 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST2.SH_label_3d_R02 completed successfully
2025-06-27 12:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:39 | INFO     | ResMonitor.end_timer: KURT_TEST2.SH_label_3d_R02 took 0.05 seconds (00:00:00)
2025-06-27 12:39 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST2.SH_label_3d_R02 CPU usage: 0.05s user, 0.00s system
2025-06-27 12:39 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST2.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:39 | INFO     | FactorMonitorContext.__exit__: KURT_TEST2.SH_label_3d_R02 completed successfully
2025-06-27 12:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:39 | INFO     | ResMonitor.end_timer: METRIC_TEST2.SH_label_3d_R02 took 0.11 seconds (00:00:00)
2025-06-27 12:39 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST2.SH_label_3d_R02 CPU usage: 0.11s user, 0.02s system
2025-06-27 12:39 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST2.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:39 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST2.SH_label_3d_R02 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:39 | INFO     | FctsGPMiner.mine: start mining on TEST2.SH...
2025-06-27 12:39 | INFO     | ResMonitor.end_timer: GP_MINE_TEST2.SH_label_3d_R03 took 2.24 seconds (00:00:02)
2025-06-27 12:39 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST2.SH_label_3d_R03 CPU usage: 0.91s user, 0.05s system
2025-06-27 12:39 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST2.SH_label_3d_R03 memory delta: -0.00MB
2025-06-27 12:39 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST2.SH_label_3d_R03 completed successfully
2025-06-27 12:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:39 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:39 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:39 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:39 | INFO     | ResMonitor.end_timer: CALC_TEST2.SH_label_3d_R03 took 0.15 seconds (00:00:00)
2025-06-27 12:39 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST2.SH_label_3d_R03 CPU usage: 0.12s user, 0.00s system
2025-06-27 12:39 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST2.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:39 | INFO     | FactorMonitorContext.__exit__: CALC_TEST2.SH_label_3d_R03 completed successfully
2025-06-27 12:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:39 | INFO     | ResMonitor.end_timer: SKEW_TEST2.SH_label_3d_R03 took 0.06 seconds (00:00:00)
2025-06-27 12:39 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST2.SH_label_3d_R03 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:39 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST2.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:39 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST2.SH_label_3d_R03 completed successfully
2025-06-27 12:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:39 | INFO     | ResMonitor.end_timer: KURT_TEST2.SH_label_3d_R03 took 0.03 seconds (00:00:00)
2025-06-27 12:39 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST2.SH_label_3d_R03 CPU usage: 0.03s user, 0.00s system
2025-06-27 12:39 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST2.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:39 | INFO     | FactorMonitorContext.__exit__: KURT_TEST2.SH_label_3d_R03 completed successfully
2025-06-27 12:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:39 | INFO     | ResMonitor.end_timer: METRIC_TEST2.SH_label_3d_R03 took 0.11 seconds (00:00:00)
2025-06-27 12:39 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST2.SH_label_3d_R03 CPU usage: 0.09s user, 0.00s system
2025-06-27 12:39 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST2.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:39 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST2.SH_label_3d_R03 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:39 | INFO     | ResMonitor.end_timer: GP_MINING_GP_20250627_123744_955497 took 82.67 seconds (00:01:22)
2025-06-27 12:39 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_20250627_123744_955497 CPU usage: 64.06s user, 27.52s system
2025-06-27 12:39 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_20250627_123744_955497 memory delta: 1365.59MB
2025-06-27 12:39 | INFO     | MonitorContext.__exit__: GP_MINING_GP_20250627_123744_955497 completed successfully
2025-06-27 12:40 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:40 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 12:40 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 12:40 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 12:40 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 12:40 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 12:40 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-27 12:40 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['volume', 'amount']
2025-06-27 12:40 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 12:40 | DEBUG    | BaseObj.normalize_df_all:   标签列(2): ['label_1d', 'label_3d']
2025-06-27 12:40 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(1): 1个
2025-06-27 12:40 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-27 12:40 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 12 (基础7+归一化2+保持0+标签2+筛选后特征1)
2025-06-27 12:40 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 12
2025-06-27 12:40 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-27 12:40 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:40 | INFO     | FctsGPMiner.mine: start mining on TEST1.SH...
2025-06-27 12:40 | INFO     | ResMonitor.end_timer: GP_MINE_TEST1.SH_label_1d_R01 took 8.73 seconds (00:00:08)
2025-06-27 12:40 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST1.SH_label_1d_R01 CPU usage: 4.62s user, 3.72s system
2025-06-27 12:40 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST1.SH_label_1d_R01 memory delta: 319.06MB
2025-06-27 12:40 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST1.SH_label_1d_R01 completed successfully
2025-06-27 12:40 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:40 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:40 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:40 | INFO     | ResMonitor.end_timer: CALC_TEST1.SH_label_1d_R01 took 0.21 seconds (00:00:00)
2025-06-27 12:40 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST1.SH_label_1d_R01 CPU usage: 0.19s user, 0.08s system
2025-06-27 12:40 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST1.SH_label_1d_R01 memory delta: 8.38MB
2025-06-27 12:40 | INFO     | FactorMonitorContext.__exit__: CALC_TEST1.SH_label_1d_R01 completed successfully
2025-06-27 12:40 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:40 | INFO     | ResMonitor.end_timer: SKEW_TEST1.SH_label_1d_R01 took 0.10 seconds (00:00:00)
2025-06-27 12:40 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST1.SH_label_1d_R01 CPU usage: 0.14s user, 0.03s system
2025-06-27 12:40 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST1.SH_label_1d_R01 memory delta: 3.19MB
2025-06-27 12:40 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST1.SH_label_1d_R01 completed successfully
2025-06-27 12:40 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:40 | INFO     | ResMonitor.end_timer: KURT_TEST1.SH_label_1d_R01 took 0.04 seconds (00:00:00)
2025-06-27 12:40 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST1.SH_label_1d_R01 CPU usage: 0.05s user, 0.00s system
2025-06-27 12:40 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST1.SH_label_1d_R01 memory delta: 2.18MB
2025-06-27 12:40 | INFO     | FactorMonitorContext.__exit__: KURT_TEST1.SH_label_1d_R01 completed successfully
2025-06-27 12:40 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:40 | INFO     | ResMonitor.end_timer: METRIC_TEST1.SH_label_1d_R01 took 41.73 seconds (00:00:41)
2025-06-27 12:40 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST1.SH_label_1d_R01 CPU usage: 41.89s user, 22.86s system
2025-06-27 12:40 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST1.SH_label_1d_R01 memory delta: 1194.77MB
2025-06-27 12:40 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST1.SH_label_1d_R01 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:40 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:40 | INFO     | FctsGPMiner.mine: start mining on TEST1.SH...
2025-06-27 12:40 | INFO     | ResMonitor.end_timer: GP_MINE_TEST1.SH_label_1d_R02 took 2.11 seconds (00:00:02)
2025-06-27 12:40 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST1.SH_label_1d_R02 CPU usage: 0.86s user, 0.02s system
2025-06-27 12:40 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST1.SH_label_1d_R02 memory delta: 3.82MB
2025-06-27 12:40 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST1.SH_label_1d_R02 completed successfully
2025-06-27 12:40 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:40 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:40 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:40 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:40 | INFO     | ResMonitor.end_timer: CALC_TEST1.SH_label_1d_R02 took 0.17 seconds (00:00:00)
2025-06-27 12:40 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST1.SH_label_1d_R02 CPU usage: 0.11s user, 0.02s system
2025-06-27 12:40 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST1.SH_label_1d_R02 memory delta: 0.03MB
2025-06-27 12:40 | INFO     | FactorMonitorContext.__exit__: CALC_TEST1.SH_label_1d_R02 completed successfully
2025-06-27 12:40 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:40 | INFO     | ResMonitor.end_timer: SKEW_TEST1.SH_label_1d_R02 took 0.06 seconds (00:00:00)
2025-06-27 12:40 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST1.SH_label_1d_R02 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:40 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST1.SH_label_1d_R02 memory delta: 0.01MB
2025-06-27 12:40 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST1.SH_label_1d_R02 completed successfully
2025-06-27 12:40 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:40 | INFO     | ResMonitor.end_timer: KURT_TEST1.SH_label_1d_R02 took 0.02 seconds (00:00:00)
2025-06-27 12:40 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST1.SH_label_1d_R02 CPU usage: 0.02s user, 0.00s system
2025-06-27 12:40 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST1.SH_label_1d_R02 memory delta: 0.00MB
2025-06-27 12:40 | INFO     | FactorMonitorContext.__exit__: KURT_TEST1.SH_label_1d_R02 completed successfully
2025-06-27 12:40 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:40 | INFO     | ResMonitor.end_timer: METRIC_TEST1.SH_label_1d_R02 took 0.07 seconds (00:00:00)
2025-06-27 12:40 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST1.SH_label_1d_R02 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:40 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST1.SH_label_1d_R02 memory delta: 0.02MB
2025-06-27 12:40 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST1.SH_label_1d_R02 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:40 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:40 | INFO     | FctsGPMiner.mine: start mining on TEST1.SH...
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: GP_MINE_TEST1.SH_label_1d_R03 took 2.28 seconds (00:00:02)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST1.SH_label_1d_R03 CPU usage: 0.88s user, 0.03s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST1.SH_label_1d_R03 memory delta: 0.82MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST1.SH_label_1d_R03 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: CALC_TEST1.SH_label_1d_R03 took 0.16 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST1.SH_label_1d_R03 CPU usage: 0.12s user, 0.02s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST1.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: CALC_TEST1.SH_label_1d_R03 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: SKEW_TEST1.SH_label_1d_R03 took 0.07 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST1.SH_label_1d_R03 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST1.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST1.SH_label_1d_R03 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: KURT_TEST1.SH_label_1d_R03 took 0.02 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST1.SH_label_1d_R03 CPU usage: 0.03s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST1.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: KURT_TEST1.SH_label_1d_R03 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: METRIC_TEST1.SH_label_1d_R03 took 0.08 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST1.SH_label_1d_R03 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST1.SH_label_1d_R03 memory delta: 0.02MB
2025-06-27 12:41 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST1.SH_label_1d_R03 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FctsGPMiner.mine: start mining on TEST1.SH...
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: GP_MINE_TEST1.SH_label_3d_R01 took 3.20 seconds (00:00:03)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST1.SH_label_3d_R01 CPU usage: 1.02s user, 0.02s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST1.SH_label_3d_R01 memory delta: -39.94MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST1.SH_label_3d_R01 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: CALC_TEST1.SH_label_3d_R01 took 0.18 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST1.SH_label_3d_R01 CPU usage: 0.12s user, 0.02s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST1.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: CALC_TEST1.SH_label_3d_R01 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: SKEW_TEST1.SH_label_3d_R01 took 0.07 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST1.SH_label_3d_R01 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST1.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST1.SH_label_3d_R01 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: KURT_TEST1.SH_label_3d_R01 took 0.03 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST1.SH_label_3d_R01 CPU usage: 0.03s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST1.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: KURT_TEST1.SH_label_3d_R01 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: METRIC_TEST1.SH_label_3d_R01 took 0.13 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST1.SH_label_3d_R01 CPU usage: 0.11s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST1.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:41 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST1.SH_label_3d_R01 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FctsGPMiner.mine: start mining on TEST1.SH...
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: GP_MINE_TEST1.SH_label_3d_R02 took 2.43 seconds (00:00:02)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST1.SH_label_3d_R02 CPU usage: 1.03s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST1.SH_label_3d_R02 memory delta: -34.80MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST1.SH_label_3d_R02 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: CALC_TEST1.SH_label_3d_R02 took 0.18 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST1.SH_label_3d_R02 CPU usage: 0.11s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST1.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: CALC_TEST1.SH_label_3d_R02 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: SKEW_TEST1.SH_label_3d_R02 took 0.07 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST1.SH_label_3d_R02 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST1.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST1.SH_label_3d_R02 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: KURT_TEST1.SH_label_3d_R02 took 0.02 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST1.SH_label_3d_R02 CPU usage: 0.03s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST1.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: KURT_TEST1.SH_label_3d_R02 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: METRIC_TEST1.SH_label_3d_R02 took 0.09 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST1.SH_label_3d_R02 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST1.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:41 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST1.SH_label_3d_R02 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FctsGPMiner.mine: start mining on TEST1.SH...
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: GP_MINE_TEST1.SH_label_3d_R03 took 2.38 seconds (00:00:02)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST1.SH_label_3d_R03 CPU usage: 1.00s user, 0.03s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST1.SH_label_3d_R03 memory delta: -16.76MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST1.SH_label_3d_R03 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: CALC_TEST1.SH_label_3d_R03 took 0.16 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST1.SH_label_3d_R03 CPU usage: 0.12s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST1.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: CALC_TEST1.SH_label_3d_R03 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: SKEW_TEST1.SH_label_3d_R03 took 0.07 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST1.SH_label_3d_R03 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST1.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST1.SH_label_3d_R03 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: KURT_TEST1.SH_label_3d_R03 took 0.02 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST1.SH_label_3d_R03 CPU usage: 0.02s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST1.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: KURT_TEST1.SH_label_3d_R03 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: METRIC_TEST1.SH_label_3d_R03 took 0.08 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST1.SH_label_3d_R03 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST1.SH_label_3d_R03 memory delta: 0.02MB
2025-06-27 12:41 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST1.SH_label_3d_R03 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FctsGPMiner.mine: start mining on TEST2.SH...
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: GP_MINE_TEST2.SH_label_1d_R01 took 2.25 seconds (00:00:02)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST2.SH_label_1d_R01 CPU usage: 0.95s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST2.SH_label_1d_R01 memory delta: 0.03MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST2.SH_label_1d_R01 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: CALC_TEST2.SH_label_1d_R01 took 0.14 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST2.SH_label_1d_R01 CPU usage: 0.14s user, 0.02s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST2.SH_label_1d_R01 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: CALC_TEST2.SH_label_1d_R01 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: SKEW_TEST2.SH_label_1d_R01 took 0.07 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST2.SH_label_1d_R01 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST2.SH_label_1d_R01 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST2.SH_label_1d_R01 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: KURT_TEST2.SH_label_1d_R01 took 0.03 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST2.SH_label_1d_R01 CPU usage: 0.03s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST2.SH_label_1d_R01 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: KURT_TEST2.SH_label_1d_R01 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: METRIC_TEST2.SH_label_1d_R01 took 0.12 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST2.SH_label_1d_R01 CPU usage: 0.12s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST2.SH_label_1d_R01 memory delta: 0.02MB
2025-06-27 12:41 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST2.SH_label_1d_R01 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FctsGPMiner.mine: start mining on TEST2.SH...
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: GP_MINE_TEST2.SH_label_1d_R02 took 2.34 seconds (00:00:02)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST2.SH_label_1d_R02 CPU usage: 1.00s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST2.SH_label_1d_R02 memory delta: -16.68MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST2.SH_label_1d_R02 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: CALC_TEST2.SH_label_1d_R02 took 0.19 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST2.SH_label_1d_R02 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST2.SH_label_1d_R02 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: CALC_TEST2.SH_label_1d_R02 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: SKEW_TEST2.SH_label_1d_R02 took 0.06 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST2.SH_label_1d_R02 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST2.SH_label_1d_R02 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST2.SH_label_1d_R02 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: KURT_TEST2.SH_label_1d_R02 took 0.03 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST2.SH_label_1d_R02 CPU usage: 0.03s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST2.SH_label_1d_R02 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: KURT_TEST2.SH_label_1d_R02 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: METRIC_TEST2.SH_label_1d_R02 took 0.12 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST2.SH_label_1d_R02 CPU usage: 0.11s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST2.SH_label_1d_R02 memory delta: 0.01MB
2025-06-27 12:41 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST2.SH_label_1d_R02 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FctsGPMiner.mine: start mining on TEST2.SH...
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: GP_MINE_TEST2.SH_label_1d_R03 took 5.16 seconds (00:00:05)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST2.SH_label_1d_R03 CPU usage: 1.33s user, 0.03s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST2.SH_label_1d_R03 memory delta: -47.90MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST2.SH_label_1d_R03 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: CALC_TEST2.SH_label_1d_R03 took 0.30 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST2.SH_label_1d_R03 CPU usage: 0.19s user, 0.02s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST2.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: CALC_TEST2.SH_label_1d_R03 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: SKEW_TEST2.SH_label_1d_R03 took 0.09 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST2.SH_label_1d_R03 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST2.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST2.SH_label_1d_R03 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: KURT_TEST2.SH_label_1d_R03 took 0.04 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST2.SH_label_1d_R03 CPU usage: 0.05s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST2.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: KURT_TEST2.SH_label_1d_R03 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: METRIC_TEST2.SH_label_1d_R03 took 0.16 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST2.SH_label_1d_R03 CPU usage: 0.12s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST2.SH_label_1d_R03 memory delta: 0.00MB
2025-06-27 12:41 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST2.SH_label_1d_R03 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FctsGPMiner.mine: start mining on TEST2.SH...
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: GP_MINE_TEST2.SH_label_3d_R01 took 2.54 seconds (00:00:02)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST2.SH_label_3d_R01 CPU usage: 1.09s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST2.SH_label_3d_R01 memory delta: -0.01MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST2.SH_label_3d_R01 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: CALC_TEST2.SH_label_3d_R01 took 0.21 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST2.SH_label_3d_R01 CPU usage: 0.11s user, 0.03s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST2.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: CALC_TEST2.SH_label_3d_R01 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: SKEW_TEST2.SH_label_3d_R01 took 0.07 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST2.SH_label_3d_R01 CPU usage: 0.08s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST2.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST2.SH_label_3d_R01 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: KURT_TEST2.SH_label_3d_R01 took 0.03 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST2.SH_label_3d_R01 CPU usage: 0.03s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST2.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: KURT_TEST2.SH_label_3d_R01 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: METRIC_TEST2.SH_label_3d_R01 took 0.12 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST2.SH_label_3d_R01 CPU usage: 0.12s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST2.SH_label_3d_R01 memory delta: 0.00MB
2025-06-27 12:41 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST2.SH_label_3d_R01 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FctsGPMiner.mine: start mining on TEST2.SH...
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: GP_MINE_TEST2.SH_label_3d_R02 took 2.41 seconds (00:00:02)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST2.SH_label_3d_R02 CPU usage: 1.03s user, 0.02s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST2.SH_label_3d_R02 memory delta: 0.02MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST2.SH_label_3d_R02 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: CALC_TEST2.SH_label_3d_R02 took 0.17 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST2.SH_label_3d_R02 CPU usage: 0.11s user, 0.02s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST2.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: CALC_TEST2.SH_label_3d_R02 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: SKEW_TEST2.SH_label_3d_R02 took 0.06 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST2.SH_label_3d_R02 CPU usage: 0.06s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST2.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST2.SH_label_3d_R02 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: KURT_TEST2.SH_label_3d_R02 took 0.03 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST2.SH_label_3d_R02 CPU usage: 0.03s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST2.SH_label_3d_R02 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: KURT_TEST2.SH_label_3d_R02 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: METRIC_TEST2.SH_label_3d_R02 took 0.13 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST2.SH_label_3d_R02 CPU usage: 0.14s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST2.SH_label_3d_R02 memory delta: 0.01MB
2025-06-27 12:41 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST2.SH_label_3d_R02 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FctsGPMiner.mine: start mining on TEST2.SH...
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: GP_MINE_TEST2.SH_label_3d_R03 took 2.72 seconds (00:00:02)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_TEST2.SH_label_3d_R03 CPU usage: 1.00s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_TEST2.SH_label_3d_R03 memory delta: -69.60MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: GP_MINE_TEST2.SH_label_3d_R03 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 开始计算74个因子表达式
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(amount_norm) 时出现异常: ts_median_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(volume_norm) 时出现异常: ts_zscore_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(amount_norm) 时出现异常: ts_cons_n2(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_20(amount_norm) 时出现异常: ts_skew_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(amount_norm) 时出现异常: ts_cons_n10(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_20(amount_norm) 时出现异常: ts_min_20(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(volume_norm) 时出现异常: ts_sum_8(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_40(amount_norm) 时出现异常: ts_day_max_40(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_intercept_20(volume_norm) 时出现异常: ta_lr_intercept_20(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_kama_55(amount_norm) 时出现异常: ta_kama_55(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_10(volume_norm) 时出现异常: ts_median_10(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_14(volume_norm) 时出现异常: ta_rocr_14(df["volume"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocr_25(amount_norm) 时出现异常: ta_rocr_25(df["amount"]_norm)——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argrange_5(ts_sum_3(volume_norm)) 时出现异常: ts_argrange_5(ts_sum_3(df["volume"]_norm))——eval异常
2025-06-27 12:41 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_skew_10(ta_lr_intercept_20(volume_norm)) 时出现异常: ts_skew_10(ta_lr_intercept_20(df["volume"]_norm))——eval异常
2025-06-27 12:41 | INFO     | FactorLoader.get_fct_df: 返回结果: 59个指定因子
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: CALC_TEST2.SH_label_3d_R03 took 0.15 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: CALC_TEST2.SH_label_3d_R03 CPU usage: 0.12s user, 0.02s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: CALC_TEST2.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: CALC_TEST2.SH_label_3d_R03 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: SKEW_TEST2.SH_label_3d_R03 took 0.07 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: SKEW_TEST2.SH_label_3d_R03 CPU usage: 0.05s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: SKEW_TEST2.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: SKEW_TEST2.SH_label_3d_R03 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: KURT_TEST2.SH_label_3d_R03 took 0.03 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: KURT_TEST2.SH_label_3d_R03 CPU usage: 0.03s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: KURT_TEST2.SH_label_3d_R03 memory delta: 0.00MB
2025-06-27 12:41 | INFO     | FactorMonitorContext.__exit__: KURT_TEST2.SH_label_3d_R03 completed successfully
2025-06-27 12:41 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: METRIC_TEST2.SH_label_3d_R03 took 0.13 seconds (00:00:00)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: METRIC_TEST2.SH_label_3d_R03 CPU usage: 0.12s user, 0.00s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: METRIC_TEST2.SH_label_3d_R03 memory delta: 0.01MB
2025-06-27 12:41 | ERROR    | FactorMonitorContext.__exit__: METRIC_TEST2.SH_label_3d_R03 failed with error: window_shape cannot be larger than input array shape
2025-06-27 12:41 | INFO     | ResMonitor.end_timer: GP_MINING_GP_20250627_124005_723367 took 86.09 seconds (00:01:26)
2025-06-27 12:41 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_20250627_124005_723367 CPU usage: 62.17s user, 27.36s system
2025-06-27 12:41 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_20250627_124005_723367 memory delta: 1318.85MB
2025-06-27 12:41 | INFO     | MonitorContext.__exit__: GP_MINING_GP_20250627_124005_723367 completed successfully
2025-06-27 13:03 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-27 13:03 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 13:03 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 13:03 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-27 13:03 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-27 13:03 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 13:03 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-27 13:03 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-27 13:03 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 13:03 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-27 13:03 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-27 13:03 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-27 13:03 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-27 13:03 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['LOW4', 'QTLU5', 'JZ012_34', 'KSFT', 'R_0', 'CLOSE3', 'JZ004_58', 'b_macd_hist', 'LOW0', 'JZ004_20']...
2025-06-27 13:03 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-27 13:03 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-27 13:03 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-27 13:03 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-27 13:03 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-27 13:03 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-27 13:03 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-27 13:03 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-27 13:03 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征69
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(69个)，跳过相关性过滤
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1045, 69), y形状: (1045,)
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 53
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-27 13:03 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=7, 饱和法=7, 平坦法=13 -> 选择=7
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=7, 最大R²=0.2526
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 53
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-27 13:03 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=15, 饱和法=14, 平坦法=7 -> 选择=14
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=14, 最大R²=0.2556
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 53
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-27 13:03 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=9, 饱和法=8, 平坦法=10 -> 选择=9
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=9, 最大R²=0.2487
2025-06-27 13:03 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-27 13:03 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-27 13:03 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-27 13:03 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_gain.png
2025-06-27 13:03 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_weight.png
2025-06-27 13:04 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_cover.png
2025-06-27 13:04 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-27 13:04 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-27 13:04 | INFO     | FeatSelection.select_features: 选择特征: 14个 + base2keep: 4个 + 标签: 1个
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 13:05 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 13:05 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 13:05 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 13:05 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 13:05 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-27 13:05 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-27 13:05 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 13:05 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-27 13:05 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-27 13:05 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-27 13:05 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-27 13:05 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-27 13:05 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-27 13:05 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-27 13:05 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 8.66 seconds (00:00:08)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 4.19s user, 3.80s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 390.81MB
2025-06-27 13:05 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 13:05 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.20 seconds (00:00:00)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.27s user, 0.08s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 10.01MB
2025-06-27 13:05 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.10 seconds (00:00:00)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.19s user, 0.02s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 2.91MB
2025-06-27 13:05 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.03 seconds (00:00:00)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.05s user, 0.00s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: -1.44MB
2025-06-27 13:05 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 29.51 seconds (00:00:29)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 30.78s user, 17.73s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 1142.58MB
2025-06-27 13:05 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.02 seconds (00:00:00)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.05MB
2025-06-27 13:05 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 3.34 seconds (00:00:03)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.38s user, 0.02s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 0.24MB
2025-06-27 13:05 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 13:05 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 0.14 seconds (00:00:00)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 0.14s user, 0.00s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 13:05 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.08 seconds (00:00:00)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.08s user, 0.00s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 13:05 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.02 seconds (00:00:00)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.03s user, 0.00s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 13:05 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 0.26 seconds (00:00:00)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 0.44s user, 0.00s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: 0.04MB
2025-06-27 13:05 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 0.01 seconds (00:00:00)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 13:05 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 2.94 seconds (00:00:02)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.31s user, 0.00s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:05 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 13:05 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 0.14 seconds (00:00:00)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 0.12s user, 0.02s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:05 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.08 seconds (00:00:00)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.08s user, 0.00s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:05 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.02 seconds (00:00:00)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.02s user, 0.00s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:05 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 0.27 seconds (00:00:00)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 0.34s user, 0.02s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: 0.71MB
2025-06-27 13:05 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-27 13:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:05 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:05 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-27 13:05 | INFO     | ResMonitor.end_timer: GP_MINING_GP_20250627_130501_653734 took 46.29 seconds (00:00:46)
2025-06-27 13:05 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_20250627_130501_653734 CPU usage: 37.72s user, 21.83s system
2025-06-27 13:05 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_20250627_130501_653734 memory delta: 1558.87MB
2025-06-27 13:05 | ERROR    | MonitorContext.__exit__: GP_MINING_GP_20250627_130501_653734 failed with error: 'str' object has no attribute 'strftime'
2025-06-27 13:07 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:07 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 13:07 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 13:07 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 13:07 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 13:07 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 13:07 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-27 13:07 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-27 13:07 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 13:07 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-27 13:07 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-27 13:07 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-27 13:07 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-27 13:07 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-27 13:07 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-27 13:07 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-27 13:07 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-27 13:07 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:07 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 13:07 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 18.85 seconds (00:00:18)
2025-06-27 13:07 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 7.91s user, 6.59s system
2025-06-27 13:07 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 442.58MB
2025-06-27 13:07 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-27 13:07 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:07 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 13:07 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 13:07 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.38 seconds (00:00:00)
2025-06-27 13:07 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.44s user, 0.16s system
2025-06-27 13:07 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 8.26MB
2025-06-27 13:07 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-27 13:07 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:07 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.21 seconds (00:00:00)
2025-06-27 13:07 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.25s user, 0.09s system
2025-06-27 13:07 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 4.64MB
2025-06-27 13:07 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-27 13:07 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:07 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.04 seconds (00:00:00)
2025-06-27 13:07 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.06s user, 0.03s system
2025-06-27 13:07 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: 1.51MB
2025-06-27 13:07 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-27 13:07 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:08 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 57.27 seconds (00:00:57)
2025-06-27 13:08 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 48.38s user, 25.08s system
2025-06-27 13:08 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 699.60MB
2025-06-27 13:08 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-27 13:08 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:08 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 13:08 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.03 seconds (00:00:00)
2025-06-27 13:08 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-06-27 13:08 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.60MB
2025-06-27 13:08 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-27 13:08 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:08 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 13:08 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 4.87 seconds (00:00:04)
2025-06-27 13:08 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.44s user, 0.02s system
2025-06-27 13:08 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 2.75MB
2025-06-27 13:08 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-27 13:08 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:08 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 13:08 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 13:08 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 0.27 seconds (00:00:00)
2025-06-27 13:08 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 0.22s user, 0.00s system
2025-06-27 13:08 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: 0.23MB
2025-06-27 13:08 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-27 13:08 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:08 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.14 seconds (00:00:00)
2025-06-27 13:08 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.14s user, 0.00s system
2025-06-27 13:08 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: 0.05MB
2025-06-27 13:08 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-27 13:08 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:08 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.03 seconds (00:00:00)
2025-06-27 13:08 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.03s user, 0.00s system
2025-06-27 13:08 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 13:08 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-27 13:08 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:08 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 0.41 seconds (00:00:00)
2025-06-27 13:08 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 0.50s user, 0.00s system
2025-06-27 13:08 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: 2.56MB
2025-06-27 13:08 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-27 13:08 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:08 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 13:08 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 0.01 seconds (00:00:00)
2025-06-27 13:08 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-27 13:08 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 13:08 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-27 13:08 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:08 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 13:08 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 7.28 seconds (00:00:07)
2025-06-27 13:08 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.48s user, 0.00s system
2025-06-27 13:08 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.02MB
2025-06-27 13:08 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-27 13:08 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:08 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 13:08 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 13:08 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 0.25 seconds (00:00:00)
2025-06-27 13:08 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 0.22s user, 0.00s system
2025-06-27 13:08 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: 0.18MB
2025-06-27 13:08 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-27 13:08 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:08 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.13 seconds (00:00:00)
2025-06-27 13:08 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.14s user, 0.00s system
2025-06-27 13:08 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:08 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-27 13:08 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:08 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.03 seconds (00:00:00)
2025-06-27 13:08 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.03s user, 0.00s system
2025-06-27 13:08 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:08 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-27 13:08 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:08 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 0.39 seconds (00:00:00)
2025-06-27 13:08 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 0.56s user, 0.00s system
2025-06-27 13:08 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: 0.78MB
2025-06-27 13:08 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-27 13:08 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:08 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 13:08 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-27 13:08 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 0.02s user, 0.00s system
2025-06-27 13:08 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:08 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-27 13:08 | INFO     | ResMonitor.end_timer: GP_MINING_GP_20250627_130723_856358 took 91.29 seconds (00:01:31)
2025-06-27 13:08 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_20250627_130723_856358 CPU usage: 60.12s user, 32.34s system
2025-06-27 13:08 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_20250627_130723_856358 memory delta: 1177.78MB
2025-06-27 13:08 | ERROR    | MonitorContext.__exit__: GP_MINING_GP_20250627_130723_856358 failed with error: FactorZooConnector.create_batch() got an unexpected keyword argument 'source_universe'
2025-06-27 13:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:09 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 13:09 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 13:09 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 13:09 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 13:09 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 13:09 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-27 13:09 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-27 13:09 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 13:09 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-27 13:09 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-27 13:09 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-27 13:09 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-27 13:09 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-27 13:09 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-27 13:09 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-27 13:09 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-27 13:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:09 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 13:10 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 20.60 seconds (00:00:20)
2025-06-27 13:10 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 8.48s user, 7.02s system
2025-06-27 13:10 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 456.15MB
2025-06-27 13:10 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-27 13:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:10 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 13:10 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 13:10 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.32 seconds (00:00:00)
2025-06-27 13:10 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.44s user, 0.14s system
2025-06-27 13:10 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 6.32MB
2025-06-27 13:10 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-27 13:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:10 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.18 seconds (00:00:00)
2025-06-27 13:10 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.22s user, 0.12s system
2025-06-27 13:10 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: -0.77MB
2025-06-27 13:10 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-27 13:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:10 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.03 seconds (00:00:00)
2025-06-27 13:10 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.03s user, 0.00s system
2025-06-27 13:10 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: -1.50MB
2025-06-27 13:10 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-27 13:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:11 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 56.70 seconds (00:00:56)
2025-06-27 13:11 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 48.45s user, 24.36s system
2025-06-27 13:11 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 1085.34MB
2025-06-27 13:11 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-27 13:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:11 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 13:11 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.02 seconds (00:00:00)
2025-06-27 13:11 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-06-27 13:11 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.05MB
2025-06-27 13:11 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-27 13:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:11 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 13:11 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 4.88 seconds (00:00:04)
2025-06-27 13:11 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.44s user, 0.00s system
2025-06-27 13:11 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 0.08MB
2025-06-27 13:11 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-27 13:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:11 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 13:11 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 13:11 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 0.22 seconds (00:00:00)
2025-06-27 13:11 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 0.20s user, 0.00s system
2025-06-27 13:11 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: 0.07MB
2025-06-27 13:11 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-27 13:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:11 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.12 seconds (00:00:00)
2025-06-27 13:11 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.11s user, 0.00s system
2025-06-27 13:11 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 13:11 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-27 13:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:11 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.03 seconds (00:00:00)
2025-06-27 13:11 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.03s user, 0.00s system
2025-06-27 13:11 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 13:11 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-27 13:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:11 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 0.38 seconds (00:00:00)
2025-06-27 13:11 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 0.53s user, 0.00s system
2025-06-27 13:11 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: 0.01MB
2025-06-27 13:11 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-27 13:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:11 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 13:11 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 0.01 seconds (00:00:00)
2025-06-27 13:11 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 0.02s user, 0.00s system
2025-06-27 13:11 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 13:11 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-27 13:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:11 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 13:11 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 4.85 seconds (00:00:04)
2025-06-27 13:11 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.47s user, 0.00s system
2025-06-27 13:11 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:11 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-27 13:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:11 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 13:11 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 13:11 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 0.22 seconds (00:00:00)
2025-06-27 13:11 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 0.22s user, 0.00s system
2025-06-27 13:11 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: 0.06MB
2025-06-27 13:11 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-27 13:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:11 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.13 seconds (00:00:00)
2025-06-27 13:11 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.12s user, 0.00s system
2025-06-27 13:11 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:11 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-27 13:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:11 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.03 seconds (00:00:00)
2025-06-27 13:11 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.03s user, 0.00s system
2025-06-27 13:11 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:11 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-27 13:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:11 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 0.38 seconds (00:00:00)
2025-06-27 13:11 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 0.56s user, 0.02s system
2025-06-27 13:11 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: 1.53MB
2025-06-27 13:11 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-27 13:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:11 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 13:11 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-27 13:11 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-27 13:11 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:11 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-27 13:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:11 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20210120_20250519_1d_label_1_001 took 0.00 seconds (00:00:00)
2025-06-27 13:11 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20210120_20250519_1d_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 13:11 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20210120_20250519_1d_label_1_001 memory delta: 0.00MB
2025-06-27 13:11 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20210120_20250519_1d_label_1_001 completed successfully
2025-06-27 13:11 | INFO     | ResMonitor.end_timer: GP_MINING_GP_20250627_130944_404846 took 89.82 seconds (00:01:29)
2025-06-27 13:11 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_20250627_130944_404846 CPU usage: 60.80s user, 31.97s system
2025-06-27 13:11 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_20250627_130944_404846 memory delta: 1565.57MB
2025-06-27 13:11 | INFO     | MonitorContext.__exit__: GP_MINING_GP_20250627_130944_404846 completed successfully
2025-06-27 13:12 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:12 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 13:12 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 13:12 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 13:12 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 13:12 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 13:12 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-27 13:12 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-27 13:12 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 13:12 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-27 13:12 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-27 13:12 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-27 13:12 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-27 13:12 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-27 13:12 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-27 13:12 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-27 13:12 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-27 13:12 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:12 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 13:12 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 18.11 seconds (00:00:18)
2025-06-27 13:12 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 9.00s user, 6.22s system
2025-06-27 13:12 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 448.73MB
2025-06-27 13:12 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-27 13:12 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:12 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 13:12 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 13:12 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.34 seconds (00:00:00)
2025-06-27 13:12 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.48s user, 0.16s system
2025-06-27 13:12 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 6.65MB
2025-06-27 13:12 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-27 13:12 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:12 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.19 seconds (00:00:00)
2025-06-27 13:12 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.23s user, 0.09s system
2025-06-27 13:12 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 4.28MB
2025-06-27 13:12 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-27 13:12 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:12 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.04 seconds (00:00:00)
2025-06-27 13:12 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.08s user, 0.02s system
2025-06-27 13:12 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: -1.26MB
2025-06-27 13:12 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-27 13:12 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:13 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 58.06 seconds (00:00:58)
2025-06-27 13:13 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 48.95s user, 24.58s system
2025-06-27 13:13 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 1045.23MB
2025-06-27 13:13 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-27 13:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:13 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 13:13 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.02 seconds (00:00:00)
2025-06-27 13:13 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-27 13:13 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.29MB
2025-06-27 13:13 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-27 13:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:13 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 13:13 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 4.84 seconds (00:00:04)
2025-06-27 13:13 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.48s user, 0.02s system
2025-06-27 13:13 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 0.78MB
2025-06-27 13:13 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-27 13:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:13 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 13:13 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 13:13 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 0.22 seconds (00:00:00)
2025-06-27 13:13 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 0.19s user, 0.00s system
2025-06-27 13:13 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: 0.02MB
2025-06-27 13:13 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-27 13:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:13 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.13 seconds (00:00:00)
2025-06-27 13:13 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.12s user, 0.00s system
2025-06-27 13:13 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: 0.05MB
2025-06-27 13:13 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-27 13:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:13 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.03 seconds (00:00:00)
2025-06-27 13:13 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.03s user, 0.00s system
2025-06-27 13:13 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 13:13 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-27 13:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:13 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 0.36 seconds (00:00:00)
2025-06-27 13:13 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 0.55s user, 0.00s system
2025-06-27 13:13 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: 0.92MB
2025-06-27 13:13 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-27 13:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:13 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 13:13 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 0.01 seconds (00:00:00)
2025-06-27 13:13 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 0.02s user, 0.00s system
2025-06-27 13:13 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 13:13 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-27 13:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:13 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 13:13 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 5.02 seconds (00:00:05)
2025-06-27 13:13 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.48s user, 0.00s system
2025-06-27 13:13 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.04MB
2025-06-27 13:13 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-27 13:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:13 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 13:13 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 13:13 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 0.23 seconds (00:00:00)
2025-06-27 13:13 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 0.20s user, 0.00s system
2025-06-27 13:13 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: 0.14MB
2025-06-27 13:13 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-27 13:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:13 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.14 seconds (00:00:00)
2025-06-27 13:13 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.14s user, 0.00s system
2025-06-27 13:13 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:13 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-27 13:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:13 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.03 seconds (00:00:00)
2025-06-27 13:13 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.02s user, 0.02s system
2025-06-27 13:13 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:13 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-27 13:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:13 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 0.40 seconds (00:00:00)
2025-06-27 13:13 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 0.47s user, 0.02s system
2025-06-27 13:13 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: 0.35MB
2025-06-27 13:13 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-27 13:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:13 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 13:13 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-27 13:13 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 0.02s user, 0.00s system
2025-06-27 13:13 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:13 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-27 13:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:13 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20210120_20250519_1d_label_1_001 took 0.02 seconds (00:00:00)
2025-06-27 13:13 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20210120_20250519_1d_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 13:13 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20210120_20250519_1d_label_1_001 memory delta: 0.01MB
2025-06-27 13:13 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20210120_20250519_1d_label_1_001 completed successfully
2025-06-27 13:13 | INFO     | ResMonitor.end_timer: GP_MINING_GP_20250627_131221_209220 took 88.91 seconds (00:01:28)
2025-06-27 13:13 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_20250627_131221_209220 CPU usage: 62.00s user, 31.36s system
2025-06-27 13:13 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_20250627_131221_209220 memory delta: 1521.09MB
2025-06-27 13:13 | INFO     | MonitorContext.__exit__: GP_MINING_GP_20250627_131221_209220 completed successfully
2025-06-27 13:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:31 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 13:31 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 13:31 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 13:31 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 13:31 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 13:31 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-27 13:31 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-27 13:31 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 13:31 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-27 13:31 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-27 13:31 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-27 13:31 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-27 13:31 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-27 13:31 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-27 13:31 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-27 13:31 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-27 13:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:31 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 8.37 seconds (00:00:08)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 4.27s user, 3.20s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 365.81MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:32 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 13:32 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.19 seconds (00:00:00)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.25s user, 0.09s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 12.03MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.11 seconds (00:00:00)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.16s user, 0.06s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 1.47MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.02 seconds (00:00:00)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.06s user, 0.00s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: 3.28MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 30.45 seconds (00:00:30)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 32.61s user, 16.42s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 1155.67MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:32 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.05MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:32 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 2.58 seconds (00:00:02)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.33s user, 0.00s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 0.19MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:32 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 13:32 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 0.16 seconds (00:00:00)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 0.14s user, 0.00s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.08 seconds (00:00:00)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.08s user, 0.00s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.02 seconds (00:00:00)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.03s user, 0.00s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 0.31 seconds (00:00:00)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 0.36s user, 0.00s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: -5.75MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:32 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 0.01 seconds (00:00:00)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:32 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 3.04 seconds (00:00:03)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.33s user, 0.00s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.04MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:32 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 13:32 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 0.15 seconds (00:00:00)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 0.12s user, 0.02s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.09 seconds (00:00:00)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.09s user, 0.00s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.02 seconds (00:00:00)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.02s user, 0.00s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 0.30 seconds (00:00:00)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 0.41s user, 0.00s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: 1.31MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:32 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20210120_20250519_1d_label_1_001 took 0.00 seconds (00:00:00)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20210120_20250519_1d_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20210120_20250519_1d_label_1_001 memory delta: 0.01MB
2025-06-27 13:32 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20210120_20250519_1d_label_1_001 completed successfully
2025-06-27 13:32 | INFO     | ResMonitor.end_timer: GP_MINING_GP_20250627_133153_890822 took 46.45 seconds (00:00:46)
2025-06-27 13:32 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_20250627_133153_890822 CPU usage: 39.61s user, 20.03s system
2025-06-27 13:32 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_20250627_133153_890822 memory delta: 1543.66MB
2025-06-27 13:32 | INFO     | MonitorContext.__exit__: GP_MINING_GP_20250627_133153_890822 completed successfully
2025-06-27 14:10 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: TEST_GP_510300.SH_20240101_20240131_1d_20250627141009
2025-06-27 14:10 | ERROR    | FactorValueManager.load_batch_data: 批次目录不存在: FAKE_BATCH_ID_12345
2025-06-27 14:10 | WARNING  | FactorValueManager.load_batch_data: 指定因子不存在于批次 TEST_GP_510300.SH_20240101_20240131_1d_20250627141009
2025-06-27 14:14 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_510300.SH_20240101_20240331_1d_20250627141433
2025-06-27 14:14 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_510500.SH_20240101_20240331_1d_20250627141433
2025-06-27 14:14 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_159985.SZ_20240101_20240331_1d_20250627141433
2025-06-27 14:35 | ERROR    | OptimizedFactorValueManager.save_batch_data: 保存批次数据失败 test_optimized_batch: 'PerformanceConfig' object is not subscriptable
2025-06-27 14:35 | ERROR    | OptimizedFactorValueManager.save_batch_data: 保存批次数据失败 perf_test_batch: 'PerformanceConfig' object is not subscriptable
2025-06-27 14:46 | ERROR    | OptimizedFactorValueManager.save_batch_data: 保存批次数据失败 test_optimized_batch: 'PerformanceConfig' object is not subscriptable
2025-06-27 14:46 | ERROR    | OptimizedFactorValueManager.save_batch_data: 保存批次数据失败 perf_test_batch: 'PerformanceConfig' object is not subscriptable
2025-06-27 14:49 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: fixed_test_batch
2025-06-27 14:50 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: fixed_test_batch
2025-06-27 14:50 | ERROR    | FactorValueManager.delete_batch_data: 批次数据删除失败 fixed_test_batch: 'FactorValueManager' object has no attribute 'by_batch_dir'
2025-06-27 14:54 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: fixed_test_batch
2025-06-27 14:54 | INFO     | FactorValueManager.delete_batch_data: 批次数据删除成功: fixed_test_batch
2025-06-27 15:03 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 15:03 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 15:03 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 15:03 | ERROR    | FactorValueManager._optimized_disk_load: 批次目录不存在: test1
2025-06-27 15:03 | ERROR    | FactorValueManager._optimized_disk_load: 批次目录不存在: test2
2025-06-27 15:03 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: test1/L2 耗时5.0ms
2025-06-27 15:03 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: test2/L3 耗时4.0ms
2025-06-27 15:03 | INFO     | FactorValueManager.batch_load_multiple: 批量加载完成: 2 个批次, 耗时 0.01s
2025-06-27 15:03 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 15:03 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 15:55 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 15:55 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 15:55 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 15:55 | ERROR    | FactorValueManager._optimized_disk_load: 批次目录不存在: test1
2025-06-27 15:55 | ERROR    | FactorValueManager._optimized_disk_load: 批次目录不存在: test2
2025-06-27 15:55 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: test1/L2 耗时5.0ms
2025-06-27 15:55 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: test2/L3 耗时4.0ms
2025-06-27 15:55 | INFO     | FactorValueManager.batch_load_multiple: 批量加载完成: 2 个批次, 耗时 0.01s
2025-06-27 15:55 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 15:55 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 16:11 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 16:11 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 16:11 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 16:11 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 16:11 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 16:11 | ERROR    | FactorValueManager._optimized_disk_load: 批次目录不存在: test1
2025-06-27 16:11 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: test1/L2 耗时18.9ms
2025-06-27 16:11 | ERROR    | FactorValueManager._optimized_disk_load: 批次目录不存在: test2
2025-06-27 16:11 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: test2/L3 耗时2.0ms
2025-06-27 16:11 | INFO     | FactorValueManager.batch_load_multiple: 批量加载完成: 2 个批次, 耗时 0.02s
2025-06-27 16:11 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 16:11 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 16:12 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 16:12 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 16:12 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 16:12 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 16:12 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: fixed_test_batch
2025-06-27 16:12 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: fixed_test_batch 耗时281.7ms
2025-06-27 16:13 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: fixed_test_batch/L2
2025-06-27 16:13 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: fixed_test_batch/L2 耗时298.2ms
2025-06-27 16:13 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: fixed_test_batch/L2
2025-06-27 16:13 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: fixed_test_batch/L2 耗时29.9ms
2025-06-27 16:13 | INFO     | FactorValueManager.delete_batch_data: 批次数据删除成功: fixed_test_batch
2025-06-27 16:22 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 16:22 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 16:22 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 GP_510050.SH_20250627_L0_test01: 'L0'
2025-06-27 16:22 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 GP_510050.SH_20250626_L0_test02: 'L0'
2025-06-27 16:22 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 GP_159915.SZ_20250627_L0_test03: 'L0'
2025-06-27 16:22 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 GP_MULTI_20250627_L0_test04: 'L0'
2025-06-27 16:22 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: TEST_GP_510300.SH_20240101_20240131_1d_20250627141009/L2
2025-06-27 16:22 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: TEST_GP_510300.SH_20240101_20240131_1d_20250627141009/L2 耗时105.0ms
2025-06-27 16:23 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 16:23 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 16:23 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_510050.SH_20250627_L0_test01
2025-06-27 16:23 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_510050.SH_20250627_L0_test01 耗时70.4ms
2025-06-27 16:23 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_510050.SH_20250626_L0_test02
2025-06-27 16:23 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_510050.SH_20250626_L0_test02 耗时17.9ms
2025-06-27 16:23 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_159915.SZ_20250627_L0_test03
2025-06-27 16:23 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_159915.SZ_20250627_L0_test03 耗时16.9ms
2025-06-27 16:23 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_MULTI_20250627_L0_test04
2025-06-27 16:23 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_MULTI_20250627_L0_test04 耗时17.1ms
2025-06-27 16:23 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: TEST_GP_510300.SH_20240101_20240131_1d_20250627141009/L2
2025-06-27 16:23 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: TEST_GP_510300.SH_20240101_20240131_1d_20250627141009/L2 耗时65.6ms
2025-06-27 16:23 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 16:23 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 17:02 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 17:02 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 17:02 | INFO     | FactorLoader.get_fct_df: 开始计算5个因子表达式
2025-06-27 17:02 | ERROR    | FactorLoader.get_fct_df: 计算因子 mul(feature_1, feature_2) 时出现异常: mul(df["feature_1"], df["feature_2"])——eval异常
2025-06-27 17:02 | ERROR    | FactorLoader.get_fct_df: 计算因子 add(ma_5, feature_3) 时出现异常: add(df["ma_5"], df["feature_3"])——eval异常
2025-06-27 17:02 | ERROR    | FactorLoader.get_fct_df: 计算因子 div(volume, close) 时出现异常: div(volume, close)——eval异常
2025-06-27 17:02 | ERROR    | FactorLoader.get_fct_df: 计算因子 sub(high, low) 时出现异常: sub(high, low)——eval异常
2025-06-27 17:02 | ERROR    | FactorLoader.get_fct_df: 计算因子 mul(rsi_14, 0.01) 时出现异常: mul(df["rsi_14"], 0.01)——eval异常
2025-06-27 17:02 | WARNING  | FactorLoader.get_fct_df: 没有成功处理任何因子
2025-06-27 17:02 | INFO     | FactorLoader.get_fct_df: 开始计算4个因子表达式
2025-06-27 17:02 | ERROR    | FactorLoader.get_fct_df: 计算因子 add(feature_1, feature_2) 时出现异常: add(df["feature_1"], df["feature_2"])——eval异常
2025-06-27 17:02 | ERROR    | FactorLoader.get_fct_df: 计算因子 mul(close, volume) 时出现异常: mul(close, volume)——eval异常
2025-06-27 17:02 | ERROR    | FactorLoader.get_fct_df: 计算因子 div(high, low) 时出现异常: div(high, low)——eval异常
2025-06-27 17:02 | ERROR    | FactorLoader.get_fct_df: 计算因子 sub(feature_3, ma_5) 时出现异常: sub(df["feature_3"], df["ma_5"])——eval异常
2025-06-27 17:02 | WARNING  | FactorLoader.get_fct_df: 没有成功处理任何因子
2025-06-27 17:02 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 17:02 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 17:02 | INFO     | FactorLoader.get_fct_df: 开始计算5个因子表达式
2025-06-27 17:02 | ERROR    | FactorLoader.get_fct_df: 计算因子 volume / close 时出现异常: volume / close——eval异常
2025-06-27 17:02 | ERROR    | FactorLoader.get_fct_df: 计算因子 high - low 时出现异常: high - low——eval异常
2025-06-27 17:02 | INFO     | FactorLoader.get_fct_df: 返回结果: 3个指定因子
2025-06-27 17:02 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_510050.SH_20250627_L0_176634
2025-06-27 17:02 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_510050.SH_20250627_L0_176634 耗时152.5ms
2025-06-27 17:02 | INFO     | FactorLoader.get_fct_df: 开始计算4个因子表达式
2025-06-27 17:02 | ERROR    | FactorLoader.get_fct_df: 计算因子 close * volume 时出现异常: close * volume——eval异常
2025-06-27 17:02 | ERROR    | FactorLoader.get_fct_df: 计算因子 high / low 时出现异常: high / low——eval异常
2025-06-27 17:02 | INFO     | FactorLoader.get_fct_df: 返回结果: 2个指定因子
2025-06-27 17:02 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_159915.SZ_20250627_L0_176634
2025-06-27 17:02 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_159915.SZ_20250627_L0_176634 耗时19.0ms
2025-06-27 17:02 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_MULTI_20250627_L0_test04/L0
2025-06-27 17:02 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_MULTI_20250627_L0_test04/L0 耗时169.3ms
2025-06-27 17:03 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 17:03 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 17:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:05 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 17:05 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 17:05 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 17:05 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 17:05 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 17:05 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-27 17:05 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-27 17:05 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 17:05 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-27 17:05 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-27 17:05 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-27 17:05 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-27 17:05 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-27 17:05 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-27 17:05 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-27 17:05 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-27 17:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:05 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 17:05 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 10.60 seconds (00:00:10)
2025-06-27 17:05 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 5.19s user, 4.16s system
2025-06-27 17:05 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 88.86MB
2025-06-27 17:05 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-27 17:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:05 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 17:05 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 17:05 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.31 seconds (00:00:00)
2025-06-27 17:05 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.30s user, 0.11s system
2025-06-27 17:05 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 13.52MB
2025-06-27 17:05 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-27 17:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:05 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.19 seconds (00:00:00)
2025-06-27 17:05 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.28s user, 0.11s system
2025-06-27 17:05 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 4.93MB
2025-06-27 17:05 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-27 17:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:05 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.02 seconds (00:00:00)
2025-06-27 17:05 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.02s user, 0.02s system
2025-06-27 17:05 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: 1.50MB
2025-06-27 17:05 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-27 17:05 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:06 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 39.72 seconds (00:00:39)
2025-06-27 17:06 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 32.39s user, 19.17s system
2025-06-27 17:06 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 1301.94MB
2025-06-27 17:06 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-27 17:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:06 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 17:06 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-06-27 17:06 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-06-27 17:06 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.13MB
2025-06-27 17:06 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-27 17:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:06 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 17:06 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 2.79 seconds (00:00:02)
2025-06-27 17:06 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.33s user, 0.02s system
2025-06-27 17:06 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 0.54MB
2025-06-27 17:06 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-27 17:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:06 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 17:06 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 17:06 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 0.16 seconds (00:00:00)
2025-06-27 17:06 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 0.14s user, 0.02s system
2025-06-27 17:06 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: 0.06MB
2025-06-27 17:06 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-27 17:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:06 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.10 seconds (00:00:00)
2025-06-27 17:06 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.09s user, 0.00s system
2025-06-27 17:06 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 17:06 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-27 17:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:06 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.02 seconds (00:00:00)
2025-06-27 17:06 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.03s user, 0.00s system
2025-06-27 17:06 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 17:06 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-27 17:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:06 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 0.25 seconds (00:00:00)
2025-06-27 17:06 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 0.42s user, 0.00s system
2025-06-27 17:06 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: 0.39MB
2025-06-27 17:06 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-27 17:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:06 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 17:06 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 0.01 seconds (00:00:00)
2025-06-27 17:06 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-27 17:06 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 17:06 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-27 17:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:06 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 17:06 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 2.93 seconds (00:00:02)
2025-06-27 17:06 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.34s user, 0.00s system
2025-06-27 17:06 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.20MB
2025-06-27 17:06 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-27 17:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:06 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 17:06 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 17:06 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 0.18 seconds (00:00:00)
2025-06-27 17:06 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 0.17s user, 0.00s system
2025-06-27 17:06 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: 0.01MB
2025-06-27 17:06 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-27 17:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:06 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.09 seconds (00:00:00)
2025-06-27 17:06 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.09s user, 0.00s system
2025-06-27 17:06 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 17:06 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-27 17:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:06 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.02 seconds (00:00:00)
2025-06-27 17:06 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.02s user, 0.00s system
2025-06-27 17:06 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 17:06 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-27 17:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:06 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 0.27 seconds (00:00:00)
2025-06-27 17:06 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 0.39s user, 0.00s system
2025-06-27 17:06 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: 0.78MB
2025-06-27 17:06 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-27 17:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:06 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 17:06 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-27 17:06 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-27 17:06 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 17:06 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-27 17:06 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 17:06 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 17:06 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:06 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250627_L0_755699_label_1_001 took 0.00 seconds (00:00:00)
2025-06-27 17:06 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250627_L0_755699_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 17:06 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250627_L0_755699_label_1_001 memory delta: 0.02MB
2025-06-27 17:06 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250627_L0_755699_label_1_001 completed successfully
2025-06-27 17:06 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250627_L0_755699 took 58.26 seconds (00:00:58)
2025-06-27 17:06 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250627_L0_755699 CPU usage: 40.58s user, 23.89s system
2025-06-27 17:06 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250627_L0_755699 memory delta: 1430.30MB
2025-06-27 17:06 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250627_L0_755699 completed successfully
2025-06-27 17:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:09 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 17:09 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 17:09 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 17:09 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 17:09 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 17:09 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-27 17:09 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-27 17:09 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 17:09 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-27 17:09 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-27 17:09 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-27 17:09 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-27 17:09 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-27 17:09 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-27 17:09 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-27 17:09 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-27 17:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:09 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 17:09 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 13.37 seconds (00:00:13)
2025-06-27 17:09 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 7.16s user, 5.14s system
2025-06-27 17:09 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 389.35MB
2025-06-27 17:09 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-27 17:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:09 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 17:09 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 17:09 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.29 seconds (00:00:00)
2025-06-27 17:09 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.41s user, 0.11s system
2025-06-27 17:09 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 10.93MB
2025-06-27 17:09 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-27 17:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:09 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.17 seconds (00:00:00)
2025-06-27 17:09 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.31s user, 0.02s system
2025-06-27 17:09 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 3.78MB
2025-06-27 17:09 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-27 17:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:09 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.04 seconds (00:00:00)
2025-06-27 17:09 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.06s user, 0.00s system
2025-06-27 17:09 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: 0.18MB
2025-06-27 17:09 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-27 17:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:10 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 53.76 seconds (00:00:53)
2025-06-27 17:10 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 48.45s user, 26.58s system
2025-06-27 17:10 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 1097.29MB
2025-06-27 17:10 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-27 17:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:10 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 17:10 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.02 seconds (00:00:00)
2025-06-27 17:10 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-06-27 17:10 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.13MB
2025-06-27 17:10 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-27 17:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:10 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 17:10 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 5.65 seconds (00:00:05)
2025-06-27 17:10 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.47s user, 0.06s system
2025-06-27 17:10 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: -318.03MB
2025-06-27 17:10 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-27 17:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:10 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 17:10 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 17:10 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 0.22 seconds (00:00:00)
2025-06-27 17:10 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 0.20s user, 0.00s system
2025-06-27 17:10 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: 0.66MB
2025-06-27 17:10 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-27 17:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:10 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.13 seconds (00:00:00)
2025-06-27 17:10 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.12s user, 0.00s system
2025-06-27 17:10 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: 0.02MB
2025-06-27 17:10 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-27 17:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:10 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.04 seconds (00:00:00)
2025-06-27 17:10 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.03s user, 0.00s system
2025-06-27 17:10 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 17:10 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-27 17:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:10 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 0.36 seconds (00:00:00)
2025-06-27 17:10 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 0.45s user, 0.00s system
2025-06-27 17:10 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: 2.61MB
2025-06-27 17:10 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-27 17:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:10 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 17:10 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 0.01 seconds (00:00:00)
2025-06-27 17:10 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-27 17:10 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 17:10 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-27 17:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:10 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 17:10 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 5.35 seconds (00:00:05)
2025-06-27 17:10 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.33s user, 0.00s system
2025-06-27 17:10 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.05MB
2025-06-27 17:10 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-27 17:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:10 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 17:10 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 17:10 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 0.13 seconds (00:00:00)
2025-06-27 17:10 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 0.14s user, 0.00s system
2025-06-27 17:10 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: 0.01MB
2025-06-27 17:10 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-27 17:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:10 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.09 seconds (00:00:00)
2025-06-27 17:10 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.09s user, 0.00s system
2025-06-27 17:10 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 17:10 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-27 17:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:10 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.02 seconds (00:00:00)
2025-06-27 17:10 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.02s user, 0.00s system
2025-06-27 17:10 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 17:10 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-27 17:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:10 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 0.23 seconds (00:00:00)
2025-06-27 17:10 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 0.38s user, 0.00s system
2025-06-27 17:10 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: 0.02MB
2025-06-27 17:10 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-27 17:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:10 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 17:10 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-27 17:10 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 0.02s user, 0.00s system
2025-06-27 17:10 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 17:10 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-27 17:10 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 17:10 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 17:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:10 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-27 17:10 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-27 17:10 | INFO     | ResMonitor.end_timer: CALC_VALUES_SH510050 took 0.01 seconds (00:00:00)
2025-06-27 17:10 | INFO     | ResMonitor.end_timer_cpu: CALC_VALUES_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-27 17:10 | INFO     | ResMonitor.end_memory_monitor: CALC_VALUES_SH510050 memory delta: 0.01MB
2025-06-27 17:10 | INFO     | FactorMonitorContext.__exit__: CALC_VALUES_SH510050 completed successfully
2025-06-27 17:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:10 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_SH510050_20250627_L0_598371
2025-06-27 17:10 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_SH510050_20250627_L0_598371 耗时134.9ms
2025-06-27 17:10 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.14 seconds (00:00:00)
2025-06-27 17:10 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.05s user, 0.02s system
2025-06-27 17:10 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 7.02MB
2025-06-27 17:10 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-06-27 17:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:10 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250627_L0_598371_label_1_001 took 0.00 seconds (00:00:00)
2025-06-27 17:10 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250627_L0_598371_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 17:10 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250627_L0_598371_label_1_001 memory delta: 0.01MB
2025-06-27 17:10 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250627_L0_598371_label_1_001 completed successfully
2025-06-27 17:10 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250627_L0_598371 took 80.71 seconds (00:01:20)
2025-06-27 17:10 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250627_L0_598371 CPU usage: 59.09s user, 32.20s system
2025-06-27 17:10 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250627_L0_598371 memory delta: 1211.10MB
2025-06-27 17:10 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250627_L0_598371 completed successfully
2025-06-27 17:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:18 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 17:18 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 17:18 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 17:18 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 17:18 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 17:18 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-27 17:18 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-27 17:18 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 17:18 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-27 17:18 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-27 17:18 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-27 17:18 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-27 17:18 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-27 17:18 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-27 17:18 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-27 17:18 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-27 17:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:18 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 17:18 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 7.96 seconds (00:00:07)
2025-06-27 17:18 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 3.97s user, 3.53s system
2025-06-27 17:18 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 377.60MB
2025-06-27 17:18 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-27 17:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:18 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 17:18 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 17:18 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.18 seconds (00:00:00)
2025-06-27 17:18 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.20s user, 0.11s system
2025-06-27 17:18 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 9.49MB
2025-06-27 17:18 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-27 17:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:18 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.13 seconds (00:00:00)
2025-06-27 17:18 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.19s user, 0.03s system
2025-06-27 17:18 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 1.36MB
2025-06-27 17:18 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-27 17:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:18 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.02 seconds (00:00:00)
2025-06-27 17:18 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.02s user, 0.02s system
2025-06-27 17:18 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: 1.74MB
2025-06-27 17:18 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-27 17:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:19 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 31.61 seconds (00:00:31)
2025-06-27 17:19 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 33.03s user, 19.30s system
2025-06-27 17:19 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 698.22MB
2025-06-27 17:19 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-27 17:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:19 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 17:19 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.02 seconds (00:00:00)
2025-06-27 17:19 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-27 17:19 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.59MB
2025-06-27 17:19 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-27 17:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:19 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 17:19 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 3.67 seconds (00:00:03)
2025-06-27 17:19 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.38s user, 0.00s system
2025-06-27 17:19 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 2.41MB
2025-06-27 17:19 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-27 17:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:19 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 17:19 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 17:19 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 0.17 seconds (00:00:00)
2025-06-27 17:19 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 0.14s user, 0.02s system
2025-06-27 17:19 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: 0.57MB
2025-06-27 17:19 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-27 17:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:19 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.10 seconds (00:00:00)
2025-06-27 17:19 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.09s user, 0.00s system
2025-06-27 17:19 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: 0.05MB
2025-06-27 17:19 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-27 17:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:19 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.02 seconds (00:00:00)
2025-06-27 17:19 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.03s user, 0.00s system
2025-06-27 17:19 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 17:19 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-27 17:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:19 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 0.27 seconds (00:00:00)
2025-06-27 17:19 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 0.47s user, 0.02s system
2025-06-27 17:19 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: 1.12MB
2025-06-27 17:19 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-27 17:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:19 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 17:19 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 0.01 seconds (00:00:00)
2025-06-27 17:19 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 0.02s user, 0.00s system
2025-06-27 17:19 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 17:19 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-27 17:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:19 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 17:19 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 2.72 seconds (00:00:02)
2025-06-27 17:19 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.34s user, 0.00s system
2025-06-27 17:19 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.10MB
2025-06-27 17:19 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-27 17:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:19 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 17:19 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 17:19 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 0.16 seconds (00:00:00)
2025-06-27 17:19 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 0.16s user, 0.00s system
2025-06-27 17:19 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: 0.01MB
2025-06-27 17:19 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-27 17:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:19 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.10 seconds (00:00:00)
2025-06-27 17:19 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.08s user, 0.00s system
2025-06-27 17:19 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 17:19 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-27 17:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:19 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.02 seconds (00:00:00)
2025-06-27 17:19 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.02s user, 0.00s system
2025-06-27 17:19 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 17:19 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-27 17:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:19 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 0.28 seconds (00:00:00)
2025-06-27 17:19 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 0.41s user, 0.02s system
2025-06-27 17:19 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: 0.02MB
2025-06-27 17:19 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-27 17:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:19 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 17:19 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-27 17:19 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-27 17:19 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 17:19 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-27 17:19 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 17:19 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 17:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:19 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-27 17:19 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-27 17:19 | INFO     | ResMonitor.end_timer: CALC_VALUES_SH510050 took 0.01 seconds (00:00:00)
2025-06-27 17:19 | INFO     | ResMonitor.end_timer_cpu: CALC_VALUES_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-27 17:19 | INFO     | ResMonitor.end_memory_monitor: CALC_VALUES_SH510050 memory delta: 0.01MB
2025-06-27 17:19 | INFO     | FactorMonitorContext.__exit__: CALC_VALUES_SH510050 completed successfully
2025-06-27 17:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:19 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_SH510050_20250627_L0_886805
2025-06-27 17:19 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_SH510050_20250627_L0_886805 耗时143.9ms
2025-06-27 17:19 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.15 seconds (00:00:00)
2025-06-27 17:19 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.03s user, 0.02s system
2025-06-27 17:19 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 7.29MB
2025-06-27 17:19 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-06-27 17:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:19 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250627_L0_886805_label_1_001 took 0.00 seconds (00:00:00)
2025-06-27 17:19 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250627_L0_886805_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 17:19 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250627_L0_886805_label_1_001 memory delta: 0.02MB
2025-06-27 17:19 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250627_L0_886805_label_1_001 completed successfully
2025-06-27 17:19 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250627_L0_886805 took 48.14 seconds (00:00:48)
2025-06-27 17:19 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250627_L0_886805 CPU usage: 39.92s user, 23.19s system
2025-06-27 17:19 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250627_L0_886805 memory delta: 1113.61MB
2025-06-27 17:19 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250627_L0_886805 completed successfully
2025-06-27 17:27 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:27 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 17:27 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 17:27 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 17:27 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 17:27 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 17:27 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-27 17:27 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-27 17:27 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 17:27 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-27 17:27 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-27 17:27 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-27 17:27 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-27 17:27 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-27 17:27 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-27 17:27 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-27 17:27 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-27 17:27 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:27 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 17:27 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 34.80 seconds (00:00:34)
2025-06-27 17:27 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 10.22s user, 9.53s system
2025-06-27 17:27 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 149.02MB
2025-06-27 17:27 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-27 17:27 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:27 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 17:27 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 17:27 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.50 seconds (00:00:00)
2025-06-27 17:27 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.41s user, 0.17s system
2025-06-27 17:27 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 14.36MB
2025-06-27 17:27 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-27 17:27 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:27 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.40 seconds (00:00:00)
2025-06-27 17:27 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.28s user, 0.06s system
2025-06-27 17:27 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 8.86MB
2025-06-27 17:27 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-27 17:27 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:27 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.08 seconds (00:00:00)
2025-06-27 17:27 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.05s user, 0.05s system
2025-06-27 17:27 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: -0.59MB
2025-06-27 17:27 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-27 17:27 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:29 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 98.08 seconds (00:01:38)
2025-06-27 17:29 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 40.16s user, 22.42s system
2025-06-27 17:29 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: -32.80MB
2025-06-27 17:29 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-27 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:29 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 17:29 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.04 seconds (00:00:00)
2025-06-27 17:29 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-06-27 17:29 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.69MB
2025-06-27 17:29 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-27 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:29 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 17:29 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 3.64 seconds (00:00:03)
2025-06-27 17:29 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.38s user, 0.00s system
2025-06-27 17:29 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: -18.36MB
2025-06-27 17:29 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-27 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:29 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 17:29 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 17:29 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 0.16 seconds (00:00:00)
2025-06-27 17:29 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 0.14s user, 0.00s system
2025-06-27 17:29 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: 0.25MB
2025-06-27 17:29 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-27 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:29 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.11 seconds (00:00:00)
2025-06-27 17:29 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.11s user, 0.00s system
2025-06-27 17:29 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: 0.45MB
2025-06-27 17:29 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-27 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:29 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.03 seconds (00:00:00)
2025-06-27 17:29 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.03s user, 0.00s system
2025-06-27 17:29 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 17:29 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-27 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:29 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 0.29 seconds (00:00:00)
2025-06-27 17:29 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 0.42s user, 0.00s system
2025-06-27 17:29 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: 1.98MB
2025-06-27 17:29 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-27 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:29 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 17:29 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 0.01 seconds (00:00:00)
2025-06-27 17:29 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-27 17:29 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 17:29 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-27 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:29 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 17:29 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 3.03 seconds (00:00:03)
2025-06-27 17:29 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.34s user, 0.00s system
2025-06-27 17:29 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: -0.57MB
2025-06-27 17:29 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-27 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:29 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 17:29 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 17:29 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 0.18 seconds (00:00:00)
2025-06-27 17:29 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 0.17s user, 0.00s system
2025-06-27 17:29 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 17:29 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-27 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:29 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.10 seconds (00:00:00)
2025-06-27 17:29 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.11s user, 0.00s system
2025-06-27 17:29 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 17:29 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-27 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:29 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.03 seconds (00:00:00)
2025-06-27 17:29 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.03s user, 0.00s system
2025-06-27 17:29 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 17:29 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-27 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:29 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 0.27 seconds (00:00:00)
2025-06-27 17:29 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 0.39s user, 0.00s system
2025-06-27 17:29 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: 0.02MB
2025-06-27 17:29 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-27 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:29 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 17:29 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-27 17:29 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-27 17:29 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 17:29 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-27 17:29 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 17:29 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:29 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-27 17:29 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-27 17:29 | INFO     | ResMonitor.end_timer: CALC_VALUES_SH510050 took 0.01 seconds (00:00:00)
2025-06-27 17:29 | INFO     | ResMonitor.end_timer_cpu: CALC_VALUES_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-27 17:29 | INFO     | ResMonitor.end_memory_monitor: CALC_VALUES_SH510050 memory delta: 0.01MB
2025-06-27 17:29 | INFO     | FactorMonitorContext.__exit__: CALC_VALUES_SH510050 completed successfully
2025-06-27 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:29 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_SH510050_20250627_L0_540561
2025-06-27 17:29 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_SH510050_20250627_L0_540561 耗时163.7ms
2025-06-27 17:29 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.17 seconds (00:00:00)
2025-06-27 17:29 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.02s user, 0.05s system
2025-06-27 17:29 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 7.26MB
2025-06-27 17:29 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-06-27 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:29 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250627_L0_540561_label_1_001 took 0.00 seconds (00:00:00)
2025-06-27 17:29 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250627_L0_540561_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 17:29 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250627_L0_540561_label_1_001 memory delta: 0.02MB
2025-06-27 17:29 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250627_L0_540561_label_1_001 completed successfully
2025-06-27 17:29 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250627_L0_540561 took 142.92 seconds (00:02:22)
2025-06-27 17:29 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250627_L0_540561 CPU usage: 53.78s user, 32.62s system
2025-06-27 17:29 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250627_L0_540561 memory delta: 155.21MB
2025-06-27 17:29 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250627_L0_540561 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 17:34 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 17:34 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 17:34 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 17:34 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 17:34 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-27 17:34 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-27 17:34 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 17:34 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-27 17:34 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-27 17:34 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-27 17:34 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-27 17:34 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-27 17:34 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-27 17:34 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-27 17:34 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 9.59 seconds (00:00:09)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 4.33s user, 3.91s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 186.86MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 17:34 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.20 seconds (00:00:00)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.25s user, 0.11s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 11.56MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.12 seconds (00:00:00)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.12s user, 0.08s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 6.87MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.03 seconds (00:00:00)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.03s user, 0.02s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: 2.47MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 33.94 seconds (00:00:33)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 32.28s user, 18.81s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 785.72MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.02 seconds (00:00:00)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.68MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 2.96 seconds (00:00:02)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.36s user, 0.02s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 2.66MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 17:34 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 0.19 seconds (00:00:00)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 0.16s user, 0.00s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: 0.66MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.09 seconds (00:00:00)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.09s user, 0.00s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: 0.07MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.02 seconds (00:00:00)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.02s user, 0.00s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 0.23 seconds (00:00:00)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 0.36s user, 0.00s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: 1.20MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 0.01 seconds (00:00:00)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 2.60 seconds (00:00:02)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.34s user, 0.00s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.18MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 17:34 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 0.14 seconds (00:00:00)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 0.14s user, 0.00s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.10 seconds (00:00:00)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.09s user, 0.00s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.02 seconds (00:00:00)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.02s user, 0.00s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 0.22 seconds (00:00:00)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 0.44s user, 0.06s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: 0.80MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 0.02s user, 0.00s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-27 17:34 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 17:34 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-27 17:34 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: CALC_VALUES_SH510050 took 0.02 seconds (00:00:00)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: CALC_VALUES_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: CALC_VALUES_SH510050 memory delta: 0.01MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: CALC_VALUES_SH510050 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_SH510050_20250627_L0_035766
2025-06-27 17:34 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_SH510050_20250627_L0_035766 耗时170.7ms
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.17 seconds (00:00:00)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.03s user, 0.02s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 8.00MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250627_L0_035766_label_1_001 took 0.00 seconds (00:00:00)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250627_L0_035766_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250627_L0_035766_label_1_001 memory delta: 0.02MB
2025-06-27 17:34 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250627_L0_035766_label_1_001 completed successfully
2025-06-27 17:34 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250627_L0_035766 took 51.37 seconds (00:00:51)
2025-06-27 17:34 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250627_L0_035766 CPU usage: 39.50s user, 23.25s system
2025-06-27 17:34 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250627_L0_035766 memory delta: 1021.37MB
2025-06-27 17:34 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250627_L0_035766 completed successfully
2025-06-27 17:59 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 17:59 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 18:07 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 18:07 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 18:09 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 18:09 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 18:09 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 18:09 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 18:09 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 18:09 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 18:11 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 18:11 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 18:11 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 18:11 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 18:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:35 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-27 18:35 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-27 18:35 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-27 18:35 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-27 18:35 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-27 18:35 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-27 18:35 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-27 18:35 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-27 18:35 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-27 18:35 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-27 18:35 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-27 18:35 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-27 18:35 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-27 18:35 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-27 18:35 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-27 18:35 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-27 18:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:35 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 18:35 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 9.88 seconds (00:00:09)
2025-06-27 18:35 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 4.38s user, 4.02s system
2025-06-27 18:35 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 15.92MB
2025-06-27 18:35 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-27 18:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:35 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 18:35 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 18:35 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.23 seconds (00:00:00)
2025-06-27 18:35 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.23s user, 0.08s system
2025-06-27 18:35 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: -0.96MB
2025-06-27 18:35 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-27 18:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:35 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.13 seconds (00:00:00)
2025-06-27 18:35 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.19s user, 0.05s system
2025-06-27 18:35 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 1.13MB
2025-06-27 18:35 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-27 18:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:35 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.03 seconds (00:00:00)
2025-06-27 18:35 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.05s user, 0.02s system
2025-06-27 18:35 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: 0.04MB
2025-06-27 18:35 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-27 18:35 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:36 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 39.71 seconds (00:00:39)
2025-06-27 18:36 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 32.88s user, 20.20s system
2025-06-27 18:36 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 891.19MB
2025-06-27 18:36 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-27 18:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:36 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 18:36 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-06-27 18:36 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-27 18:36 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.59MB
2025-06-27 18:36 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-27 18:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:36 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 18:36 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 2.69 seconds (00:00:02)
2025-06-27 18:36 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.33s user, 0.02s system
2025-06-27 18:36 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 1.98MB
2025-06-27 18:36 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-27 18:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:36 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 18:36 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 18:36 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 0.17 seconds (00:00:00)
2025-06-27 18:36 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 0.16s user, 0.00s system
2025-06-27 18:36 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: 0.16MB
2025-06-27 18:36 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-27 18:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:36 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.10 seconds (00:00:00)
2025-06-27 18:36 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.08s user, 0.00s system
2025-06-27 18:36 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: 0.05MB
2025-06-27 18:36 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-27 18:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:36 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.03 seconds (00:00:00)
2025-06-27 18:36 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.03s user, 0.00s system
2025-06-27 18:36 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 18:36 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-27 18:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:36 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 0.24 seconds (00:00:00)
2025-06-27 18:36 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 0.38s user, 0.00s system
2025-06-27 18:36 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: 1.83MB
2025-06-27 18:36 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-27 18:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:36 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 18:36 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 0.01 seconds (00:00:00)
2025-06-27 18:36 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 0.03s user, 0.00s system
2025-06-27 18:36 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-27 18:36 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-27 18:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:36 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-27 18:36 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 2.93 seconds (00:00:02)
2025-06-27 18:36 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.38s user, 0.00s system
2025-06-27 18:36 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.18MB
2025-06-27 18:36 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-27 18:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:36 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-27 18:36 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-27 18:36 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 0.15 seconds (00:00:00)
2025-06-27 18:36 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 0.16s user, 0.00s system
2025-06-27 18:36 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 18:36 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-27 18:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:36 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.10 seconds (00:00:00)
2025-06-27 18:36 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.11s user, 0.00s system
2025-06-27 18:36 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 18:36 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-27 18:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:36 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.02 seconds (00:00:00)
2025-06-27 18:36 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.02s user, 0.00s system
2025-06-27 18:36 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 18:36 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-27 18:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:36 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 0.23 seconds (00:00:00)
2025-06-27 18:36 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 0.36s user, 0.00s system
2025-06-27 18:36 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: 0.01MB
2025-06-27 18:36 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-27 18:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:36 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-27 18:36 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-27 18:36 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 0.02s user, 0.00s system
2025-06-27 18:36 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-27 18:36 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-27 18:36 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 18:36 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 18:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:36 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-27 18:36 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-27 18:36 | INFO     | ResMonitor.end_timer: CALC_VALUES_SH510050 took 0.01 seconds (00:00:00)
2025-06-27 18:36 | INFO     | ResMonitor.end_timer_cpu: CALC_VALUES_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-27 18:36 | INFO     | ResMonitor.end_memory_monitor: CALC_VALUES_SH510050 memory delta: 0.01MB
2025-06-27 18:36 | INFO     | FactorMonitorContext.__exit__: CALC_VALUES_SH510050 completed successfully
2025-06-27 18:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:36 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_SH510050_20250627_L0_331987
2025-06-27 18:36 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_SH510050_20250627_L0_331987 耗时160.1ms
2025-06-27 18:36 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.16 seconds (00:00:00)
2025-06-27 18:36 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.02s user, 0.05s system
2025-06-27 18:36 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 7.00MB
2025-06-27 18:36 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-06-27 18:36 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-27 18:36 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250627_L0_331987_label_1_001 took 0.00 seconds (00:00:00)
2025-06-27 18:36 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250627_L0_331987_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-06-27 18:36 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250627_L0_331987_label_1_001 memory delta: 0.02MB
2025-06-27 18:36 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250627_L0_331987_label_1_001 completed successfully
2025-06-27 18:36 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250627_L0_331987 took 57.48 seconds (00:00:57)
2025-06-27 18:36 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250627_L0_331987 CPU usage: 40.12s user, 24.64s system
2025-06-27 18:36 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250627_L0_331987 memory delta: 932.28MB
2025-06-27 18:36 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250627_L0_331987 completed successfully
2025-06-27 18:36 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 18:36 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 18:37 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 18:37 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 19:27 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 19:27 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 19:28 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 19:28 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 19:32 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 19:32 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 19:32 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 19:32 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-27 19:38 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-27 19:38 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
