2025-06-23 15:21 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-23 15:21 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-23 15:21 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-23 15:21 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-23 15:21 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-23 15:21 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-23 15:21 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-23 15:21 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-23 15:21 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-23 15:21 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(9): 9个
2025-06-23 15:21 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 7 列
2025-06-23 15:21 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了2个低方差特征
2025-06-23 15:21 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['ftool_ABSOLUTE(roc_10)', 'ftool_ABSOLUTE(JZ001_10_20)']
2025-06-23 15:21 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-23 15:21 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 13 (基础5+归一化0+保持0+标签1+筛选后特征7)
2025-06-23 15:21 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 13
2025-06-23 15:21 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-23 15:23 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-23 15:23 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-23 15:23 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-23 15:23 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-23 15:23 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-23 15:23 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-23 15:23 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-23 15:23 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-23 15:23 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-23 15:23 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-23 15:23 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-23 15:23 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-23 15:23 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-23 15:23 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ004_98', 'tr_ma20', 'b_macd_wt_hist', 'b_atr_60', 'JZ014_troc_mul3_9', 'MAX5', 'smadiff5', 'tr_ma10', 'JZ012_34', 'KMID']...
2025-06-23 15:23 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-23 15:23 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-23 15:23 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-23 15:23 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
