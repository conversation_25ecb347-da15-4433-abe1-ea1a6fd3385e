''' 支持 多品种+多特征 的 因子遗传算法挖掘 并自动化筛选合格因子 '''
from os import cpu_count
from pathlib import Path
import pandas as pd
from datetime import datetime
from common.cls_base import BaseObj, MonitorContext, ResMonitor
from config import REPORTS_DIR, settings, cfg_mine
from config import fitness_params, mine_runnum, rankic_params, sr_params
from core.polyfactor.gplearn.gplearn_miner import FctsGPMiner
from core.polyfactor.gplearn.functions import _function_map
from datafeed.features.feature_utils import FeatPreprocessing
from factor.factorloader import FactorLoader
from factor.pipeline_utils import pl_results_to_csv
from factorzoo import factorzoo
from factorzoo import FactorMonitorContext, FactorZooRunManager
from factorzoo import factorstore


def analyze_and_report(batch_id: str):
    """分析并报告性能"""
    run_manager = FactorZooRunManager()
    analysis_report = run_manager.get_gp_pipeline_analysis(batch_id=batch_id)

    print("\n" + "="*80)
    print("📊 GP挖掘流水线性能分析报告")
    print("="*80)

    if not analysis_report['steps_performance']:
        print("    ⚠️  无流水线性能数据")
    else:
        steps_performance = analysis_report['steps_performance']
        overall_stats = analysis_report['overall_stats']
        
        print("\n🔍 流水线各步骤性能概览:")
        print(f"{'步骤名称':<20} {'运行次数':<8} {'平均耗时(s)':<12} {'平均内存(MB)':<12} {'成功率':<8}")
        print("-"*65)
        
        for step_name, stats in steps_performance.items():
            avg_time = stats['avg_time']
            avg_memory = stats['avg_memory']
            success_rate = stats['success_rate']
            print(f"{step_name:<20} {stats['run_count']:<8} {avg_time:<12.1f} {avg_memory:<12.1f} {success_rate:<7.1f}%")
        
        print("\n📈 流水线整体统计:")
        print(f"    🔧 总操作次数: {overall_stats['total_operations']}")
        print(f"    ⏱️  平均总耗时: {overall_stats['avg_total_time']:.1f} s")
        print(f"    📊 累计计算时间: {overall_stats['sum_total_time']:.1f} s")
        if overall_stats['bottleneck_step']:
            print(f"    🔥 性能瓶颈: {overall_stats['bottleneck_step']}")
        
        # 计算各步骤耗时占比
        if steps_performance:
            total_time = sum(stats['avg_time'] * stats['run_count'] for stats in steps_performance.values())
            print("\n⚡ 各步骤耗时占比:")
            sorted_steps = sorted(steps_performance.items(), key=lambda x: x[1]['avg_time'], reverse=True)
            for step_name, stats in sorted_steps:
                step_time = stats['avg_time'] * stats['run_count']
                percentage = (step_time / total_time * 100) if total_time > 0 else 0
                print(f"    {step_name:<20}: {percentage:>6.1f}%")

    # 如果有批次ID，显示本次运行的详细统计
    batch_summary = run_manager.get_batch_performance_summary(batch_id)
    if batch_summary and batch_summary.get('steps_breakdown'):
        print(f"\n🎯 本次运行批次 {batch_id} 性能明细:")
        print(f"{'操作类型':<20} {'执行次数':<8} {'总耗时(s)':<12} {'平均耗时(s)':<12}")
        print("-"*64)
        
        for step in batch_summary['steps_breakdown']:
            op_type = step['operation_type']
            count = step['count']
            total_time = step['total_time']  # 已经是秒
            avg_time = total_time / count if count > 0 else 0
            print(f"{op_type:<20} {count:<8} {total_time:<12.3f} {avg_time:<12.3f}")
            
        print(f"\n📊 批次总体统计:")
        print(f"    总操作数: {batch_summary['total_operations']}")
        print(f"    操作类型: {batch_summary['operation_types']}")
        print(f"    总计算时间: {batch_summary['total_compute_time']:.3f}s")
        print(f"    平均内存: {batch_summary['avg_memory_mb']:.1f}MB")
        print(f"    成功率: {batch_summary['success_rate']:.1f}%")

    print("\n" + "="*80)
    print("✨ 性能监控报告完成，详细数据已保存到FactorZoo性能日志表")
    print("="*80)

if __name__ == "__main__":
    # """ ============================ 1. 加载配置 ============================= """
    feat_data_path = REPORTS_DIR / 'features' / 'df_all_selected.csv' # 特征数据地址
    # 创建全局监控器用于独立计时
    global_monitor = ResMonitor()
    global_monitor.start_timer("global_process")
    try:
        ''' =============================== 0. 全局监控与批次初始化 =============================== '''
        # 整体流程监控
        global_uid = BaseObj.gen_ordered_uid()
        current_date = datetime.now().strftime('%Y%m%d')
        batch_id = f"GP_MULTI_{current_date}_L0_{global_uid[-6:]}"
        BaseObj.log(f"🚀 开始因子挖掘流程...全局批次ID: {batch_id}................", level="INFO")
        
        with MonitorContext(f"GP_MINING_{batch_id}") as global_ctx:
            ''' =============================== 1. 数据加载与预处理 =============================== '''
            BaseObj.log("STEP01: 加载特征数据...", level="INFO")
            df_all = pd.read_csv(feat_data_path, index_col=0)
            
            # 1.1 如果X和y没有在feat_extract时norm, 则先norm
            feat_norm_model = cfg_mine.feat.norm.model
            mine_norm_model = cfg_mine.mine.norm.model
            if feat_norm_model == 'robust' and mine_norm_model == 'linear':
                BaseObj.log("⚠️  特征归一化模型为robust, 挖掘模型为linear, 将先norm特征, 再挖掘", level="WARNING")
                df_all = FeatPreprocessing.normalize_df_all(df_all, model_type="linear", cfg=cfg_mine)
              
            if df_all.index.name != 'datetime' or 'symbol' not in df_all.columns:
                BaseObj.log('df的index必须为datetime, 且必须包含symbol列', level="ERROR")
                exit(1)
            df_all = df_all.set_index('symbol', append=True)
            df_all = df_all.dropna(axis=0, how='any')
            
            ''' =============================== 2. 数据预处理 + 初始化环境 =============================== '''
            # 2.1 装仓数据列: ret; 挖掘筛选: 某步用t_return
            nextbar_open = cfg_mine.mine.run.nextbar # 是否延迟到下一bar开盘open, 否则用close
            rankic_tdelay = cfg_mine.mine.filter.rankic.tdelay # 挖掘后筛选时, 用t期收益率算ic
            if nextbar_open: # NOTE: 'ret'是为了装仓位用; 'label_'是为了训练用
                df_all['ret'] = df_all['open'].shift(-1) / df_all['open'] - 1  # ret不是label不能考虑T期
                y_raw_t_return = (df_all['open'].shift(-rankic_tdelay) / df_all['open'] - 1).copy()  # t期收益率
            else:    
                df_all['ret'] = df_all['close'].shift(-1) / df_all['close'] - 1
                y_raw_t_return = (df_all['close'].shift(-rankic_tdelay) / df_all['close'] - 1).copy()
            
            # 2.2 分离特征和标签
            label_cols = [col for col in df_all.columns if col.startswith('label_')]
            X_all = df_all.drop(columns=['ret', *label_cols])
            y_raw = df_all['ret'].copy()
            
            # 检查是否有label列
            if not label_cols:
                BaseObj.log("⚠️  未找到label列!", level='ERROR')
                exit(1)
            
            # 2.3 挖掘参数配置
            func_set = list(_function_map.keys())
            job_num = cfg_mine.get('mine.run.jobnum')  # 从配置中获取job数
            if job_num == -1:
                job_num = cpu_count()-1  # 获取当前电脑的最大处理器核心数
            elif job_num <= cpu_count():
                job_num = job_num  # 使用指定的核心数
            else:
                job_num = cpu_count()-1  # 使用当前电脑的最大处理器核心数
            fee_rate = cfg_mine.mine.run.fee  # 万1手续费
            free_rate = cfg_mine.mine.run.free    # 无风险利率
            
            discovered_factors = {} # 因子库（按symbol和label分类存储）
            
            BaseObj.log(f"STEP02: 延迟1个BAR={nextbar_open}, T期={rankic_tdelay}, {len(label_cols)} 个预测目标: {label_cols}", level="INFO")
            
            ''' =============================== 3. 多轮挖掘配置 =============================== '''
            # 多轮挖掘参数（利用随机性发现更多优质因子）
            n_runs_per_target = mine_runnum()  # 每个目标挖掘runnum轮，利用随机性
            BaseObj.log(f"STEP03: 手续费={fee_rate}, 无风险={free_rate}, 并行核数={job_num}, 挖掘轮次={mine_runnum()}", level="INFO")
            
            ''' =============================== 4. 品种 × 目标 × 轮次 三层挖掘 =============================== '''
            symbols = df_all.index.get_level_values('symbol').unique()
            
            # 外层循环：遍历每个品种（symbol）
            for symbol in symbols:
                # 提取该品种的所有数据
                X_symbol = X_all.xs(symbol, level='symbol')
                y_raw_symbol = y_raw.xs(symbol, level='symbol')  # 用于评估的原始收益率
                y_raw_t_return_symbol = y_raw_t_return.xs(symbol, level='symbol')
                feature_names = X_symbol.columns.tolist() # OHLC + 选中的特征(本例是normed)
                
                # 初始化该品种的因子库
                discovered_factors[symbol] = {}
                
                # 中层循环：遍历每个预测目标（label）
                for label_col in label_cols:
                    BaseObj.log(f"STEP04: 挖掘品种: {symbol} | 目标: {label_col}", level="INFO")
                    
                    # 获取当前label的目标数据
                    y_label_symbol = df_all[label_col].xs(symbol, level='symbol')
                    
                    # 初始化该目标的因子列表
                    discovered_factors[symbol][label_col] = []
                    all_run_factors = []      # 收集所有轮次的f类因子
                    all_run_pj_factors = []   # 收集所有轮次的pj类因子
                    
                    # 内层循环：多轮挖掘（利用随机性）
                    for i in range(n_runs_per_target):
                        print(f"\n    🔄 第 {i+1}/{n_runs_per_target} 轮挖掘")
                        run_uid = FctsGPMiner.gen_ordered_uid() # 生成唯一标识符             
                        run_prefix = f"{symbol}_{label_col}_R{i+1:02d}"
                        
                        # 执行GP挖掘 - 监控GP挖掘步骤
                        try:
                            with FactorMonitorContext(f"GP_MINE_{run_prefix}", 
                                                       operation_type='gp_mining', 
                                                       batch_id=batch_id, 
                                                       symbol=symbol,
                                                       data_size=len(X_symbol)) as mine_ctx:
                                mine_selected = FctsGPMiner.mine(
                                    X_train=X_symbol,
                                    feature_names=feature_names,
                                    y_train=y_label_symbol,      
                                    y_raw=y_raw_symbol,          # 原始收益率用于评估
                                    symbol=symbol,
                                    func_set=list(_function_map.keys()), 
                                    job_num=job_num,
                                    g_uid=f"{symbol}_{label_col}_run{run_uid}",
                                    **fitness_params()
                                )
                                # 记录结果备注
                            
                            # 加载计算因子值并自动筛选
                            if mine_selected:
                                # 因子值计算步骤 - 监控计算耗时
                                with FactorMonitorContext(f"CALC_{run_prefix}", 
                                                           operation_type='factor_calculation', 
                                                           batch_id=batch_id,
                                                           symbol=symbol,
                                                           data_size=len(mine_selected)) as calc_ctx:
                                    fct_df = FactorLoader.get_fct_df(X_symbol,
                                                                     model_type=cfg_mine.mine.norm.model,
                                                                     norm_type='X', 
                                                                     fcts=mine_selected)
                                    # 计算完成
                                
                                # Skew筛选步骤 - 监控筛选效果
                                with FactorMonitorContext(f"SKEW_{run_prefix}", 
                                                           operation_type='select_by_skew', 
                                                           batch_id=batch_id,
                                                           symbol=symbol, 
                                                           data_size=len(mine_selected)) as skew_ctx:
                                    skewthresh = cfg_mine.mine.filter.skewthresh
                                    skew_selected = FctsGPMiner.select_by_skew(fct_df, skew_threshold=skewthresh)
                                    skew_count = len(skew_selected) if skew_selected else 0
                                    # Skew筛选完成
                            else:
                                skew_selected = None
                                
                            if skew_selected:
                                # Kurt筛选步骤 - 监控筛选效果
                                with FactorMonitorContext(f"KURT_{run_prefix}", 
                                                           operation_type='select_by_kurt', 
                                                           batch_id=batch_id,
                                                           symbol=symbol,
                                                           data_size=len(skew_selected)) as kurt_ctx:
                                    kurtthresh = cfg_mine.mine.filter.kurtthresh
                                    kurt_selected = FctsGPMiner.select_by_kurt(fct_df[skew_selected], kurt_threshold=kurtthresh)
                                    kurt_count = len(kurt_selected) if kurt_selected else 0
                                    # Kurt筛选完成
                            else:
                                kurt_selected = None
                                
                            if kurt_selected:
                                # 指标筛选步骤 (SIC/SR) - 监控筛选效果和计算耗时
                                metric2use = cfg_mine.mine.filter.metric2use
                                with FactorMonitorContext(f"METRIC_{run_prefix}", 
                                                           operation_type=f'select_by_{metric2use}', 
                                                           batch_id=batch_id,
                                                           symbol=symbol,
                                                           data_size=len(kurt_selected)) as metric_ctx:
                                    if metric2use == 'sic':
                                        metric_top, metric_pj = FctsGPMiner.select_by_rolling_rankic(
                                                                                              fct_df[kurt_selected], 
                                                                                              y_label_symbol,
                                                                                              y_raw_t_return_symbol,
                                                                                              symbol,
                                                                                              **rankic_params()
                                                                                              )
                                        # 获取因子名称列表: f类 + pj类
                                        metric_selected = metric_top.index.tolist() if not metric_top.empty else []
                                        metric_pj_selected = metric_pj.index.tolist() if not metric_pj.empty else []
                                        
                                    elif metric2use == 'sr':
                                        sr_top, sr_pj = FctsGPMiner.select_by_sharp(fct_df[kurt_selected], 
                                                                                      y_label_symbol,
                                                                                      y_raw_symbol,
                                                                                      symbol,
                                                                                      **sr_params()
                                                                                      )
                                        # 获取因子名称列表: f类 + pj类
                                        metric_selected = sr_top.index.tolist() if not sr_top.empty else []
                                        metric_pj_selected = sr_pj.index.tolist() if not sr_pj.empty else []
                                    
                                    metric_count = len(metric_selected) if metric_selected else 0
                                    pj_count = len(metric_pj_selected) if metric_pj_selected else 0
                                    # 指标筛选完成
                            else:
                                metric_selected = None
                                metric_pj_selected = None
                                
                            if metric_selected or metric_pj_selected:
                                # 相关性筛选步骤 - 监控最终筛选效果
                                with FactorMonitorContext(f"CORR_{run_prefix}", 
                                                           operation_type='select_by_corr', 
                                                           batch_id=batch_id,
                                                           symbol=symbol,
                                                           data_size=len(metric_selected or []) + len(metric_pj_selected or [])) as corr_ctx:
                                    
                                    # f类因子相关性筛选
                                    final_selected = FctsGPMiner.select_by_corr(fct_df[metric_selected], 
                                                                                y_norm_se=y_label_symbol,
                                                                                corr_threshold=cfg_mine.mine.filter.corrthresh) if metric_selected else []
                                    # pj类因子相关性筛选
                                    final_pj_selected = FctsGPMiner.select_by_corr(fct_df[metric_pj_selected], 
                                                                                   y_norm_se=y_label_symbol,
                                                                                   corr_threshold=cfg_mine.mine.filter.corrthresh) if metric_pj_selected else []
                                    
                                    final_count = len(final_selected) if final_selected else 0
                                    final_pj_count = len(final_pj_selected) if final_pj_selected else 0
                                    # 相关性筛选完成
                                
                                if final_selected or final_pj_selected:
                                    # 收集f类因子和pj因子到不同列表
                                    if final_selected:
                                        all_run_factors.extend(final_selected)
                                        print(f"      ✅ 本轮发现f类因子 {len(final_selected)} 个")
                                    if final_pj_selected:
                                        # 存储pj类因子
                                        all_run_pj_factors.extend(final_pj_selected)
                                        print(f"      ✅ 本轮发现pj类因子 {len(final_pj_selected)} 个")
                                    
                                    # 显示部分因子（f类和pj类各显示前2个）
                                    if final_selected:
                                        display_count = min(2, len(final_selected))
                                        for j, factor in enumerate(final_selected[:display_count], 1):
                                            print(f"        f{j}: {factor}")
                                    if final_pj_selected:
                                        display_count = min(2, len(final_pj_selected))
                                        for j, factor in enumerate(final_pj_selected[:display_count], 1):
                                            print(f"        pj{j}: {factor}")
                                else:
                                    print("      ⚠️  本轮未发现因子")
                            else:
                                print("      ⚠️  本轮未发现因子")
                            
                        except Exception as e:
                            print(f"      ❌ 本轮挖掘出错: {str(e)}")
                            continue
                    
                    # 跨轮次因子去重和筛选
                    total_candidates = len(all_run_factors) + len(all_run_pj_factors)
                    if total_candidates > 0:
                        print(f"      总计候选因子: f类{len(all_run_factors)}个 + pj类{len(all_run_pj_factors)}个")
                        
                        # f类因子去重（基于表达式字符串）
                        unique_factors = []
                        seen_expressions = set()
                        for factor in all_run_factors:
                            factor_str = str(factor)
                            if factor_str not in seen_expressions:
                                unique_factors.append(factor)
                                seen_expressions.add(factor_str)
                        
                        # pj类因子去重
                        unique_pj_factors = []
                        seen_pj_expressions = set()
                        for factor in all_run_pj_factors:
                            factor_str = str(factor)
                            if factor_str not in seen_pj_expressions and factor_str not in seen_expressions:
                                unique_pj_factors.append(factor)
                                seen_pj_expressions.add(factor_str)
                        
                        # 保存该目标的所有去重因子
                        discovered_factors[symbol][label_col] = unique_factors
                        # 为pj因子创建独立存储结构
                        if symbol not in discovered_factors:
                            discovered_factors[symbol] = {}
                        if f"{label_col}_pj" not in discovered_factors[symbol]:
                            discovered_factors[symbol][f"{label_col}_pj"] = []
                        discovered_factors[symbol][f"{label_col}_pj"] = unique_pj_factors
                        
                        total_unique = len(unique_factors) + len(unique_pj_factors)
                        print(f"      最终收集: {total_unique} 个去重因子（f类{len(unique_factors)} + pj类{len(unique_pj_factors)}）")
                        
                    else:
                        print(f"    ❌ 该目标未发现任何因子")
            
            ''' =============================== 5. 结果统计与保存 =============================== '''
            BaseObj.log("STEP05: 结果统计并保持csv...", level="INFO")
            output_path = REPORTS_DIR / 'gplearn' / 'discovered_factors_multirun.csv'
            pl_results_to_csv(discovered_factors, str(output_path))

            ''' =============================== 6. FactorZoo批次创建与因子入库 =============================== '''
            # 统计总因子数以决定是否入库
            grand_total = sum(
                len(factors)
                for symbol_data in discovered_factors.values()
                for factors in symbol_data.values()
            )
            if grand_total > 0:
                print(f"\n🏦 开始入库到FactorZoo...")
                
                # 从toml配置中读取数据源配置信息
                frequency = getattr(cfg_mine.data_source, 'freq', 'D') if hasattr(cfg_mine, 'data_source') else 'D'

                # 6.1 按照新批次概念：同品种+同周期+同起始日期 = 同批次
                # 处理datetime索引，可能是字符串格式
                datetime_values = df_all.index.get_level_values('datetime')
                if isinstance(datetime_values[0], str):
                    # 如果是字符串，先转换为datetime
                    datetime_values = pd.to_datetime(datetime_values)
                
                start_date = datetime_values.min().strftime('%Y%m%d')
                end_date = datetime_values.max().strftime('%Y%m%d')

                # ✨ 新增：因子值持久化到FactorValueManager
                print(f"\n💾 开始因子值持久化...")
                
                # 6.2 按品种持久化因子值
                total_persistence_success = 0
                total_persistence_failed = 0
                
                for symbol in symbols:
                    # 统计该品种的因子数量
                    symbol_factor_count = sum(len(discovered_factors[symbol][label]) for label in label_cols)
                    if symbol_factor_count == 0:
                        print(f"    ⚠️  品种 {symbol} 无因子，跳过持久化")
                        continue
                    
                    # 为每个品种创建因子值批次ID
                    symbol_batch_id = f"GP_{symbol}_{current_date}_L0_{global_uid[-6:]}"
                    print(f"\n📋 持久化品种因子值: {symbol_batch_id}")
                    
                    try:
                        # 获取该品种的基础数据
                        X_symbol = X_all.xs(symbol, level='symbol')
                        
                        # 智能检测可用的价格列 - 根据配置文件中的base2keep设置
                        available_price_cols = []
                        # 首先尝试配置文件中指定的基础列
                        preferred_cols = ['open', 'high', 'low', 'close']
                        for col in preferred_cols:
                            if col in df_all.columns:
                                available_price_cols.append(col)
                        
                        # 如果还有其他可能的价格相关列，也加入
                        for col in ['volume', 'amount', 'open_interest']:
                            if col in df_all.columns:
                                available_price_cols.append(col)
                        
                        # 使用可用的价格列作为基础数据
                        base_data_symbol = df_all[available_price_cols].xs(symbol, level='symbol')
                        print(f"    📊 使用基础数据列: {available_price_cols}")
                        
                        # 计算收益率列以匹配原库格式 (仅保留open, close, ret, ret_open)
                        if 'open' in base_data_symbol.columns and 'close' in base_data_symbol.columns:
                            # 计算收益率 - 与文件开始部分逻辑保持一致
                            base_data_symbol = base_data_symbol.copy()
                            
                            # ret: 收盘价收益率 (对应close.shift(-1) / close - 1)
                            base_data_symbol['ret'] = base_data_symbol['close'].shift(-1) / base_data_symbol['close'] - 1
                            # ret_open: 开盘价相对前一日收盘的收益率 (对应open / close.shift(1) - 1)
                            base_data_symbol['ret_open'] = base_data_symbol['open'] / base_data_symbol['open'].shift(1) - 1
                            
                            # 填充缺失值
                            base_data_symbol['ret'] = base_data_symbol['ret'].fillna(0)
                            base_data_symbol['ret_open'] = base_data_symbol['ret_open'].fillna(0)
                            
                            print(f"    📊 收益率计算完成: ret(close收益率), ret_open(开盘收益率)")
                        else:
                            print(f"    ⚠️  缺少open或close列，无法计算收益率，保持原格式")
                        
                        # 收集该品种所有标签的因子
                        all_symbol_factors = []      # f类因子
                        all_symbol_pj_factors = []   # pj类因子
                        factor_meta_info = {}
                        
                        for label_col in label_cols:
                            # 收集f类因子
                            if label_col in discovered_factors[symbol]:
                                factors = discovered_factors[symbol][label_col]
                                if factors:
                                    all_symbol_factors.extend(factors)
                                    # 记录每个因子对应的标签
                                    for factor in factors:
                                        factor_meta_info[str(factor)] = {
                                            'target_label': label_col,
                                            'symbol': symbol,
                                            'creation_method': 'gp_mining',
                                            'pipeline_step': 'L0',
                                            'factor_type': 'f'
                                        }
                        
                            # 收集pj类因子
                            pj_key = f"{label_col}_pj"
                            if pj_key in discovered_factors[symbol]:
                                pj_factors = discovered_factors[symbol][pj_key]
                                if pj_factors:
                                    all_symbol_pj_factors.extend(pj_factors)
                                    # 记录每个pj因子对应的标签
                                    for factor in pj_factors:
                                        factor_meta_info[str(factor)] = {
                                            'target_label': label_col,
                                            'symbol': symbol,
                                            'creation_method': 'gp_mining',
                                            'pipeline_step': 'L0',
                                            'factor_type': 'pj'
                                        }
                        
                        total_symbol_factors = len(all_symbol_factors) + len(all_symbol_pj_factors)
                        if total_symbol_factors > 0:
                            print(f"    🔄 计算因子值: f类{len(all_symbol_factors)}个 + pj类{len(all_symbol_pj_factors)}个")
                            
                            # 分别计算f类和pj类因子值
                            fct_values_df = pd.DataFrame()
                            pj_values_df = pd.DataFrame()
                            
                            if all_symbol_factors:
                                # 计算f类因子值
                                with FactorMonitorContext(f"CALC_F_VALUES_{symbol}", 
                                                           operation_type='factor_f_value_calculation', 
                                                           batch_id=symbol_batch_id,
                                                           symbol=symbol,
                                                           data_size=len(all_symbol_factors)) as calc_ctx:
                                    
                                    fct_values_df = FactorLoader.get_fct_df(
                                        X_symbol,
                                        model_type=cfg_mine.mine.norm.model,
                                        norm_type='X', 
                                        fcts=all_symbol_factors
                                    )
                                    # f类因子计算完成
                            
                            if all_symbol_pj_factors:
                                # 计算pj类因子值
                                with FactorMonitorContext(f"CALC_PJ_VALUES_{symbol}", 
                                                           operation_type='factor_pj_value_calculation', 
                                                           batch_id=symbol_batch_id,
                                                           symbol=symbol,
                                                           data_size=len(all_symbol_pj_factors)) as calc_ctx:
                                    
                                    pj_values_df = FactorLoader.get_fct_df(
                                        X_symbol,
                                        model_type=cfg_mine.mine.norm.model,
                                        norm_type='X', 
                                        fcts=all_symbol_pj_factors
                                    )
                                    # pj类因子计算完成
                            
                            if not fct_values_df.empty or not pj_values_df.empty:
                                # 准备分层持久化数据
                                factor_data_dict = {}
                                if not fct_values_df.empty:
                                    factor_data_dict['L0'] = fct_values_df      # L0阶段：f类因子
                                if not pj_values_df.empty:
                                    factor_data_dict['L0_pj'] = pj_values_df    # L0_pj阶段：pj类因子
                                
                                # 准备元数据
                                persistence_metadata = {
                                    'symbol': symbol,
                                    'date': current_date,
                                    'stage': 'L0',
                                    'total_factors': total_symbol_factors,
                                    'f_factors': len(all_symbol_factors),
                                    'pj_factors': len(all_symbol_pj_factors),
                                    'labels': list(label_cols),
                                    'factor_count_by_label': {
                                        label: len(discovered_factors[symbol].get(label, [])) + len(discovered_factors[symbol].get(f"{label}_pj", []))
                                        for label in label_cols
                                    },
                                    'mine_config': {
                                        'mine_runnum': n_runs_per_target,
                                        'job_num': job_num,
                                        'mine_norm_model': cfg_mine.mine.norm.model,
                                        'func_set_size': len(func_set),
                                    },
                                    'factor_meta_info': factor_meta_info,
                                    'data_range': {
                                        'start_date': start_date,
                                        'end_date': end_date,
                                        'frequency': frequency
                                    },
                                    'creation_time': datetime.now().isoformat(),
                                    'global_batch_id': batch_id
                                }
                                
                                # 执行因子值持久化
                                with FactorMonitorContext(f"PERSIST_{symbol}", 
                                                           operation_type='factor_value_persistence', 
                                                           batch_id=symbol_batch_id,
                                                           symbol=symbol,
                                                           data_size=fct_values_df.shape[0] * fct_values_df.shape[1]) as persist_ctx:
                                    
                                    persistence_success = factorstore.save_batch_data(
                                        batch_id=symbol_batch_id,
                                        base_data=base_data_symbol,
                                        factor_data_dict=factor_data_dict,
                                        metadata=persistence_metadata
                                    )
                                    
                                    if persistence_success:
                                        total_persistence_success += 1
                                        print(f"    ✅ 因子值持久化成功: f类{len(all_symbol_factors)}个 + pj类{len(all_symbol_pj_factors)}个")
                                        if not fct_values_df.empty:
                                            print(f"       f类数据维度: {fct_values_df.shape}")
                                            print(f"       时间范围: {fct_values_df.index.min()} ~ {fct_values_df.index.max()}")
                                        if not pj_values_df.empty:
                                            print(f"       pj类数据维度: {pj_values_df.shape}")
                                    else:
                                        total_persistence_failed += 1
                                        print(f"    ❌ 因子值持久化失败")
                            else:
                                print(f"    ⚠️  因子值计算结果为空")
                                total_persistence_failed += 1
                        else:
                            print(f"    ⚠️  该品种无有效因子")
                            
                    except Exception as e:
                        total_persistence_failed += 1
                        print(f"    ❌ 因子值持久化异常: {str(e)}")
                        import traceback
                        traceback.print_exc()
                
                # 6.3 因子值持久化结果统计
                print(f"\n📊 因子值持久化完成:")
                print(f"    ✅ 成功持久化: {total_persistence_success} 个品种")
                print(f"    ❌ 持久化失败: {total_persistence_failed} 个品种")
                if total_persistence_success + total_persistence_failed > 0:
                    success_rate = total_persistence_success / (total_persistence_success + total_persistence_failed) * 100
                    print(f"    📈 持久化成功率: {success_rate:.1f}%")
                
                # 6.4 验证持久化结果
                if total_persistence_success > 0:
                    print(f"\n🔍 验证持久化结果:")
                    try:
                        # 检查可用批次
                        available_batches = factorstore.get_available_batches(stage='L0', date=current_date)
                        print(f"    📋 今日L0批次: {len(available_batches)} 个")
                        
                        # 展示部分批次
                        for batch_id in available_batches[:3]:
                            batch_info = factorstore.get_batch_info(batch_id)
                            if batch_info:
                                print(f"    📁 {batch_id}: {batch_info.get('creation_time', 'Unknown')}")
                            else:
                                print(f"    📁 {batch_id}: 元数据不可用")
                        
                        if len(available_batches) > 3:
                            print(f"    ... 及其他 {len(available_batches) - 3} 个批次")
                            
                    except Exception as e:
                        print(f"    ⚠️  验证异常: {str(e)}")

                # 6.5 因子分类自动判断（FactorZoo入库用）
                def auto_classify_factor(expr_str: str) -> str:
                    expr_lower = expr_str.lower()
                    if any(word in expr_lower for word in ['volume', 'vol', 'amount']):
                        return 'PRICE_VOLUME'
                    elif any(word in expr_lower for word in ['ts_mean', 'ts_sum', 'ts_max', 'ts_min', 'rolling']):
                        return 'PRICE_TREND'
                    elif any(word in expr_lower for word in ['close', 'open', 'high', 'low']):
                        return 'PRICE_VOLUME'
                    else:
                        return 'PRICE_VOLUME'  # 默认分类
                
                # 6.6 按品种分别创建批次和入库因子
                total_success_count = 0
                total_failed_count = 0
                
                for symbol in symbols:
                    # 为每个品种创建独立批次 - 使用统一编码规范
                    symbol_batch_id = f"GP_{symbol}_{current_date}_L0_{global_uid[-6:]}"
                    symbol_pj_batch_id = f"GP_{symbol}_{current_date}_L0_PJ_{global_uid[-6:]}"
                    print(f"\n📋 创建品种批次: {symbol_batch_id} (f类) + {symbol_pj_batch_id} (pj类)")
                    
                    # 统计该品种的因子数量
                    symbol_factor_count = sum(len(discovered_factors[symbol].get(label, [])) for label in label_cols)
                    symbol_pj_count = sum(len(discovered_factors[symbol].get(f"{label}_pj", [])) for label in label_cols)
                    
                    if symbol_factor_count == 0 and symbol_pj_count == 0:
                        print(f"    ⚠️  品种 {symbol} 无因子，跳过批次创建")
                        continue
                    
                    # 创建f类因子批次记录
                    batch_success = False
                    pj_batch_success = False
                    
                    if symbol_factor_count > 0:
                        batch_success = factorzoo.create_batch(
                            batch_id=symbol_batch_id,
                            batch_name=f"GP挖掘L0_f类_{symbol}_{current_date}",
                            creation_tool="gplearn",
                            source_symbols=[symbol],
                            source_frequencies=[frequency],
                            source_date_ranges={
                                "start": datetime_values.min().strftime('%Y-%m-%d'),
                                "end": datetime_values.max().strftime('%Y-%m-%d')
                            },
                            generation_params={
                                "symbol": symbol,
                                "date": current_date,
                                "stage": "L0",
                                "factor_type": "f",
                                "mine_runnum": n_runs_per_target,
                                "job_num": job_num,
                                "func_set": func_set[:10],
                                "labels": list(label_cols),
                                "symbol_factor_count": symbol_factor_count,
                                "batch_encoding": "GP_{symbol}_{date}_L0_{uid}"
                            }
                        )
                    
                    # 创建pj类因子批次记录  
                    if symbol_pj_count > 0:
                        pj_batch_success = factorzoo.create_batch(
                            batch_id=symbol_pj_batch_id,
                            batch_name=f"GP挖掘L0_pj类_{symbol}_{current_date}",
                            creation_tool="gplearn",
                            source_symbols=[symbol],
                            source_frequencies=[frequency],
                            source_date_ranges={
                                "start": datetime_values.min().strftime('%Y-%m-%d'),
                                "end": datetime_values.max().strftime('%Y-%m-%d')
                            },
                            generation_params={
                                "symbol": symbol,
                                "date": current_date,
                                "stage": "L0",
                                "factor_type": "pj",
                                "mine_runnum": n_runs_per_target,
                                "job_num": job_num,
                                "func_set": func_set[:10],
                                "labels": list(label_cols),
                                "symbol_pj_count": symbol_pj_count,
                                "batch_encoding": "GP_{symbol}_{date}_L0_PJ_{uid}"
                            }
                        )
                    
                    print(f"    批次创建结果: f类{'✅' if batch_success else '❌'} + pj类{'✅' if pj_batch_success else '❌'}")
                    
                    # 分别入库f类和pj类因子
                    symbol_success = 0
                    symbol_failed = 0
                    symbol_pj_success = 0
                    symbol_pj_failed = 0
                    
                    # 入库f类因子
                    for label_col in label_cols:
                        if label_col in discovered_factors[symbol]:
                            factors = discovered_factors[symbol][label_col]
                            if not factors:
                                continue
                            
                            print(f"  📊 入库f类因子 {symbol}-{label_col}: {len(factors)} 个")
                            
                            for i, factor_expr in enumerate(factors):
                                factor_id = f"F_GP_{symbol_batch_id}_{label_col}_{i+1:03d}"
                                
                                # 使用因子专用监控上下文
                                with FactorMonitorContext(factor_id, 
                                                           operation_type='registration', 
                                                           batch_id=symbol_batch_id,
                                                           factor_id=factor_id,
                                                           symbol=symbol) as ctx:
                                    try:
                                        # 计算因子复杂度评分 - 基于表达式长度和操作符数量
                                        expr_str = str(factor_expr)
                                        complexity_score = min(100, len(expr_str) + expr_str.count('(') * 2 + expr_str.count(','))

                                        # 评估资源强度 - 基于复杂度
                                        if complexity_score < 30:
                                            resource_intensity = 'low'
                                        elif complexity_score < 60:
                                            resource_intensity = 'medium'
                                        else:
                                            resource_intensity = 'high'

                                        # 添加f类因子到FactorZoo - 包含性能数据
                                        add_success = factorzoo.add_factor(
                                            factor_id=factor_id,
                                            factor_name=f"GP_f_{symbol}_{label_col}_{i+1}",
                                            factor_expression=expr_str,
                                            factor_type="time_series",
                                            data_source_type="single_symbol",
                                            symbols=[symbol],
                                            frequencies=[frequency],
                                            date_ranges={
                                                "start": datetime_values.min().strftime('%Y-%m-%d'),
                                                "end": datetime_values.max().strftime('%Y-%m-%d')
                                            },
                                            creation_method="auto_generation",
                                            generation_tool="gplearn",
                                            pipeline_step="L0",
                                            pipeline_mode="auto_pipeline",
                                            primary_category=auto_classify_factor(expr_str),
                                            batch_id=symbol_batch_id,
                                            target_label=label_col,
                                            status="active",
                                            # 性能相关字段 - 顺带填充
                                            complexity_score=complexity_score,
                                            resource_intensity=resource_intensity
                                        )
                                        
                                        if add_success:
                                            symbol_success += 1
                                        else:
                                            symbol_failed += 1
                                            
                                    except Exception as e:
                                        symbol_failed += 1
                                        print(f"    ⚠️  f类因子 {factor_id} 入库失败: {str(e)}")
                    
                    # 入库pj类因子
                    for label_col in label_cols:
                        pj_key = f"{label_col}_pj"
                        if pj_key in discovered_factors[symbol]:
                            pj_factors = discovered_factors[symbol][pj_key]
                            if not pj_factors:
                                continue
                            
                            print(f"  📊 入库pj类因子 {symbol}-{label_col}: {len(pj_factors)} 个")
                            
                            for i, factor_expr in enumerate(pj_factors):
                                factor_id = f"F_GP_PJ_{symbol_pj_batch_id}_{label_col}_{i+1:03d}"
                                
                                # 使用因子专用监控上下文
                                with FactorMonitorContext(factor_id, 
                                                           operation_type='registration', 
                                                           batch_id=symbol_pj_batch_id,
                                                           factor_id=factor_id,
                                                           symbol=symbol) as ctx:
                                    try:
                                        # 计算因子复杂度评分 - 基于表达式长度和操作符数量
                                        expr_str = str(factor_expr)
                                        complexity_score = min(100, len(expr_str) + expr_str.count('(') * 2 + expr_str.count(','))

                                        # 评估资源强度 - 基于复杂度
                                        if complexity_score < 30:
                                            resource_intensity = 'low'
                                        elif complexity_score < 60:
                                            resource_intensity = 'medium'
                                        else:
                                            resource_intensity = 'high'

                                        # 添加pj类因子到FactorZoo - 包含性能数据
                                        add_success = factorzoo.add_factor(
                                            factor_id=factor_id,
                                            factor_name=f"GP_pj_{symbol}_{label_col}_{i+1}",
                                            factor_expression=expr_str,
                                            factor_type="time_series",
                                            data_source_type="single_symbol",
                                            symbols=[symbol],
                                            frequencies=[frequency],
                                            date_ranges={
                                                "start": datetime_values.min().strftime('%Y-%m-%d'),
                                                "end": datetime_values.max().strftime('%Y-%m-%d')
                                            },
                                            creation_method="auto_generation",
                                            generation_tool="gplearn",
                                            pipeline_step="L0",
                                            pipeline_mode="auto_pipeline",
                                            primary_category=auto_classify_factor(expr_str),
                                            batch_id=symbol_pj_batch_id,
                                            target_label=label_col,
                                            status="active",
                                            # 性能相关字段 - 顺带填充
                                            complexity_score=complexity_score,
                                            resource_intensity=resource_intensity
                                        )
                                        
                                        if add_success:
                                            symbol_pj_success += 1
                                        else:
                                            symbol_pj_failed += 1
                                            
                                    except Exception as e:
                                        symbol_pj_failed += 1
                                        print(f"    ⚠️  pj类因子 {factor_id} 入库失败: {str(e)}")
                    
                    # 更新f类因子批次的统计
                    if batch_success and symbol_success > 0:
                        try:
                            with factorzoo.get_connection() as conn:
                                cursor = conn.cursor()
                                cursor.execute("""
                                    UPDATE factor_batches 
                                    SET total_generated = ?, l0_count = ?, end_time = ?
                                    WHERE batch_id = ?
                                """, (symbol_factor_count, symbol_success, datetime.now(), symbol_batch_id))
                                conn.commit()
                            print(f"    ✅ f类批次统计已更新: 成功{symbol_success}个，失败{symbol_failed}个")
                        except Exception as e:
                            print(f"    ⚠️  f类批次统计更新失败: {str(e)}")
                    
                    # 更新pj类因子批次的统计
                    if pj_batch_success and symbol_pj_success > 0:
                        try:
                            with factorzoo.get_connection() as conn:
                                cursor = conn.cursor()
                                cursor.execute("""
                                    UPDATE factor_batches 
                                    SET total_generated = ?, l0_count = ?, end_time = ?
                                    WHERE batch_id = ?
                                """, (symbol_pj_count, symbol_pj_success, datetime.now(), symbol_pj_batch_id))
                                conn.commit()
                            print(f"    ✅ pj类批次统计已更新: 成功{symbol_pj_success}个，失败{symbol_pj_failed}个")
                        except Exception as e:
                            print(f"    ⚠️  pj类批次统计更新失败: {str(e)}")
                    
                    total_success_count += symbol_success + symbol_pj_success
                    total_failed_count += symbol_failed + symbol_pj_failed
                
                # 6.7 全局入库结果统计
                print(f"\n📊 FactorZoo整体入库完成:")
                print(f"    ✅ 成功入库: {total_success_count} 个因子（f类+pj类）")
                print(f"    ❌ 入库失败: {total_failed_count} 个因子")
                if total_success_count + total_failed_count > 0:
                    print(f"    📈 入库成功率: {total_success_count/(total_success_count+total_failed_count)*100:.1f}%")
                
                # 统计创建的批次数（f类+pj类）
                f_batches = len([s for s in symbols if sum(len(discovered_factors[s].get(l, [])) for l in label_cols) > 0])
                pj_batches = len([s for s in symbols if sum(len(discovered_factors[s].get(f"{l}_pj", [])) for l in label_cols) > 0])
                total_batches = f_batches + pj_batches
                print(f"    🗂️  创建批次数: {total_batches} 个（f类{f_batches} + pj类{pj_batches}）")
                
            else:
                print(f"\n🏦 无因子需要入库")
            
    finally:
        # 确保在任何情况下都能记录全局耗时
        global_elapsed_time = global_monitor.end_timer("global_process")
        peak_memory = global_monitor.log_memory_usage()
        
        print(f"\n⏱️  全局流程耗时: {global_elapsed_time}")
        print(f"💾 内存峰值: {peak_memory}")
        
        # 生成性能报告
        if 'batch_id' in locals():
            analyze_and_report(batch_id)