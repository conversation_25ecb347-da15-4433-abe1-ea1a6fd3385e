import pandas as pd
# from autogluon.tabular import TabularPredictor
from loguru import logger
from .algo_base import Algo

""" changed by JZAL on 240217"""

class SelectAll(Algo):
    """
    Sets temp['selected'] with all securities (based on universe).

    Selects all the securities and saves them in temp['selected'].
    By default, SelectAll does not include securities that have no
    data (nan) on current date or those whose price is zero or negative.

    Args:
        * include_no_data (bool): Include securities that do not have data?
        * include_negative (bool): Include securities that have negative
          or zero prices?
    Sets:
        * selected

    """

    def __init__(self, include_no_data=False, include_negative=False):
        super(SelectAll, self).__init__()
        self.include_no_data = include_no_data
        self.include_negative = include_negative

    def __call__(self, target):
        target.temp["selected"] = target.symbols
        return True


class SelectThese(Algo):
    def __init__(self, tickers):
        super(SelectThese, self).__init__()
        self.tickers = tickers

    def __call__(self, target):
        '''
        selected = []
        for s in self.tickers:
            if s in target.bar_df.index:
                selected.append(s)

        '''
        # print(self.tickers)
        target.temp['selected'] = self.tickers
        # if len(selected) == 0:
        #    return False
        return True


class SelectBySignal(Algo):
    def __init__(self, rules=[], at_least_count=1, exclude=False):
        super(SelectBySignal, self).__init__()
        self.rules = rules
        self.at_least_count = at_least_count
        self.exclude = exclude

    def _check_if_matched(self, df_bar, rules, at_least_count):
        matched_items = []
        for symbol in list(df_bar.index):
            bar = df_bar.loc[symbol]
            match = 0
            for i, rule in enumerate(rules):
                # expr = re.sub('ind\((.*?)\)', 'bar["\\1"]', rule)
                # if eval(expr):
                if rule in list(bar.index):
                    if bar[rule]:
                        match += 1
                else:
                    logger.warning('rule:{}指标未计算？'.format(rule))
            if match >= at_least_count:
                matched_items.append(symbol)
        return matched_items

    def __call__(self, target):
        df_bar = target.df_bar
        matched = None
        if self.rules and len(self.rules):
            matched = self._check_if_matched(df_bar, self.rules, self.at_least_count)
        if len(matched) == 0:
            return True

        if self.exclude:  # 平仓信息命中
            if 'selected' not in target.temp.keys():
                return True
            selected = target.temp['selected']
            excluded = []
            for s in selected:
                if s not in matched:
                    excluded.append(s)
            target.temp['selected'] = excluded  # 要平仓的排除掉
        else:  # 选中信号命中
            target.temp['selected'] = matched  # 选择要持仓的
        return True


class SelectHolding(Algo):
    def __call__(self, target):
        curr_holding = target.get_current_holding_symbols()

        if 'selected' not in target.temp.keys():
            target.temp['selected'] = curr_holding
        else:
            selected = list(set(list(target.temp['selected']) + curr_holding))
            target.temp['selected'] = selected
        return True


class SelectByModel(Algo):
    def __init__(self, model_path="mymodel/"):
        self.predictor = TabularPredictor.load(model_path)

    def __call__(self, target):
        df_bar = target.df_bar
        df_bar['pred'] = self.predictor.predict(df_bar)
        # print(df_bar)
        df_bar['sell'] = df_bar['pred'] == 0
        df_bar['buy'] = df_bar['pred'] == 1
        # print(df_bar)
        return True


class SelectTopK(Algo):
    def __init__(self, factor_name='order_by', K=1, drop_top_n=0, b_ascending=False):
        super(SelectTopK, self).__init__() # add by JZAL
        self.K = K
        self.drop_top_n = drop_top_n  # 这算是一个魔改，就是把最强的N个弃掉，尤其动量指标，过尤不及。
        self.factor_name = factor_name
        self.b_ascending = b_ascending

    def __call__(self, target):

        key = 'selected'
        if key not in target.temp.keys():
            return True
        selected = target.temp[key]

        # print(target.now)
        df_bar = target.df_bar
        factor_sorted = df_bar.sort_values(by=self.factor_name, ascending=self.b_ascending)

        symbols = factor_sorted.index
        # bar_df = bar_df.sort_values(self.order_by, ascending=self.b_ascending)

        if not selected:
            start = 0
            if self.drop_top_n <= len(symbols):
                start = self.drop_top_n
                ordered = symbols[start: start + self.K]
            else:
                ordered = []
        else:
            ordered = []
            count = 0
            for s in symbols:  # 一定是当天有记录的
                if s in selected:
                    count += 1
                    if count > self.drop_top_n:
                        ordered.append(s)

                    if len(ordered) >= self.K:
                        break

        target.temp[key] = ordered

        return True
    
""" changed by JZAL on 240217"""

class V2SelectBySignal(Algo):
    def __init__(self, rules_buy=[], buy_at_least_count=1, rules_sell=[], 
                 sell_at_least_count=1, b_before_rank=False):
        super(V2SelectBySignal, self).__init__()
        self.rules_buy = rules_buy
        self.rules_sell = rules_sell

        if buy_at_least_count > len(rules_buy):
            buy_at_least_count = len(rules_buy)
        if buy_at_least_count <= 0:
            buy_at_least_count = 1
        self.buy_at_least_count = buy_at_least_count

        if sell_at_least_count > len(rules_sell):
            sell_at_least_count = len(rules_sell)
        if sell_at_least_count <= 0:
            sell_at_least_count = 1
        self.sell_at_least_count = sell_at_least_count
        
        self.b_before_rank = b_before_rank

    def _check_if_matched(self, df_bar, rules, at_least_count):
        # df_bar['roc_20'] = df_bar['roc_20'].astype(float)
        se_count = pd.Series(index=df_bar.index, data=0)
        for r in rules:
            se_count += df_bar.eval(r) # 大于/小于等此类表达式计算

        matched_items = se_count[(se_count.values >= at_least_count)].index
        return list(matched_items)

    def __call__(self, target):
        key='selected'
        selected=[]
        if key in target.temp.keys():
            selected=target.temp[key]
        if len(selected) == 0 :
            df_bar = target.df_bar
            target.temp[key]=list(df_bar.index) 
        else:
           df_bar = target.df_bar[target.df_bar.index.isin(selected)]  
        if len(self.rules_buy) == 0 and len(self.rules_sell) == 0:
            return True
        matched_buy = []
        matched_sell = []
        if self.rules_buy and len(self.rules_buy) and not df_bar.empty:
            matched_buy = self._check_if_matched(df_bar, self.rules_buy, self.buy_at_least_count)
        else:
            matched_buy = list(df_bar.index)  # 没有配置买入规则，但有卖出，就是选全选。

        if self.rules_sell and len(self.rules_sell) and not df_bar.empty:
            matched_sell = self._check_if_matched(df_bar, self.rules_sell, self.sell_at_least_count)
        if self.b_before_rank: #在排序前执行
            holdings, _ = target.get_current_holding_symbols()
            if holdings and len(holdings) > 0:
                matched_buy += holdings
                matched_buy = list(set(matched_buy))

        if matched_sell:
            for sell in matched_sell:
                if sell in matched_buy:
                    matched_buy.remove(sell)

        matched_buy = list(set(matched_buy))
        target.temp[key] = matched_buy
        # if len(matched_buy) > 1:
        #    print(matched_buy)
        #print('{}: {}'.format(target.now,target.temp['selected']))
        return True
class V2SelectTopK(Algo):
    def __init__(self, factor_name='order_by', K=1, drop_top_n=0, b_ascending=False):
        super(V2SelectTopK, self).__init__() # add by JZAL
        self.K = K
        self.drop_top_n = drop_top_n  # 这算是一个魔改，就是把最强的N个弃掉，尤其动量指标，过尤不及。
        self.factor_name = factor_name
        self.b_ascending = b_ascending

    def __call__(self, target):

        key = 'selected'
        df_bar = target.df_bar
        if key not in target.temp.keys():
            selected = list(df_bar.index)
        else:
            selected = target.temp[key]

        factor_sorted = df_bar.sort_values(by=self.factor_name, ascending=self.b_ascending)

        symbols = factor_sorted.index
        # bar_df = bar_df.sort_values(self.order_by, ascending=self.b_ascending)

        ordered = []
        count = 0
        for s in symbols:  # 一定是当天有记录的
            if s in selected:
                count += 1
                if count > self.drop_top_n:
                    ordered.append(s)

                if len(ordered) >= self.K:
                    break

        target.temp[key] = ordered
        #print('{}: {}'.format(target.now,target.temp['selected']))
        return True