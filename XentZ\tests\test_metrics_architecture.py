#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绩效计算架构重构测试
测试新的分层架构和工厂模式的正确性
"""

import unittest
import numpy as np
import pandas as pd
import time

# 导入重构后的模块
from factor.metrics_core import (
    calc_sharpe_ratio, calc_max_drawdown, calc_annual_return,
    calc_annual_volatility, calc_basic_metrics_batch
)
from factor.metrics_factory import (
    MetricsFactory, calculate_metrics, calculate_sharpe_ratio,
    FastMetricsCalculator, DetailedMetricsCalculator, FactorMetricsCalculator
)
from factor.performance_utils import perf_calculator


class TestCoreMetrics(unittest.TestCase):
    """测试核心计算模块"""
    
    def setUp(self):
        """设置测试数据"""
        np.random.seed(42)
        self.returns = np.random.randn(252) * 0.02  # 日收益率
        self.returns_series = pd.Series(self.returns)
        self.multi_returns = np.random.randn(252, 3) * 0.02  # 多资产收益率
    
    def test_core_sharpe_calculation(self):
        """测试核心夏普比率计算"""
        sharpe = calc_sharpe_ratio(self.returns)
        self.assertIsInstance(sharpe, float)
        self.assertTrue(np.isfinite(sharpe))
        
        # 测试多资产
        sharpe_multi = calc_sharpe_ratio(self.multi_returns)
        self.assertEqual(len(sharpe_multi), 3)
        self.assertTrue(all(np.isfinite(sharpe_multi)))
    
    def test_core_max_drawdown(self):
        """测试核心最大回撤计算"""
        cum_returns = np.cumprod(1 + self.returns)
        max_dd = calc_max_drawdown(cum_returns)
        self.assertIsInstance(max_dd, float)
        self.assertGreaterEqual(max_dd, 0)
        
        # 测试多资产
        cum_returns_multi = np.cumprod(1 + self.multi_returns, axis=0)
        max_dd_multi = calc_max_drawdown(cum_returns_multi)
        self.assertEqual(len(max_dd_multi), 3)
        self.assertTrue(all(max_dd_multi >= 0))
    
    def test_batch_calculation_performance(self):
        """测试批量计算性能"""
        start_time = time.time()
        metrics = calc_basic_metrics_batch(self.multi_returns)
        elapsed_time = time.time() - start_time
        
        # 验证结果
        self.assertIn('sharpe_ratio', metrics)
        self.assertIn('max_drawdown', metrics)
        self.assertEqual(len(metrics['sharpe_ratio']), 3)
        
        # 性能要求：批量计算应该很快
        self.assertLess(elapsed_time, 0.1, "批量计算应在0.1秒内完成")
        
        print(f"批量计算3个资产252天数据耗时: {elapsed_time:.4f}秒")


class TestMetricsFactory(unittest.TestCase):
    """测试绩效计算工厂"""
    
    def setUp(self):
        """设置测试数据"""
        np.random.seed(42)
        self.returns = pd.Series(np.random.randn(252) * 0.02)
    
    def test_factory_creation(self):
        """测试工厂创建不同类型的计算器"""
        # 测试所有类型的计算器
        for calc_type in MetricsFactory.get_available_types():
            calculator = MetricsFactory.create_calculator(calc_type)
            self.assertIsNotNone(calculator)
            
            # 测试计算功能
            metrics = calculator.calculate_metrics(self.returns)
            self.assertIn('sharpe_ratio', metrics)
            self.assertIn('annual_return', metrics)
    
    def test_calculator_consistency(self):
        """测试不同计算器的结果一致性"""
        fast_calc = MetricsFactory.create_calculator('fast')
        detailed_calc = MetricsFactory.create_calculator('detailed')
        
        fast_metrics = fast_calc.calculate_metrics(self.returns)
        detailed_metrics = detailed_calc.calculate_metrics(self.returns)
        
        # 核心指标应该一致（允许小的数值误差）
        self.assertAlmostEqual(
            fast_metrics['sharpe_ratio'], 
            detailed_metrics['sharpe_ratio'], 
            places=4
        )
        self.assertAlmostEqual(
            fast_metrics['annual_return'], 
            detailed_metrics['annual_return'], 
            places=4
        )
    
    def test_convenience_functions(self):
        """测试便捷函数"""
        # 测试通用计算函数
        metrics = calculate_metrics(self.returns, 'fast')
        self.assertIn('sharpe_ratio', metrics)
        
        # 测试夏普比率便捷函数
        sharpe = calculate_sharpe_ratio(self.returns, method='fast')
        self.assertIsInstance(sharpe, float)
        self.assertEqual(sharpe, metrics['sharpe_ratio'])
    
    def test_performance_comparison(self):
        """测试不同计算器的性能对比"""
        # 创建较大的数据集
        large_returns = pd.Series(np.random.randn(2520) * 0.02)  # 10年数据
        
        # 测试快速计算器
        start_time = time.time()
        fast_metrics = calculate_metrics(large_returns, 'fast')
        fast_time = time.time() - start_time
        
        # 测试详细计算器
        start_time = time.time()
        detailed_metrics = calculate_metrics(large_returns, 'detailed')
        detailed_time = time.time() - start_time
        
        print(f"快速计算器耗时: {fast_time:.4f}秒")
        print(f"详细计算器耗时: {detailed_time:.4f}秒")
        
        # 快速计算器应该更快（但结果应该一致）
        self.assertLessEqual(fast_time, detailed_time * 2)  # 允许2倍差异
        self.assertAlmostEqual(
            fast_metrics['sharpe_ratio'], 
            detailed_metrics['sharpe_ratio'], 
            places=3
        )


class TestArchitectureIntegration(unittest.TestCase):
    """测试架构集成"""
    
    def setUp(self):
        """设置测试数据"""
        np.random.seed(42)
        self.returns = pd.Series(np.random.randn(252) * 0.02)
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        # 原有的perf_calculator应该仍然工作
        old_metrics = perf_calculator.calculate_basic_metrics(self.returns)
        
        # 新的工厂方法应该产生一致的结果
        new_metrics = calculate_metrics(self.returns, 'detailed')
        
        # 核心指标应该一致
        self.assertAlmostEqual(
            old_metrics['sharpe_ratio'], 
            new_metrics['sharpe_ratio'], 
            places=4
        )
        self.assertAlmostEqual(
            old_metrics['max_drawdown'], 
            new_metrics['max_drawdown'], 
            places=4
        )
    
    def test_api_consistency(self):
        """测试API一致性"""
        # 所有计算器都应该支持相同的基本参数
        for calc_type in MetricsFactory.get_available_types():
            calculator = MetricsFactory.create_calculator(calc_type)
            
            # 测试基本参数
            metrics = calculator.calculate_metrics(
                self.returns, 
                annualization_factor=252,
                risk_free_rate=0.02
            )
            
            # 应该包含核心指标
            required_metrics = ['annual_return', 'volatility', 'sharpe_ratio']
            for metric in required_metrics:
                self.assertIn(metric, metrics)
                self.assertTrue(np.isfinite(metrics[metric]))
    
    def test_error_handling_consistency(self):
        """测试错误处理一致性"""
        # 测试空数据
        empty_returns = pd.Series([])
        
        detailed_calc = MetricsFactory.create_calculator('detailed')
        detailed_result = detailed_calc.calculate_metrics(empty_returns)
        self.assertEqual(detailed_result, {})
        
        # 测试NaN数据
        nan_returns = pd.Series([np.nan] * 10)
        detailed_result = detailed_calc.calculate_metrics(nan_returns)
        self.assertEqual(detailed_result, {})


class TestDesignPrinciples(unittest.TestCase):
    """测试设计原则的实现"""
    
    def test_single_responsibility(self):
        """测试单一职责原则"""
        # 核心计算模块应该只负责计算
        from factor import metrics_core
        
        # 检查核心模块的函数都是纯计算函数
        core_functions = [
            'calc_sharpe_ratio', 'calc_max_drawdown', 
            'calc_annual_return', 'calc_annual_volatility'
        ]
        
        for func_name in core_functions:
            self.assertTrue(hasattr(metrics_core, func_name))
    
    def test_open_closed_principle(self):
        """测试开闭原则"""
        # 应该能够轻松添加新的计算器类型
        class CustomMetricsCalculator:
            def calculate_metrics(self, returns, **kwargs):
                return {'custom_metric': 1.0}
        
        # 工厂应该支持扩展
        MetricsFactory._calculators['custom'] = CustomMetricsCalculator
        
        custom_calc = MetricsFactory.create_calculator('custom')
        result = custom_calc.calculate_metrics(pd.Series([0.01, 0.02]))
        self.assertEqual(result['custom_metric'], 1.0)
        
        # 清理
        del MetricsFactory._calculators['custom']
    
    def test_interface_segregation(self):
        """测试接口隔离原则"""
        # 不同的计算器应该实现相同的核心接口
        from factor.metrics_factory import MetricsCalculatorInterface
        
        for calc_type in MetricsFactory.get_available_types():
            calculator = MetricsFactory.create_calculator(calc_type)
            self.assertIsInstance(calculator, MetricsCalculatorInterface)


if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)
