#!/usr/bin/env python
# -*- coding:utf-8 -*-

from hikyuu import *

def part(period: int = 25) -> Indicator:
    """
    趋势评分指标：年化收益率 × R平方
    
    趋势评分通过计算价格的线性回归，综合评估趋势的强度和方向。
    指标值越高表示上升趋势越强，值越低表示下降趋势越强，接近0则表示无明显趋势。
    
    计算方法：
    1. 对收盘价取对数，进行线性回归
    2. 计算斜率的年化收益率
    3. 计算线性回归的R平方值
    4. 将年化收益率与R平方相乘得到趋势评分
    
    参数：
        period: 计算窗口长度，默认25天
        
    返回：
        Indicator: 趋势评分指标
    """
    # 获取对数收盘价
    log_c = LN(CLOSE())
    
    # 计算线性回归斜率
    slope = SLOPE(log_c, period)
    
    # 计算年化收益率 (使用252个交易日)
    annualized_returns = EXP(slope * 252) - 1
    
    # 使用REF为滑动窗口提供数据
    y_data = REF(log_c, period-1)
    y_mean = MA(y_data, period)
    
    # x 的均值是 (n+1)/2
    x_mean = (period + 1) / 2
    
    # 计算截距：b = y_mean - slope * x_mean
    intercept = y_mean - slope * x_mean
    
    # 创建序列1到period作为x值
    ix = PRICELIST(list(range(1, period + 1)))
    
    # 计算预测值
    y_pred = slope * ix + intercept
    
    # 计算残差
    residuals = y_data - y_pred
    
    # 残差平方和
    ss_res = SUM(residuals * residuals, period)
    
    # 总离差平方和
    y_diff = y_data - y_mean
    ss_tot = SUM(y_diff * y_diff, period)
    
    # R平方计算
    r_squared = 1 - (ss_res / ss_tot)
    
    # 处理零方差情况(避免除零错误)
    r_squared = IF(ss_tot == 0, 0, r_squared)
    
    # 综合评分
    score = annualized_returns * r_squared
    
    # 创建结果指标
    result = score
    result.name = "趋势评分"
    
    return result


if __name__ == "__main__":
    import os
    import sys
    
    if sys.platform == 'win32':
        os.system('chcp 65001')

    # 仅加载测试需要的数据
    options = {
        "stock_list": ["sh000001"],
        "ktype_list": ["day"],
        "load_history_finance": False,
        "load_weight": False,
        "start_spot": False,
        "spot_worker_num": 1,
    }
    load_hikyuu(**options)

    stk = sm[options['stock_list'][0]]
    k = stk.get_kdata(Query(-3000))
    ind = part()
    ind(k).plot(label=f"{stk.name}趋势评分", legend_on=True)

    import matplotlib.pyplot as plt
    plt.show()