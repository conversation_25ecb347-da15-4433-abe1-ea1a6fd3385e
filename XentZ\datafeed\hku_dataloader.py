#!/usr/bin/env python
# -*- coding: utf-8 -*-

from hikyuu.interactive import *
from datetime import datetime
import pandas as pd
from loguru import logger
from datafeed.dataloader import DataLoader

class HKUDataloader(DataLoader):
    """HKU数据加载器，用于从HKU数据源读取数据并转换为所需格式
        支持的频率格式:
        基础周期:
            - D, DAY: 日线
            - W, WEEK: 周线
            - M, MONTH: 月线
            - Q, QUARTER: 季线
            - H, HALFYEAR: 半年线
            - Y, YEAR: 年线
        分钟周期:
            - 1min, M1, MIN: 1分钟线
            - 5min, M5, MIN5: 5分钟线
            - 15min, M15, MIN15: 15分钟线
            - 30min, M30, MIN30: 30分钟线
    """
    
    # 简单缓存，避免重复访问HKU环境
    _symbols_cache = None
    _data_cache = {}
    
    FREQ_MAP = {
        # 基础周期
        'D': Query.DAY,       # 日线
        'W': Query.WEEK,      # 周线
        'M': Query.MONTH,     # 月线
        'Q': Query.QUARTER,   # 季线
        'H': Query.HALFYEAR,  # 半年线
        'Y': Query.YEAR,      # 年线
        
        # 分钟周期
        '1min': Query.MIN,    # 1分钟线
        '5min': Query.MIN5,   # 5分钟线
        '15min': Query.MIN15, # 15分钟线
        '30min': Query.MIN30, # 30分钟线
        '60min': Query.MIN60, # 60分钟线
        
        # 别名支持
        'DAY': Query.DAY,
        'WEEK': Query.WEEK,
        'MONTH': Query.MONTH,
        'QUARTER': Query.QUARTER,
        'HALFYEAR': Query.HALFYEAR,
        'YEAR': Query.YEAR,
        'MIN': Query.MIN,
        'MIN5': Query.MIN5,
        'MIN15': Query.MIN15,
        'MIN30': Query.MIN30,
        'MIN60': Query.MIN60,
        # 添加更多分钟周期别名
        'M1': Query.MIN,
        'M5': Query.MIN5,
        'M15': Query.MIN15,
        'M30': Query.MIN30,
        'M60': Query.MIN60
    }

    RECOVER_MAP = {
        # 复权类型
        'NO_RECOVER': Query.NO_RECOVER,      # 不复权
        'FORWARD': Query.FORWARD,            # 前向复权
        'BACKWARD': Query.BACKWARD,          # 后向复权
        'EQUAL_FORWARD': Query.EQUAL_FORWARD,  # 等比前向复权
        'EQUAL_BACKWARD': Query.EQUAL_BACKWARD  # 等比后向复权
    }

    def __init__(self):
        pass

    @staticmethod
    def get_recover_type(recover: str) -> int:
        """将复权类型字符串转换为HKU的Query.RecoverType"""
        recover = recover.upper()  # 转大写处理
        return HKUDataloader.RECOVER_MAP.get(recover, Query.NO_RECOVER)
    
    @staticmethod
    def get_ktype(freq: str) -> int:
        """将标准频率字符串转换为HKU的Query.KTYPE"""
        if freq.endswith('min'):  # 对分钟周期特殊处理
            return HKUDataloader.FREQ_MAP.get(freq.lower(), Query.DAY)
        freq = freq.upper()  # 其他周期转大写处理
        return HKUDataloader.FREQ_MAP.get(freq, Query.DAY)

    @staticmethod
    def get_HKU_symbols(symbols: list[str] = None):
        if symbols is None:
            # 缓存股票列表，避免重复遍历sm
            if HKUDataloader._symbols_cache is None:
                HKUDataloader._symbols_cache = [stock.market_code for stock in sm]
            symbols = HKUDataloader._symbols_cache
        else: # 统一代码格式处理: 000300.SH -> SH000300
            symbols = [f"{(s.split('.')[1]).upper()}{s.split('.')[0]}" 
                       if '.' in s else s.upper() 
                       for s in symbols if isinstance(s,str)]
        return symbols

    @staticmethod
    def load_a_backtrader_df(symbol: str, start_date='20100101', 
                          end_date=datetime.now().strftime('%Y%m%d'),
                          freq = 'D', 
                          recover = 'NO_RECOVER'):
        recover = HKUDataloader.get_recover_type(recover)
        ktype = HKUDataloader.get_ktype(freq)
        df = HKUDataloader.load_df_all([symbol], set_index=True,
                                  start_date=start_date, end_date=end_date,
                                  ktype=ktype, recover=recover)
        df['openinterest'] = 0
        df = df[['open', 'high', 'low', 'close', 'volume', 'openinterest']]

        return df

    @staticmethod
    def load_df_all(symbols: list[str] = None, set_index=False, 
               start_date='20100101', 
               end_date=datetime.now().strftime('%Y%m%d'),
               freq = 'D', recover = 'NO_RECOVER'):
        """从HKU数据源读取多个品种的数据并返回df
        Returns:
            [idx] datetime open high low close amount volume symbol
            [datetime] open high low close amount volume symbol
        """
        # 生成缓存键
        cache_key = f"{sorted(symbols) if symbols else 'all'}_{start_date}_{end_date}_{freq}_{recover}"
        
        # 检查缓存
        if cache_key in HKUDataloader._data_cache:
            df = HKUDataloader._data_cache[cache_key].copy()
        else:
            recover = HKUDataloader.get_recover_type(recover)
            ktype = HKUDataloader.get_ktype(freq)
            dfs = []
            if symbols is None:
                # 使用缓存的符号列表
                symbols = HKUDataloader.get_HKU_symbols()
            else: # 统一代码格式处理: 000300.SH -> SH000300
                symbols = [f"{(s.split('.')[1]).upper()}{s.split('.')[0]}" 
                           if '.' in s else s.upper() 
                           for s in symbols if isinstance(s,str)]
            # 构建Query 通过sm读取HKU存的这些symbol的数据
            query = Query(Datetime(start_date), Datetime(end_date), 
                          ktype, recover)
            for s in symbols:
                try:
                    stock = sm[s]
                    kdata = stock.get_kdata(query)
                    df = kdata.to_df()
                    df['symbol'] = s
                    dfs.append(df)
                except:
                    logger.warning(f"symbol {s} not found")
                
            df = pd.concat(dfs, axis=0)
            
            # 缓存数据（限制缓存大小）
            if len(HKUDataloader._data_cache) > 3:
                # 删除最旧的缓存
                oldest_key = next(iter(HKUDataloader._data_cache))
                del HKUDataloader._data_cache[oldest_key]
            HKUDataloader._data_cache[cache_key] = df.copy()
        if set_index:
            df.sort_index(inplace=True,ascending=True)
        else:
            if df.index.name == 'datetime':
                df.reset_index(inplace=True, drop=False)
            df.sort_values(by='datetime', ascending=True, inplace=True)
            df.reset_index(drop=True, inplace=True)

        return df

    @staticmethod
    def load_col_pivot_df(symbols: list[str], col='close', start_date='20100101', 
            end_date=datetime.now().strftime('%Y%m%d'),
            freq = 'D', recover = 'NO_RECOVER'):
        '''
        从HKU的数据中多品种的指定列的数据,返回透视图, 适合多品种的同列对比
        '''      
        ktype = HKUDataloader.get_ktype(freq)
        df_all = HKUDataloader.load_df_all(symbols, set_index=True,
                                      start_date=start_date, end_date=end_date,
                                      freq=ktype, recover=recover)
        if col not in df_all.columns:
            logger.error(f'{col}列不存在')
            return None
        df_close = df_all.pivot_table(values=col, index=df_all.index, columns='symbol')
        return df_close





    # def load_sectors(): # TODO: 修缮
    #     """加载股票行业分类数据
        
    #     使用中万一级行业分类体系
        
    #     Returns:
    #         pd.DataFrame: 包含股票代码和对应行业分类的DataFrame
    #     """
    #     # 实现加载行业分类数据的逻辑
    #     # 可以从CSV文件、数据库或API加载
    #     # 这里仅示例实现，需根据实际数据源修改
    #     sectors_file = os.path.join(os.path.dirname(__file__), '../../data/sectors.csv')
        
    #     if os.path.exists(sectors_file):
    #         sectors = pd.read_csv(sectors_file, index_col=0)
    #     else:
    #         # 示例数据，实际使用时替换为真实数据
    #         data = {
    #             'asset': ['688721.XSHG', '603310.XSHG', '301611.XSHE', '301571.XSHE', '603207.XSHG'],
    #             'sector': ['电子', '基础化工', '电子', '国防军工', '医药生物']
    #         }
    #         sectors = pd.DataFrame(data)
    #         sectors = sectors.set_index('asset')
        
    #     return sectors

    # def get_sector_dummies(universe: List[str], days: List[datetime.date]): # TODO: 修缮
    #     """获取行业虚拟变量表
        
    #     返回结果是一个以 asset, date 为索引，行业名称为列，0/1 为值的 dataFrame。
    #     注意本实现中，我们使用的是中万一级行业分类。
        
    #     Args:
    #         universe: 股票列表，因子生成范围
    #         days: 日期列表，因子生成日期。
            
    #     Returns:
    #         以 asset, date 为索引，行业名称为列，0/1 为值的 dataFrame
    #     """
    #     sectors = load_sectors()
    #     filtered = sectors[sectors.index.isin(universe)]
        
    #     dfs = []
    #     for day in days:
    #         df = filtered.to_frame(name="sector")
    #         df["date"] = day
    #         df = df.reset_index().set_index(["date", "asset"])
    #         dfs.append(df)
        
    #     df = pd.concat(dfs)
    #     return pd.get_dummies(df, columns=["sector"])

    # def get_valuation(instruments: Tuple[str], days: List[datetime.date]):
    #     """获取对数化后的各 instrument 对应日期的市值
        
    #     Args:
    #         instruments: 股票代码
    #         days: 日期列表，与因子生成日期对应
    #     Return:
    #         返回以日期、instrument 为索引，对数化市值为单元格值的 DataFrame
    #     """
    #     dfs = []
    #     for day in days:
    #         valuation = fetch_valuation(day)
    #         filtered = valuation[valuation.index.isin(instruments)]
    #         filtered = filtered.to_frame("market_value")
    #         filtered["date"] = day
    #         dfs.append(filtered)
        
    #     df = pd.concat(dfs)
    #     df.set_index(["date", df.index], inplace=True)
    #     return df

    # def get_clean_factor(factor: pd.DataFrame,  # TODO: 待修缮
    #                     neutral_mkt = True,
    #                     neutral_sector = False)->pd.DataFrame:
    #     """对原生因子`factor`进行预处理: 去极值、标准化、中性化、缺失值处理
        
    #     Args:
    #         factor: 提取的原生因子
    #         neutral_mkt: 是否要做市值中性化
    #         neutral_sector: 是否要做行业中性化
        
    #     Return:
    #         处理后的因子。以日期为索引，instrument 为列，单元格值为因子值
    #     """
    #     # 去极值
    #     factor = mad_clip(factor['mom'].unstack(level='asset'))
        
    #     # 缺失值处理，如果在这一步不进行处理，则后面的标准化，特别是中性化将无法计算
    #     factor = handle_missed(factor)
        
    #     # 对因子进行标准化，ZSCORE 可以直接处理 DATAFRAME
    #     factor = zscore(factor, axis=1)
        
    #     instruments = factor.columns.to_list()
    #     days = factor.index.to_list()
        
    #     cap = pd.DataFrame([])
    #     if neutral_mkt:
    #         cap = np.log(get_valuation(instruments, days))
        
    #     industry = pd.DataFrame([])
    #     if neutral_sector:
    #         industry = get_sector_dummies(instruments, days)
        
    #     X = pd.concat((cap, industry), axis=1)
    #     X = X.dropna(how = "any", axis=0)
        
    #     model = LinearRegression(fit_intercept=False)
    #     residues = []
        
    #     for date, group in X.groupby(by="date", group_keys=True):
    #         Xi = group.droplevel(0)
    #         Xi = Xi[Xi.index.isin(instruments)]
    #         y = factor[factor.index == date].T
            
    #         res = model.fit(Xi, y)
    #         coef = res.coef_
            
    #         residue = y - np.dot(Xi, coef.T)
    #         residues.append(residue)
        
    #     clean_factor = pd.concat(residues, axis=1)
    #     return clean_factor.T.stack(level='asset')

    # def regression_test(factor, returns): # TODO: 修缮
    #     """因子回归测试函数
        
    #     对每个交易日截面上的因子值与对应的未来收益率进行回归分析，
    #     使用稳健回归方法(RLM)减少异常值影响
        
    #     Args:
    #         factor: 因子值，多级索引DataFrame，索引包含date和asset
    #         returns: 收益率，多级索引DataFrame，索引包含date和asset
            
    #     Returns:
    #         factor_return: 各日期回归系数列表
    #         t: 各日期t统计量列表
    #     """
    #     # 去除缺失值
    #     factor = factor.dropna()
    #     returns = returns.dropna()
        
    #     # 获取因子日期列表
    #     factor_dates = factor.index.get_level_values("date")
        
    #     # 计算有效日期（returns中存在但factor中不存在的日期被排除）
    #     valid_dates = returns.index.drop(returns.index.difference(factor_dates))
        
    #     # 初始化结果列表
    #     factor_return = []
    #     t = []
        
    #     # 对每个有效日期进行回归
    #     for date in valid_dates:
    #         # 获取对应日期的收益率和因子
    #         y = returns.loc[date]
    #         x = factor.xs(date, level="date")
            
    #         # 使用稳健回归模型(Huber方法)
    #         model = sm.RLM(y.values.T, x.T.values, M=sm.robust.norms.HuberT())
    #         res = model.fit()
            
    #         # 存储回归系数和t值
    #         factor_return.append(res.params.item())
    #         t.append(res.tvalues.item())
        
    #     return factor_return, t



if __name__ == "__main__":
    # 示例代码
    loader = HKUDataloader()
    print(loader.get_HKU_symbols())
    
    # 获取单只股票的DataFrame
    # df = loader.get_df(['000001.SH'])
    # df = loader.get_df(['159915.SZ'],
    #                    set_index=True)
    # dfp = loader.get(['159915.SZ'], col='close')
    # print(df)
    
    # 获取多只股票的DataFrame并转换为透视图
    # df = loader.get(['000001.SH', '000002.SH'], pivot=True)
    # print(
