import pandas as pd
import numpy as np
from datetime import datetime,time
from pytdx.hq import TdxHq_API
import akshare as ak
import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent.parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
from config import DATA_DIR_FEATHER, DATA_DIR_OTHER

code = '510050.SH'
interval = '15m'

''' ================================ S1: 增量下载小周期etf数据(tdx接口) ============================== '''
interval_num = ''.join(filter(str.isdigit, interval))
path_name = DATA_DIR_FEATHER.joinpath('etfs').joinpath(code + '_' + interval_num + '.feather')
data = pd.read_feather(path_name)
data.reset_index(inplace=True)
data.rename(columns={'date': 'timestamp'}, inplace=True)  # 对其中etime一列重命名为timestamp
data = data[['timestamp', 'open', 'high', 'low', 'close', 'volume']]  # 截取timestamp、高开低收、收盘价这几列

data['timestamp'] = pd.to_datetime(data['timestamp'])
current_dt = datetime.combine(datetime.now().date(),time(15,0))
dt_diff = current_dt - pd.to_datetime(data['timestamp'].iloc[-1])
# check_start_day = pd.to_datetime(data['timestamp'].iloc[-1]) + pd.DateOffset(days=1)
check_start_day = pd.to_datetime(data['timestamp'].iloc[-1]) + pd.Timedelta(minutes=15)
print("--   incremental Kdata begin:     {}".format(check_start_day.date()))

minutes_15_k=["09:45","10:00","10:15","10:30","10:45","11:00","11:15","11:30","13:15","13:30","13:45","14:00","14:15","14:30","14:45","15:00"]  # 每个bar的结束时间点
i_cnt = dt_diff.days*len(minutes_15_k) // 800

api = TdxHq_API()
current_data = pd.DataFrame()
if api.connect('**************', 7709):  # 注意这里的IP地址和数据接口
    for i in range(i_cnt,-1,-1):
        temp_data = api.to_df(
            api.get_security_bars(1, 1, '510050', i*800, 800))  # 注意这里，第一个1表示是15分钟的数据，其中0为5分钟K线 1 15分钟K线 2 30分钟K线 3 1小时K线 4 日K线
        current_data = pd.concat([current_data, temp_data], axis=0)
    api.disconnect()  # 调用完以后一定要关闭接口
else:
    print("connect failed")
current_data = current_data[['datetime', 'open', 'high', 'low', 'close', 'vol']]
current_data.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']  # 对current_data这列重命名
current_data['timestamp'] = pd.to_datetime(current_data['timestamp'])

history_data_to_add = 500  # 需要添加的历史数据行数
additional_data = data.iloc[-history_data_to_add:]
data_to_verified = pd.concat([additional_data, current_data], axis=0)  # 第四步用
data_to_verified = data_to_verified.sort_values(by='timestamp', ascending=True)  # 要以timestamp作为排序基准
data_to_verified = data_to_verified.drop_duplicates('timestamp')  # 注意这一步非常重要，把相同的timestamp去掉
data_to_verified = data_to_verified.reset_index(drop=True)  # 重置索引，这里的drop=True和del data['index']作用一样
data_to_verified = data_to_verified.set_index('timestamp')
data_to_verified.index.name = 'date'

data = pd.concat([data, current_data], axis=0)  # 合并数据
data = data.sort_values(by='timestamp', ascending=True)  # 注意这一步是非常必要的，要以timestamp作为排序基准
data = data.drop_duplicates('timestamp')  # 注意这一步非常重要，把相同的timestamp去掉
data = data.reset_index(drop=True)  # 重置索引，这里的drop=True和del data['index']作用一样
check_end_day = data['timestamp'].iloc[-1]
print("--   incremental Kdata end:       {}".format(check_end_day.date()))
data = data.set_index('timestamp')

''' ========================= S2: 下载日线数据, check增量下载的小周期数据是否完整(resample) =================== '''
data_resampled = data.loc[check_start_day-pd.DateOffset(days=1):check_end_day].resample('D').agg({
    'open': 'first',
    'high': 'max',
    'low': 'min',
    'close': 'last',
    'volume': 'sum'
})
data_resampled.dropna(inplace=True)
data_resampled = data_resampled.drop(data_resampled.index[0])
i_daily_cnt = dt_diff.days // 800
current_data = pd.DataFrame() 
# get daily k from tdx
if api.connect('**************', 7709):
    for i in range(i_daily_cnt,-1,-1):
        temp_data = api.to_df(
            api.get_security_bars(4, 1, '510050', i*800, 800))  # 注意这里，第一个1表示是15分钟的数据，其中0为5分钟K线 1 15分钟K线 2 30分钟K线 3 1小时K线 4 日K线
        current_data = pd.concat([current_data, temp_data], axis=0)    
    api.disconnect()
current_data = current_data[['datetime', 'open', 'high', 'low', 'close', 'vol']]
current_data.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']  # 对current_data这列重命名
current_data['timestamp'] = pd.to_datetime(current_data['timestamp'])
current_data = current_data.sort_values(by='timestamp', ascending=True)
current_data = current_data.drop_duplicates('timestamp')
current_data = current_data.reset_index(drop=True)
current_data = current_data.set_index('timestamp')

df_tmp = current_data.loc[check_start_day:check_end_day]
df_tmp.index = pd.to_datetime(df_tmp.index.date)
print(f"df_tmp shape: {df_tmp.shape}")
print(f"data_resampled shape: {data_resampled.shape}")

data.index.name = 'date'
rs = np.allclose(df_tmp, data_resampled, atol=1e-5)
if rs:
    print('check well!')
    data.reset_index(drop=False).to_feather(path_name)   # NOTE:写入文件, 索引变为普通列
    print(data.info())
    print(data.shape)
    print(data.tail(3))
else:
    print('check bad!')

''' ================================ S3: check并增量更新交易日历文件 ============================== '''
astock_calendar_path = DATA_DIR_OTHER.joinpath('astock_calendar.csv')
# df_dl_astock_calendar = ak.tool_trade_date_hist_sina()
# df_dl_astock_calendar['trade_date'] = pd.to_datetime(df_dl_astock_calendar['trade_date'])
# ''' >> 为pandas日期格式 timestamp64[ns]
#      trade_date
# 0    1990-12-19
# '''
# # df_dl_astock_calendar.to_csv(astock_calendar_path,index=False,header=True) # 初始化交易日历(全量)

# df_exist_astock_calendar = pd.read_csv(astock_calendar_path).dropna()
# df_exist_astock_calendar['trade_date'] = pd.to_datetime(df_exist_astock_calendar['trade_date'])
# df_new_astock_calendar = (df_dl_astock_calendar[~df_dl_astock_calendar.isin(df_exist_astock_calendar)]).dropna()

# if not df_new_astock_calendar.empty:
#     df_new_astock_calendar.to_csv(astock_calendar_path, mode='a', index=False, header=False)
#     print('incr. calendar date: bgn -- {}'.format(df_new_astock_calendar['trade_date'].iloc[0].strftime('%Y-%m-%d')))
#     print('incr. calendar date: end -- {}'.format(df_new_astock_calendar['trade_date'].iloc[-1].strftime('%Y-%m-%d')))
    
''' ================= S4: 根据交易日历文件(日级)和minutes_15_k变量 校验全量数据的连续性和完整性 ======================== '''
# 读取交易日历文件
df_exist_astock_calendar = pd.read_csv(astock_calendar_path).dropna()
df_exist_astock_calendar['trade_date'] = pd.to_datetime(df_exist_astock_calendar['trade_date'])

# 定义15分钟K线的时间戳
minutes_15_k = ["09:45", "10:00", "10:15", "10:30", "10:45", "11:00", "11:15", "11:30",
                "13:15", "13:30", "13:45", "14:00", "14:15", "14:30", "14:45", "15:00"]

all_trading_minutes = []

# 获取data的第一行和最后一行数据的时间戳
start_timestamp = data_to_verified.index[0]
end_timestamp = data_to_verified.index[-1]

start_timestamp = datetime.combine(start_timestamp.date(), datetime.min.time())
end_timestamp = datetime.combine(end_timestamp.date(), datetime.max.time())

relevant_trading_dates = df_exist_astock_calendar[
    (df_exist_astock_calendar['trade_date'] >= start_timestamp) &
    (df_exist_astock_calendar['trade_date'] <= end_timestamp)
]['trade_date'].dt.strftime('%Y-%m-%d').values

# 为每个相关交易日生成15分钟K线的时间戳
all_trading_minutes = []
for date_str in relevant_trading_dates:
    for time_str in minutes_15_k:
        timestamp = pd.to_datetime(date_str + ' ' + time_str)
        if timestamp >= start_timestamp and timestamp <= end_timestamp:
            all_trading_minutes.append(timestamp)

# 将时间戳列表转换为DataFrame
all_trading_minutes_df = pd.DataFrame(all_trading_minutes, columns=['timestamp'])

# 检查数据集中缺失的交易日
missing_minutes = all_trading_minutes_df[~all_trading_minutes_df['timestamp'].isin(data_to_verified.index)]
missing_minutes = missing_minutes.set_index('timestamp')

# 检查数据集中缺失的数据点
missing_data = missing_minutes.join(data_to_verified, how='left')
missing_data = missing_data[missing_data.isnull().any(axis=1)]

# 打印结果
if missing_data.empty:
    print("数据连续性和完整性校验通过，没有缺失的数据点。")
else:
    print("数据连续性和完整性校验发现缺失的数据点：")
    print(missing_data)

# 如果需要，可以将缺失的数据点保存到文件
# missing_data.to_csv('missing_data.csv')
