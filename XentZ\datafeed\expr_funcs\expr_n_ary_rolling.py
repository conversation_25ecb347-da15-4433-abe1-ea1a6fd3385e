import pandas as pd
import numpy as np
import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent.parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
from datafeed.expr_funcs.expr_utils import calc_by_symbol, calc_by_date

''' ts_开头的只有 ts_vwap '''
@calc_by_symbol
def rl_vwap(h,l,c,v, N:int) -> pd.Series: # 不用norm 会在dataloader里统一处理
    x = (((h + l + c) / 3) * v).rolling(window=N, min_periods=int(N/2)).sum() / v.rolling(window=N, min_periods=int(N/2)).sum()
    return pd.Series(x)

@calc_by_symbol
def cm_vwap(h,l,c,v) -> pd.Series: # 不用norm 会在dataloader里统一处理
    x = (((h + l + c) / 3) * v).cumsum() / v.cumsum()
    return pd.Series(x)

@calc_by_symbol
def eom(high, low, volume, N=14): # EOM（Ease of Movement）
    mid_price = (high + low) / 2
    price_change = np.diff(mid_price, prepend=mid_price.iloc[0])
    box_ratio = volume / (high - low)
    eom_raw = price_change / box_ratio
    eom = pd.Series(eom_raw).rolling(window=N, min_periods=1).mean()
    return eom

if __name__ == "__main__":
    dates = pd.date_range('20230101', periods=5)
    high_prices = pd.Series([100, 105, 110, 108, 112], index=dates)
    low_prices = pd.Series([95, 100, 102, 105, 107], index=dates)
    close_prices = pd.Series([98, 102, 105, 106, 109], index=dates)
    volumes = pd.Series([1000, 1500, 2000, 1200, 1800], index=dates)

    # 调用cm_vwap函数计算CVWAP
    cvwap_series = cm_vwap(high_prices, low_prices, close_prices, volumes)

    print("Cumulative Volume Weighted Average Price (CVWAP):")
    print(cvwap_series)