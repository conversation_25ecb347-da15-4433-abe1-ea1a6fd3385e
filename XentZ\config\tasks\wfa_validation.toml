# WFA (Walk-Forward Analysis) 动态稳健性检验任务配置
dynaconf_include = ["_datasets/ts_single_etf.toml"]

[task]
name = "WFA_Validation_on_Single_ETF"
desc = "对单ETF数据集进行滚动窗口前向分析，以检验策略或因子的稳健性"

# ============================== 数据切分 ============================== #
[time_split]
# 定义不同阶段的数据范围, 若不指定end_date, 则默认取到最新
train_start_date = "2020-01-01"
# train_end_date = "2023-01-01"

target_start_date = "2021-01-01"
# target_end_date = "2024-01-01"

# ============================== 交叉验证/WFA定义 ============================== #
[cross_validation]
type = "wfa" # walk-forward analysis
train_window = 750 # 训练期窗口, 交易日数
validation_window = 250 # 验证期窗口
test_window = 250 # 测试期(OOS)窗口
step = 125 # 步进长度 