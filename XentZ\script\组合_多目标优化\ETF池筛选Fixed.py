from hikyuu import *
from hikyuu.interactive import *
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

from datafeed.hku_dataloader import HKUDataloader
from config import REPORTS_DIR

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

''' =========  基本参数集中配置   ========= '''
LOOKBACK_PERIOD = 252  # 一年
LONG_WINDOW = 252 # 要求: 小于等于LOOKBACK_PERIOD
SHORT_WINDOW = 21

def calculate_diversity_score(corr_matrix, etf):
    """计算ETF的多样性得分，使用改进的方法"""
    corrs = corr_matrix[etf].drop(etf)  # 排除自身
    
    # 1. 计算绝对相关性
    abs_corrs = corrs.abs()
    avg_abs_corr = abs_corrs.mean()
    
    # 2. 计算负相关资产比例 (特别有价值的对冲资产)
    neg_corr_ratio = (corrs < -0.2).mean()
    
    # 3. 计算高相关资产比例 (潜在冗余)
    high_corr_ratio = (abs_corrs > 0.7).mean()
    
    # 综合多样性指标 (值越高越好)
    diversity_score = (1 - avg_abs_corr) * 0.6 + neg_corr_ratio * 0.3 + (1 - high_corr_ratio) * 0.1
    
    return diversity_score, avg_abs_corr, neg_corr_ratio, high_corr_ratio

def normalize_metric(values, method='minmax', center=None, scale=1.0):
    
    values = np.array(values)
    
    if method == 'minmax':
        min_val, max_val = min(values), max(values)
        if max_val > min_val:
            return (values - min_val) / (max_val - min_val)
        return np.ones_like(values) * 0.5
    
    elif method == 'sigmoid':
        if center is None:
            center = np.mean(values)
        return 1 / (1 + np.exp(-(values - center) / scale))
    
    elif method == 'zscore':
        mean, std = np.mean(values), np.std(values)
        if std > 0:
            z_scores = (values - mean) / std
            # 将z-score转换到[0,1]范围，假设z-score在[-3,3]范围内
            return (z_scores + 3) / 6
        return np.ones_like(values) * 0.5
    
    elif method == 'rank':
        # 排名标准化，简单且对异常值不敏感
        ranks = np.argsort(np.argsort(values))
        return ranks / (len(ranks) - 1)
    
    elif method == 'hybrid':
        # 混合方法：对极端值使用排名，对中间值使用sigmoid
        ranks = np.argsort(np.argsort(values)) / (len(values) - 1)
        
        # 计算sigmoid值
        if center is None:
            center = np.mean(values)
        sigmoid = 1 / (1 + np.exp(-(values - center) / scale))
        
        # 混合：极端值(前10%和后10%)使用排名，中间值使用sigmoid
        result = np.copy(sigmoid)
        extreme_mask = (ranks < 0.1) | (ranks > 0.9)
        result[extreme_mask] = ranks[extreme_mask]
        
        return result
    
    return values  # 默认返回原始值

def do_select(returns_df, lookback_period=252, 
              max_etfs=15, min_etfs=8, corr_threshold=0.7):
    """
    returns_df: ETF收益率DataFrame
    lookback_period: 回看期(默认一年)
    max_etfs: 最大ETF数量
    min_etfs: 最小ETF数量
    corr_threshold: 相关性阈值，高于此值的ETF对将被筛选
    """    
    # 计算相关性矩阵
    corr_matrix = returns_df.corr()
    # 计算每个ETF的关键指标
    etf_metrics = {}
    # 收集所有指标的值，用于后续标准化
    all_returns = []
    all_sharpes = []
    all_drawdowns = []
    all_diversity_scores = []
    
    for etf in returns_df.columns:
        # 计算年化收益率
        annual_return = returns_df[etf].mean() * 252
        all_returns.append(annual_return)
        
        # 计算年化波动率
        annual_vol = returns_df[etf].std() * np.sqrt(252)
        
        # 计算最大回撤
        cum_returns = (1 + returns_df[etf]).cumprod()
        running_max = cum_returns.cummax()
        drawdown = (cum_returns / running_max) - 1
        max_drawdown = drawdown.min()
        all_drawdowns.append(max_drawdown)
        
        # 计算夏普比率 (假设无风险利率为3%)
        sharpe = (annual_return - 0.03) / annual_vol if annual_vol > 0 else 0
        all_sharpes.append(sharpe)
        
        # 计算改进的多样性指标
        diversity_score, avg_abs_corr, neg_corr_ratio, high_corr_ratio = calculate_diversity_score(corr_matrix, etf)
        all_diversity_scores.append(diversity_score)
        
        etf_metrics[etf] = {
            'return': annual_return,
            'volatility': annual_vol,
            'max_drawdown': max_drawdown,
            'sharpe': sharpe,
            'diversity_score': diversity_score,
            'avg_abs_corr': avg_abs_corr,
            'neg_corr_ratio': neg_corr_ratio,
            'high_corr_ratio': high_corr_ratio
        }
    # 对夏普比率使用sigmoid，因为我们希望强调中等表现和优秀表现的差异
    capped_sharpes = [max(s, -3) for s in all_sharpes]  # 将极端负值截断在-3
    sharpe_norm = normalize_metric(capped_sharpes, method='sigmoid', center=0, scale=0.7)
    drawdown_norm = 1 - normalize_metric(all_drawdowns, method='minmax')
    diversity_norm = normalize_metric(all_diversity_scores, method='minmax')
    # 计算标准化后的评分
    for i, etf in enumerate(etf_metrics.keys()):
        etf_metrics[etf]['norm_diversity'] = diversity_norm[i]
        etf_metrics[etf]['norm_sharpe'] = sharpe_norm[i]
        etf_metrics[etf]['norm_drawdown'] = drawdown_norm[i]
        # 综合评分 - 所有指标都在[0,1]范围内
        if all_sharpes[i] < 0:
            # 负夏普比率时，降低夏普在评分中的权重，增加多样性权重
            score = 0.6 * diversity_norm[i] + 0.2 * sharpe_norm[i] + 0.2 * drawdown_norm[i]
        else:
            # 正夏普比率时，使用原始权重
            score = 0.5 * diversity_norm[i] + 0.3 * sharpe_norm[i] + 0.2 * drawdown_norm[i]
        etf_metrics[etf]['score'] = score
    
    # 第一步：按多样性得分排序，选择前max_etfs*5个ETF作为候选
    sorted_by_diversity = sorted(etf_metrics.items(), 
                                key=lambda x: x[1]['diversity_score'], 
                                reverse=True)
    
    candidate_etfs = [item[0] for item in 
                      sorted_by_diversity[:min(max_etfs*5, len(sorted_by_diversity))]]
    
    # 第二步：使用贪心算法选择最终ETF池
    selected_etfs = []
    # 首先选择多样性最高的ETF
    selected_etfs.append(candidate_etfs[0])
    # 贪心选择剩余ETF
    while len(selected_etfs) < max_etfs and len(candidate_etfs) > 0:
        best_etf = None
        best_score = float('-inf')
        
        for etf in candidate_etfs:
            if etf in selected_etfs:
                continue
            
            # 计算与已选ETF的相关系数
            corrs = [corr_matrix.loc[etf, selected] for selected in selected_etfs]
            max_corr = max(corrs)
            
            # 如果相关性太高，跳过
            if max_corr > corr_threshold:
                continue
            
            # 使用预先计算的标准化评分
            score = etf_metrics[etf]['score']
            
            if score > best_score:
                best_score = score
                best_etf = etf
        
        # 如果找不到合适的ETF，或已达到最小要求，则停止
        if best_etf is None:
            if len(selected_etfs) >= min_etfs:
                break
            else:
                # 放宽相关性限制，继续寻找
                corr_threshold += 0.1
                continue
        
        # 添加最佳ETF到选择列表
        selected_etfs.append(best_etf)
    
    # 打印选择结果
    print(f"\n为轮动策略选择的{len(selected_etfs)}个ETF池:")
    print("代码\t综合得分\t年化收益\t波动率\t最大回撤\t夏普比率\t多样性\t负相关比例")
    for etf in selected_etfs:
        metrics = etf_metrics[etf]
        print(f"{etf}\t{metrics['score']:.4f}\t{metrics['return']:.2%}\t{metrics['volatility']:.2%}\t"
              f"{metrics['max_drawdown']:.2%}\t{metrics['sharpe']:.2f}\t"
              f"{metrics['diversity_score']:.2f}\t{metrics['neg_corr_ratio']:.2f}")
    
    # 计算并打印选中ETF之间的相关性矩阵
    sub_matrix = corr_matrix.loc[selected_etfs, selected_etfs]
    print("\n选中ETF之间的相关性矩阵:")
    print(sub_matrix)
    
    # 计算平均相关系数
    corr_values = []
    for i in range(len(selected_etfs)):
        for j in range(i+1, len(selected_etfs)):
            corr_values.append(sub_matrix.iloc[i, j])
    
    avg_corr = sum(corr_values) / len(corr_values) if corr_values else 0
    
    print(f"\n选中ETF的平均相关系数: {avg_corr:.4f}")
    
    return selected_etfs

def select_etf_and_load_data():
    ''' ==================== 1.ETF初筛(专家经验) ==================== '''
    query = Query(-LOOKBACK_PERIOD,recover_type=Query.EQUAL_BACKWARD)
    
    stks = [s for s in sm if s.type == (constant.STOCKTYPE_ETF) and 
            MA(AMO(s.get_kdata(query)), n=LONG_WINDOW)[-1] >= 10000 and # 日均1亿额
            MA(STDEV(CLOSE(s.get_kdata(query)), n=SHORT_WINDOW)/MA(CLOSE(s.get_kdata(query)), n=SHORT_WINDOW), n=LONG_WINDOW)[-1] > 0.01 and # 1%波动(去量纲后)
            s.start_datetime <= Datetime(2020, 1, 1)] # 上市日期不晚于2020年1月1日

    ''' ==================== 2.ETF综合筛(corr/shp/mdd综合评分) ==================== '''
    returns_dict = {}
    for stk in stks:
        try:
            kdata = stk.get_kdata(query)  # 获取一年数据
            close = pd.Series([k.close for k in kdata], index=[k.datetime for k in kdata])
            returns = close.pct_change().dropna()
            returns_dict[stk.market_code] = returns
        except Exception as e:
            print(f"处理{stk.market_code}时出错: {e}")
            
    returns_df = pd.DataFrame(returns_dict)
    
    corr_matrix = returns_df.corr()

    # 使用相关性筛选算法选择ETF
    selected_etfs = do_select(
        returns_df,
        lookback_period=LOOKBACK_PERIOD,  # 一年
        max_etfs=15,          # 最多选择15只ETF
        min_etfs=8,           # 至少选择8只ETF
        corr_threshold=0.5    # 相关性阈值
    )

    # 输出筛选结果
    print("\n筛选后的ETF列表:")
    for code in selected_etfs:
        stk = sm[code]
        print(f"{code} {stk.name}")

    print(f"筛选前ETF数量: {len(stks)}")
    print(f"筛选后ETF数量: {len(selected_etfs)}")

    ''' ==================== 3.组装etf池品种收益率透视图 ==================== '''
    start_date = '20100101'  # 可根据需要调整
    end_date = datetime.now().strftime('%Y%m%d')

    # 调用HKUDataloader的get方法获取透视图
    # 参数说明: symbols列表, 需要的列(默认close), 开始日期, 结束日期, 频率(D日线), 复权方式
    returns_df = HKUDataloader.get(selected_etfs, col='close', 
                                start_date=start_date, end_date=end_date,
                                freq='D', recover='EQUAL_BACKWARD')
    # 计算日收益率
    returns_df = returns_df.pct_change().dropna()
    # 查看数据格式
    print("\n收益率数据示例:")
    print(returns_df.head(3))
    
    return returns_df

if __name__ == '__main__':
    returns_df = select_etf_and_load_data()
    # 设置生成报告存放位置
    ts = datetime.now().strftime("%Y%m%d_%H%M")
    script_name = os.path.basename(__file__).split('.')[0]
    outdir = REPORTS_DIR.joinpath(f"{script_name}_{ts}")
    os.makedirs(outdir, exist_ok=True)
    
    # 保存选中的ETF列表
    selected_etfs = returns_df.columns.tolist()
    with open(os.path.join(outdir, "selected_etfs.txt"), "w", encoding="utf-8") as f:
        for etf in selected_etfs:
            stk = sm[etf]
            f.write(f"{etf}, # {stk.name}\n")
    
    # 保存收益率数据到CSV
    returns_df.to_csv(os.path.join(outdir, "etf_returns.csv"))
    
    # 计算并保存相关性矩阵
    corr_matrix = returns_df.corr()
    corr_matrix.to_csv(os.path.join(outdir, "etf_corr.csv"), encoding="utf-8-sig")
    
    # 绘制并保存相关性热力图
    plt.figure(figsize=(12, 10))
    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, fmt='.2f')
    plt.title("选中ETF之间的相关性热力图")
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig(os.path.join(outdir, "etf_corr_heatmap.png"), dpi=300)
    plt.close()
    
    # 计算平均相关系数和最大相关系数
    corr_values = []
    for i in range(len(selected_etfs)):
        for j in range(i+1, len(selected_etfs)):
            corr_values.append(corr_matrix.iloc[i, j])
    
    avg_corr = sum(corr_values) / len(corr_values) if corr_values else 0
    max_corr = max(corr_values) if corr_values else 0
    min_corr = min(corr_values) if corr_values else 0
    
    # 保存相关性统计数据
    with open(os.path.join(outdir, "correlation_stats.txt"), "w", encoding="utf-8") as f:
        f.write(f"选中ETF数量: {len(selected_etfs)}\n")
        f.write(f"平均相关系数: {avg_corr:.4f}\n")
        f.write(f"最大相关系数: {max_corr:.4f}\n")
        f.write(f"最小相关系数: {min_corr:.4f}\n")
        f.write(f"负相关比例: {sum(1 for c in corr_values if c < 0) / len(corr_values):.2%}\n")
    
    # 打印结果摘要
    print(f"\n选中的ETF数量: {len(selected_etfs)}")
    print(f"收益率数据形状: {returns_df.shape}")
    print(f"数据时间范围: {returns_df.index[0]} 至 {returns_df.index[-1]}")
    print(f"平均相关系数: {avg_corr:.4f}")
    print(f"最大相关系数: {max_corr:.4f}")
    print(f"最小相关系数: {min_corr:.4f}")
    print(f"\n📂 结果保存在 {outdir}/")
    
    # 确定样本内(in)和样本外(out)的时间范围
    all_dates = returns_df.index
    in_start_date = all_dates[-LOOKBACK_PERIOD] if len(all_dates) > LOOKBACK_PERIOD else all_dates[0]
    
    # 分割样本内和样本外数据
    returns_in = returns_df[returns_df.index >= in_start_date]
    returns_out = returns_df[returns_df.index < in_start_date]
    
    # 计算等权重组合
    weights = np.ones(len(selected_etfs)) / len(selected_etfs)
    
    # 计算样本内(in)的等权重组合收益率和净值
    port_in = (returns_in * weights).sum(axis=1)
    nav_in = (1 + port_in).cumprod()
    
    # 计算样本外(out)的等权重组合收益率和净值
    if len(returns_out) > 0:
        port_out = (returns_out * weights).sum(axis=1)
        nav_out = (1 + port_out).cumprod()
    else:
        nav_out = pd.Series([], dtype=float)
    
    # 保存净值数据
    nav_in.to_csv(os.path.join(outdir, "nav_equal_weight_in.csv"), encoding="utf-8-sig")
    if len(nav_out) > 0:
        nav_out.to_csv(os.path.join(outdir, "nav_equal_weight_out.csv"), encoding="utf-8-sig")
    
    # 绘制样本内(in)净值曲线
    plt.figure(figsize=(12, 6))
    nav_in.plot(title=f"样本内等权重净值曲线 ({in_start_date.strftime('%Y-%m-%d')}至今)")
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.ylabel("净值")
    plt.tight_layout()
    plt.savefig(os.path.join(outdir, "nav_equal_weight_in.png"), dpi=300)
    plt.close()
    
    # 绘制完整净值曲线(样本外+样本内)
    if len(nav_out) > 0:
        plt.figure(figsize=(14, 7))
        
        # 调整样本内净值起点与样本外终点对齐
        scale_factor = nav_out.iloc[-1] if len(nav_out) > 0 else 1.0
        nav_in_scaled = nav_in / nav_in.iloc[0] * scale_factor
        
        # 创建完整的净值序列
        full_dates = list(nav_out.index) + list(nav_in.index)
        full_values = list(nav_out.values) + list(nav_in_scaled.values)
        nav_full = pd.Series(full_values, index=full_dates)
        
        # 绘制完整净值曲线
        nav_full.plot(label="等权重组合净值")
        plt.axvline(x=in_start_date, color='r', linestyle='--', 
                   label=f"样本内/样本外分界 ({in_start_date.strftime('%Y-%m-%d')})")
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.title("等权重组合完整净值曲线")
        plt.ylabel("净值")
        plt.tight_layout()
        plt.savefig(os.path.join(outdir, "nav_equal_weight_full.png"), dpi=300)
        plt.close()
        
        # 计算并保存等权重组合的绩效指标
        annual_return_in = port_in.mean() * 252
        annual_vol_in = port_in.std() * np.sqrt(252)
        sharpe_in = annual_return_in / annual_vol_in if annual_vol_in > 0 else 0
        
        cum_returns_in = (1 + port_in).cumprod()
        max_drawdown_in = ((cum_returns_in / cum_returns_in.cummax()) - 1).min()
        
        if len(port_out) > 0:
            annual_return_out = port_out.mean() * 252
            annual_vol_out = port_out.std() * np.sqrt(252)
            sharpe_out = annual_return_out / annual_vol_out if annual_vol_out > 0 else 0
            
            cum_returns_out = (1 + port_out).cumprod()
            max_drawdown_out = ((cum_returns_out / cum_returns_out.cummax()) - 1).min()
        else:
            annual_return_out = float('nan')
            annual_vol_out = float('nan')
            sharpe_out = float('nan')
            max_drawdown_out = float('nan')
        
        # 保存绩效指标
        with open(os.path.join(outdir, "equal_weight_performance.txt"), "w", encoding="utf-8") as f:
            f.write("=== 等权重组合绩效指标 ===\n\n")
            f.write("--- 样本内(IN) ---\n")
            f.write(f"时间范围: {in_start_date.strftime('%Y-%m-%d')} 至 {all_dates[-1].strftime('%Y-%m-%d')}\n")
            f.write(f"年化收益率: {annual_return_in:.2%}\n")
            f.write(f"年化波动率: {annual_vol_in:.2%}\n")
            f.write(f"夏普比率: {sharpe_in:.2f}\n")
            f.write(f"最大回撤: {max_drawdown_in:.2%}\n\n")
            
            if not np.isnan(annual_return_out):
                f.write("--- 样本外(OUT) ---\n")
                f.write(f"时间范围: {all_dates[0].strftime('%Y-%m-%d')} 至 {in_start_date.strftime('%Y-%m-%d')}\n")
                f.write(f"年化收益率: {annual_return_out:.2%}\n")
                f.write(f"年化波动率: {annual_vol_out:.2%}\n")
                f.write(f"夏普比率: {sharpe_out:.2f}\n")
                f.write(f"最大回撤: {max_drawdown_out:.2%}\n")
    
    # 更新结果摘要
    print(f"- 等权重净值曲线(样本内): nav_equal_weight_in.png")
    if len(nav_out) > 0:
        print(f"- 等权重净值曲线(完整): nav_equal_weight_full.png")
        print(f"- 等权重组合绩效指标: equal_weight_performance.txt")
    
