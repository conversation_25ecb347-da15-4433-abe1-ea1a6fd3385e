from gui.proj_loader import ProjConfig, AlgoConfig, from_toml
from config import DATA_DIR_PRJ
from engine.algos import *


# 网格策略
def gen_grid():
    proj = ProjConfig()
    proj.name = '网格策略'
    proj.commission = 0.0001
    proj.slippage = 0.0001
    proj.symbols = ['B0']  # 证券池列表
    proj.benchmark = '000300.SH'
    proj.start_date = '20100101'

    # 这里是因子列表
    proj.fields = ['max(high,1440)', 'min(low,1440)', '(max+min)/2']
    proj.names = ['max', 'min', 'mid']

    # 这里是策略算子列表
    proj.algos.append(AlgoConfig(name='AlgoGrid', args=[]))
    return proj


# 示例-资产配置-再平衡
def gen_portfolio_rebalance():
    proj = ProjConfig()
    proj.name = '示例-资产配置-再平衡'
    proj.commission = 0.0001
    proj.slippage = 0.0001
    proj.symbols = ['000300.SH', '399006.SZ']  # 证券池列表
    proj.benchmark = '000300.SH'
    proj.start_date = '20100101'
    proj.data_folder = 'index'  # 这里指定data/数据目录

    # 这里是策略算子列表
    proj.algos.append(AlgoConfig(name=RunWeekly().name))  # 再平衡周期
    proj.algos.append(AlgoConfig(name='SelectAll'))  # 选股,直接使用字符串，效果一样
    proj.algos.append(AlgoConfig(name=WeightEqually().name))  # 仓位权重
    proj.algos.append(AlgoConfig(name=Rebalance().name))  # 执行再平衡

    return proj


# ETF动量轮动
def gen_etf_rolling():
    proj = ProjConfig()
    proj.name = 'ETF趋势交易_动量轮动'
    proj.commission = 0.0001
    proj.slippage = 0.0001
    proj.symbols = symbols = [
        '159915.SZ',  # 创业板ETF
        '510300.SH',  # 沪深300ETF
        '518880.SH',  # 黄金ETF
        '513110.SH',  # 纳指100指数
        '513520.SH',  # 日经ETF
    ]  # 证券池列表

    proj.fields = ['roc(close,20)', 'roc_20>0.02', 'roc_20<-0.02', 'slope_pair(high,low,18)', 'rsrs>1.0', 'rsrs<0.8']
    proj.names = ['roc_20', 'buy', 'sell', 'rsrs', 'rsrs_gte', 'rsrs_lte']
    proj.benchmark = '000300.SH'
    proj.start_date = '20100101'
    proj.data_folder = 'etfs'  # 这里指定data/数据目录

    # 这里是策略算子列表
    proj.algos.append(AlgoConfig(name=SelectBySignal().name, kwargs={'rules': ['buy']}))  # 选股,直接使用字符串，效果一样
    proj.algos.append(AlgoConfig(name=SelectHolding().name))  # 选股,直接使用字符串，效果一样
    proj.algos.append(
        AlgoConfig(name=SelectBySignal().name, kwargs={'rules': ['sell'], 'exclude': True}))  # 选股,直接使用字符串，效果一样
    proj.algos.append(AlgoConfig(name=WeightEqually().name))  # 仓位权重
    # proj.algos.append(AlgoConfig(name=PrintTempData().name))
    proj.algos.append(AlgoConfig(name=Rebalance().name))  # 执行再平衡

    return proj


# 静待花开的聚宝盆
def gen_flower():
    proj = ProjConfig()
    proj.name = '静待花开的聚宝盘'
    proj.commission = 0.0001
    proj.slippage = 0.0001
    proj.symbols = [
        '511220.SH',  # 城投债
        '512010.SH',  # 医药
        '518880.SH',  # 黄金
        '163415.SZ',  # 兴全商业
        '159928.SZ',  # 消费
        '161903.SZ',  # 万家行业优选
        '513100.SH'  # 纳指
    ]  # 证券池列表
    proj.benchmark = '000300.SH'
    proj.start_date = '20100101'
    proj.data_folder = 'etfs'  # 这里指定data/数据目录，这里的数据在etfs下

    proj.fields = ['roc(close,20)', 'roc_20>0.02', 'roc_20<-0.02']
    proj.names = ['roc_20', 'buy', 'sell']

    # 这里是策略算子列表
    # proj.algos.append(AlgoConfig(name=PrintDate().name))
    proj.algos.append(AlgoConfig(name='RunDays', args=[5]))  # 再平衡周期

    proj.algos.append(AlgoConfig(name=SelectBySignal().name, kwargs={'rules': ['buy']}))  # 选股,直接使用字符串，效果一样
    proj.algos.append(AlgoConfig(name=SelectHolding().name))  # 选股,直接使用字符串，效果一样
    proj.algos.append(
        AlgoConfig(name=SelectBySignal().name, kwargs={'rules': ['sell'], 'exclude': True}))  # 选股,直接使用字符串，效果一样
    proj.algos.append(AlgoConfig(name=WeightEqually().name))  # 仓位权重
    # proj.algos.append(AlgoConfig(name=PrintTempData().name))
    proj.algos.append(AlgoConfig(name=Rebalance().name))  # 执行再平衡

    return proj


proj = gen_etf_rolling()
print(proj)
df = proj.load_df().dropna()
print(df)
# 保存到目录
#proj.to_toml(path=DATA_DIR_PRJ.resolve())

p = from_toml(DATA_DIR_PRJ.joinpath('{}.toml'.format(proj.name)))
print(p)

algos = p.parse_algos()
print(algos)

df.dropna(inplace=True)
print(df)

from engine.backtrader_engine import BacktraderEngine

e = BacktraderEngine(df, benchmark=p.benchmark,
                     slippage=p.slippage, commission=p.commission)
e.run_algo_strategy(algos)
e.analysis(console=True)
