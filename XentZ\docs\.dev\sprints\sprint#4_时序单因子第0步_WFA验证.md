# Sprint #4: 时序单因子第0步 - WFA动态稳健性检验

## 需求背景
基于 `docs/.dev/06_时序单因子质检.md` 和 `docs/.dev/06-1_时序单因子-第0步.md` 的设计，实现时序单因子的第0步验证：样本内（IS）动态稳健性检验，使用Walk-Forward Analysis (WFA) 方法对L2阶段通过的因子进行高强度压力测试。

## 核心目标
- 实现WFA滚动窗口验证算法
- 集成quantstats专业绩效分析
- 生成丰富的可视化报告
- 与FactorZoo无缝集成
- 支持批量处理和人工审查

## 技术栈选择
```
算法层: numpy + pandas + scipy (WFA核心计算)
可视化: quantstats + matplotlib + seaborn (专业绩效分析)
数据存储: FactorZoo.factor_evaluations (绩效指标存储)
配置层: dynaconf + toml (参数管理)
```

## 总体架构设计

### 1. 文件组织结构
```
XentZ/
├── config/tasks/
│   └── ts_l3_wfa.toml                    # L3阶段WFA验证配置
├── factor/
│   ├── validation_utils.py               # WFA核心算法库 (新增)
│   └── performance_utils.py              # 统一绩效接口 (可选新增)
├── script/投研_因子挖掘集成/
│   └── L3动态稳健性检验.py               # WFA业务流程脚本 (新增)
└── (外部报告目录)
    D:/myquant/reports/XentZ/wfa_validation/
    ├── factor_reports/{factor_id}/       # 按因子组织的详细报告
    └── batch_summary/                    # 批次汇总报告
```

### 2. 核心设计原则
- **过程式编程**: 遵循项目现有风格，避免过度抽象
- **函数复用**: 复用原库绩效计算函数，避免重复开发
- **配置驱动**: 使用统一的配置管理系统
- **可视化优先**: 使用quantstats生成专业报告
- **FactorZoo集成**: 无缝集成现有因子管理体系

### 3. WFA算法核心参数 (经过优化)
```python
# 固定的最优参数配置
TRAINING_WINDOW = 750    # 约3年，足够学习因子分布特征
TESTING_WINDOW = 60      # 约3个月，平衡统计显著性与时效性
STEP_SIZE = 60           # 等于测试窗口，避免重叠，确保独立性
TANH_K = 5               # S型曲线陡峭度，平衡噪声过滤与信号响应
HOLD_N = 1               # 1日持有期，适合高频因子
GAP = 1                  # 最小间隔，避免信息泄露
```

### 4. 通过标准 (统一标准)
```python
# 主要标准 (必须全部满足)
min_sharpe = 0.5         # 最小夏普比率
max_mdd = 0.30           # 最大回撤上限
min_win_rate = 0.55      # 最小胜率

# 补充标准 (用于筛选优质因子)
min_calmar = 0.8         # 最小卡玛比率
max_volatility = 0.25    # 最大年化波动率
min_skewness = -0.5      # 最小偏度
max_kurtosis = 5.0       # 最大峰度
```

## Sprint任务拆分

### Phase 1: 核心算法实现 (优先级: 高)
**预计工期**: 3-4天

#### Task 1.1: WFA核心算法实现
- **文件**: `factor/validation_utils.py`
- **功能**: 实现WFA滚动窗口验证核心逻辑
- **关键函数**:
  - `run_wfa_validation()`: WFA主流程
  - `calculate_ecdf_mapping()`: 经验分布函数学习
  - `apply_tanh_position_mapping()`: S型仓位映射
  - `calculate_spearman_correlation_optimized()`: 相关性计算
- **技术要求**:
  - 使用向量化操作提升性能
  - 复用 `factor/factor_utils.py` 中的numba加速函数
  - 严格按照文档中的算法流程实现
- **验收标准**:
  - 单因子WFA验证功能正常
  - 性能测试通过（处理750+60窗口<5秒）
  - 输出PnL序列格式正确

#### Task 1.2: 绩效计算复用集成
- **文件**: `factor/validation_utils.py`
- **功能**: 集成现有绩效计算函数
- **复用函数**:
  - `原库.polyfactorX.jzal_pro.utils.perf_utils.calc_stats()`
  - `factor.factor_utils.fast_spearman()`
  - `core.backtrader_extends.performer.year_frac()`
- **技术要求**:
  - 避免重复实现标准绩效指标
  - 保持计算一致性
  - 优化函数调用性能
- **验收标准**:
  - 绩效指标计算准确
  - 与原库计算结果一致
  - 支持WFA所需的全部指标

### Phase 2: 配置管理与业务集成 (优先级: 高)
**预计工期**: 2-3天

#### Task 2.1: 配置文件设计
- **文件**: `config/tasks/ts_l3_wfa.toml`
- **功能**: WFA验证任务配置
- **配置内容**:
  - 继承数据集配置 (`dynaconf_include`)
  - WFA核心参数配置
  - 通过标准定义
  - 输出配置选项
- **技术要求**:
  - 遵循项目配置命名规范
  - 使用经过优化的参数
  - 支持配置继承和合并
- **验收标准**:
  - 配置加载正常
  - 参数验证通过
  - 与现有配置系统兼容

#### Task 2.2: 业务流程脚本实现
- **文件**: `script/投研_因子挖掘集成/L3动态稳健性检验.py`
- **功能**: WFA验证主流程编排
- **核心流程**:
  - 从FactorZoo查询L2_PASSED因子
  - 批量WFA验证处理
  - 结果判定和状态更新
  - 异常处理和进度监控
- **技术要求**:
  - 遵循项目过程式编程风格
  - 使用封装的log输出信息
  - 支持批量处理和错误恢复
- **验收标准**:
  - 完整业务流程运行正常
  - 异常处理机制完善
  - 日志输出清晰详细

### Phase 3: 可视化报告生成 (优先级: 高)
**预计工期**: 3-4天

#### Task 3.1: quantstats专业报告集成
- **文件**: `factor/validation_utils.py` (报告生成函数)
- **功能**: 使用quantstats生成专业绩效报告
- **报告类型**:
  - 完整HTML报告 (`qs.reports.html`)
  - 关键图表单独保存 (snapshot, drawdown, heatmap等)
  - 指标摘要JSON文件
- **技术要求**:
  - 集成quantstats库
  - 自动化报告生成
  - 支持批量处理
- **验收标准**:
  - HTML报告生成正常
  - 图表质量符合要求
  - 指标数据准确完整

#### Task 3.2: 自定义补充图表
- **文件**: `factor/validation_utils.py` (自定义图表函数)
- **功能**: 生成WFA特有的补充分析图表
- **图表类型**:
  - 滚动指标分析图
  - 风险分析图表
  - 收益分布对比图
  - 年度收益条形图
- **技术要求**:
  - 使用matplotlib + seaborn
  - 保持图表风格一致
  - 优化图表可读性
- **验收标准**:
  - 图表生成正常
  - 视觉效果良好
  - 信息展示清晰

#### Task 3.3: 批量报告管理
- **文件**: `factor/validation_utils.py` (批量处理函数)
- **功能**: 批量生成和管理WFA报告
- **管理功能**:
  - 批量因子报告生成
  - 批次汇总报告
  - 报告目录组织
  - 处理进度监控
- **技术要求**:
  - 支持大批量处理
  - 错误恢复机制
  - 进度可视化
- **验收标准**:
  - 批量处理稳定
  - 汇总报告准确
  - 目录结构清晰

### Phase 4: FactorZoo集成与数据管理 (优先级: 高)
**预计工期**: 2-3天

#### Task 4.1: WFA绩效数据入库
- **文件**: `factor/validation_utils.py` (数据库集成函数)
- **功能**: 将WFA结果保存到FactorZoo
- **存储内容**:
  - 核心绩效指标 (sharpe_ratio, max_drawdown等)
  - 详细结果JSON (WFA特有指标)
  - 评价元数据 (evaluation_method='walk_forward_analysis')
- **技术要求**:
  - 复用FactorZoo现有表结构
  - 数据格式标准化
  - 支持历史记录查询
- **验收标准**:
  - 数据入库成功
  - 查询功能正常
  - 数据完整性验证通过

#### Task 4.2: 因子状态管理
- **文件**: `script/投研_因子挖掘集成/L3动态稳健性检验.py`
- **功能**: 基于WFA结果更新因子状态
- **状态管理**:
  - L3_PASSED: 通过WFA验证
  - L3_FAILED: 未通过WFA验证
  - 状态变更历史记录
- **技术要求**:
  - 与FactorZoo状态系统集成
  - 支持批量状态更新
  - 记录详细的失败原因
- **验收标准**:
  - 状态更新准确
  - 历史记录完整
  - 查询接口正常

## 验收标准总览

### 功能验收
- [ ] WFA算法实现正确，输出结果符合预期
- [ ] 配置系统集成完善，参数加载正常
- [ ] 业务流程完整，支持端到端处理
- [ ] 可视化报告丰富，图表质量高
- [ ] FactorZoo集成无缝，数据管理完善

### 性能验收
- [ ] 单因子WFA验证时间 < 10秒
- [ ] 批量处理100个因子 < 30分钟
- [ ] 报告生成时间合理
- [ ] 内存使用优化

### 质量验收
- [ ] 代码风格符合项目规范
- [ ] 异常处理机制完善
- [ ] 日志输出清晰详细
- [ ] 文档注释完整

## 风险控制

### 技术风险
- **依赖库兼容性**: quantstats等新库与现有环境的兼容性
- **性能瓶颈**: 大批量因子处理的性能优化
- **数据一致性**: 与现有绩效计算的一致性验证

### 业务风险
- **参数调优**: WFA参数设置的合理性验证
- **通过标准**: 筛选标准的严格程度平衡
- **人工审查**: 自动化与人工判断的平衡

### 缓解措施
- 分阶段实施，逐步验证
- 充分测试和性能调优
- 建立完善的监控和日志机制
- 保持与业务专家的密切沟通

## 后续扩展方向
- 支持多品种并行处理
- 集成更多绩效分析指标
- 开发交互式审查界面
- 支持参数自动优化
- 集成机器学习异常检测

---

**注**: 本sprint专注于第0步的核心实现，后续的第一步(时间维度OOS)和第二步(跨品种OOS)将在独立的sprint中实施。
