import backtrader as bt
import pandas as pd
from loguru import logger


class StrategyBase(bt.Strategy):
    def log(self, txt, dt=None):
        dt = dt or self.datas[0].datetime.date(0)
        logger.info('%s, %s' % (dt.isoformat(), txt))
        
    def __init__(self):
        self.order = None # 引用未决订单(尚未执行的订单)
         
    def prenext(self):
        self.next()
        
    # 取当前的日期
    def get_current_dt(self):
        dt = self.datas[0].datetime.date(0).strftime('%Y-%m-%d')
        return dt

    # 取当前持仓的symbol列表
    def get_current_holding_symbols(self):
        long_holdings = []
        short_holdings = []
        for name in self.getdatanames():
            data = self.getdatabyname(name)
            # for data in self.datas:
            if self.getposition(data).size > 0:
                long_holdings.append(name)
            elif self.getposition(data).size < 0:
                short_holdings.append(name)
        return long_holdings,short_holdings

    def get_symbol_mv(self,symbol):
        pos = self.getpositionbyname(symbol)
        return pos.size * pos.price

    def get_total_mv(self):
        self.get

    # 打印订单日志
    def notify_order(self, order):
        #return
        order_status = ['Created', 'Submitted', 'Accepted', 'Partial',
                        'Completed', 'Canceled', 'Expired', 'Margin', 'Rejected']
        # 未被处理的订单
        if order.status in [order.Submitted, order.Accepted]:
            # 处于未决状态,不重置直接返回
            return
            # self.log('未处理订单：订单号:%.0f, 标的: %s, 状态状态: %s' % (order.ref,
                                                        #    order.data._name,
                                                        #    order_status[order.status]))
        # 订单已决
        if order.status in [order.Partial, order.Completed]:
            pass
            # if order.isbuy():
            #     self.log(
            #         '买入, 状态: %s, 订单号:%.0f, 标的: %s, 数量: %.2f, 价格: %.2f, 成本: %.2f, 手续费 %.2f' %
            #         (order_status[order.status],  # 订单状态
            #          order.ref,  # 订单编号
            #          order.data._name,  # 股票名称
            #          order.executed.size,  # 成交量
            #          order.executed.price,  # 成交价
            #          order.executed.value,  # 成交额
            #          order.executed.comm))  # 佣金
            # else:  # Sell
            #     self.log(
            #         '卖出, status: %s, ref:%.0f, name: %s, Size: %.2f, Price: %.2f, Cost: %.2f, Comm %.2f' %
            #         (order_status[order.status],
            #          order.ref,
            #          order.data._name,
            #          order.executed.size,
            #          order.executed.price,
            #          order.executed.value,
            #          order.executed.comm))
        elif order.status in [order.Canceled, order.Margin, order.Rejected, order.Expired]:
            # order.Margin资金不足，订单无法成交
            self.log('未完成订单，订单号:%.0f, 标的 : %s, 订单状态: %s' % (
                order.ref, order.data._name, order_status[order.status]))
        # 此时无未决订单, 重置order
        self.order = None

    def notify_trade(self, trade):
        # logger.debug('trade......', trade.status)
        # 交易刚打开时
        # if trade.justopened:
        #     # return
        #     self.log('开仓, 标的: %s, 股数: %.2f,价格: %.2f' % (
        #         trade.getdataname(), trade.size, trade.price))
        # # 交易结束
        # elif trade.isclosed:
        #     # return
        #     self.log('平仓, 标的: %s, 股数: %.2f,价格: %.2f, GROSS %.2f, NET %.2f, 手续费 %.2f' % (
        #         trade.getdataname(), trade.size, trade.price, trade.pnl, trade.pnlcomm, trade.commission))
        # # 更新交易状态
        # else:
        #     self.log('交易更新, 标的: %s, 仓位: %.2f,价格: %.2f' % (
        #         trade.getdataname(), trade.size, trade.price))
        return


class StrategyAlgo(StrategyBase):
    def __init__(self, algo_list, engine, global_observer=None):
        super().__init__()
        self.algos = algo_list
        self.df_data = engine.df_data
        self.temp = {}
        self.perm = {}
        self.index = -1
        self.dates = list(self.df_data.index.unique())
        self.symbols = list(self.df_data['symbol'].unique())
        self.global_observer = global_observer

    def global_notify(self, data):
        if self.global_observer:
            self.global_observer.notify(data)

    def next(self):
        if self.order: # 有未决订单, 则无动作
            return
        self.index += 1
        self.now = self.dates[self.index]
        self.global_notify({'msg_type': 'ON_BAR', 'step': self.index,'progress': round(self.index/len(self.dates),2)})

        #self.df_bar = self.df_data.loc[self.now]
        self.df_bar = self.df_data.loc[self.now].copy() # by JZAL
        if type(self.df_bar) is pd.Series:
            self.df_bar = self.df_bar.to_frame().T
        self.df_bar.reset_index(drop=False, inplace=True)
        self.df_bar.rename(columns={self.df_bar.columns[0]: 'date'}, inplace=True)
        self.df_bar.set_index('symbol', inplace=True)
        ''' pivot将df长格式变为宽格式 '''
        self.df_close = self.df_data[:self.now].pivot(columns='symbol', values='close')   # 不包含self.now本身对应的行
        ''' added by JZAL'''
        #for c in self.task.names:
        #    self.df_bar[c] = self.df_bar[c].astype(float)    
        self.temp.clear()
        ''' end'''
        for algo in self.algos:
            if algo(self) is False:  # 如果algo返回False,直接不运行
                return
        ''' print selected '''
        # self.log(self.temp['weights'])