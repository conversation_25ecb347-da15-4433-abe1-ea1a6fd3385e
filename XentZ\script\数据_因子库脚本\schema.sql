-- =====================================================
-- XentZ 因子动物园管理系统 - 完整建库脚本
-- 创建时间: 2024-12-25
-- 说明: 基于多轮讨论和改进的最终设计方案
-- 特性: 支持自动生成+流水线、手工创建、ResMonitor集成
-- 版本: FactorZoo v2.0.0 (修复版)
-- =====================================================

-- 开始事务
BEGIN TRANSACTION;

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- =====================================================
-- 1. 批次管理表
-- =====================================================
CREATE TABLE factor_batches (
    batch_id TEXT PRIMARY KEY,                    -- GP_20241225_001, TSFRESH_20241225_001
    batch_name TEXT,                              -- 批次描述名称
    creation_tool TEXT NOT NULL,                  -- 'gplearn'/'tsfresh'/'deap'/'manual'
    
    -- 数据源信息
    source_symbols TEXT,                          -- JSON数组: ["510050.SH"] 或 ["CSI300"]
    source_frequencies TEXT,                      -- JSON数组: ["15min", "1h"]
    source_date_ranges TEXT,                      -- JSON: {"start": "2020-01-01", "end": "2024-12-01"}
    source_universe TEXT,                         -- 截面因子的股票池ID
    
    -- 生成参数
    generation_params TEXT,                       -- JSON: 工具参数配置
    total_generated INTEGER DEFAULT 0,            -- 总生成数量
    
    -- 流水线统计
    l0_count INTEGER DEFAULT 0,                   -- L0原始数量
    l1_count INTEGER DEFAULT 0,                   -- L1初筛数量  
    l2_count INTEGER DEFAULT 0,                   -- L2精筛数量
    l3_count INTEGER DEFAULT 0,                   -- L3最终数量
    
    -- 时间信息
    start_time DATETIME,                          -- 开始生成时间
    end_time DATETIME,                            -- 完成时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 2. 股票池定义表（支持版本化）
-- =====================================================
CREATE TABLE universes (
    universe_id TEXT NOT NULL,                    -- 'CSI300', 'CSI500', 'CUSTOM_TECH_001'
    version INTEGER NOT NULL,                     -- 版本号，从1开始
    universe_name TEXT NOT NULL,                  -- '沪深300', '中证500'
    universe_type TEXT NOT NULL,                  -- 'index_components'/'custom'/'dynamic'
    
    -- 静态股票池
    static_symbols TEXT,                          -- JSON数组：固定成分股
    
    -- 动态股票池定义
    selection_criteria TEXT,                      -- JSON：动态选股条件
    update_frequency TEXT,                        -- 'daily'/'monthly'/'quarterly'
    
    -- 版本管理
    effective_date DATE NOT NULL,                 -- 生效日期
    expiry_date DATE,                            -- 失效日期，NULL表示当前版本
    is_current BOOLEAN DEFAULT FALSE,             -- 是否当前版本
    
    -- 描述信息
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (universe_id, version)
);

-- =====================================================
-- 3. 因子分类表（层级结构）
-- =====================================================
CREATE TABLE factor_categories (
    category_id TEXT PRIMARY KEY,
    category_name TEXT NOT NULL,
    parent_category TEXT,                         -- 支持层级分类
    category_level INTEGER DEFAULT 1,             -- 分类层级：1=主分类，2=子分类
    description TEXT,
    sort_order INTEGER DEFAULT 0,                 -- 排序字段
    
    FOREIGN KEY (parent_category) REFERENCES factor_categories(category_id)
);

-- =====================================================
-- 4. 因子表达式主表（核心表）
-- =====================================================
CREATE TABLE factors (
    -- 主键标识
    factor_id TEXT PRIMARY KEY,                   -- F_GP_20241225_001_001, F_MANUAL_20241225_001
    factor_name TEXT NOT NULL,                    -- 简短易记名称
    factor_expression TEXT NOT NULL,              -- 完整因子表达式
    
    -- 因子类型
    factor_type TEXT NOT NULL CHECK (factor_type IN ('time_series', 'cross_section', 'panel')),
    market_type TEXT,                             -- 'A_STOCK'/'FUTURES'/'US_STOCK'/'CRYPTO'等
    
    -- 数据来源信息
    data_source_type TEXT NOT NULL CHECK (data_source_type IN ('single_symbol', 'multi_symbol', 'universe')),
    symbols TEXT NOT NULL,                        -- JSON数组或universe_id
    frequencies TEXT NOT NULL,                    -- JSON数组: ["15min", "1h"]
    date_ranges TEXT NOT NULL,                    -- JSON: {"start": "2020-01-01", "end": "2024-12-01"}
    
    -- 截面因子特有属性
    universe_id TEXT,                             -- 关联股票池ID
    universe_version INTEGER,                     -- 股票池版本
    cross_section_scope TEXT,                     -- 'intra_industry'/'cross_industry'/'market_wide'
    ranking_method TEXT,                          -- 'percentile'/'z_score'/'industry_neutral'
    rebalance_frequency TEXT,                     -- 'daily'/'weekly'/'monthly'
    
    -- 诞生信息（调整版）
    creation_method TEXT NOT NULL CHECK (creation_method IN (
        'auto_generation',   -- 自动生成（GP/TSFresh/DEAP等）
        'manual_creation',   -- 手工创建
        'paper_reference',   -- 论文参考
        'experience_based'   -- 经验总结
    )),
    generation_tool TEXT,                         -- 'gplearn'/'tsfresh'/'deap'/'custom_algo'等
    pipeline_mode TEXT CHECK (pipeline_mode IN (
        'auto_pipeline',     -- 自动生成 + 自动流水线筛选
        'manual_pipeline',   -- 手工创建 + 手工流水线筛选  
        'direct_submit',     -- 直接提交（跳过流水线）
        'hybrid'            -- 混合模式
    )),
    
    batch_id TEXT,                               -- 批次号(自动生成时必填)
    target_label TEXT,                           -- 挖掘时使用的目标标签 (label_ret_1d, label_ret_5d等)
    creation_date DATE NOT NULL,                  -- 诞生日期
    creator TEXT,                                -- 创建者/工具版本
    reference_info TEXT,                         -- 参考文章/论文/经验来源
    
    -- 分类信息
    primary_category TEXT NOT NULL,              -- 主分类
    secondary_category TEXT,                     -- 次分类  
    signal_type TEXT,                            -- 信号类型
    
    -- 状态管理
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'deprecated', 'testing', 'disabled')),
    pipeline_step TEXT DEFAULT 'L0',             -- L0:原始 L1:初筛 L2:精筛 L3:最终
    selection_reason TEXT,                       -- 进入当前步骤的原因
    
    -- 技术属性和性能信息（ResMonitor集成）
    complexity_score INTEGER,                    -- 表达式复杂度评分
    computation_cost TEXT,                       -- 计算成本估算 'low'/'medium'/'high'
    last_computation_time_ms INTEGER,            -- 最近一次计算耗时(毫秒)
    avg_computation_time_ms INTEGER,             -- 平均计算耗时
    max_memory_usage_mb REAL,                    -- 最大内存使用(MB)
    resource_intensity TEXT CHECK (resource_intensity IN ('low', 'medium', 'high')),
    performance_notes TEXT,                      -- 性能备注
    last_performance_update DATETIME,            -- 最后性能更新时间
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (batch_id) REFERENCES factor_batches(batch_id),
    FOREIGN KEY (universe_id, universe_version) REFERENCES universes(universe_id, version),
    FOREIGN KEY (primary_category) REFERENCES factor_categories(category_id),
    FOREIGN KEY (secondary_category) REFERENCES factor_categories(category_id)
);

-- =====================================================
-- 5. 因子评价表（支持多次评价）
-- =====================================================
CREATE TABLE factor_evaluations (
    eval_id TEXT PRIMARY KEY,                     -- EVAL_F001_20241225_001
    factor_id TEXT NOT NULL,
    
    -- 评价基本信息
    evaluation_name TEXT,                         -- 评价名称/标识
    evaluation_method TEXT NOT NULL,              -- 'backtest'/'forward_test'/'paper_trading'/'live_trading'
    evaluator TEXT,                              -- 评价者
    
    -- 评价时间和条件
    evaluation_period_start DATE NOT NULL,
    evaluation_period_end DATE NOT NULL,
    benchmark_symbol TEXT,                        -- 基准标的
    evaluation_params TEXT,                       -- JSON: 评价参数配置
    
    -- 收益指标
    total_return REAL,                           -- 总收益率
    annual_return REAL,                          -- 年化收益率
    excess_return REAL,                          -- 超额收益率
    
    -- 风险调整收益指标
    sharpe_ratio REAL,                           -- 夏普比率
    sortino_ratio REAL,                          -- 索提诺比率
    calmar_ratio REAL,                           -- 卡玛比率
    information_ratio REAL,                      -- 信息比率
    
    -- 风险指标
    max_drawdown REAL,                           -- 最大回撤
    volatility REAL,                             -- 波动率
    downside_volatility REAL,                    -- 下行波动率
    var_95 REAL,                                 -- 95% VaR
    cvar_95 REAL,                                -- 95% CVaR
    
    -- 胜率指标
    win_rate REAL,                               -- 胜率
    profit_loss_ratio REAL,                      -- 盈亏比
    max_consecutive_wins INTEGER,                -- 最大连续盈利次数
    max_consecutive_losses INTEGER,              -- 最大连续亏损次数
    
    -- 相关性指标
    correlation_to_benchmark REAL,               -- 与基准相关性
    beta REAL,                                   -- Beta值
    alpha REAL,                                  -- Alpha值
    tracking_error REAL,                         -- 跟踪误差
    
    -- 交易特征指标
    turnover_rate REAL,                          -- 换手率
    avg_holding_period REAL,                     -- 平均持仓周期
    transaction_cost_impact REAL,                -- 交易成本影响
    
    -- 稳定性指标
    monthly_win_rate REAL,                       -- 月度胜率
    yearly_consistency REAL,                     -- 年度一致性
    regime_stability TEXT,                       -- JSON: 不同市场环境下的表现
    
    -- 评价状态
    evaluation_status TEXT DEFAULT 'completed' CHECK (evaluation_status IN ('running', 'completed', 'failed')),
    
    -- 备注和详细结果
    notes TEXT,                                  -- 评价备注
    detailed_results TEXT,                       -- JSON: 详细评价结果
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (factor_id) REFERENCES factors(factor_id)
);

-- =====================================================
-- 6. 因子池管理表
-- =====================================================
CREATE TABLE factor_pools (
    pool_id TEXT PRIMARY KEY,                    -- POOL_MOMENTUM_V1, POOL_MEAN_REV_V2
    pool_name TEXT NOT NULL,                     -- 因子池名称
    pool_description TEXT,                       -- 因子池描述
    
    -- 池配置
    selection_criteria TEXT,                     -- JSON: 选择标准
    max_factors INTEGER,                         -- 最大因子数量限制
    rebalance_frequency TEXT,                    -- 调仓频率
    
    -- 状态管理
    pool_status TEXT DEFAULT 'active' CHECK (pool_status IN ('active', 'inactive', 'testing')),
    version INTEGER DEFAULT 1,                   -- 池版本号
    
    -- 应用信息
    usage_scenario TEXT,                         -- 使用场景描述
    target_market TEXT,                          -- 目标市场
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 7. 因子池成员表
-- =====================================================
CREATE TABLE factor_pool_members (
    pool_id TEXT NOT NULL,
    factor_id TEXT NOT NULL,
    
    -- 成员属性
    weight REAL DEFAULT 1.0,                    -- 因子权重
    entry_date DATE NOT NULL,                   -- 加入日期
    exit_date DATE,                             -- 退出日期，NULL表示当前成员
    entry_reason TEXT,                          -- 加入原因
    exit_reason TEXT,                           -- 退出原因
    
    -- 状态
    member_status TEXT DEFAULT 'active' CHECK (member_status IN ('active', 'inactive')),
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (pool_id, factor_id, entry_date),
    FOREIGN KEY (pool_id) REFERENCES factor_pools(pool_id),
    FOREIGN KEY (factor_id) REFERENCES factors(factor_id)
);

-- =====================================================
-- 8. 因子标签表（灵活标签系统）
-- =====================================================
CREATE TABLE factor_tags (
    factor_id TEXT NOT NULL,
    tag_name TEXT NOT NULL,
    tag_value TEXT,                              -- 标签值，可为空
    tag_type TEXT DEFAULT 'custom',              -- 'system'/'custom'/'auto'
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (factor_id, tag_name),
    FOREIGN KEY (factor_id) REFERENCES factors(factor_id)
);

-- =====================================================
-- 9. 因子关系表（预留，优先级不高）
-- =====================================================
CREATE TABLE factor_relationships (
    relationship_id TEXT PRIMARY KEY,
    factor_id_1 TEXT NOT NULL,
    factor_id_2 TEXT NOT NULL,
    relationship_type TEXT NOT NULL,             -- 'correlation'/'derived_from'/'similar_to'
    relationship_value REAL,                     -- 关系强度值
    confidence_level REAL,                       -- 置信度
    description TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (factor_id_1) REFERENCES factors(factor_id),
    FOREIGN KEY (factor_id_2) REFERENCES factors(factor_id)
);

-- =====================================================
-- 10. 因子性能日志表
-- =====================================================
CREATE TABLE factor_performance_logs (
    run_uid TEXT PRIMARY KEY,
    operation_type TEXT NOT NULL,
    factor_id TEXT,
    batch_id TEXT,
    start_time DATETIME,
    end_time DATETIME,
    total_time_ms INTEGER,
    cpu_time TEXT,
    memory_usage_mb REAL,
    data_size INTEGER,
    success BOOLEAN,
    error_message TEXT,
    notes TEXT,
    created_at DATETIME
);

-- =====================================================
-- 11. 因子值存储表
-- =====================================================
CREATE TABLE factor_values (
    value_id TEXT PRIMARY KEY,
    factor_id TEXT NOT NULL,
    symbol TEXT NOT NULL,
    date DATE NOT NULL,
    value REAL,
    quality_score REAL,
    data_source TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (factor_id) REFERENCES factors(factor_id)
);

-- =====================================================
-- 12. 创建索引
-- =====================================================

-- 因子表核心索引
CREATE INDEX idx_factors_batch_id ON factors(batch_id);
CREATE INDEX idx_factors_creation_date ON factors(creation_date);
CREATE INDEX idx_factors_pipeline_step ON factors(pipeline_step);
CREATE INDEX idx_factors_status ON factors(status);
CREATE INDEX idx_factors_primary_category ON factors(primary_category);
CREATE INDEX idx_factors_factor_type ON factors(factor_type);
CREATE INDEX idx_factors_market_type ON factors(market_type);
CREATE INDEX idx_factors_creation_method ON factors(creation_method);
CREATE INDEX idx_factors_generation_tool ON factors(generation_tool);
CREATE INDEX idx_factors_pipeline_mode ON factors(pipeline_mode);
CREATE INDEX idx_factors_target_label ON factors(target_label);

-- 性能相关索引
CREATE INDEX idx_factors_computation_time ON factors(last_computation_time_ms);
CREATE INDEX idx_factors_resource_intensity ON factors(resource_intensity);

-- 唯一性约束索引：同一数据源下表达式唯一
CREATE UNIQUE INDEX idx_factors_unique_full_context ON factors(
    factor_expression, factor_type, symbols, frequencies, date_ranges, 
    pipeline_step, pipeline_mode, creation_method, target_label
) WHERE status != 'deprecated';

-- 评价表索引
CREATE INDEX idx_evaluations_factor_id ON factor_evaluations(factor_id);
CREATE INDEX idx_evaluations_period ON factor_evaluations(evaluation_period_start, evaluation_period_end);
CREATE INDEX idx_evaluations_method ON factor_evaluations(evaluation_method);
CREATE INDEX idx_evaluations_sharpe ON factor_evaluations(sharpe_ratio);

-- 因子池索引
CREATE INDEX idx_pool_members_pool_id ON factor_pool_members(pool_id);
CREATE INDEX idx_pool_members_factor_id ON factor_pool_members(factor_id);
CREATE INDEX idx_pool_members_status ON factor_pool_members(member_status);

-- 标签索引
CREATE INDEX idx_factor_tags_tag_name ON factor_tags(tag_name);
CREATE INDEX idx_factor_tags_tag_type ON factor_tags(tag_type);

-- 股票池索引
CREATE INDEX idx_universes_current ON universes(universe_id, is_current);
CREATE INDEX idx_universes_effective_date ON universes(effective_date);

-- 性能日志表索引
CREATE INDEX idx_factor_performance_logs_operation_type ON factor_performance_logs(operation_type);
CREATE INDEX idx_factor_performance_logs_total_time ON factor_performance_logs(total_time_ms);
CREATE INDEX idx_factor_performance_logs_memory ON factor_performance_logs(memory_usage_mb);
CREATE INDEX idx_factor_performance_logs_batch_id ON factor_performance_logs(batch_id);
CREATE INDEX idx_factor_performance_logs_start_time ON factor_performance_logs(start_time);

-- 因子值表索引
CREATE INDEX idx_factor_values_factor_id ON factor_values(factor_id);
CREATE INDEX idx_factor_values_date ON factor_values(date);

-- =====================================================
-- 13. 预置分类数据
-- =====================================================

-- 主分类
INSERT INTO factor_categories (category_id, category_name, parent_category, category_level, description, sort_order) VALUES
('PRICE_VOLUME', '量价类', NULL, 1, '基于价格和成交量的技术指标', 1),
('MACRO', '宏观类', NULL, 1, '宏观经济相关因子', 2),
('INVENTORY', '库存类', NULL, 1, '库存、持仓相关因子', 3),
('INDUSTRY', '行业类', NULL, 1, '行业轮动、板块相关因子', 4),
('SENTIMENT', '情绪类', NULL, 1, '市场情绪、资金流向相关', 5),
('VOLATILITY', '波动率类', NULL, 1, '波动率、风险相关因子', 6),
('CROSS_VALUATION', '截面估值', NULL, 1, '相对估值类截面因子', 7),
('CROSS_QUALITY', '截面质量', NULL, 1, '财务质量类截面因子', 8),
('CROSS_GROWTH', '截面成长', NULL, 1, '成长性类截面因子', 9),
('CROSS_MOMENTUM', '截面动量', NULL, 1, '相对动量类截面因子', 10);

-- 量价类子分类
INSERT INTO factor_categories (category_id, category_name, parent_category, category_level, description, sort_order) VALUES
('PRICE_TREND', '价格趋势', 'PRICE_VOLUME', 2, '趋势跟踪类指标', 11),
('PRICE_MEAN_REV', '价格均值回归', 'PRICE_VOLUME', 2, '均值回归类指标', 12),
('VOLUME_PROFILE', '成交量形态', 'PRICE_VOLUME', 2, '成交量分析相关', 13),
('PRICE_MOMENTUM', '价格动量', 'PRICE_VOLUME', 2, '价格动量指标', 14),
('VOLATILITY_BREAKOUT', '波动率突破', 'PRICE_VOLUME', 2, '基于波动率的突破信号', 15);

-- 宏观类子分类
INSERT INTO factor_categories (category_id, category_name, parent_category, category_level, description, sort_order) VALUES
('INTEREST_RATE', '利率相关', 'MACRO', 2, '利率变化影响', 21),
('CURRENCY', '汇率相关', 'MACRO', 2, '汇率波动影响', 22),
('COMMODITY', '商品相关', 'MACRO', 2, '大宗商品价格影响', 23),
('ECONOMIC_DATA', '经济数据', 'MACRO', 2, '经济指标相关', 24);

-- 期货市场特有分类
INSERT INTO factor_categories (category_id, category_name, parent_category, category_level, description, sort_order) VALUES
('FUTURES_BASIS', '基差类', 'PRICE_VOLUME', 2, '现货期货基差相关', 31),
('FUTURES_CURVE', '期限结构', 'PRICE_VOLUME', 2, '期货曲线形态', 32),
('OPEN_INTEREST', '持仓量', 'PRICE_VOLUME', 2, '持仓量变化相关', 33);

-- 数字货币特有分类
INSERT INTO factor_categories (category_id, category_name, parent_category, category_level, description, sort_order) VALUES
('ONCHAIN_DATA', '链上数据', NULL, 1, '区块链数据相关因子', 41),
('DEFI_METRICS', 'DeFi指标', 'ONCHAIN_DATA', 2, '去中心化金融指标', 42),
('SOCIAL_SENTIMENT', '社交情绪', 'SENTIMENT', 2, '社交媒体情绪', 43);

-- 美股特有分类
INSERT INTO factor_categories (category_id, category_name, parent_category, category_level, description, sort_order) VALUES
('FUNDAMENTAL_US', '美股基本面', 'MACRO', 2, '美股财务数据', 51),
('ESG_FACTORS', 'ESG因子', NULL, 1, '环境社会治理因子', 52),
('EARNINGS_SURPRISE', '财报超预期', 'FUNDAMENTAL_US', 2, '财报业绩超预期', 53);

-- 截面估值子分类
INSERT INTO factor_categories (category_id, category_name, parent_category, category_level, description, sort_order) VALUES
('CS_PE_RELATIVE', '相对PE', 'CROSS_VALUATION', 2, '行业内PE排名', 61),
('CS_PB_RELATIVE', '相对PB', 'CROSS_VALUATION', 2, '行业内PB排名', 62),
('CS_PS_RELATIVE', '相对PS', 'CROSS_VALUATION', 2, '行业内PS排名', 63),
('CS_EV_EBITDA', '相对EV/EBITDA', 'CROSS_VALUATION', 2, 'EV/EBITDA行业排名', 64);

-- 截面质量子分类
INSERT INTO factor_categories (category_id, category_name, parent_category, category_level, description, sort_order) VALUES
('CS_ROE_RANK', 'ROE排名', 'CROSS_QUALITY', 2, '净资产收益率排名', 71),
('CS_DEBT_RATIO', '负债率排名', 'CROSS_QUALITY', 2, '资产负债率排名', 72),
('CS_CASH_FLOW', '现金流排名', 'CROSS_QUALITY', 2, '经营现金流排名', 73);

-- 截面动量子分类
INSERT INTO factor_categories (category_id, category_name, parent_category, category_level, description, sort_order) VALUES
('CS_PRICE_MOMENTUM', '价格动量排名', 'CROSS_MOMENTUM', 2, '相对价格表现排名', 81),
('CS_EARNINGS_REV', '盈利预期修正', 'CROSS_MOMENTUM', 2, '分析师预期修正排名', 82);

-- =====================================================
-- 14. 预置股票池数据
-- =====================================================

-- A股主要指数
INSERT INTO universes (universe_id, version, universe_name, universe_type, selection_criteria, update_frequency, effective_date, is_current, description) VALUES
('CSI300', 1, '沪深300', 'index_components', '{"index_code": "000300.SH", "source": "index_provider"}', 'quarterly', '2020-01-01', TRUE, '沪深300指数成分股'),
('CSI500', 1, '中证500', 'index_components', '{"index_code": "000905.SH", "source": "index_provider"}', 'quarterly', '2020-01-01', TRUE, '中证500指数成分股'),
('CSI1000', 1, '中证1000', 'index_components', '{"index_code": "000852.SH", "source": "index_provider"}', 'quarterly', '2020-01-01', TRUE, '中证1000指数成分股');

-- 期货合约池
INSERT INTO universes (universe_id, version, universe_name, universe_type, selection_criteria, update_frequency, effective_date, is_current, description) VALUES
('COMMODITY_MAIN', 1, '商品期货主力', 'dynamic', '{"contract_type": "main", "categories": ["metal", "energy", "agriculture"]}', 'daily', '2020-01-01', TRUE, '主要商品期货主力合约'),
('FINANCIAL_FUTURES', 1, '金融期货', 'dynamic', '{"categories": ["stock_index", "treasury_bond"]}', 'daily', '2020-01-01', TRUE, '金融期货合约');

-- 美股股票池
INSERT INTO universes (universe_id, version, universe_name, universe_type, selection_criteria, update_frequency, effective_date, is_current, description) VALUES
('SP500', 1, '标普500', 'index_components', '{"index_code": "SPX", "source": "yahoo_finance"}', 'quarterly', '2020-01-01', TRUE, '标普500指数成分股'),
('NASDAQ100', 1, '纳斯达克100', 'index_components', '{"index_code": "NDX", "source": "yahoo_finance"}', 'quarterly', '2020-01-01', TRUE, '纳斯达克100指数成分股');

-- 数字货币池
INSERT INTO universes (universe_id, version, universe_name, universe_type, selection_criteria, update_frequency, effective_date, is_current, description) VALUES
('TOP_CRYPTO', 1, '主流数字货币', 'dynamic', '{"market_cap_rank": "<=50", "volume_min": 1000000}', 'weekly', '2020-01-01', TRUE, '市值前50且日交易量>100万美元的数字货币');

-- 自定义股票池示例
INSERT INTO universes (universe_id, version, universe_name, universe_type, selection_criteria, update_frequency, effective_date, is_current, description) VALUES
('CUSTOM_LIQUID', 1, '高流动性股票池', 'dynamic', '{"avg_volume_min": 1000000, "market_cap_min": 1000000000}', 'monthly', '2020-01-01', TRUE, '日均成交额>100万且市值>10亿的A股'),
('CUSTOM_TECH', 1, '科技股票池', 'custom', '{"industries": ["software", "hardware", "internet", "ai"]}', 'quarterly', '2020-01-01', TRUE, '自定义科技行业股票池');

-- =====================================================
-- 15. 创建触发器（自动更新时间戳）
-- =====================================================

-- 因子表更新时间戳触发器
CREATE TRIGGER update_factors_timestamp 
    AFTER UPDATE ON factors
    FOR EACH ROW
BEGIN
    UPDATE factors SET updated_at = CURRENT_TIMESTAMP WHERE factor_id = NEW.factor_id;
END;

-- 批次表更新时间戳触发器
CREATE TRIGGER update_batches_timestamp 
    AFTER UPDATE ON factor_batches
    FOR EACH ROW
BEGIN
    UPDATE factor_batches SET updated_at = CURRENT_TIMESTAMP WHERE batch_id = NEW.batch_id;
END;

-- 因子池更新时间戳触发器
CREATE TRIGGER update_pools_timestamp 
    AFTER UPDATE ON factor_pools
    FOR EACH ROW
BEGIN
    UPDATE factor_pools SET updated_at = CURRENT_TIMESTAMP WHERE pool_id = NEW.pool_id;
END;

-- 性能更新触发器
CREATE TRIGGER update_performance_timestamp 
    AFTER UPDATE OF last_computation_time_ms, avg_computation_time_ms, max_memory_usage_mb ON factors
    FOR EACH ROW
BEGIN
    UPDATE factors SET last_performance_update = CURRENT_TIMESTAMP WHERE factor_id = NEW.factor_id;
END;

-- =====================================================
-- 16. 创建视图（常用查询）
-- =====================================================

-- 活跃因子概览视图（增强版）
CREATE VIEW v_active_factors AS
SELECT 
    f.factor_id,
    f.factor_name,
    f.factor_type,
    f.market_type,
    f.creation_method,
    f.generation_tool,
    f.pipeline_mode,
    f.pipeline_step,
    f.primary_category,
    pc.category_name as primary_category_name,
    f.secondary_category,
    sc.category_name as secondary_category_name,
    f.batch_id,
    f.creation_date,
    f.resource_intensity,
    f.last_computation_time_ms,
    f.status,
    f.created_at
FROM factors f
LEFT JOIN factor_categories pc ON f.primary_category = pc.category_id
LEFT JOIN factor_categories sc ON f.secondary_category = sc.category_id
WHERE f.status = 'active';

-- 因子评价汇总视图
CREATE VIEW v_factor_performance_summary AS
SELECT 
    f.factor_id,
    f.factor_name,
    f.pipeline_step,
    f.creation_method,
    f.generation_tool,
    COUNT(fe.eval_id) as evaluation_count,
    AVG(fe.sharpe_ratio) as avg_sharpe_ratio,
    MAX(fe.sharpe_ratio) as max_sharpe_ratio,
    AVG(fe.annual_return) as avg_annual_return,
    AVG(fe.max_drawdown) as avg_max_drawdown,
    MAX(fe.evaluation_period_end) as latest_evaluation_date,
    f.last_computation_time_ms,
    f.resource_intensity
FROM factors f
LEFT JOIN factor_evaluations fe ON f.factor_id = fe.factor_id
WHERE f.status = 'active'
GROUP BY f.factor_id, f.factor_name, f.pipeline_step, f.creation_method, f.generation_tool;

-- 批次统计视图（增强版）
CREATE VIEW v_batch_statistics AS
SELECT 
    fb.batch_id,
    fb.batch_name,
    fb.creation_tool,
    fb.total_generated,
    COUNT(f.factor_id) as actual_count,
    SUM(CASE WHEN f.pipeline_step = 'L0' THEN 1 ELSE 0 END) as l0_count,
    SUM(CASE WHEN f.pipeline_step = 'L1' THEN 1 ELSE 0 END) as l1_count,
    SUM(CASE WHEN f.pipeline_step = 'L2' THEN 1 ELSE 0 END) as l2_count,
    SUM(CASE WHEN f.pipeline_step = 'L3' THEN 1 ELSE 0 END) as l3_count,
    AVG(f.last_computation_time_ms) as avg_computation_time,
    fb.start_time,
    fb.end_time
FROM factor_batches fb
LEFT JOIN factors f ON fb.batch_id = f.batch_id
GROUP BY fb.batch_id;

-- 性能统计视图
CREATE VIEW v_performance_stats AS
SELECT 
    f.creation_method,
    f.generation_tool,
    f.resource_intensity,
    COUNT(*) as factor_count,
    AVG(f.last_computation_time_ms) as avg_computation_time,
    MAX(f.last_computation_time_ms) as max_computation_time,
    AVG(f.max_memory_usage_mb) as avg_memory_usage,
    MAX(f.max_memory_usage_mb) as max_memory_usage
FROM factors f
WHERE f.status = 'active' AND f.last_computation_time_ms IS NOT NULL
GROUP BY f.creation_method, f.generation_tool, f.resource_intensity;

-- =====================================================
-- 17. 完成建库
-- =====================================================

-- 提交事务
COMMIT; 