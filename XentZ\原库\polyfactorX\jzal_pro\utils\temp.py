# TODO: 整合到gplearn框架中的素材
# 广义sharpe(GSR)

import riskfolio as rp
import pandas as pd

# 准备资产的历史收益数据
returns = pd.Series(...)  # 这里应该填入你的收益数据

# 将 Series 转换为 DataFrame
returns = returns.to_frame()

# 设置参数
risk_aversion = 2.5  # 风险厌恶系数
expected_return = 'hist'  # 期望收益，这里使用历史平均收益
risk_measure = 'MV'  # 风险度量，这里使用马科维茨风险（Mean-Variance）

# 创建投资组合对象
port = rp.Portfolio(returns=returns)

# 计算广义夏普比率
model = 'Classic'  # 使用经典模型
port.assets_stats(model=model)
gsr = port.calc_gsr(risk_aversion=risk_aversion, expected_return=expected_return, risk_measure=risk_measure)

print(f"广义夏普比率为: {gsr}")