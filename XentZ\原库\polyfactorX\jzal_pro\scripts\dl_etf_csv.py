import akshare as ak
from datetime import datetime
import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent.parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
from config import DATA_DIR_CSVS

def download_data(symbol):
    print(symbol)
    # symbol = '513500.SH'
    code = symbol[:6]
    df = ak.fund_etf_hist_em(symbol=code, period="daily", start_date="20000101",
                             end_date=datetime.now().strftime('%Y%m%d'), adjust="hfq")
    cols = {'日期': 'date', '开盘': 'open', '收盘': 'close', '最高': 'high', '最低': 'low', '成交量': 'volume',
            '换手率': 'turn_over', '成交额': 'amount'}
    df.rename(columns=cols, inplace=True)
    df['date'] = df['date'].apply(lambda x: str(x).replace('-', ''))
    df = df[cols.values()]
    df['symbol'] = symbol
    print(df)

    DATA_DIR_CSVS.joinpath('etfs').mkdir(exist_ok=True)

    df.to_csv(DATA_DIR_CSVS.joinpath('etfs').joinpath(symbol + '.csv'), index=None)
    print(df)


symbols = ["510300.SH", # 沪深300 - bechmark
           "513100.SH",  # 纳指
           "159934.SZ",  # 黄金
           "510880.SH",  # 红利
           "513520.SH",  # 日经
           "159915.SZ"   # 创业
           ]

#symbols = ['510300.SH']
for symbol in symbols:
    download_data(symbol)
