2025-06-26 19:47 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-26 19:47 | INFO     | ResMonitor.end_timer: test took 1.00 seconds (00:00:01)
2025-06-26 19:47 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-26 19:47 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-26 19:47 | INFO     | ResMonitor.end_timer: test took 0.22 seconds (00:00:00)
2025-06-26 19:48 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-26 19:48 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-26 19:48 | INFO     | ResMonitor.end_timer: test took 1.00 seconds (00:00:01)
2025-06-26 19:48 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-26 19:48 | INFO     | ResMonitor.end_timer: F_TEST_001 took 0.51 seconds (00:00:00)
2025-06-26 19:48 | INFO     | ResMonitor.end_timer_cpu: F_TEST_001 CPU usage: 0.00s user, 0.00s system
2025-06-26 19:48 | INFO     | ResMonitor.end_memory_monitor: F_TEST_001 memory delta: 0.01MB
2025-06-26 19:48 | INFO     | FactorMonitorContext.__exit__: F_TEST_001 completed successfully
2025-06-26 19:48 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-26 19:48 | INFO     | ResMonitor.end_timer: general_task took 0.20 seconds (00:00:00)
2025-06-26 19:48 | INFO     | ResMonitor.end_timer_cpu: general_task CPU usage: 0.00s user, 0.00s system
2025-06-26 19:48 | INFO     | ResMonitor.end_memory_monitor: general_task memory delta: 0.00MB
2025-06-26 19:48 | INFO     | MonitorContext.__exit__: general_task completed successfully
2025-06-26 19:49 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-26 19:49 | INFO     | ResMonitor.end_timer: test took 0.10 seconds (00:00:00)
2025-06-26 19:49 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-26 19:49 | INFO     | ResMonitor.end_timer: context_test took 0.10 seconds (00:00:00)
2025-06-26 19:49 | INFO     | ResMonitor.end_timer_cpu: context_test CPU usage: 0.00s user, 0.00s system
2025-06-26 19:49 | INFO     | ResMonitor.end_memory_monitor: context_test memory delta: 0.00MB
2025-06-26 19:49 | INFO     | MonitorContext.__exit__: context_test completed successfully
2025-06-26 19:49 | INFO     | ResMonitor.__init__: Resource monitor started
