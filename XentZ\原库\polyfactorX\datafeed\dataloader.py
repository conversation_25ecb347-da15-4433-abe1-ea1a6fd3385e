from typing import Literal, <PERSON>ple
import pandas as pd
import numpy as np
from datetime import datetime
import duckdb
import os
import pyarrow.feather as feather
from tqdm import tqdm
import abc
from config import DATA_DIR_FACTOR
from enums import NORM_PARAMS_DEFAULT, NORM_PARAMS_LABEL,LABEL_MULTIPLE,LABEL_SINGLE, EXPR_NAME_EXCLUDED
from jzal_pro.cls_common import ResMonitor
from .expr import calc_expr

from loguru import logger
# logger.add("logs/myapp_{time}.log", level="INFO", format="{time} - {name} - {level} - {message}", rotation="1 week", compression="zip")


class Dataloader:
    def __init__(self, path, symbols, start_date='20100101', end_date=datetime.now().strftime('%Y%m%d')):
        self.symbols = symbols
        self.path = path
        self.start_date = start_date
        if not end_date or end_date == '':
            end_date = datetime.now().strftime('%Y%m%d')
        self.end_date = end_date
    def log(self, txt: str, level: str = "INFO"):
        # 根据传入的日志级别选择相应的 Loguru logger 方法
        if level.upper() == "DEBUG":
            logger.debug('%s: %s' % (self.__class__.__name__, txt))
        elif level.upper() == "INFO":
            logger.info('%s: %s' % (self.__class__.__name__, txt))
        elif level.upper() == "WARNING":
            logger.warning('%s: %s' % (self.__class__.__name__, txt))
        elif level.upper() == "ERROR":
            logger.error('%s: %s' % (self.__class__.__name__, txt))
        elif level.upper() == "CRITICAL":
            logger.critical('%s: %s' % (self.__class__.__name__, txt))
        else:
            raise ValueError(f"Unsupported log level: {level}")
        
    @abc.abstractmethod
    def _load_dfs(self) -> list:
        pass
    
    @abc.abstractmethod
    def _load_dfs_sample(self, n_sample:int = 500) -> list: # 算子值检验用
        pass

    def _concat_dfs(self, dfs: list):
        df = pd.concat(dfs)
        df.sort_index(inplace=True)
        return df

    def _reset_index(self, df: pd.DataFrame):
        trade_calendar = list(set(df.index))
        trade_calendar.sort()

        def _ffill_df(sub_df: pd.DataFrame):
            df_new = sub_df.reindex(trade_calendar, method='ffill')
            return df_new

        df = df.groupby('symbol', group_keys=False).apply(lambda sub_df: _ffill_df(sub_df))
        return df

    def load(self, fields=None, names=None) -> pd.DataFrame:
        ''' 返回带features的df_all(拼接后) - 原qlab方式 '''
        dfs = self._load_dfs() # 加载OHLC此类数据, 返回list
        # df = self._reset_index(df)
        if not fields or not names or len(fields) != len(names):
            return self._concat_dfs(dfs) # 拼接
        else:
            # 截面rank需要
            dfs_expr = []
            for df in tqdm(dfs): # 此刻为LIST对象
                cols = []
                count = 0
                for field, name in tqdm(zip(fields, names)):
                    se = calc_expr(df, field)
                    count += 1
                    if count < 10:
                        df[name] = se
                    else:
                        se.name = name
                        cols.append(se)
                if len(cols):
                    df_cols = pd.concat(cols, axis=1)
                    df_cols = df_cols.reindex(df.index)
                    df = pd.concat([df, df_cols], axis=1)
                dfs_expr.append(df)
            df_all = self._concat_dfs(dfs_expr)
            # todo 如果需要支持截面rank，逻辑加在这里
            # todo groupby('date').rank(pct=True)
            df_all = df_all.loc[self.start_date: self.end_date]
            return df_all
    def load_sample_no_norm(self, fields=None, names=None, n_sample = 500):  # 算子不norm
        ''' 算子值检验用 '''
        if not fields or not names or len(fields) != len(names):
            self.log('特征中names或exprs没设置或设置异常!',level='error')
            return []
        dfs = self._load_dfs_sample(n_sample=n_sample) # 加载OHLC此类数据, 返回list; df['date']为索引; 其他列已float32过(改回float64,talib需要)
        if dfs:
            dfs_expr = []
            for i, df in enumerate(tqdm(dfs)):                
                count = 0
                for field, name in tqdm(zip(fields, names)):
                    se = calc_expr(df, field) # se的长度和df一致
                    se = pd.Series(se, index=df.index)
                    df.loc[:,name] = se
                    count += 1
                    if count % 10 == 0:
                        df = df.copy()  # 碎片化处理                      
                df = df.replace([np.inf, np.nan, -np.inf],0.0)
                df = df.copy()  # 碎片化处理
                # 依次保存带基础数据和特征值的df,每个品种-分钟 pair 保存一份
                dfs_expr.append(df)
            return dfs_expr # [OHLC,volume_norm, R_0, V_0, sma5,...] NOTE: 此时还是要有OHLC的,因子里有对open引用
        else:
            self.log('dfs为空, 加载品种基础数据失败!',level='error')
            return []

    def load_sample_as_norm(self, fields=None, names=None, n_sample = 500):  # 采样数据算子计算且norm
        ''' 算子值检验用 '''
        ''' 不进行拼接, 返回带有feature且norm且float64的dfs; 支持510050.SH_15 这样格式的symbols '''
        # TODO: 聚合外部feature数据,比如宏观,库存,玄学等
        # 对date和symbol外的列进行asfloat
        # 对除OHLC和symbol和date外的列进行norm
        if not fields or not names or len(fields) != len(names):
            self.log('特征中names或exprs没设置或设置异常!',level='error')
            return [], []
        # 加载OHLC此类数据, 返回list; df['date']为索引; 其他列已float32过(改回float64,talib需要)
        dfs = self._load_dfs_sample(n_sample=n_sample+NORM_PARAMS_DEFAULT.window) 
        base_cols_no_norm = ['open', 'high', 'low', 'close', 'symbol'] # 基础col不norm
        base_cols_as_norm = []
        base_features = []
        if dfs:
            # NOTE:对volume这类不带算子的基础数据先norm
            # NOTE:每个品种的基础数据列完全一致(task.columns设置)
            base_cols_as_norm = [col for col in dfs[0].columns if col not in base_cols_no_norm + [dfs[0].index.name]]
            dfs_expr = []
            import jzal_pro.utils.expr_utils as eu
            for i, df in enumerate(tqdm(dfs)):
                # ''' res. monitor start...'''
                # monitor = ResMonitor()
                # monitor.start_timer(f"DataFrame {i} processing")
                
                dfs[i] = df.loc[self.start_date: self.end_date]
                normed_list = []
                for col in base_cols_as_norm:
                    df.loc[:, col+'_norm'] = eu.norm(df[col], params=NORM_PARAMS_DEFAULT).astype(np.float32) # (1000,4,0) volume不做对数
                    normed_list.append(col+'_norm')
                base_cols_as_norm += normed_list
                count = 0
                base_cols_all = base_cols_no_norm + base_cols_as_norm # 处理过的或不需处理的cols LIST
                for field, name in tqdm(zip(fields, names)):
                    if name not in base_cols_all:
                        se = calc_expr(df, field) # se的长度和df一致
                        se = pd.Series(se, index=df.index)
                        # df.loc[:,name] = se
                        if not name.startswith('Constant('): # NOTE:常数列不做norm!
                            try:
                                if name.startswith('label_'):
                                    df.loc[:,name] = eu.norm(se, params=NORM_PARAMS_LABEL).astype(np.float32)
                                else:
                                    df.loc[:,name] = eu.norm(se, params=NORM_PARAMS_DEFAULT).astype(np.float32) # (1000,4,0)
                                normed_list.append(name)
                            except Exception as e:
                                self.log('load{}后对features统一norm计算异常:{}'.format(df['symbol'],e))  
                        else:
                            df.loc[:,name] = se
                        count += 1
                        if count % 10 == 0:
                            df = df.copy()  # 碎片化处理                      
                df = df.replace([np.inf, np.nan, -np.inf],0.0)
                df = df.copy()  # 碎片化处理
                # ''' res. monitor recording... '''
                # monitor.log_memory_usage(f"DataFrame {i} - Final copy after all columns inserted")
                # monitor.log_cpu_usage(f"DataFrame {i} - Final copy after all columns inserted")
                # monitor.end_timer(f"DataFrame {i} processing")
                # 依次保存带基础数据和特征值的df,每个品种-分钟 pair 保存一份
                # if to_csv is not None:
                #     file_name = '{}_{}_norm_{}.csv'.format(df['symbol'].iloc[0], to_csv, datetime.now().strftime('%y%m%d_%H'))
                #     path_name = DATA_DIR_FACTOR.joinpath(file_name)
                #     df.to_csv(path_name)
                # if is_plot:
                #     import jzal_pro.utils.factor_utils as plt_u
                #     plt_u.plot_hist(df,normed_list)
                dfs_expr.append(df)
            temp_list = [col for col in dfs_expr[0].columns if col.endswith('_norm') ]
            base_features = ['open','high','low','close'] + temp_list + names
            base_features = [col for col in base_features if col not in set(LABEL_SINGLE + LABEL_MULTIPLE)]
            # base_features 为特征聚合list, 输入给gplearn
            return dfs_expr, base_features # [OHLC,volume_norm, R_0, V_0, sma5,...] NOTE: 此时还是要有OHLC的,因子里有对open引用
        else:
            self.log('dfs为空, 加载品种基础数据失败!',level='error')
            return [],[]
           
    def load_as_norm(self, fields=None, names=None, expr_name_excluded: list=EXPR_NAME_EXCLUDED,
                     to_csv: Literal['features', None] = 'features', 
                     is_plot:bool = True) -> Tuple[list, list]:
        ''' 不进行拼接, 返回带有feature且norm且float64的dfs; 支持510050.SH_15 这样格式的symbols '''
        # TODO: 聚合外部feature数据,比如宏观,库存,玄学等
        # 对date和symbol外的列进行asfloat
        # 对除OHLC和symbol和date外的列进行norm
        if not fields or not names or len(fields) != len(names):
            self.log('特征中names或exprs没设置或设置异常!',level='error')
            return [], []
        dfs = self._load_dfs() # 加载OHLC此类数据, 返回list; df['date']为索引; 其他列已float32过(改回float64,talib需要)
        base_cols_no_norm = ['open', 'high', 'low', 'close', 'symbol'] # 基础col不norm
        base_cols_as_norm = []
        base_features = []
        if dfs:
            # NOTE:对volume这类不带算子的基础数据先norm
            # NOTE:每个品种的基础数据列完全一致(task.columns设置)
            base_cols_as_norm = [col for col in dfs[0].columns if col not in base_cols_no_norm + [dfs[0].index.name]]
            dfs_expr = []
            import jzal_pro.utils.expr_utils as eu
            for i, df in enumerate(tqdm(dfs)):
                # ''' res. monitor start...'''
                # monitor = ResMonitor()
                # monitor.start_timer(f"DataFrame {i} processing")
                
                dfs[i] = df.loc[self.start_date: self.end_date]
                normed_list = []
                for col in base_cols_as_norm:
                    df.loc[:, col+'_norm'] = eu.norm(df[col], params=NORM_PARAMS_DEFAULT).astype(np.float32) # (1000,4,0) volume不做对数
                    normed_list.append(col+'_norm')
                base_cols_as_norm += normed_list
                count = 0
                base_cols_all = base_cols_no_norm + base_cols_as_norm # 处理过的或不需处理的cols LIST
                for field, name in tqdm(zip(fields, names)):
                    if name not in base_cols_all:
                        se = calc_expr(df, field) # se的长度和df一致
                        se = pd.Series(se, index=df.index)
                        # df.loc[:,name] = se
                        if not name.startswith('ts_cons_'): # NOTE:常数列不做norm!
                            try:
                                if name.startswith('label_'):
                                    df.loc[:,name] = eu.norm(se, params=NORM_PARAMS_LABEL).astype(np.float32)
                                else:
                                    df.loc[:,name] = eu.norm(se, params=NORM_PARAMS_DEFAULT).astype(np.float32) # (1000,4,0)
                                normed_list.append(name)
                            except Exception as e:
                                self.log('load{}后对features统一norm计算异常:{}'.format(df['symbol'],e))  
                        else:
                            df.loc[:,name] = se
                        count += 1
                        if count % 10 == 0:
                            df = df.copy()  # 碎片化处理
                expr_name_excluded = [name for name in expr_name_excluded if name in df.columns]  # 只保留实际存在的列名 
                if expr_name_excluded:           
                    df = df.drop(columns=expr_name_excluded).copy()
                df = df.replace([np.inf, np.nan, -np.inf],0.0)
                # ''' res. monitor recording... '''
                # monitor.log_memory_usage(f"DataFrame {i} - Final copy after all columns inserted")
                # monitor.log_cpu_usage(f"DataFrame {i} - Final copy after all columns inserted")
                # monitor.end_timer(f"DataFrame {i} processing")
                # 依次保存带基础数据和特征值的df,每个品种-分钟 pair 保存一份
                # if to_csv is not None:
                #     file_name = '{}_{}_norm_{}.csv'.format(df['symbol'].iloc[0], to_csv, datetime.now().strftime('%y%m%d_%H'))
                #     path_name = DATA_DIR_FACTOR.joinpath(file_name)
                #     df.to_csv(path_name)
                # if is_plot:
                #     import jzal_pro.utils.factor_utils as plt_u
                #     plt_u.plot_hist(df,normed_list)
                dfs_expr.append(df)
            temp_list = [col for col in dfs_expr[0].columns if col.endswith('_norm') ]
            base_features = ['open','high','low','close'] + temp_list + names
            base_features = [col for col in base_features if col not in set(LABEL_SINGLE + LABEL_MULTIPLE + EXPR_NAME_EXCLUDED)]
            # base_features 为特征聚合list, 输入给gplearn
            return dfs_expr, base_features # [OHLC,volume_norm, R_0, V_0, sma5,...] NOTE: 此时还是要有OHLC的,因子里有对open引用
        else:
            self.log('dfs为空, 加载品种基础数据失败!',level='error')
            return [],[]

class Duckdbloader(Dataloader):
    def __init__(self, path, symbols, columns, start_date='20100101',
                 end_date=datetime.now().strftime('%Y%m%d'), folder='/*',
                 ):
        super().__init__(None, symbols, start_date, end_date)
        self.path = path
        self.columns = columns
        if not self.columns:
            self.columns = ['open', 'high', 'low', 'close', 'volume'] # 缺省这些列
        self.columns.extend(['symbol', 'date'])
        self.folder = folder

    def _load_dfs(self):

        if self.columns:
            cols_str = ','.join(self.columns)
            # cols_str += ',' + "strftime(DATE 'date', '%Y/%m/%d')"
        symbols_str = None
        if self.symbols and len(self.symbols):
            symbols = ["'{}'".format(s) for s in self.symbols]
            symbols_str = ",".join(symbols)

        ''' add by JZAL'''
        actual_start_date = '20100101'  # 因timedelta不能体现交易日历，先用20100101
        ''' end by JZAL'''
        query_str = """
    select {} from '{}{}.csv'
    where date >= '{}' and date <= '{}'
    """.format(cols_str, self.path, self.folder, actual_start_date, self.end_date)
        if symbols_str:
            query_str += ' and symbol IN ({})'.format(symbols_str)

        df = duckdb.query(
            query_str
        ).df()
        df['date'] = df['date'].apply(lambda x: str(x))
        df.set_index('date', inplace=True)
        df.index = pd.to_datetime(df.index)
        df.sort_index(inplace=True, ascending=True)

        dfs = [df for _, df in df.groupby('symbol')]
        return dfs
    
class Featherloader(Dataloader):
    def __init__(self, path, symbols, columns, start_date='20100101',
                 end_date=datetime.now().strftime('%Y%m%d') ):
        super().__init__(None, symbols, start_date, end_date)
        self.path = path
        self.columns = columns
        if not self.columns:
            self.columns = ['open', 'high', 'low', 'close', 'volume']  # 不用指出date/symbol, load逻辑会加入

    def _load_dfs(self) -> list:
        feathers = os.listdir(self.path.resolve())
        dfs = []
        df = pd.DataFrame()
        for ff in feathers:
            symbol = ff.replace('.feather','')
            if symbol in self.symbols:
                df = feather.read_feather(self.path.joinpath(ff).resolve())
                # df['date'] = df['date'].apply(lambda x: str(x))
                # df.set_index('date', inplace=True)
                # df['symbol'] = ff.replace('.feather','')
                # df.index = pd.to_datetime(df.index)
                # df.sort_index(inplace=True, ascending=True)
                # df = df.loc[self.start_date:self.end_date,:]
                ''' JZAL: optimizated... ''' 
                df['date'] = pd.to_datetime(df['date'])
                df.set_index('date', inplace=True)
                df['symbol'] = symbol
                df.sort_index(inplace=True, ascending=True)
                df = df.loc[self.start_date:self.end_date, :]
                
                ''' JZAL: optimze columns loading'''
                if  len(self.columns) < df.columns.size and set(self.columns).issubset(df.columns):
                    if 'symbol' not in self.columns:
                        self.columns.append('symbol')
                    df = df[self.columns]
                # 将除了 'symbol' 和 'date' 索引外的其他列转换为 float32 --- 改回64(talib要求64)
                df.loc[:, df.columns != 'symbol'] = df.loc[:, df.columns != 'symbol'].astype('float64')
                dfs.append(df)
        #dfs = [df for _, df in df.groupby('symbol')] 
        return dfs

    def _load_dfs_sample(self, n_sample :int = 500) -> list:
        ''' 算子值检测用途, 少量采样 '''
        feathers = os.listdir(self.path.resolve())
        dfs = []
        df = pd.DataFrame()
        for ff in feathers:
            symbol = ff.replace('.feather','')
            if symbol in self.symbols:
                df = feather.read_feather(self.path.joinpath(ff).resolve())
                df = df.tail(min(df.shape[0], n_sample))  # 取n_sample行样本
                
                ''' JZAL: optimizated... ''' 
                df['date'] = pd.to_datetime(df['date'])
                df.set_index('date', inplace=True)
                df['symbol'] = symbol
                df.sort_index(inplace=True, ascending=True)
                
                ''' JZAL: optimze columns loading'''
                if  len(self.columns) < df.columns.size and set(self.columns).issubset(df.columns):
                    if 'symbol' not in self.columns:
                        self.columns.append('symbol')
                    df = df[self.columns]
                # 将除了 'symbol' 和 'date' 索引外的其他列转换为 float32 --- 改回64(talib要求64)
                df.loc[:, df.columns != 'symbol'] = df.loc[:, df.columns != 'symbol'].astype('float64')
                dfs.append(df)
        return dfs
