''' L1相关性筛选脚本 - 对GP挖掘结果进行进一步筛选
基于原库L1Corr方法，采用过程式编程风格，注重执行效率
复用gplearn.gplearn_miner.FctsGPMiner.select_by_corr方法
'''
import pandas as pd
import numpy as np
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from common.cls_base import BaseObj, MonitorContext
from config import REPORTS_DIR, cfg_mine
from core.polyfactor.gplearn.gplearn_miner import FctsGPMiner
from factor.factorloader import FactorLoader
from factorzoo import factorzoo
from factorzoo import FactorMonitorContext, FactorZooRunManager
from factorzoo import factorstore


def load_multi_batch_factors(batch_ids: List[str], pipeline_step: str = 'L0') -> Dict[str, pd.DataFrame]:
    """从多个批次加载因子数据 - 按symbol+target_label组织确保可比性
    
    Args:
        batch_ids: 批次ID列表
        pipeline_step: 管道步骤，默认L0
        
    Returns:
        Dict[context_key, data_dict]: 按上下文组织的数据，context_key="symbol_target_label"
    """
    print(f"开始加载 {len(batch_ids)} 个批次的因子数据...")
    
    symbol_factor_data = {}
    total_factors_loaded = 0
    
    # 遍历批次加载数据
    for batch_id in batch_ids:
        try:
            # 获取批次信息和因子元数据
            batch_info = factorstore.get_batch_info(batch_id)
            if not batch_info:
                print(f"批次 {batch_id} 信息不存在，跳过")
                continue
            
            symbol = batch_info.get('symbol', 'unknown')
            if symbol == 'unknown':
                print(f"批次 {batch_id} 品种信息缺失，跳过")
                continue
            
            # 从FactorZoo获取该批次的target_label信息
            batch_factors = factorzoo.search_factors({'batch_id': batch_id}, limit=1000)
            target_labels = set(f.get('target_label', 'unknown') for f in batch_factors if f.get('target_label'))
            
            # 加载批次数据
            base_data, factor_data = factorstore.load_batch_data(
                batch_id=batch_id, pipeline_step=pipeline_step
            )
            
            if factor_data.empty:
                print(f"批次 {batch_id} 因子数据为空，跳过")
                continue
            
            # 按symbol+target_label分组，确保只有相同上下文的因子才合并
            for target_label in target_labels:
                context_key = f"{symbol}_{target_label}"
                
                if context_key not in symbol_factor_data:
                    symbol_factor_data[context_key] = {
                        'symbol': symbol,
                        'target_label': target_label,
                        'base_data': base_data,
                        'factor_data': factor_data,
                        'batch_ids': [batch_id]
                    }
                else:
                    # 合并相同上下文的因子数据（相同时间索引）
                    existing_factor_data = symbol_factor_data[context_key]['factor_data']
                    merged_factor_data = pd.concat([existing_factor_data, factor_data], axis=1)
                    # 去重列名
                    merged_factor_data = merged_factor_data.loc[:, ~merged_factor_data.columns.duplicated()]
                    symbol_factor_data[context_key]['factor_data'] = merged_factor_data
                    symbol_factor_data[context_key]['batch_ids'].append(batch_id)
            
            total_factors_loaded += factor_data.shape[1]
            print(f"批次 {batch_id} 加载完成: {symbol}, target_labels: {target_labels}, {factor_data.shape[1]} 个因子")
            
        except Exception as e:
            print(f"加载批次 {batch_id} 失败: {str(e)}")
            continue
    
    print(f"多批次加载完成: {len(symbol_factor_data)} 个上下文, 总计 {total_factors_loaded} 个因子")
    return symbol_factor_data


def apply_corr_filter_for_symbol(context_key: str, data_dict: Dict, corr_threshold: float = 0.3) -> List[str]:
    """对单个上下文应用相关性筛选 - 直接复用FctsGPMiner.select_by_corr方法
    
    Args:
        context_key: 上下文键 "symbol_target_label"
        data_dict: 包含base_data和factor_data的字典
        corr_threshold: 相关性阈值
        
    Returns:
        List[str]: 筛选后的因子列表
    """
    symbol = data_dict['symbol']
    target_label = data_dict['target_label']
    base_data = data_dict['base_data']
    factor_data = data_dict['factor_data']
    
    if factor_data.empty:
        return []
    
    print(f"开始对 {symbol}[{target_label}] 的 {factor_data.shape[1]} 个因子进行相关性筛选...")
    
    # 构建完整数据集 - 包含因子值和收益率
    full_data = factor_data.copy()
    
    # 添加收益率列 - 根据配置决定使用哪种收益率
    if 'ret' in base_data.columns:
        full_data['ret'] = base_data['ret']
    elif 'close' in base_data.columns:
        # 计算收益率
        full_data['ret'] = base_data['close'].pct_change().fillna(0)
    else:
        print(f"上下文 {context_key} 缺少收益率数据，无法进行筛选")
        return []
    
    # 添加开盘收益率（如果需要）
    if 'ret_open' not in full_data.columns and 'open' in base_data.columns:
        full_data['ret_open'] = base_data['open'].pct_change().fillna(0)
    
    # 确定使用的收益率列
    ret_col = 'ret_open' if cfg_mine.mine.run.nextbar and 'ret_open' in full_data.columns else 'ret'
    
    # 分离特征和标签 - 准备调用FctsGPMiner.select_by_corr
    y_norm_se = full_data[ret_col].copy()
    factor_df = full_data.drop(columns=[ret_col, 'ret', 'ret_open'], errors='ignore')
    
    if factor_df.empty:
        print(f"上下文 {context_key} 有效因子数量不足，跳过筛选")
        return []
    
    # 直接调用FctsGPMiner.select_by_corr方法 - 复用原库逻辑
    try:
        selected_factors = FctsGPMiner.select_by_corr(
            df=factor_df,
            y_norm_se=y_norm_se,
            corr_threshold=corr_threshold,
            current_symbol=symbol
        )
        
        print(f"上下文 {context_key} 相关性筛选完成: {len(selected_factors)}/{factor_data.shape[1]} 个因子保留")
        return selected_factors
        
    except Exception as e:
        print(f"上下文 {context_key} 相关性筛选失败: {str(e)}")
        return []


def save_filtering_results(results: Dict[str, List[str]], current_date: str, global_uid: str):
    """保存筛选结果到文件"""
    try:
        # 转换为DataFrame格式保存
        all_results = []
        for symbol, factors in results.items():
            for i, factor in enumerate(factors, 1):
                all_results.append({
                    'symbol': symbol.split('_')[0] if '_' in symbol else symbol,
                    'factor_id': i,
                    'expression': factor,
                    'filter_stage': 'L1_corr',
                    'filter_date': current_date
                })
        
        if all_results:
            result_df = pd.DataFrame(all_results)
            output_path = REPORTS_DIR / 'gplearn' / f'l1_corr_filtered_factors_{current_date}_{global_uid[-6:]}.csv'
            output_path.parent.mkdir(parents=True, exist_ok=True)
            result_df.to_csv(output_path, index=False)
            print(f"💾 筛选结果已保存: {output_path}")
            return True
            
    except Exception as e:
        print(f"保存筛选结果失败: {str(e)}")
        return False


def persist_and_register_factors(filtered_results: Dict[str, List[str]],
                                symbol_factor_data: Dict[str, pd.DataFrame],
                                current_date: str, global_uid: str, corr_threshold: float = 0.3) -> bool:
    """持久化筛选后的因子值和入库到FactorZoo"""
    print("🏦 开始因子值持久化和入库...")
    
    total_success = 0
    total_failed = 0
    
    # 遍历每个上下文处理
    for context_key, selected_factors in filtered_results.items():
        if not selected_factors:
            continue
            
        try:
            # 获取该上下文的原始数据
            data_dict = symbol_factor_data[context_key]
            symbol = data_dict['symbol']
            target_label = data_dict['target_label']
            base_data = data_dict['base_data']
            full_factor_data = data_dict['factor_data']
            
            # 提取筛选后的因子数据
            filtered_factor_data = full_factor_data[selected_factors].copy()
            
            # 创建L1批次ID
            l1_batch_id = f"L1_CORR_{symbol}_{target_label}_{current_date}_{global_uid[-6:]}"
            
            print(f"📋 处理上下文 {context_key}: {len(selected_factors)} 个筛选因子")
            
            # 1. 持久化因子值
            with FactorMonitorContext(f"PERSIST_L1_{symbol}_{target_label}", 
                                    operation_type='l1_factor_persistence', 
                                    batch_id=l1_batch_id,
                                    symbol=symbol,
                                    data_size=filtered_factor_data.shape[0] * filtered_factor_data.shape[1]):
                
                # 准备持久化元数据
                persistence_metadata = {
                    'symbol': symbol,
                    'target_label': target_label,
                    'date': current_date,
                    'stage': 'L1',
                    'filter_method': 'correlation_filter',
                    'total_factors': len(selected_factors),
                    'source_batches': data_dict.get('batch_ids', []),
                    'filter_params': {
                        'corr_threshold': cfg_mine.mine.filter.corrthresh,
                        'filter_stage': 'L1_corr'
                    },
                    'creation_time': datetime.now().isoformat(),
                    'global_batch_id': f"L1_FILTER_{global_uid}"
                }
                
                # 执行因子值持久化
                persistence_success = factorstore.save_batch_data(
                    batch_id=l1_batch_id,
                    base_data=base_data,
                    factor_data_dict={'L1': filtered_factor_data},
                    metadata=persistence_metadata
                )
                
                if persistence_success:
                    print(f"    ✅ 因子值持久化成功: {filtered_factor_data.shape}")
                else:
                    print("    ❌ 因子值持久化失败，跳过该上下文")
                    total_failed += len(selected_factors)
                    continue # 直接跳到下一个上下文
            
            # 2. 创建FactorZoo批次
            batch_creation_success = factorzoo.create_batch(
                batch_id=l1_batch_id,
                batch_name=f"L1相关性筛选_{symbol}_{target_label}_{current_date}",
                creation_tool="l1_corr_filter",
                source_symbols=[symbol],
                source_frequencies=["1d"],
                source_date_ranges={
                    "start": str(base_data.index.min())[:10] if hasattr(base_data.index.min(), 'strftime') else str(base_data.index.min())[:10],
                    "end": str(base_data.index.max())[:10] if hasattr(base_data.index.max(), 'strftime') else str(base_data.index.max())[:10]
                },
                generation_params={
                    "symbol": symbol,
                    "target_label": target_label,
                    "date": current_date,
                    "stage": "L1",
                    "filter_method": "correlation_filter",
                    "corr_threshold": cfg_mine.mine.filter.corrthresh,
                    "source_batches": data_dict.get('batch_ids', []),
                    "factor_count": len(selected_factors)
                }
            )
            
            # 如果批次创建失败，则中止处理
            if not batch_creation_success:
                print(f"    ❌ 数据库批次记录创建失败: {l1_batch_id}，跳过因子注册")
                total_failed += len(selected_factors)
                # 可选：删除已持久化的文件，避免孤立文件
                # factorstore.delete_batch_data(l1_batch_id)
                continue

            # 3. 入库筛选后的因子
            symbol_success = 0
            symbol_failed = 0
            
            for i, factor_expr in enumerate(selected_factors):
                factor_id = f"F_L1_CORR_{l1_batch_id}_{i+1:03d}"
                
                try:
                    # 因子分类
                    expr_lower = str(factor_expr).lower()
                    if any(word in expr_lower for word in ['volume', 'vol', 'amount']):
                        factor_category = 'PRICE_VOLUME'
                    elif any(word in expr_lower for word in ['ts_mean', 'ts_sum', 'ts_max', 'ts_min', 'rolling']):
                        factor_category = 'PRICE_TREND'
                    else:
                        factor_category = 'PRICE_VOLUME'
                    
                    # 计算因子复杂度评分 - 基于表达式长度和操作符数量
                    expr_str = str(factor_expr)
                    complexity_score = min(100, len(expr_str) + expr_str.count('(') * 2 + expr_str.count(','))

                    # 评估资源强度 - 基于复杂度（L2筛选后的因子通常复杂度较高）
                    if complexity_score < 40:  # L2阈值稍高
                        resource_intensity = 'low'
                    elif complexity_score < 70:
                        resource_intensity = 'medium'
                    else:
                        resource_intensity = 'high'

                    # 添加因子到FactorZoo - 使用新的UPSERT逻辑
                    add_success = factorzoo.add_factor(
                        factor_id=factor_id,
                        factor_name=f"L1_CORR_{symbol}_{target_label}_{i+1}",
                        factor_expression=expr_str,
                        factor_type="time_series",
                        data_source_type="single_symbol",
                        symbols=[symbol],
                        frequencies=["1d"],
                        date_ranges={
                            "start": str(base_data.index.min())[:10] if hasattr(base_data.index.min(), 'strftime') else str(base_data.index.min())[:10],
                            "end": str(base_data.index.max())[:10] if hasattr(base_data.index.max(), 'strftime') else str(base_data.index.max())[:10]
                        },
                        creation_method="auto_generation",
                        generation_tool="l1_corr_filter",
                        pipeline_step="L1",
                        pipeline_mode="auto_pipeline",
                        primary_category=factor_category,
                        batch_id=l1_batch_id,
                        target_label=target_label,
                        status="active",
                        # 性能相关字段 - 顺带填充
                        complexity_score=complexity_score,
                        resource_intensity=resource_intensity
                    )

                    if add_success:
                        symbol_success += 1
                    else:
                        symbol_failed += 1
                        print(f"    ⚠️  因子 {factor_id} 入库失败")

                except Exception as e:
                    symbol_failed += 1
                    print(f"    ⚠️  因子 {factor_id} 处理失败: {str(e)}")
            
            # 如果有任何因子入库失败，则认为整个批次失败
            if symbol_failed > 0:
                print(f"    ❌ 上下文 {context_key} 有 {symbol_failed} 个因子入库失败，标记整个批次为失败。")
                total_failed += symbol_success # 之前统计成功的也算失败
                total_success -= symbol_success
                # 强烈建议回滚：删除批次和数据
                print(f"    ℹ️  建议手动清理或实现自动回滚: {l1_batch_id}")
            else:
                total_success += symbol_success

            print(f"    📊 {context_key} 处理完成: 成功{symbol_success}个，失败{symbol_failed}个")
            
        except Exception as e:
            print(f"❌ 处理上下文 {context_key} 时出错: {str(e)}")
            total_failed += len(selected_factors)
            continue
    
    # 全局统计
    print("📊 因子持久化和入库完成:")
    print(f"    ✅ 成功处理: {total_success} 个因子")
    if total_failed > 0:
        print(f"    ❌ 处理失败: {total_failed} 个因子")
    if total_success + total_failed > 0:
        success_rate = total_success / (total_success + total_failed) * 100
        print(f"    📈 成功率: {success_rate:.1f}%")
    
    return total_success > 0


def run_l1_corr_filter_pipeline(batch_ids: List[str] = None,
                               corr_threshold: float = None,
                               pipeline_step: str = 'L0') -> Dict[str, List[str]]:
    """运行L1相关性筛选管道 - 主流程函数

    Args:
        batch_ids: 指定批次ID列表，None则自动获取
        corr_threshold: 相关性阈值，None则使用配置
        pipeline_step: 输入管道步骤

    Returns:
        筛选结果字典
    """
    print("🚀 开始L1相关性筛选流程...")
    start_time = datetime.now()
    global_uid = BaseObj.gen_ordered_uid()
    current_date = start_time.strftime('%Y%m%d')

    with MonitorContext(f"L1_CORR_FILTER_{global_uid}") as global_ctx:

        # 1. 获取批次列表
        if batch_ids is None:
            available_batches = factorstore.get_available_batches(stage=pipeline_step)

            if not available_batches:
                print(f"❌ 未找到可用的{pipeline_step}批次，请先运行mine_core.py生成因子")
                return {}

            # 选择最近的批次进行处理
            target_batches = available_batches[-10:] if len(available_batches) >= 10 else available_batches
        else:
            target_batches = batch_ids

        print(f"📋 将处理以下批次: {len(target_batches)} 个")
        for i, batch_id in enumerate(target_batches[:5], 1):  # 只显示前5个
            print(f"    {i}. {batch_id}")
        if len(target_batches) > 5:
            print(f"    ... 及其他 {len(target_batches) - 5} 个批次")

        # 2. 确定相关性阈值
        if corr_threshold is None:
            corr_threshold = cfg_mine.mine.filter.corrthresh

        print(f"🎯 相关性阈值: {corr_threshold}")

        # 3. 加载多批次因子数据
        symbol_factor_data = load_multi_batch_factors(target_batches, pipeline_step)

        if not symbol_factor_data:
            print("未加载到有效的因子数据")
            return {}

        # 4. 按上下文进行相关性筛选
        filtered_results = {}
        total_input_factors = 0
        total_output_factors = 0

        for context_key, data_dict in symbol_factor_data.items():
            print(f"\n{'='*50}")
            print(f"🎯 处理上下文: {context_key}")
            print(f"{'='*50}")

            try:
                with FactorMonitorContext(f"L1_CORR_{context_key}",
                                        operation_type='l1_corr_filtering',
                                        batch_id=f"L1_{context_key}_{current_date}",
                                        symbol=data_dict['symbol'],
                                        data_size=data_dict['factor_data'].shape[1]):

                    # 应用相关性筛选
                    selected_factors = apply_corr_filter_for_symbol(
                        context_key, data_dict, corr_threshold
                    )

                    input_count = data_dict['factor_data'].shape[1]
                    output_count = len(selected_factors)

                    total_input_factors += input_count
                    total_output_factors += output_count

                    if selected_factors:
                        filtered_results[context_key] = selected_factors
                        print(f"✅ {context_key} 筛选完成: {output_count} 个因子保留")

                        # 显示部分因子（前2个）
                        for j, factor in enumerate(selected_factors[:2], 1):
                            factor_str = str(factor)
                            if len(factor_str) > 60:
                                factor_str = factor_str[:57] + "..."
                            print(f"    {j}. {factor_str}")
                        if len(selected_factors) > 2:
                            print(f"    ... 及其他 {len(selected_factors) - 2} 个因子")
                    else:
                        print(f"⚠️  {context_key} 未筛选出有效因子")

            except Exception as e:
                print(f"❌ 处理上下文 {context_key} 时出错: {str(e)}")
                continue

        # 5. 统计和保存结果
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        filter_efficiency = (total_output_factors / total_input_factors * 100) if total_input_factors > 0 else 0

        print(f"\n🎉 L1相关性筛选完成!")
        print(f"📊 处理统计:")
        print(f"    输入因子: {total_input_factors} 个")
        print(f"    输出因子: {total_output_factors} 个")
        print(f"    筛选效率: {filter_efficiency:.1f}%")
        print(f"    处理上下文: {len(filtered_results)} 个")
        print(f"    处理耗时: {processing_time:.1f} 秒")

        # 6. 保存筛选结果
        if filtered_results:
            save_filtering_results(filtered_results, current_date, global_uid)

            # 7. 持久化筛选后的因子值和入库
            persistence_success = persist_and_register_factors(
                filtered_results, symbol_factor_data, current_date, global_uid, corr_threshold
            )

            if persistence_success:
                print("✅ 因子持久化和入库完成")
            else:
                print("⚠️  因子持久化和入库部分失败")

        # 8. 保存运行统计
        run_stats_data = {
            'total_input_factors': total_input_factors,
            'total_output_factors': total_output_factors,
            'batch_processed': len(target_batches),
            'symbols_processed': list(filtered_results.keys()),
            'processing_time': processing_time,
            'filter_efficiency': filter_efficiency,
            'corr_threshold': corr_threshold,
            'pipeline_step': pipeline_step
        }

        try:
            stats_path = REPORTS_DIR / f'l1_corr_filter_stats_{current_date}_{global_uid[-6:]}.json'
            with open(stats_path, 'w', encoding='utf-8') as f:
                json.dump(run_stats_data, f, indent=2, ensure_ascii=False)
            print(f"📈 运行统计已保存: {stats_path}")
        except Exception as e:
            print(f"保存运行统计失败: {str(e)}")

        return filtered_results


if __name__ == "__main__":
    # 支持命令行参数或交互式运行
    import sys

    if len(sys.argv) > 1:
        # 命令行模式
        if sys.argv[1] == '--help' or sys.argv[1] == '-h':
            print("L1相关性筛选脚本使用说明:")
            print("python l1_corr_filter.py [--threshold 0.3] [--stage L0]")
            print("  --threshold: 相关性阈值，默认0.3")
            print("  --stage: 输入管道步骤，默认L0")
            exit(0)

        # 解析参数
        corr_threshold = None
        pipeline_step = 'L0'

        for i, arg in enumerate(sys.argv[1:]):
            if arg == '--threshold' and i + 2 < len(sys.argv):
                corr_threshold = float(sys.argv[i + 2])
            elif arg == '--stage' and i + 2 < len(sys.argv):
                pipeline_step = sys.argv[i + 2]

        # 运行筛选
        results = run_l1_corr_filter_pipeline(
            corr_threshold=corr_threshold,
            pipeline_step=pipeline_step
        )
    else:
        # 交互式模式
        results = run_l1_corr_filter_pipeline()

    print("🎉 L1相关性筛选流程完成!")
