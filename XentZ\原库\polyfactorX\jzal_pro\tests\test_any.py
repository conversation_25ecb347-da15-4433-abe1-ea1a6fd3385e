import numpy as np
import pandas as pd
import numba as nb
from pathlib import Path
import sys
import time
path_root = Path(__file__).parent.absolute().parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
# from utils.perf_utils import calc_rolling_rankic


# 原始实现
def calc_rolling_rankic_original(fct_values: np.ndarray, returns: np.ndarray, 
                               window: int = 240, min_periods: int = 120) -> np.ndarray:
    """原始实现"""
    fct_values = fct_values.astype(np.float32)
    returns = returns.astype(np.float32)
    
    if fct_values.ndim == 1:
        fct_values = fct_values.reshape(-1, 1)
    n_samples, n_factors = fct_values.shape

    rolling_rankic = np.full((n_samples, n_factors), np.nan)
    
    # fct_values = np.nan_to_num(fct_values, nan=0.0, posinf=0.0, neginf=0.0)
    # returns = np.nan_to_num(returns, nan=0.0, posinf=0.0, neginf=0.0)
    
    ret_roll = np.lib.stride_tricks.sliding_window_view(returns, window)
    ret_ranks = np.argsort(np.argsort(ret_roll, axis=1), axis=1).astype(np.int32)
    # print(ret_roll[0]),exit()
    for j in range(n_factors):
        fct_roll = np.lib.stride_tricks.sliding_window_view(fct_values[:, j], window)
        fct_ranks = np.argsort(np.argsort(fct_roll, axis=1), axis=1).astype(np.int32)
        
        valid_mask = (np.sum(~np.isclose(fct_roll, 0.0), axis=1) >= min_periods)
        corr = vectorized_spearman(fct_ranks, ret_ranks)
        corr[~valid_mask] = np.nan
        
        rolling_rankic[window-1:, j] = corr
    
    return rolling_rankic.squeeze()

@nb.jit(nopython=True)
def rank_1d(arr):
    """计算1D数组的排名，与numpy双重argsort结果一致"""
    arr = arr.copy()
    n = len(arr)
    # 第一次argsort：获取值从小到大的索引位置
    # 注意：np.argsort默认是稳定排序，相同值的相对顺序保持不变
    first_sort = np.argsort(arr)
    ranks = np.zeros(n, dtype=np.int32)
    # 第二次：将排序位置赋值给原始位置
    for i in range(n):
        ranks[first_sort[i]] = i
    return ranks

@nb.jit(nopython=True, parallel=True)
def fast_spearman(x: np.ndarray, y: np.ndarray) -> np.ndarray:
    """使用numba加速的Spearman相关系数计算"""
    x = x.copy()
    y = y.copy()
    n = x.shape[1]
    rho = np.empty(x.shape[0])
    
    if n <= 1:
        rho.fill(np.nan)
        return rho
    
    for i in nb.prange(x.shape[0]):
        d_squared = (x[i] - y[i]) ** 2
        sum_d_squared = np.sum(d_squared)
        rho[i] = 1 - (6 * sum_d_squared) / (n * (n**2 - 1))
    
    return rho

@nb.jit(nopython=True, parallel=True)
def calc_rolling_rankic_fast_call(fct_values: np.ndarray, returns: np.ndarray, 
                           window: int = 240, min_periods: int = 120) -> np.ndarray:
    """使用numba并行优化的滚动RankIC计算实现"""
    # 数据预处理：处理NaN和无穷值
    fct_values_clean = fct_values.copy().astype(np.float32)
    returns_clean = returns.copy().astype(np.float32)
    
    if fct_values.ndim == 1:
        fct_values = fct_values.reshape(-1, 1)
    n_samples, n_factors = fct_values.shape
    
    rolling_rankic = np.full((n_samples, n_factors), np.nan)
    
    # 计算收益率的滑动窗口
    ret_roll = np.lib.stride_tricks.sliding_window_view(returns_clean, window)
    n_windows = ret_roll.shape[0]
    
    # 预计算收益率排名
    ret_ranks = np.empty_like(ret_roll, dtype=np.int32)
    for i in range(n_windows):
        ret_ranks[i] = rank_1d(ret_roll[i])
    
    # 并行处理每个因子
    for j in nb.prange(n_factors):
        fct_roll = np.lib.stride_tricks.sliding_window_view(fct_values_clean[:, j], window)
        fct_ranks = np.empty_like(fct_roll, dtype=np.int32)
        
        # 计算因子排名
        for i in range(n_windows):
            fct_ranks[i] = rank_1d(fct_roll[i])
        
        # 使用简单的比较操作替代isclose
        valid_mask = np.zeros(n_windows, dtype=np.bool_)
        for i in range(n_windows):
            valid_count = 0
            for k in range(window):
                if abs(fct_roll[i, k]) > 1e-8:  # 使用小阈值替代isclose
                    valid_count += 1
            valid_mask[i] = (valid_count >= min_periods)
        
        corr = fast_spearman(fct_ranks, ret_ranks)
        corr[~valid_mask] = np.nan
        rolling_rankic[window-1:, j] = corr
    
    return rolling_rankic

def vectorized_spearman(x: np.ndarray, y: np.ndarray) -> np.ndarray:
    """计算Spearman相关系数的向量化实现"""
    n = x.shape[1]
    d_squared = (x - y) ** 2
    sum_d_squared = np.sum(d_squared, axis=1)
    rho = 1 - (6 * sum_d_squared) / (n * (n**2 - 1))
    rho = np.where(n <= 1, np.nan, rho)
    return rho

# 在文件开头，所有函数定义之后，测试函数之前添加预编译代码
def warmup_jit_functions():
    """预热所有jit函数，避免第一次运行时的编译开销"""
    # 生成小规模测试数据
    n_samples, n_factors = 300, 2
    fct_values = np.random.randn(n_samples, n_factors).astype(np.float32)
    returns = np.random.randn(n_samples).astype(np.float32)
    
    # 预热所有jit函数
    # _ = rank_1d(np.array([1., 2., 3.]))
    # _ = fast_spearman(np.random.randn(5, 5), np.random.randn(10, 5))
    _ = calc_rolling_rankic_fast_call(fct_values, returns)

# 在程序启动时预热
warmup_jit_functions()

def test_rolling_rankic_all_implementations():
    """测试所有实现的性能和结果一致性"""
    from sklearnex import patch_sklearn
    patch_sklearn()  # Intel优化
    
    window = 240
    
    np.random.seed(42)
    test_cases = [
        # (1000, 2),    # 小数据集
        (10000, 5),   # 中等数据集
        # (50000, 10),  # 大数据集
        # (70000, 20),  # 超大数据集
        # (100000, 1000),  # 超大数据集
    ]
    
    implementations = {
        "原始实现": calc_rolling_rankic_original,
        # "Numba优化": calc_rolling_rankic_wrapper,
        "外部sperman": calc_rolling_rankic_fast_call,
        # "外部sperman2": calc_rolling_rankic_fast_call,
    }
    
    for n_samples, n_factors in test_cases:
        print(f"\n{'='*50}")
        print(f"测试数据规模: {n_samples}行 x {n_factors}个因子")
        print('='*50)
        
        # 生成测试数据
        fct_values = np.random.randn(n_samples, n_factors).astype(np.float32)
        returns = np.random.randn(n_samples).astype(np.float32)
        
        # 添加一些异常值
        # fct_values[np.random.rand(*fct_values.shape) < 0.1] = np.nan # bad
        # fct_values[np.random.rand(*fct_values.shape) < 0.1] = np.inf # bad
        returns[np.random.rand(n_samples) < 0.1] = np.nan
        
        # 数据预处理
        fct_values_clean = np.nan_to_num(fct_values.copy(), nan=0.0, posinf=0.0, neginf=0.0)
        returns_clean = np.nan_to_num(returns.copy(), nan=0.0, posinf=0.0, neginf=0.0)
        # fct_values_clean = fct_values.copy()
        # returns_clean = returns.copy()
        
        # 运行所有实现并记录结果
        results = {}
        times = {}
        
        for name, impl in implementations.items():
            print(f"\n运行 {name}...")
            start_time = time.time()
            results[name] = impl(fct_values_clean, returns_clean)
            times[name] = time.time() - start_time
            # print(times["原始实现"])
            # exit()
        
        # 比较结果
        print("\n结果比较:")
        base_result = results["原始实现"]
        for name in implementations:
            if name == "原始实现":
                continue
            
            is_equal = np.allclose(base_result, results[name], equal_nan=True, rtol=1e-5, atol=1e-5)
            max_diff = np.nanmax(np.abs(base_result - results[name]))
            
            print(f"\n{name} vs 原始实现:")
            print(f"结果一致性: {'通过' if is_equal else '失败'}")
            print(f"最大差异: {max_diff}")
        
        # 性能比较
        print("\n性能比较:")
        base_time = times["原始实现"]
        for name, t in times.items():
            speedup = base_time / t if name != "原始实现" else 1.0
            print(f"{name:12s}: {t:.4f}秒 (提升{speedup:.2f}倍)")
        
        # 如果结果不一致，显示详细差异
        for name in implementations:
            if name == "原始实现":
                continue
            
            if not np.allclose(base_result, results[name], equal_nan=True, rtol=1e-5, atol=1e-5):
                print(f"\n{name} 详细差异分析:")
                diff_mask = ~np.isclose(base_result, results[name], equal_nan=True)
                if base_result.ndim == 1:
                    diff_indices = np.where(diff_mask)[0]
                    print(f"发现 {len(diff_indices)} 处差异")
                else:
                    diff_rows, diff_cols = np.where(diff_mask)
                    unique_positions = list(zip(diff_rows, diff_cols))
                    print(f"发现 {len(unique_positions)} 处差异")
            
            print(results["原始实现"])
            print(results["外部sperman"])
if __name__ == '__main__':
    test_rolling_rankic_all_implementations()