import numpy as np
import numba as nb
from common.cls_base import BaseObj

def warmup_jit_functions():
    """预热jit函数，避免第一次运行时的编译开销"""
    # 生成小规模测试数据
    n_samples, n_factors = 300, 2
    fct_values = np.random.randn(n_samples, n_factors).astype(np.float32)
    returns = np.random.randn(n_samples).astype(np.float32)

    # 预热jit函数（只有calc_rolling_rankic使用了jit）
    _ = FactorPerf.calc_rolling_rankic(fct_values, returns)
    
@nb.jit(nopython=True)
def rank_1d(arr):
    """计算1D数组的排名，与numpy双重argsort结果一致"""
    arr = arr.copy()
    n = len(arr)
    # 第一次argsort：获取值从小到大的索引位置
    # 注意：np.argsort默认是稳定排序，相同值的相对顺序保持不变
    first_sort = np.argsort(arr)
    ranks = np.zeros(n, dtype=np.int32)
    # 第二次：将排序位置赋值给原始位置
    for i in range(n):
        ranks[first_sort[i]] = i
    return ranks

@nb.jit(nopython=True, parallel=True)
def fast_spearman(x: np.ndarray, y: np.ndarray) -> np.ndarray:
    """使用numba加速的Spearman相关系数计算"""
    x = x.copy()
    y = y.copy()
    n = x.shape[1]
    rho = np.empty(x.shape[0])
    
    if n <= 1:
        rho.fill(np.nan)
        return rho
    
    for i in nb.prange(x.shape[0]):
        d_squared = (x[i] - y[i]) ** 2
        sum_d_squared = np.sum(d_squared)
        rho[i] = 1 - (6 * sum_d_squared) / (n * (n**2 - 1))
    
    return rho

class FactorPerf(BaseObj):
    """ === 统一绩效计算核心模块 ===

    设计哲学：学习 calc_sr 的极简优雅风格
    - 极简主义：每个函数都追求最少的代码行数
    - 高性能：全部使用numba优化和向量化计算
    - 无状态：纯函数设计，无副作用
    - 批量处理：天然支持多资产同时计算
    - 健壮性：优雅的异常处理，一行代码解决边界问题
    """
    def __init__(self):
        pass
    
    @staticmethod
    @nb.jit(nopython=True, parallel=True)
    def calc_rolling_rankic(fct_values: np.ndarray, returns: np.ndarray, 
                            window: int = 240, min_periods: int = 120) -> np.ndarray:
        """
        计算滚动 RankIC 矩阵（每个时间窗口对应一个 RankIC 值）
        
        Args:
            fct_values: 因子值序列, shape (n_samples,) 或 (n_samples, n_factors)
            returns: 未来 t 期收益率序列（与因子序列对齐）, shape (n_samples,)
            window: 滚动窗口大小
            min_periods: 每个窗口要求的最小有效样本数（这里以因子值非 0 的数量判断）
            
        Returns:
            滚动 RankIC 矩阵，形状为 (n_samples, n_factors) —— 前 window-1 行为 NaN，
            后续每一行对应一个窗口计算得到的 RankIC 值
        """
        """优化后实现"""
        # 数据预处理：处理NaN和无穷值
        fct_values_clean = fct_values.copy().astype(np.float32)
        returns_clean = returns.copy().astype(np.float32)
        
        if fct_values.ndim == 1:
            fct_values = fct_values.reshape(-1, 1)
        n_samples, n_factors = fct_values.shape
        
        rolling_rankic = np.full((n_samples, n_factors), np.nan)
            
        # 计算收益率的滑动窗口
        ret_roll = np.lib.stride_tricks.sliding_window_view(returns_clean, window)
        n_windows = ret_roll.shape[0]
        
        # 预计算收益率排名
        ret_ranks = np.empty_like(ret_roll, dtype=np.int32)
        for i in range(n_windows):
            ret_ranks[i] = rank_1d(ret_roll[i])
        
        # 并行处理每个因子
        for j in nb.prange(n_factors):
            fct_roll = np.lib.stride_tricks.sliding_window_view(fct_values_clean[:, j], window)
            fct_ranks = np.empty_like(fct_roll, dtype=np.int32)
            
            # 计算因子排名
            for i in range(n_windows):
                fct_ranks[i] = rank_1d(fct_roll[i])
            
            # 使用简单的比较操作替代isclose
            valid_mask = np.zeros(n_windows, dtype=np.bool_)
            for i in range(n_windows):
                valid_count = 0
                for k in range(window):
                    if abs(fct_roll[i, k]) > 1e-8:  # 使用小阈值替代isclose
                        valid_count += 1
                valid_mask[i] = (valid_count >= min_periods)
            
            corr = fast_spearman(fct_ranks, ret_ranks)
            corr[~valid_mask] = np.nan
            rolling_rankic[window-1:, j] = corr
            
        return rolling_rankic
    
    @staticmethod
    def calc_sr(bar_ret: np.ndarray, day_bars: int = 16, trading_days: int = 252, free_rate: float = 0.03) -> np.ndarray:
        '''夏普比率计算（优雅设计模板）
        输入: 收益率矩阵, shape (n_periods, n_assets)
        返回: 各资产对应的夏普比率 -- 一维数组
        '''
        # 极简数据清洗：一行代码解决所有异常值
        clean_ret = np.where(np.isfinite(bar_ret), bar_ret, 0.0)
        ret_mean = np.mean(clean_ret, axis=0)
        ann_ret = ret_mean * day_bars * trading_days
        ret_std = clean_ret.std(axis=0)
        ann_vol = np.sqrt(day_bars * trading_days) * ret_std
        cond_0 = np.isclose(ann_vol, 0)
        # 优雅的除零处理
        sr = np.where(cond_0, 0.0, (ann_ret - free_rate) / ann_vol)
        return sr

    @staticmethod
    def calc_max_drawdown(cumulative_returns: np.ndarray) -> np.ndarray:
        '''最大回撤计算（学习calc_sr的极简风格）
        输入: 累积收益序列, shape (n_periods, n_assets)
        返回: 各资产的最大回撤 -- 一维数组
        '''
        cum_ret = np.where(np.isfinite(cumulative_returns), cumulative_returns, 1.0)
        if cum_ret.ndim == 1:
            rolling_max = np.maximum.accumulate(cum_ret)
            drawdowns = (cum_ret / rolling_max) - 1
            return abs(np.min(drawdowns))
        else:
            rolling_max = np.maximum.accumulate(cum_ret, axis=0)
            drawdowns = (cum_ret / rolling_max) - 1
            return np.abs(np.min(drawdowns, axis=0))

    @staticmethod
    def calc_annual_return(returns: np.ndarray, annualization_factor: int = 252) -> np.ndarray:
        '''年化收益率计算（极简风格）'''
        clean_ret = np.where(np.isfinite(returns), returns, 0.0)
        return np.mean(clean_ret, axis=0) * annualization_factor

    @staticmethod
    def calc_annual_volatility(returns: np.ndarray, annualization_factor: int = 252) -> np.ndarray:
        '''年化波动率计算（极简风格）'''
        clean_ret = np.where(np.isfinite(returns), returns, 0.0)
        return clean_ret.std(axis=0) * np.sqrt(annualization_factor)

    @staticmethod
    def calc_win_rate(returns: np.ndarray) -> np.ndarray:
        '''胜率计算（极简风格）'''
        clean_ret = np.where(np.isfinite(returns), returns, 0.0)
        return np.mean(clean_ret > 0, axis=0)

    @staticmethod
    def calc_basic_metrics(returns: np.ndarray, annualization_factor: int = 252, risk_free_rate: float = 0.0):
        '''批量计算所有基础绩效指标（最高性能版本）
        学习calc_sr的设计哲学：一次计算，全部输出

        输入: 收益率矩阵, shape (n_periods, n_assets)
        返回: 绩效指标字典，每个值都是一维数组

        注意：此方法不使用@nb.jit装饰器，因为返回字典在numba中有限制
        '''
        # 统一数据清洗（学习calc_sr的一行解决方案）
        clean_ret = np.where(np.isfinite(returns), returns, 0.0)

        # 基础统计量（一次计算，多次复用）
        ret_mean = np.mean(clean_ret, axis=0)
        ret_std = clean_ret.std(axis=0)

        # 年化指标
        ann_ret = ret_mean * annualization_factor
        ann_vol = ret_std * np.sqrt(annualization_factor)

        # 夏普比率（学习calc_sr的优雅处理）
        zero_vol_mask = np.isclose(ann_vol, 0)
        sharpe = np.where(zero_vol_mask, 0.0, (ann_ret - risk_free_rate) / ann_vol)

        # 累积收益和最大回撤
        cum_ret = np.cumprod(1 + clean_ret, axis=0)
        rolling_max = np.maximum.accumulate(cum_ret, axis=0)
        drawdowns = (cum_ret / rolling_max) - 1
        max_dd = np.abs(np.min(drawdowns, axis=0))

        # 其他指标
        total_ret = cum_ret[-1] - 1
        win_rate = np.mean(clean_ret > 0, axis=0)

        # 卡玛比率
        zero_dd_mask = np.isclose(max_dd, 0)
        calmar = np.where(zero_dd_mask, 0.0, ann_ret / max_dd)

        return {
            'annual_return': ann_ret,
            'volatility': ann_vol,
            'sharpe_ratio': sharpe,
            'max_drawdown': max_dd,
            'calmar_ratio': calmar,
            'total_return': total_ret,
            'win_rate': win_rate
        }

    @classmethod
    def calc_single_asset_metrics(cls, returns, annualization_factor: int = 252, risk_free_rate: float = 0.0):
        '''单资产绩效计算（便捷方法）
        输入: 一维收益率序列（list, pandas Series, 或 numpy array）
        返回: 标量值的绩效指标字典
        '''
        # 边界条件处理
        if returns is None:
            return {}

        # 统一转换为numpy数组
        try:
            if hasattr(returns, 'values'):  # pandas Series
                ret_array = returns.values
            else:
                ret_array = np.asarray(returns)
        except (ValueError, TypeError):
            return {}

        # 检查数据有效性
        if ret_array.size == 0:
            return {}

        # 检查是否全是NaN或无穷值
        if not np.any(np.isfinite(ret_array)):
            return {}

        # 检查数据点是否足够
        valid_data = ret_array[np.isfinite(ret_array)]
        if len(valid_data) < 2:
            return {}

        # 确保是二维数组以使用批量计算
        if ret_array.ndim == 1:
            ret_array = ret_array.reshape(-1, 1)

        # 使用批量计算方法
        metrics = cls.calc_basic_metrics(ret_array, annualization_factor, risk_free_rate)

        # 转换为标量值
        return {key: float(value[0]) if hasattr(value, '__len__') else float(value)
                for key, value in metrics.items()}
    
    
