import streamlit as st
import akshare as ak
import plotly.express as px
import pandas as pd
from datetime import datetime, timedelta


from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import time
import logging

# 初始化全局锁和日志
akshare_lock = threading.Lock()
logger = logging.getLogger(__name__)

def process_industry_row(symbol, start_date_str, end_date_str):
    try:
        with akshare_lock:
            df = ak.stock_board_industry_hist_em(
                symbol=symbol,
                start_date=start_date_str,
                end_date=end_date_str,
                adjust="hfq"
            ).copy(deep=True)
            
        if not df.empty:
            return {
                "板块名称": symbol,
                "起始价": df.iloc[0]['开盘'],
                "收盘价": df.iloc[-1]['收盘'],
                "区间涨跌幅": (df.iloc[-1]['开盘'] - df.iloc[0]['收盘']) / df.iloc[0]['收盘'] * 100,
                "总成交额（亿）": df['成交额'].sum() / 1e8,
                "日均换手率": df['换手率'].mean()
            }
    except Exception as e:
        logger.error(f"获取{symbol}数据失败: {str(e)}")
        return None

def get_industry_data(start_date_str, end_date_str, progress_callback=None):
    board_df = ak.stock_board_industry_name_em()
    data_list = []
    total = len(board_df)
    
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = []
        for row in board_df.itertuples():
            futures.append(executor.submit(
                process_industry_row,
                row.板块名称,
                start_date_str,
                end_date_str
            ))
            time.sleep(0.33)  # 速率限制3请求/秒

        for i, future in enumerate(as_completed(futures), 1):
            result = future.result()
            if result:
                data_list.append(result)
                logger.info(f"成功获取{result['板块名称']}数据")
            
            if progress_callback:
                progress_callback(i/total, f"行业数据加载中 ({i}/{total})")
            
    return pd.DataFrame(data_list)

def process_concept_row(symbol, start_date_str, end_date_str):
    try:
        with akshare_lock:
            df = ak.stock_board_concept_hist_em(
                symbol=symbol,
                period='daily',
                start_date=pd.to_datetime(start_date_str).strftime('%Y%m%d'),
                end_date=pd.to_datetime(end_date_str).strftime('%Y%m%d')
            ).copy(deep=True)
            
        if not df.empty:
            df = df.rename(columns={
                'turnover': '成交额',
                'turnover_rate': '换手率',
                'close': '收盘'
            })
            return {
                "板块名称": symbol,
                "起始价": df.iloc[0]['开盘'],
                "收盘价": df.iloc[-1]['收盘'],
                "区间涨跌幅": (df.iloc[-1]['开盘'] - df.iloc[0]['收盘']) / df.iloc[0]['收盘'] * 100,
                "总成交额（亿）": df['成交额'].sum() / 10000,
                "日均换手率": df['换手率'].mean()
            }
    except Exception as e:
        logger.error(f"获取{symbol}概念数据失败: {str(e)}")
        return None

def get_concept_data(start_date_str, end_date_str, progress_callback=None):
    board_df = ak.stock_board_concept_name_em().rename(columns={'name': '板块名称'})
    data_list = []
    total = len(board_df)
    
    with ThreadPoolExecutor(max_workers=3) as executor:
        futures = []
        for row in board_df.itertuples():
            futures.append(executor.submit(
                process_concept_row,
                row.板块名称,
                start_date_str,
                end_date_str
            ))
            time.sleep(0.33)  # 速率限制3请求/秒

        for i, future in enumerate(as_completed(futures), 1):
            result = future.result()
            if result:
                data_list.append(result)
                logger.info(f"成功获取{result['板块名称']}概念数据")
            
            if progress_callback:
                progress_callback(i/total, f"概念数据加载中 ({i}/{total})")
            
    return pd.DataFrame(data_list).dropna().reset_index(drop=True)


# 主程序
def main():
    st.set_page_config(
        page_title="板块区间分析",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 侧边栏控件
    with st.sidebar:
        st.header("时间设置")
        start_date = st.date_input(
            "开始日期",
            value=datetime.now() - timedelta(days=1),
            min_value=datetime(2020, 1, 1)
        )
        end_date = st.date_input(
            "结束日期",
            value=datetime.now(),
            max_value=datetime.now()
        )
        
        st.header("可视化设置")
        color_scale = st.selectbox(
            "配色方案",
            options=['RdYlGn_r', 'BrBG_r', 'PiYG_r', 'RdBu_r'],
            index=0
        )
        size_metric = st.selectbox(
            "板块规模指标",
            options=['总成交额（亿）', '日均换手率'],
            index=0
        )
    
    # 数据加载
    start_str = start_date.strftime("%Y%m%d")
    end_str = end_date.strftime("%Y%m%d")
    
    # 行业数据加载
    with st.spinner('行业数据加载中...'):
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        def update_industry_progress(progress, msg):
            progress_bar.progress(min(progress, 1.0))
            status_text.info(f"{msg} | 预计剩余时间: {max(0, (1-progress)*5):.1f}秒")
            
        industry_df = get_industry_data(start_str, end_str, update_industry_progress)
        progress_bar.empty()
        status_text.empty()
        
        if not industry_df.empty:
            st.success(f"成功加载行业数据: {len(industry_df)} 条记录")
        else:
            st.error("行业数据加载失败")

    # 概念数据加载
    with st.spinner('概念数据加载中...'):
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        def update_concept_progress(progress, msg):
            progress_bar.progress(min(progress, 1.0))
            status_text.info(f"{msg} | 预计剩余时间: {max(0, (1-progress)*3):.1f}秒")
            
        concept_df = get_concept_data(start_str, end_str, update_concept_progress)
        progress_bar.empty()
        status_text.empty()
        
        if not concept_df.empty:
            st.success(f"成功加载概念数据: {len(concept_df)} 条记录")
        else:
            st.error("概念数据加载失败")
            return
            
        if industry_df.empty and concept_df.empty:
            st.error("当前时间段无有效数据，请调整日期范围")
            return
            
        # 确保数据列存在
        required_columns = ['板块名称', '区间涨跌幅', '总成交额（亿）', '日均换手率']
        for df in [industry_df, concept_df]:
            if not all(col in df.columns for col in required_columns):
                st.error(f"数据列不完整，缺少必要列: {[col for col in required_columns if col not in df.columns]}")
                return
    
    # 行业板块热力图
    with st.container():
        fig1 = px.treemap(
            industry_df,
            path=['板块名称'],
            values=size_metric,
            color='区间涨跌幅',
            color_continuous_scale=color_scale,
            range_color=[industry_df['区间涨跌幅'].min(), industry_df['区间涨跌幅'].max()],
            hover_data={
                '起始价': ':.2f',
                '收盘价': ':.2f',
                '区间涨跌幅': ':.2f%',
                '总成交额（亿）': ':.1f'
            },
            height=800
        )
        fig1.update_layout(
            margin=dict(t=40, l=0, r=0, b=0),
            title={
                'text': f"行业板块表现 {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}",
                'y': 0.95,
                'x': 0.5
            },
        )
        st.plotly_chart(fig1, use_container_width=True)
    
    # 添加分割线
    st.markdown("---")
    
    # 概念板块热力图
    with st.container():
        fig2 = px.treemap(
            concept_df,
            path=['板块名称'],
            values=size_metric,
            color='区间涨跌幅',
            color_continuous_scale=color_scale,
            range_color=[concept_df['区间涨跌幅'].min(), concept_df['区间涨跌幅'].max()],
            hover_data={
                '起始价': ':.2f',
                '收盘价': ':.2f',
                '区间涨跌幅': ':.2f%',
                '总成交额（亿）': ':.1f'
            },
            height=800
        )
        fig2.update_layout(
            margin=dict(t=40, l=0, r=0, b=0),
            title={
                'text': f"概念板块表现 {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}",
                'y': 0.95,
                'x': 0.5
            },
        )
        st.plotly_chart(fig2, use_container_width=True)
    
    # 数据表格
    with st.expander("查看详细数据排名"):
        tab1, tab2 = st.tabs(["行业板块", "概念板块"])
        
        with tab1:
            st.dataframe(
                industry_df.sort_values(by='区间涨跌幅', ascending=False),
                column_config={
                    "板块名称": st.column_config.TextColumn(width="large"),
                    "区间涨跌幅": st.column_config.NumberColumn(format="▁+%.2f%%"),
                    "总成交额（亿）": st.column_config.NumberColumn(format="%.1f 亿"),
                    "日均换手率": st.column_config.NumberColumn(format="%.2f%%")
                },
                height=400,
                hide_index=True
            )
        
        with tab2:
            st.dataframe(
                concept_df.sort_values(by='区间涨跌幅', ascending=False),
                column_config={
                    "板块名称": st.column_config.TextColumn(width="large"),
                    "区间涨跌幅": st.column_config.NumberColumn(format="▁+%.2f%%"),
                    "总成交额（亿）": st.column_config.NumberColumn(format="%.1f 亿"),
                    "日均换手率": st.column_config.NumberColumn(format="%.2f%%")
                },
                height=400,
                hide_index=True
            )


if __name__ == "__main__":
    main()
