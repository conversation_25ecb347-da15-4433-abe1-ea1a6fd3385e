import streamlit as st
from pathlib import Path
import sys

root = Path(__file__).parent.parent.parent
if str(root) not in sys.path:
    sys.path.append(str(root))

from gui.pages.utils import render_operators_page

def main():
    """算子管理页面"""
    if st.session_state.get('current_page') != "算子管理":
        st.switch_page("Home.py")
        return
        
    if 'task_mining' not in st.session_state:
        st.error("请先完成任务配置")
        return

    render_operators_page()

if __name__ == "__main__":
    main()
