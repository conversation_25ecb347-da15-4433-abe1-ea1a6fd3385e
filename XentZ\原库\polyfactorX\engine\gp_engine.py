import pandas as pd
import numpy as np
import json
import os
from multiprocessing import Pool, cpu_count
from datetime import datetime
import sys
import math
from pathlib import Path
from sklearnex import patch_sklearn # intel加速
from sklearn.linear_model import LassoCV, LinearRegression
from typing import List
path_root = Path(__file__).parent.absolute().parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
from engine.combos import ComboMgr,XLayerComboGenerator, GAComboGenerator
from engine.modelers.model_strats import LinearRegressionModel, ModelFactory
from engine.modelers.fcts_modeler import FactorsModeler
from engine.modelers.fcts_evaluator import FactorsEvaluator
from engine.miners.gplearn_miner import FctsGPMiner
from task_defs.task_fctsmine import TaskFctsMining
from datastore import TaskDataMgr, FactorData, TaskData, FactorDataMgr, FactorValueMgr, RunDataMgr, RunData
from datastore import OptResultData, OptResultDataMgr, OptRunDataManager, OptRunData
from datastore import FactorL2Data, FactorL2DataMgr
from jzal_pro.cls_common import BaseObj,ResMonitor
import jzal_pro.utils.expr_utils as eu
import jzal_pro.utils.df_utils as du
import jzal_pro.utils.perf_utils as pu
from jzal_pro.utils import calc_stats_RC, stats_saw_score, stats_topsis_score, to_day_pct_ret, to_day_close
from config import DATA_DIR_FACTOR, DATA_DIR_MODEL, DB_DIR_FACTOR
from enums import GP_LOOP, L1_STEP1_ALGO, METRIC_PARAMS_DEFAULT, MODEL_ALGO, NORM_PARAMS_POS, TRADE_ON_OPEN, LABEL_SINGLE, T_DELAY

patch_sklearn() # intel加速

class FctsGPEngine(BaseObj):
    ''' 支持多品种 多特征 的 因子挖掘 并结果分析 '''
    def __init__(self, task:TaskFctsMining):
        super().__init__()
        self.task = task
        ''' 全局变量: 计算过程中动态赋值 '''
        self.X_dfs = {} # {symbol: X_df} 动态变化因子列
        self.y_norm_dfs = {} # {symbol: y_df} 固定
        self.y_raw_dfs = {} # {symbol: y_df} 固定
        self.y_raw_t_return_dfs = {} # {symbol: y_df} 固定 t-期裸收益率
        self.best_fcts = {}
        self.pj_fcts = {}
        ''' init 环境变量'''
        self._init_env()
        
    def _init_env(self, db_name: str = 'jzal.sqlite', 
                  fctval_base_dir: str = 'factor_values',
                  fctmodel_base_dir: str = 'factor_models',
                  fctpic_base_dir: str = 'factor_pics',
                  fctpic_single_f_dir: str = 'fctpic_single_f',
                  fctpic_single_pj_dir: str = 'fctpic_single_pj',
                  fctpic_combo_dir: str = 'fctpic_combo',
                  fctval_file_max_cols: int = 200):
        ''' ========== 确定电脑支持的进程数量 ========== '''
        if self.task.job_num == -1:
            self.processes = cpu_count()  # 获取当前电脑的最大处理器核心数
        elif self.task.job_num <= cpu_count():
            self.processes = self.task.job_num  # 使用指定的核心数
        else:
            self.processes = cpu_count()  # 使用当前电脑的最大处理器核心数
        ''' ========== 初始化路径信息 目录不存在的会自动创建 ========== '''
        self.db_path = str(DB_DIR_FACTOR.joinpath(db_name))
        self.fctval_base_dir = str(DB_DIR_FACTOR.joinpath(fctval_base_dir))
        self.fctmodel_base_dir = str(DB_DIR_FACTOR.joinpath(fctmodel_base_dir))
        self.fctpic_base_dir = str(DB_DIR_FACTOR.joinpath(fctpic_base_dir))
        self.fctpic_single_f_dir = str(DB_DIR_FACTOR.joinpath(fctpic_base_dir).joinpath(fctpic_single_f_dir))  # noqa: E501
        self.fctpic_single_pj_dir = str(DB_DIR_FACTOR.joinpath(fctpic_base_dir).joinpath(fctpic_single_pj_dir))  # noqa: E501
        self.fctpic_combo_dir = str(DB_DIR_FACTOR.joinpath(fctpic_base_dir).joinpath(fctpic_combo_dir))
        # 检查并创建目录
        directories = [
            self.fctval_base_dir,
            self.fctmodel_base_dir,
            self.fctpic_base_dir,
            self.fctpic_single_f_dir,
            self.fctpic_single_pj_dir,
            self.fctpic_combo_dir
        ]
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)        
        ''' ========== 设置大宽表单表的最大列 ========== '''
        self.fctval_file_max_cols = fctval_file_max_cols # 保持整套引擎该设置一致性
        ''' ========== 初始化模型大师 ========== '''
        model_strat = ModelFactory.create(MODEL_ALGO)
        self.modeler = FactorsModeler(model_strat, self.fctmodel_base_dir)
        
    def expand_features(self):
        ''' ==========  数据加载和特征生成 独立出来方法, 挖掘或分析前做这步 =========== '''
        """ ===== 按配置文件全局参数 加载 所有内置特征 ===== """
        # 1. 从GUI配置更新全局参数
        if hasattr(self.task, 'global_config') and self.task.global_config:
            cfg = self.task.global_config
            self.task.fee_rate = cfg.get('fee_rate', self.task.fee_rate)
            self.task.split_perc = cfg.get('split_perc', self.task.split_perc)
            self.task.free_rate = cfg.get('free_rate', self.task.free_rate)
            self.task.t_delay = cfg.get('t_delay', self.task.t_delay)
            self.task.start_date = cfg.get('start_date', self.task.start_date)
            self.task.end_date = cfg.get('end_date', self.task.end_date)
            
        # 2. 加载基础数据和特征
        self.dfs, self.base_features = self.task.load_datas()  # 含norm后的features且float64过
        self.dates = list(self.dfs[0].index.unique())
        self._inner_symbols = self.task._inner_symbols  # ['510050.SH_15',..]
        ''' ========== 按pair设置的日期截断数据同时 赋值保存label/ret列 ========== '''
        for a_df in self.dfs: # NOTE: 不做多进程
            # 用 _inner_symbols 确定当前品种周期标识
            symbol_period = str(a_df['symbol'].values[0]) # eg. '510050.SH_15'
            # 1. 应用品种专属配置,并获取原始参数(如果有修改)
            orig_params = self._apply_pair_config(symbol_period)
            
            # 如果日期范围有变化,需要重新截取数据
            if orig_params and (orig_params['start_date'] != self.task.start_date or 
                               orig_params['end_date'] != self.task.end_date):
                a_df = a_df.loc[self.task.start_date:self.task.end_date].copy()
            # 2. 数据预处理
            ''' ========== 赋值: X/label/ret/未来t期 ========== '''
            '''
            X_dfs:      多数norm过的因子列, 增加了'ret'列, 区分ret或ret_open, 但此时都叫ret
            y_norm_dfs: label列, TODO: 支持多label列
            y_raw_dfs:  没norm的ret - 装仓用, 已区分了ret或ret_open
            y_raw_t_return_dfs: 没norm的t_ret - 用于算ic等未来t期收益率用
            '''
            symbol = self._prepare_a_df(a_df)
            assert len(self.X_dfs[symbol]) == len(self.y_norm_dfs[symbol])
            assert len(self.y_norm_dfs[symbol]) == len(self.y_raw_dfs[symbol])
            assert len(self.y_norm_dfs[symbol]) == len(self.y_raw_t_return_dfs[symbol])
            ''' ========== 恢复原始参数 ========== '''
            self._restore_global_config(orig_params)
            
        
    def _prepare_a_df(self, a_df: pd.DataFrame) -> str: 
        ''' 
            目标: 分解X_df, y_df, y_raw_df, 但不分训练集和测试集
            a_df: [index(date), OHLC, volume, volume_norm, symbol, R_0,... V_0, rsi_6, ...]
            返回: 品种名
        '''
        df = a_df.dropna(axis=0, how='any') # 删除缺失值的行(axis=0)
        # df['ret'] = df['close'].pct_change(periods=t).shift(-t) # puppy: pct_change对负数时有bug
        # puppy: 开挖之前算ret不用考虑手续费, 挖的过程中metric时考虑, 尽量在过程中把细节都增加进去
        if TRADE_ON_OPEN: # NOTE: 'ret'是为了装仓位用; 'label_'是为了训练用
            df['ret'] = df['open'].shift(-1) / df['open'] - 1  # ret不是label不能考虑T期
            y_raw_t_return = (df['open'].shift(-T_DELAY) / df['open'] - 1).copy()  # t期收益率
        else:    
            df['ret'] = df['close'].shift(-1) / df['close'] - 1
            y_raw_t_return = (df['close'].shift(-T_DELAY) / df['close'] - 1).copy()  # t期收益率
        y_raw = df['ret'].copy() # NOTE:把不经过norm的真实未来收益率ret传入gplearn
        # TODO: 赋值y_raw_t_return, 用于算ic等未来t期收益率用, 如果是1期就是df['close'].shift(-1) / df['close'] - 1, t期就是后面t个bar的算术累计收益率, 给出实现代码, 注意要与时序信息对齐, 比如当前date 对应的是为来t期收益率
        
        # df['ret'] = np.log(df['close'].shift(-t) / df['close']) # NOTE: 改为puppy推荐
        # NOTE: 做norm, 保证数据无偏
        # df['ret'] = eu.norm(df['ret'], params=NORM_PARAMS_LABEL)  # (1000,2,0) TODO:进一步测试
        # df['ret'] = df['ret'].astype(np.float64)
        symbol = str(df['symbol'].values[0])
        x_train = df[self.base_features] # 不含label_
        y_train = df[LABEL_SINGLE] # 为OLS模型用一元
        self.X_dfs[symbol] = x_train.copy()
        self.y_norm_dfs[symbol] = y_train.copy()
        self.y_raw_dfs[symbol] = y_raw.copy() # 装仓用
        self.y_raw_t_return_dfs[symbol] = y_raw_t_return.copy() # 用于算ic等未来t期收益率用
        return symbol

    def run(self) -> dict:
        # 识别return_col
        return_col = 'ret_open' if TRADE_ON_OPEN else 'ret'
        metric_selected = L1_STEP1_ALGO  # 'sr' - select by sharp; 'sic' - select by sic
        ''' 挖掘 分析 因子值入库 '''
        task_uids = {}
        for a_df in self.dfs: # NOTE: 不做多进程
            # 用 _inner_symbols 确定当前品种周期标识
            symbol_period = str(a_df['symbol'].values[0]) # eg. '510050.SH_15'
            symbol = symbol_period # 是个懒惰行为
            
            # 1. 应用品种专属配置,并获取原始参数(如果有修改)
            orig_params = self._apply_pair_config(symbol_period)
            # 如果日期范围有变化,需要重新截取数据 NOTE: 不截取会影响base数据和因子值索引对不上
            if orig_params and (orig_params['start_date'] != self.task.start_date or 
                               orig_params['end_date'] != self.task.end_date):
                a_df = a_df.loc[self.task.start_date:self.task.end_date].copy()
            
            # 2. 数据验证 - expand_features已经处理过数据，这里只需验证
            assert len(self.X_dfs[symbol]) == len(self.y_norm_dfs[symbol])
            assert len(self.y_norm_dfs[symbol]) == len(self.y_raw_dfs[symbol])
            assert len(self.y_norm_dfs[symbol]) == len(self.y_raw_t_return_dfs[symbol])
            
            # 5. 根据 split_perc 切分数据并提取训练集；增加排除特征处理
            split_idx = int(len(a_df) * self.task.split_perc)
            X_train_df = self.X_dfs[symbol].iloc[:split_idx].copy()
            y_norm_train_df = self.y_norm_dfs[symbol].iloc[:split_idx].copy()
            y_raw_train_df = self.y_raw_dfs[symbol].iloc[:split_idx].copy()
            y_raw_t_return_train_df = self.y_raw_t_return_dfs[symbol].iloc[:split_idx].copy()
            if hasattr(self.task, 'excluded_features') and symbol_period in self.task.excluded_features:
                excluded = self.task.excluded_features[symbol_period]
                X_train_df = X_train_df.drop(columns=excluded, errors='ignore')
                base_features = [f for f in self.base_features if f not in excluded]
            else:
                base_features = self.base_features
                
            # 6. 初始化矿工对象，传入当前品种及更新后的配置
            miner = FctsGPMiner(symbol, X_train_df, y_norm_train_df, y_raw_train_df, self.task, base_features)
            

            # 7. 初始化任务管理、因子及因子值存储对象
            task_mgr = TaskDataMgr(db_path=self.db_path)
            # 任务唯一编号确立, 同一套数据视为同一个任务uid, 新任务自动建档
            task_uid = task_mgr.get_available_uuid(symbol, self.task.start_date,
                                                   self.task.end_date, self.task.split_perc,
                                                   self.task.fee_rate)
            self.log(f"==== 基于任务号: {task_uid} ==== ...")
            
            run_mgr = RunDataMgr(db_path=self.db_path)
            f_mgr = FactorDataMgr(db_path=self.db_path, table_name=f"{symbol}_{task_uid}_f")
            pj_mgr = FactorDataMgr(db_path=self.db_path, table_name=f"{symbol}_{task_uid}_pj")
            factor_value_mgr = FactorValueMgr(self.fctval_base_dir, max_columns=self.fctval_file_max_cols)
            df = a_df.dropna(axis=0, how='any').copy() # 删除缺失值的行(axis=0)
            df = df[['open','close']].copy()
            df['ret'] = df['close'].shift(-1) / df['close'] - 1  # ret不是label,不考虑T期
            df['ret_open'] = df['open'].shift(-1) / df['open'] - 1
            # NOTE: 此处基础数据是非norm的! pure ret
            factor_value_mgr.add_base_data(task_uid, df) # 先基础数据入因子值数据库
            factor_value_mgr.add_base_data(task_uid+'_pj',df) # 先基础数据入因子值数据库 for pj
            rm = ResMonitor()
            # 4. 每套数据运行多少次gp NOTE 需要运行前设置
            a_df_runs = GP_LOOP #60000 # 6000 for weekend.
            for i in range(a_df_runs): # NOTE: gp自带多进程处理，运行前要改gp的stat为None
                # run_uid 存文件时的全局唯一号, 每套数据run一次gp的uid
                run_uid = self.gen_ordered_uid()
                rm.start_timer_cpu(run_uid)
                rm.start_timer('total')
                run_mgr.add_data(RunData(run_uid, task_uid, symbol, len(self.task.feature_names), len(self.task.func_set)))
                run_mgr.set_bgn_run(run_uid)
                ''' ============================= gplearn因子挖掘命令 =============================== '''
                rm.start_timer('gp')
                f0 = miner.mine(g_uid=run_uid) # 初筛,返回因子list, 同时保存入csv
                run_mgr.update_field(run_uid, 'gp_cost', rm.end_timer('gp'))
                ''' ============================= 自动化因子筛选流程 =============================== '''
                rm.start_timer('s1')
                # NOTE: load训练集!!!保持csv的也是训练集, 因此从文件load(训练集)和从数据库load(切片) 拼接起来是对齐的
                a_fcts_df = du.load_a_symbol_fcts(symbol, X_train_df, fcts=f0) # 除ret和ret_open,都norm, 不含label_
                # 样本内sr: 60min以下>0.8, 0.2-0.8(pj); 60min以上>0.4; 宏观0.15,0.2的比较稳定
                # y_raw_train_df 已在前面判断过是否为TRADE_ON_OPEN
                if metric_selected == 'sr':
                    f1_df,pj_df = miner.select_by_sharp(a_fcts_df, y_raw_train_df, sr_threshold = 0.8, 
                                                pjsr_threshold = 0.2, g_uid=run_uid) # 支持fcts_df中的外部因子
                elif metric_selected == 'sic':
                    f1_df,pj_df = FctsGPMiner.select_by_rolling_rankic(a_fcts_df, 
                                                                y_norm_train_df, # LABEL
                                                                y_raw_t_return_train_df,
                                                                window = 2000, 
                                                                min_period = 1000, 
                                                                top_pct=0.2, pj_pct=0.2, 
                                                                symbol=symbol, g_uid=run_uid)
                f1 = f1_df.index.tolist()
                pj = pj_df.index.tolist()
                run_mgr.update_field(run_uid, 's1_cost', rm.end_timer('s1'))
                
                rm.start_timer('s2')
                f1 = FctsGPMiner.select_by_corr(a_fcts_df[[*f1, 'ret_open' if TRADE_ON_OPEN else 'ret']],
                                                corr_threshold = 0.3, g_uid=None)#run_uid+'_f') # 2筛,返回因子list -- 换成静态方法
                pj = FctsGPMiner.select_by_corr(a_fcts_df[[*pj, 'ret_open' if TRADE_ON_OPEN else 'ret']],
                                                corr_threshold = 0.3, g_uid=None)#run_uid+'_pj') # 2筛,返回因子list -- 换成静态方法
                run_mgr.update_field(run_uid, 's2_cost', rm.end_timer('s2'))

                rm.start_timer('s3')
                f1 = miner.select_by_skew(a_fcts_df[f1],skew_threshold=0.5, g_uid=None)#run_uid+'_f')
                pj = miner.select_by_skew(a_fcts_df[pj],skew_threshold=0.5, g_uid=None)#run_uid+'_pj')
                run_mgr.update_field(run_uid, 's3_cost', rm.end_timer('s3'))

                rm.start_timer('s4')
                f1 = miner.select_by_kurt(a_fcts_df[f1],kurt_threshold=5, g_uid=run_uid+'_f')
                pj = miner.select_by_kurt(a_fcts_df[pj],kurt_threshold=5, g_uid=run_uid+'_pj')
                run_mgr.update_field(run_uid, 's4_cost', rm.end_timer('s4'))
                ''' =============================  因子筛选结果保存  =============================== '''
                rm.start_timer('save')
                
                f1 = f1 if f1 is not None else []
                pj = pj if pj is not None else []
                if (len(f1) == 0) and (len(pj) == 0):
                    self.log('{}_{}: 没筛出因子...'.format(symbol, datetime.now().strftime('%y%m%d_%H')))
                    # 计时结束
                    run_mgr.update_field(run_uid, 'save_cost', rm.end_timer('save'))
                    run_mgr.update_field(run_uid, 'total_cost', rm.end_timer('total'))
                    run_mgr.update_field(run_uid, 'cpu_usage', rm.end_timer_cpu(run_uid))
                    run_mgr.update_field(run_uid, 'memory_usage', rm.log_memory_usage())
                    run_mgr.set_end_run(run_uid)
                    continue

                a_fcts_final_df = du.load_a_symbol_fcts(symbol, self.X_dfs[symbol], fcts=f1+pj) # 结果都是norm过的; 得到全集数据; 除ret和ret_open外都normed, 不含label_
                
                if len(f1) > 0: # 有f类因子
                    self.best_fcts[symbol] = f1        
                    # 批量保存 因子和pj因子到各自数据表
                    f_data_list = [FactorData(expr=e, task_uid=task_uid, 
                                                    run_uid=run_uid, metric=f1_df.loc[e, metric_selected]) for e in f1]
                    f1 = f_mgr.add_factor_list(f_data_list)  # 因子库 NOTE: 升级了函数返回去重后的因子列表
                    # 因子值保存到parquet文件
                    if len(f1) > 0: # 间接保存去重后的因子数值
                        factor_value_mgr.add_factor_values(task_uid, a_fcts_final_df[f1])
                        self.log(f'{symbol}_{datetime.now().strftime("%y%m%d_%H")}: 去重后得{len(f1)}个f类因子...')
                    else:
                        self.log(f'{symbol}_{datetime.now().strftime("%y%m%d_%H")}: 本轮f类因子全部重复...')
                else:
                    self.log('{}_{}: 没筛选出f类因子...'.format(symbol, datetime.now().strftime('%y%m%d_%H')))
                    
                if len(pj) > 0: # 有pj类因子
                    self.log('{}_{}: 筛选出{}个pj类因子...'.format(symbol, datetime.now().strftime('%y%m%d_%H'), len(pj)))             
                    self.pj_fcts[symbol] = pj
                    # 进一步缩减pj数量(磁盘空间优化): 随机森林输出importance, 前10%留为这for的pj
                    fcteval_pj_in = FactorsEvaluator(a_fcts_final_df[[return_col]+pj].iloc[:split_idx].copy(), prices=None)
                    sorted_idx, feature_importances = fcteval_pj_in.random_forest()
                    sorted_fctnames = [fcteval_pj_in.fctnames[i] for i in sorted_idx]
                    top_10_percent = math.ceil(0.1 * len(sorted_fctnames)) # 前10%
                    pj = sorted_fctnames[:top_10_percent]
                    if len(pj) > 0: # evaluate the top 10%
                        self.log('{}_{}: 10%重要的留{}个pj类因子...'.format(symbol, datetime.now().strftime('%y%m%d_%H'), len(pj)))             
                        self.pj_fcts[symbol] = pj
                        # 批量保存 因子和pj因子到各自数据表
                        pj_data_list = [FactorData(expr=e, task_uid=task_uid, 
                                                        run_uid=run_uid, metric=pj_df.loc[e, metric_selected]) for e in pj]
                        pj = pj_mgr.add_factor_list(pj_data_list) # pj因子库 NOTE: 升级函数返回去重后的因子列表
                        # 因子值保存到parquet文件
                        if len(pj) > 0: # 间接确保了每次入parquet的列不重复
                            factor_value_mgr.add_factor_values(task_uid+'_pj',a_fcts_final_df[pj])
                            self.log('{}_{}: 去重后得{}个pj类因子...'.format(symbol, datetime.now().strftime('%y%m%d_%H'), len(pj)))
                        else:
                            self.log(f'{symbol}_{datetime.now().strftime("%y%m%d_%H")}: 本轮pj类因子全部重复...')
                else:
                    self.log(f'{symbol}_{datetime.now().strftime("%y%m%d_%H")}: 没筛选出pj类因子...')
                run_mgr.update_field(run_uid, 'save_cost', rm.end_timer('save'))
                run_mgr.update_field(run_uid, 'total_cost', rm.end_timer('total'))
                run_mgr.update_field(run_uid, 'cpu_usage', rm.end_timer_cpu(run_uid))
                run_mgr.update_field(run_uid, 'memory_usage', rm.log_memory_usage())
                run_mgr.set_end_run(run_uid)
            
            # 8. 如果有 pair 专属配置，恢复原全局设置
            self._restore_global_config(orig_params)
            task_uids[symbol] = task_uid
            
        return task_uids # 返回所有品种的task_uid

    def L2corr(self, task_uids: dict, infunc_id: str = ''):
        self.log("读因子值 + 全部因子L2corr筛选(训练集) + 入L2corr库 + 图形保存: start... ")
        for symbol, task_uid in task_uids.items():
            # 1. 应用品种专属配置
            orig_params = self._apply_pair_config(symbol)
            # ========= 运行时监控信息统计
            rm = ResMonitor()
            opt_uid = self.gen_ordered_uid()
            rm.start_timer_cpu(opt_uid)
            rm.start_timer('total')
            opt_run_mgr = OptRunDataManager(db_path=self.db_path) 
            opt_run_mgr.add_data(OptRunData(opt_uid, task_uid, symbol))
            opt_run_mgr.set_bgn_run(opt_uid)
            
            return_col = 'ret_open' if TRADE_ON_OPEN else 'ret'
            ''' ========================== 读因子库: 从数据表中 =========================== ''' 
            rm.start_timer('load') # ========= 运行时监控信息统计
            if infunc_id != '':
                infunc_id = '_' + infunc_id
            f_mgr = FactorDataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_f'+infunc_id)
            pj_mgr = FactorDataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_pj'+infunc_id)
            f_list = f_mgr.load_all_factors()
            pj_list = pj_mgr.load_all_factors()
            if len(f_list) != len(set(f_list)) or len(pj_list) != len(set(pj_list)):
                self.log('因子数据表中有重复数据.. 待检查..')
                return
            ''' ========================== 读库组成因子值数据大宽表: 从因子值数据文件中 =========================== '''
            fctval_mgr = FactorValueMgr(self.fctval_base_dir, self.fctval_file_max_cols)
            f_df = fctval_mgr.load_factor_values_by_cols(task_uid, [return_col] + f_list)
            pj_df = fctval_mgr.load_factor_values_by_cols(task_uid+'_pj', [return_col] + pj_list)
            split_idx = int(len(f_df)*self.task.split_perc)
            f_df = f_df.iloc[:split_idx].copy()
            pj_df = pj_df.iloc[:split_idx].copy()
            opt_run_mgr.update_field(opt_uid, 'cost_load', rm.end_timer('load'))   # ========= 运行时监控信息统计  
            ''' ====================== L2筛选: corr =========================== '''
            rm.start_timer('opt') # ========= 运行时监控信息统计 ========
            
            f = FctsGPMiner.select_by_corr(f_df, corr_threshold=0.3, g_uid=None)
            pj = FctsGPMiner.select_by_corr(pj_df, corr_threshold=0.3, g_uid=None)
            f = sorted(f)
            pj = sorted(pj)
            # ========= 运行时监控信息统计 ========
            opt_method = 'L2corr'
            opt_params = 'corr_threshold=0.3' # 优化算法参数 + 装配模型参数
            opt_run_mgr.update_field(opt_uid, 'opt_method', opt_method)
            opt_run_mgr.update_field(opt_uid, 'opt_params', opt_params)
            opt_run_mgr.update_field(opt_uid, 'factor_size', f_df.shape[1]+pj_df.shape[1] - 2) # 全部corr的
            opt_run_mgr.update_field(opt_uid, 'cost_opt', rm.end_timer('opt'))
            ''' ====================== 入L2库 =========================== '''
            rm.start_timer('save') # ========= 运行时监控信息统计 ========
            f_L2_mgr = FactorL2DataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_f_L2corr')
            pj_L2_mgr = FactorL2DataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_pj_L2corr')
            f_data_list = [
                            FactorL2Data(expr=e, task_uid=task_uid, opt_uid=opt_uid, scope='in')
                            for e in f
                            # for s in ['in','out', 'all']
                          ]
            f_L2_mgr.add_factor_list(f_data_list)  # 因子库
            pj_data_list = [
                            FactorL2Data(expr=e, task_uid=task_uid, opt_uid=opt_uid, scope='in')
                            for e in pj
                            # for s in ['in','out', 'all']
                          ]
            pj_L2_mgr.add_factor_list(pj_data_list) # pj因子库
            ''' ====================== 因子值图形化保存 =========================== '''
            # 观察后除去: 1.跳变的(400采样点); 2.分布怪异的(全量数据)
            save_name = symbol+'_'+task_uid+'_L2corr_f'
            save_path = Path(self.fctpic_base_dir).joinpath(save_name)
            self.modeler.show_fctval(f_df[f+[return_col]], compr_col=[return_col], 
                                compr_show_cum = True, save_path=str(save_path))
            save_name = symbol+'_'+task_uid+'_L2corr_pj'
            save_path = Path(self.fctpic_base_dir).joinpath(save_name)
            self.modeler.show_fctval(pj_df[pj+[return_col]],compr_col=[return_col], 
                                compr_show_cum = True, save_path=str(save_path))
            # ========= 运行时监控信息统计 ========
            opt_run_mgr.update_field(opt_uid, 'cost_save', rm.end_timer('save'))
            opt_run_mgr.update_field(opt_uid, 'cost_total', rm.end_timer('total'))
            opt_run_mgr.update_field(opt_uid, 'cpu_usage', rm.end_timer_cpu(opt_uid))
            opt_run_mgr.update_field(opt_uid, 'memory_usage', rm.log_memory_usage())
            opt_run_mgr.set_end_run(opt_uid)
            # 在循环结束前恢复配置
            self._restore_global_config(orig_params)
            
        ''' ====================== 停下来逐一看看单因子图表:手工删除跳阶的 =========================== '''
        self.log("读因子值 + 全部因子L2corr筛选(训练集) + 入L2corr库 + 图形保存: complete... ")
        while True:
            staircase_factors = input("1.查看因子值图 2.在L2库中删除跳阶因子 3.保存数据库 4.输入'CONTINUE'继续: ")
            if staircase_factors.lower() == 'continue':
                break
        return
    
    def L2Lasso(self, task_uids: dict, infunc_id: str):
        self.log("读库 + 读因子值 + 全部因子L2LassoCV筛选(训练集) + 入L2Lasso库 + 图形保存: start... ")
        for symbol, task_uid in task_uids.items():
            # 1. 应用品种专属配置
            orig_params = self._apply_pair_config(symbol)
            # ========= 运行时监控信息统计
            rm = ResMonitor()
            opt_uid = self.gen_ordered_uid()
            rm.start_timer_cpu(opt_uid)
            rm.start_timer('total')
            opt_run_mgr = OptRunDataManager(db_path=self.db_path) 
            opt_run_mgr.add_data(OptRunData(opt_uid, task_uid, symbol))
            opt_run_mgr.set_bgn_run(opt_uid)
            
            return_col = 'ret_open' if TRADE_ON_OPEN else 'ret'
            ''' ========================== 读L2因子库 =========================== ''' 
            rm.start_timer('load') # ========= 运行时监控信息统计
            f_L2_mgr = FactorL2DataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_f_'+infunc_id)
            pj_L2_mgr = FactorL2DataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_pj_'+infunc_id)
            f_L2_list = f_L2_mgr.load_all_factors(scope='in')
            pj_L2_list = pj_L2_mgr.load_all_factors(scope='in')
            if len(f_L2_list) != len(set(f_L2_list)) or len(pj_L2_list) != len(set(pj_L2_list)):
                self.log('L2因子库中有重复数据.. 待检查..')
                return
            ''' ========================== 读库组成因子值数据大宽表 ==================== '''
            fctval_mgr = FactorValueMgr(self.fctval_base_dir,self.fctval_file_max_cols)
            f_L2_df = fctval_mgr.load_factor_values_by_cols(task_uid, [return_col] + f_L2_list)
            pj_L2_df = fctval_mgr.load_factor_values_by_cols(task_uid+'_pj', [return_col] + pj_L2_list)
            split_idx = int(len(f_L2_df)*self.task.split_perc)
            f_L2_df = f_L2_df.iloc[:split_idx].copy()
            pj_L2_df = pj_L2_df.iloc[:split_idx].copy()
            opt_run_mgr.update_field(opt_uid, 'cost_load', rm.end_timer('load')) # ========= 运行时监控信息统计
            ''' ====================== L2筛选: lassoCV =============================== '''
            def lasso_cv_selection(df, return_col: str='ret'):
                X = df.drop(columns=[return_col]).values
                y = df[return_col].values
                X = np.nan_to_num(X)  # 将X中的空值替换为0
                y = np.nan_to_num(y)  # 将y中的空值替换为0
                lasso = LassoCV(
                    cv=5,                # 交叉验证折数，推荐值为 5
                    random_state=0,      # 随机状态，确保结果可复现
                    max_iter=100000,      # 最大迭代次数，推荐值为 10000
                    tol=1e-4,            # 收敛容忍度，推荐值为 1e-4
                    n_jobs=-1,           # 使用所有可用处理器并行计算
                    selection='cyclic'   # 选择坐标轴下降法类型，推荐 'cyclic'
                )
                lasso.fit(X, y)
                # 筛选非零系数对应的因子
                selected_features = df.drop(columns=[return_col]).columns[lasso.coef_!=0]
                self.log(f"lasso.alpha_: {lasso.alpha_}")
                self.log(f"len(lasso.coef_): {len(selected_features)}")
                self.log(f"lasso.intercept_: {lasso.intercept_}")
                self.log(f"lasso.n_iter_: {lasso.n_iter_}")
                return selected_features.tolist()
            
            rm.start_timer('opt') # ========= 运行时监控信息统计
            
            f = lasso_cv_selection(f_L2_df,return_col)
            pj = lasso_cv_selection(pj_L2_df,return_col)
            f = sorted(f)
            pj = sorted(pj)
            
            # ========= 运行时监控信息统计
            opt_method = 'L2LassoCV'
            opt_params = 'cv=5, iter=100000, tol=1e-4' # 优化算法参数 + 装配模型参数
            opt_run_mgr.update_field(opt_uid, 'opt_method', opt_method)
            opt_run_mgr.update_field(opt_uid, 'opt_params', opt_params)
            opt_run_mgr.update_field(opt_uid, 'factor_size', f_L2_df.shape[1]+pj_L2_df.shape[1]) # 全部因子
            opt_run_mgr.update_field(opt_uid, 'cost_opt', rm.end_timer('opt'))
            ''' ====================== 入L2库 =========================== '''
            rm.start_timer('save') # ========= 运行时监控信息统计
            f_L2_mgr = FactorL2DataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_f_L2Lasso')
            pj_L2_mgr = FactorL2DataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_pj_L2Lasso')
            f_data_list = [
                            FactorL2Data(expr=e, task_uid=task_uid, opt_uid=opt_uid, scope='in')
                            for e in f
                            # for s in ['in','out', 'all']
                          ]
            f_L2_mgr.add_factor_list(f_data_list)  # 因子库
            pj_data_list = [
                            FactorL2Data(expr=e, task_uid=task_uid, opt_uid=opt_uid, scope='in')
                            for e in pj
                            # for s in ['in','out', 'all']
                          ]
            pj_L2_mgr.add_factor_list(pj_data_list) # pj因子库
            ''' ====================== 因子值图形化保存 =========================== '''
            # 观察后除去: 1.跳变的(400采样点); 2.分布怪异的(全量数据)
            save_name = symbol+'_'+task_uid+'_L2lasso_f'
            save_path = Path(self.fctpic_base_dir).joinpath(save_name)
            self.modeler.show_fctval(f_L2_df[f+[return_col]], compr_col=[return_col], 
                                compr_show_cum = True, save_path=str(save_path))
            save_name = symbol+'_'+task_uid+'_L2lasso_pj'
            save_path = Path(self.fctpic_base_dir).joinpath(save_name)
            self.modeler.show_fctval(pj_L2_df[pj+[return_col]],compr_col=[return_col], 
                                compr_show_cum = True, save_path=str(save_path))
            # ========= 运行时监控信息统计
            opt_run_mgr.update_field(opt_uid, 'cost_save', rm.end_timer('save'))
            opt_run_mgr.update_field(opt_uid, 'cost_total', rm.end_timer('total'))
            opt_run_mgr.update_field(opt_uid, 'cpu_usage', rm.end_timer_cpu(opt_uid))
            opt_run_mgr.update_field(opt_uid, 'memory_usage', rm.log_memory_usage())
            opt_run_mgr.set_end_run(opt_uid)
            # 恢复全局配置
            self._restore_global_config(orig_params)
            
        ''' ====================== 停下来逐一看看单因子图表:手工删除跳阶的 =========================== '''
        self.log(" 读库 + 读因子值 + 全部因子L2LassoCV筛选(训练集) + 入L2Lasso库 + 图形保存: complete... ")
        while True:
            staircase_factors = input("LassoCV: 1.查看因子值hist/eq图 2.在L2库中删除异常因子 3.保存数据库 4.输入'CONTINUE'继续: ")
            if staircase_factors.lower() == 'continue':
                break
        return             
    
    def L2dive(self, task_uids: dict, infunc_id: str):
        self.log('L2因子库单因子深度分析(训练集): deepdive start...')
        for symbol, task_uid in task_uids.items():
            # ====== task应用pair个性化参数
            orig_params = self._apply_pair_config(symbol)
            ''' ========================== 本轮监控初始化 =========================== ''' 
            rm = ResMonitor()
            opt_uid = self.gen_ordered_uid()
            rm.start_timer_cpu(opt_uid)
            rm.start_timer('total')
            opt_run_mgr = OptRunDataManager(db_path=self.db_path) 
            opt_run_mgr.add_data(OptRunData(opt_uid, task_uid, symbol))
            opt_run_mgr.set_bgn_run(opt_uid)
            ''' ========================== 本轮环境初始化 =========================== '''
            return_col = 'ret_open' if TRADE_ON_OPEN else 'ret'
            ''' ========================== 读L2因子库 =========================== ''' 
            rm.start_timer('load')  # >> monitor setting...      
            f_L2_mgr = FactorL2DataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_f_'+infunc_id)
            pj_L2_mgr = FactorL2DataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_pj_'+infunc_id)
            # load指定轮次的结果因子list
            f_L2_list = f_L2_mgr.load_all_factors(scope='in')
            pj_L2_list = pj_L2_mgr.load_all_factors(scope='in')
                
            if len(f_L2_list) != len(set(f_L2_list)) or len(pj_L2_list) != len(set(pj_L2_list)):
                self.log('L2因子库中有重复数据.. 待检查..')
                return
            ''' ==================== 读库组成因子值数据大宽表 ====================== '''
            # 1. 大宽表: [date(index) ret fct1 fct2 ...] 
            # ----- datetime64[us] 如'2005-02-23 09:45:00'
            fctval_mgr = FactorValueMgr(self.fctval_base_dir,self.fctval_file_max_cols)
            f_L2_df = fctval_mgr.load_factor_values_by_cols(task_uid, [return_col] + f_L2_list)
            # [date] ret_col fctval1 fctval2 ...
            pj_L2_df = fctval_mgr.load_factor_values_by_cols(task_uid+'_pj', [return_col] + pj_L2_list)
            # 2. 切分数据集
            split_idx = int(len(f_L2_df)*self.task.split_perc)
            f_L2_df_in = f_L2_df.iloc[:split_idx].copy() # 样本内
            # NOTE: 仅对f进行分箱中心归零且用在样本内IN
            f_L2_df_bin0_in = du.zero_by_bins(f_L2_df_in.iloc[:, 1:], n_bins=20, zero_bins=4, algo_type=0) # 中心归零 百分比分箱法
            f_L2_df_bin1_in = du.zero_by_bins(f_L2_df_in.iloc[:, 1:], n_bins=20, zero_bins=4, algo_type=1) # 中心归零 等距分箱法
            f_L2_df_bin0_in = pd.concat([f_L2_df_in[[return_col]], f_L2_df_bin0_in], axis=1)
            f_L2_df_bin1_in = pd.concat([f_L2_df_in[[return_col]], f_L2_df_bin1_in], axis=1)
            # 
            pj_L2_df_in = pj_L2_df.iloc[:split_idx].copy()
            f_L2_df_out = f_L2_df.iloc[split_idx:].copy()
            pj_L2_df_out = pj_L2_df.iloc[split_idx:].copy()
            opt_run_mgr.update_field(opt_uid, 'cost_load', rm.end_timer('load')) # >> monitor setting... 
            ''' ====================== 单因子绩效指标计算 =========================== '''
            # ========= 运行时监控信息统计 ========
            rm.start_timer('opt')
            # ========= 绩效参数配置提取 ========
            day_bars, ann_days, fixed_return, fee_rate, weights, is_ret_open = METRIC_PARAMS_DEFAULT[symbol]
            ''' ============================ 1. f系列单因子样本内wfa ============================= '''
            # f系列样本内wfa: 原始因子值数据 ========
            f_ret_df_in, f_cost_df_in = self.modeler.wfa_ensemble_as_single(f_L2_df_in,
                                                                    step_bars=1000, 
                                                                    fee_rate=fee_rate,
                                                                    is_ret_open=is_ret_open)
            df_ratios_f_in = calc_stats_RC(f_ret_df_in, f_cost_df_in, day_bars, ann_days, fixed_return) # 计算组合的绩效指标
            df_ratios_f_in = stats_saw_score(df_ratios_f_in)
            df_ratios_f_in = stats_topsis_score(df_ratios_f_in, col_prefix='')
            # f系列样本内wfa: 分箱中心归零(等频) ========
            f_ret_df_bin0_in, f_cost_df_bin0_in = self.modeler.wfa_ensemble_as_single(f_L2_df_bin0_in,
                                                                    step_bars=1000, 
                                                                    fee_rate=fee_rate,
                                                                    is_ret_open=is_ret_open)
            df_ratios_f_bin0_in = calc_stats_RC(f_ret_df_bin0_in, f_cost_df_bin0_in, 
                                                day_bars, ann_days, fixed_return) # 计算组合的绩效指标
            df_ratios_f_bin0_in = stats_saw_score(df_ratios_f_bin0_in)
            df_ratios_f_bin0_in = stats_topsis_score(df_ratios_f_bin0_in, col_prefix='')
            # f系列样本内wfa: 分箱中心归零(等距) ========
            f_ret_df_bin1_in, f_cost_df_bin1_in = self.modeler.wfa_ensemble_as_single(f_L2_df_bin1_in,
                                                                    step_bars=1000, 
                                                                    fee_rate=fee_rate,
                                                                    is_ret_open=is_ret_open)
            df_ratios_f_bin1_in = calc_stats_RC(f_ret_df_bin1_in, f_cost_df_bin1_in, 
                                                day_bars, ann_days, fixed_return) # 计算组合的绩效指标
            df_ratios_f_bin1_in = stats_saw_score(df_ratios_f_bin1_in)
            df_ratios_f_bin1_in = stats_topsis_score(df_ratios_f_bin1_in, col_prefix='')
            # f系列样本内wfa: 原始y_hat数据图形化 ========
            # 观察后除去: 1.跳变的(400采样点); 2.分布怪异的(全量数据)
            save_name = symbol+'_'+task_uid+'_L2dive_inwfa_yhat_f'
            save_path = Path(self.fctpic_base_dir).joinpath(save_name)
            self.modeler.show_yhat_as_single_normed(f_L2_df_in[return_col], compr_col=[return_col], 
                                compr_show_cum = True, save_path = str(save_path)) # 当前yhat保存在类的属性变量y_hat_as_single_normed里
            # f系列样本内wfa: 分箱中心归零(等频) y_hat数据图形化 ========
            save_name = symbol+'_'+task_uid+'_L2dive_inwfa_yhat_f_bin0'
            save_path = Path(self.fctpic_base_dir).joinpath(save_name)
            self.modeler.show_yhat_as_single_normed(f_L2_df_bin0_in[return_col], compr_col=[return_col], 
                                compr_show_cum = True, save_path = str(save_path)) # 当前yhat保存在类的属性变量y_hat_as_single_normed里
             # f系列样本内wfa: 分箱中心归零(等距) y_hat数据图形化 ========
            save_name = symbol+'_'+task_uid+'_L2dive_inwfa_yhat_f_bin1'
            save_path = Path(self.fctpic_base_dir).joinpath(save_name)
            self.modeler.show_yhat_as_single_normed(f_L2_df_bin1_in[return_col], compr_col=[return_col], 
                                compr_show_cum = True, save_path = str(save_path)) # 当前yhat保存在类的属性变量y_hat_as_single_normed里
            ''' ============================ 2. f系列单因子样本外pure predict ============================= '''
            f_ret_df_out, f_cost_df_out = self.modeler.pure_predict_as_single(f_L2_df_out,
                                                                    fee_rate=fee_rate,
                                                                    is_ret_open=is_ret_open)
            df_ratios_f_out = calc_stats_RC(f_ret_df_out, f_cost_df_out, day_bars, ann_days, fixed_return)
            df_ratios_f_out = stats_saw_score(df_ratios_f_out)
            df_ratios_f_out = stats_topsis_score(df_ratios_f_out, col_prefix='')
            # ========= 当前单因子yhat矩阵图形化: eq + hist
            # 观察后除去: 1.跳变的(400采样点); 2.分布怪异的(全量数据)
            save_name = symbol+'_'+task_uid+'_L2dive_outpure_yhat_f'
            save_path = Path(self.fctpic_base_dir).joinpath(save_name)
            self.modeler.show_yhat_as_single_normed(f_L2_df_out[return_col], compr_col=[return_col], 
                                compr_show_cum = True, save_path = str(save_path)) # 当前yhat保存在类的属性变量y_hat_as_single_normed里
            # ========= 计算f: 拼接成总体 ========
            f_ret_df_all = pd.concat([f_ret_df_in,f_ret_df_out], axis=0)
            f_cost_df_all = pd.concat([f_cost_df_in,f_cost_df_out], axis=0)
            df_ratios_f_all = calc_stats_RC(f_ret_df_all, f_cost_df_all, day_bars, ann_days, fixed_return)
            df_ratios_f_all = stats_saw_score(df_ratios_f_all)
            df_ratios_f_all = stats_topsis_score(df_ratios_f_all, col_prefix='')
            # 分箱中心归零: 等频 (样本外不变)
            f_ret_df_bin0_all = pd.concat([f_ret_df_bin0_in,f_ret_df_out], axis=0)
            f_cost_df_bin0_all = pd.concat([f_cost_df_bin0_in,f_cost_df_out], axis=0)
            df_ratios_f_bin0_all = calc_stats_RC(f_ret_df_bin0_all, f_cost_df_bin0_all, day_bars, ann_days, fixed_return)
            df_ratios_f_bin0_all = stats_saw_score(df_ratios_f_bin0_all)
            df_ratios_f_bin0_all = stats_topsis_score(df_ratios_f_bin0_all, col_prefix='')
            # 样本内: 分箱中心归零: 等距 (样本外不变)
            f_ret_df_bin1_all = pd.concat([f_ret_df_bin1_in,f_ret_df_out], axis=0)
            f_cost_df_bin1_all = pd.concat([f_cost_df_bin1_in,f_cost_df_out], axis=0)
            df_ratios_f_bin1_all = calc_stats_RC(f_ret_df_bin1_all, f_cost_df_bin1_all, day_bars, ann_days, fixed_return)
            df_ratios_f_bin1_all = stats_saw_score(df_ratios_f_bin1_all)
            df_ratios_f_bin1_all = stats_topsis_score(df_ratios_f_bin1_all, col_prefix='')
            ''' ============================ 3. pj系列单因子样本内wfa ============================= '''
            pj_ret_df_in, pj_cost_df_in = self.modeler.wfa_ensemble_as_single(pj_L2_df_in, 
                                                                    step_bars=1000, 
                                                                    fee_rate=fee_rate,
                                                                    is_ret_open=is_ret_open)
            df_ratios_pj_in = calc_stats_RC(pj_ret_df_in, pj_cost_df_in, day_bars, ann_days, fixed_return) # 计算组合的绩效指标
            df_ratios_pj_in = stats_saw_score(df_ratios_pj_in) 
            df_ratios_pj_in = stats_topsis_score(df_ratios_pj_in, col_prefix='')
            # ========= 当前单因子yhat矩阵图形化: eq + hist
            # 观察后除去: 1.跳变的(400采样点); 2.分布怪异的(全量数据)
            save_name = symbol+'_'+task_uid+'_L2dive_inwfa_yhat_pj'
            save_path = Path(self.fctpic_base_dir).joinpath(save_name)
            self.modeler.show_yhat_as_single_normed(pj_L2_df_in[return_col], compr_col=[return_col], 
                                compr_show_cum = True, save_path = str(save_path)) # 当前yhat保存在类的属性变量y_hat_as_single_normed里
            ''' ============================ 4. pj系列单因子样本外pure predict ============================= '''
            pj_ret_df_out, pj_cost_df_out = self.modeler.pure_predict_as_single(pj_L2_df_out,
                                                                    fee_rate=fee_rate,
                                                                    is_ret_open=is_ret_open)
            df_ratios_pj_out = calc_stats_RC(pj_ret_df_out, pj_cost_df_out, day_bars, ann_days, fixed_return)
            df_ratios_pj_out = stats_saw_score(df_ratios_pj_out)
            df_ratios_pj_out = stats_topsis_score(df_ratios_pj_out, col_prefix='')
            # ========= 当前单因子yhat矩阵图形化: eq + hist
            # 观察后除去: 1.跳变的(400采样点); 2.分布怪异的(全量数据)
            save_name = symbol+'_'+task_uid+'_L2dive_outpure_yhat_pj'
            save_path = Path(self.fctpic_base_dir).joinpath(save_name)
            self.modeler.show_yhat_as_single_normed(pj_L2_df_out[return_col], compr_col=[return_col], 
                                compr_show_cum = True, save_path = str(save_path)) # 当前yhat保存在类的属性变量y_hat_as_single_normed里
            # ========= 计算pj: 拼接成总体 ========
            pj_ret_df_all = pd.concat([pj_ret_df_in,pj_ret_df_out], axis=0)
            pj_cost_df_all = pd.concat([pj_cost_df_in,pj_cost_df_out], axis=0)
            df_ratios_pj_all = calc_stats_RC(pj_ret_df_all, pj_cost_df_all, day_bars, ann_days, fixed_return)
            df_ratios_pj_all = stats_saw_score(df_ratios_pj_all)
            df_ratios_pj_all = stats_topsis_score(df_ratios_pj_all, col_prefix='')
            # ========= 运行时监控信息统计 ========
            opt_method = 'L2dive: wfa + pure + calc_stats'
            opt_params = 'wfa_step: 1000' # 优化算法参数 + 装配模型参数
            opt_run_mgr.update_field(opt_uid, 'opt_method', opt_method)
            opt_run_mgr.update_field(opt_uid, 'opt_params', opt_params)
            opt_run_mgr.update_field(opt_uid, 'factor_size', f_L2_df.shape[1]+pj_L2_df.shape[1]) # 全部参与的因子
            opt_run_mgr.update_field(opt_uid, 'cost_opt', rm.end_timer('opt'))
            ''' ====================== 单因子绩效数据更新到L2 =========================== '''
            rm.start_timer('save')
            # ========= 更新f 绩效数据入L2库: 样本内/样本外/全集 ========
            f_L2_mgr = FactorL2DataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_f_L2dive')
            pj_L2_mgr = FactorL2DataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_pj_L2dive')
            f_L2data_list = [
                FactorL2Data(
                    expr=str(expr), task_uid=task_uid, opt_uid=opt_uid, scope='in',
                    **{str(k): (round(v,3) if isinstance(v, (float, int)) else v) for k, v in row.items()}
                )
                for expr, row in df_ratios_f_in.iterrows()
            ]
            f_L2_mgr.add_factor_list(f_L2data_list) # 写库: 样本内(wfa)
            
            f_L2data_list = [
                FactorL2Data(
                    expr=str(expr), task_uid=task_uid, opt_uid=opt_uid, scope='out',
                    **{str(k): (round(v,3) if isinstance(v, (float, int)) else v) for k, v in row.items()}
                )
                for expr, row in df_ratios_f_out.iterrows()
            ]
            f_L2_mgr.add_factor_list(f_L2data_list) # 写库: 样本外(pure)

            f_L2data_list = [
                FactorL2Data(
                    expr=str(expr), task_uid=task_uid, opt_uid=opt_uid, scope='all',
                    **{str(k): (round(v,3) if isinstance(v, (float, int)) else v) for k, v in row.items()}
                )
                for expr, row in df_ratios_f_all.iterrows()
            ]
            f_L2_mgr.add_factor_list(f_L2data_list) # 写库: 全集合        
            # ========= 更新pj 绩效数据入L2库: 样本内/样本外/全集 ========
            pj_L2data_list = [
                FactorL2Data(
                    expr=str(expr), task_uid=task_uid, opt_uid=opt_uid, scope='in',
                    **{str(k): (round(v,3) if isinstance(v, (float, int)) else v) for k, v in row.items()}
                )
                for expr, row in df_ratios_pj_in.iterrows()
            ]
            pj_L2_mgr.add_factor_list(pj_L2data_list) # 写库: 样本内(wfa)
            
            pj_L2data_list = [
                FactorL2Data(
                    expr=str(expr), task_uid=task_uid, opt_uid=opt_uid, scope='out',
                    **{str(k): (round(v,3) if isinstance(v, (float, int)) else v) for k, v in row.items()}
                )
                for expr, row in df_ratios_pj_out.iterrows()
            ]
            pj_L2_mgr.add_factor_list(pj_L2data_list) # 写库: 样本外(pure)
            
            pj_L2data_list = [
                FactorL2Data(
                    expr=str(expr), task_uid=task_uid, opt_uid=opt_uid, scope='all',
                    **{str(k): (round(v,3) if isinstance(v, (float, int)) else v) for k, v in row.items()}
                )
                for expr, row in df_ratios_pj_all.iterrows()
            ]
            pj_L2_mgr.add_factor_list(pj_L2data_list) # 写库: 全集合
            ''' ====================== 单因子绩效图形保存 =========================== '''
            # ========= 初始化: 参数提取, 数据加载 ========
            day_bars = METRIC_PARAMS_DEFAULT[symbol].day_bars
            f_close_df = fctval_mgr.load_factor_values_by_cols(task_uid, ['close'])
            pj_close_df = fctval_mgr.load_factor_values_by_cols(task_uid+'_pj', ['close'])
            assert f_close_df.index.equals(pj_close_df.index)
            assert (f_close_df['close'] == pj_close_df['close']).all()
            # ========= 数据处理: benchmark数据日度化 ========
            f_day_ret_df = to_day_pct_ret(f_ret_df_all,day_bars=day_bars).dropna(how='all')
            f_day_ret_bin0_df = to_day_pct_ret(f_ret_df_bin0_all,day_bars=day_bars).dropna(how='all')
            f_day_ret_bin1_df = to_day_pct_ret(f_ret_df_bin1_all,day_bars=day_bars).dropna(how='all')
            pj_day_ret_df = to_day_pct_ret(pj_ret_df_all,day_bars=day_bars).dropna(how='all')
            
            f_day_cost_df = to_day_pct_ret(f_cost_df_all,day_bars=day_bars).dropna(how='all')
            f_day_cost_bin0_df = to_day_pct_ret(f_cost_df_bin0_all,day_bars=day_bars).dropna(how='all')
            f_day_cost_bin1_df = to_day_pct_ret(f_cost_df_bin1_all,day_bars=day_bars).dropna(how='all')
            pj_day_cost_df = to_day_pct_ret(pj_cost_df_all,day_bars=day_bars).dropna(how='all')
            
            f_close_df_temp = f_close_df.loc[f_ret_df_all.index.min():f_ret_df_all.index.max()] # to_day_close() use
            day_close_df = to_day_close(f_close_df_temp,day_bars=day_bars).dropna(how='all')
            bench_se = day_close_df['close']/day_close_df['close'].shift(1) -1
            boundary_day = f_ret_df_in.index[-1].date()

            save_path = Path(self.fctpic_single_f_dir).joinpath(opt_uid+'_f') # opt_uid:此批因子的批号
            self.modeler.show_fctperf_as_single(f_day_ret_df, f_day_cost_df,
                                                benchmark=bench_se,
                                                df_ratios_in=df_ratios_f_in,
                                                df_ratios_out=df_ratios_f_out,
                                                df_ratios_all=df_ratios_f_all,
                                                boundary_day = boundary_day,
                                                save_path=str(save_path))

            save_path = Path(self.fctpic_single_f_dir).joinpath(opt_uid+'_f_bin0') # opt_uid:此批因子的批号
            self.modeler.show_fctperf_as_single(f_day_ret_bin0_df, f_day_cost_bin0_df,
                                                benchmark=bench_se,
                                                df_ratios_in=df_ratios_f_bin0_in,
                                                df_ratios_out=df_ratios_f_out,
                                                df_ratios_all=df_ratios_f_bin0_all,
                                                boundary_day = boundary_day,
                                                save_path=str(save_path))
            
            save_path = Path(self.fctpic_single_f_dir).joinpath(opt_uid+'_f_bin1') # opt_uid:此批因子的批号
            self.modeler.show_fctperf_as_single(f_day_ret_bin1_df, f_day_cost_bin1_df,
                                                benchmark=bench_se,
                                                df_ratios_in=df_ratios_f_bin1_in,
                                                df_ratios_out=df_ratios_f_out,
                                                df_ratios_all=df_ratios_f_bin1_all,
                                                boundary_day = boundary_day,
                                                save_path=str(save_path))
            
            save_path = Path(self.fctpic_single_pj_dir).joinpath(opt_uid+'_pj') # opt_uid:此批因子的批号
            self.modeler.show_fctperf_as_single(pj_day_ret_df, pj_day_cost_df,
                                                benchmark=bench_se,
                                                df_ratios_in=df_ratios_pj_in,
                                                df_ratios_out=df_ratios_pj_out,
                                                df_ratios_all=df_ratios_pj_all,
                                                boundary_day = boundary_day,
                                                save_path=str(save_path))
            ''' ====================== 单因子值测评结果图形保存(样本内) =========================== '''
            fcteval_f_in = FactorsEvaluator(f_L2_df_in, f_close_df.iloc[:split_idx].copy())
            # 分箱中心归零(only样本内)
            fcteval_f_bin0_in = FactorsEvaluator(f_L2_df_bin0_in, f_close_df.iloc[:split_idx].copy())
            fcteval_f_bin1_in = FactorsEvaluator(f_L2_df_bin1_in, f_close_df.iloc[:split_idx].copy())
            fcteval_pj_in = FactorsEvaluator(pj_L2_df_in, pj_close_df.iloc[:split_idx].copy())
            
            save_path = Path(self.fctpic_single_f_dir).joinpath(opt_uid+'_f') # opt_uid:此批因子的批号
            fcteval_f_in.show_fcteval(save_path=str(save_path))
            
            save_path = Path(self.fctpic_single_f_dir).joinpath(opt_uid+'_f_bin0') # opt_uid:此批因子的批号
            fcteval_f_bin0_in.show_fcteval(save_path=str(save_path))
            
            save_path = Path(self.fctpic_single_f_dir).joinpath(opt_uid+'_f_bin1') # opt_uid:此批因子的批号
            fcteval_f_bin1_in.show_fcteval(save_path=str(save_path))
            
            save_path = Path(self.fctpic_single_pj_dir).joinpath(opt_uid+'_pj') # opt_uid:此批因子的批号
            fcteval_pj_in.show_fcteval(save_path=str(save_path))
            # ========= 运行时监控信息统计 ========
            opt_run_mgr.update_field(opt_uid, 'cost_save', rm.end_timer('save'))
            opt_run_mgr.update_field(opt_uid, 'cost_total', rm.end_timer('total'))
            opt_run_mgr.update_field(opt_uid, 'cpu_usage', rm.end_timer_cpu(opt_uid))
            opt_run_mgr.update_field(opt_uid, 'memory_usage', rm.log_memory_usage())
            opt_run_mgr.set_end_run(opt_uid)
            # ======== task恢复到全局参数配置
            self._restore_global_config(orig_params)
        ''' ====================== 停下来逐一看看单因子的绩效情况 =========================== '''
        self.log('L2因子库单因子深度分析(训练集): deepdive complete...')
        while True:
            staircase_factors = input("deepdive: 1.查看单因子绩效数据和图表 2.在L2库中删除异常因子 3.保存数据库 4.输入'CONTINUE'继续: ")
            if staircase_factors.lower() == 'continue':
                break
        return 
 
    def opt_combo(self, task_uids: dict, from_L2: str):
        ''' 读L2库(当前所有半自动选出的优秀单因子) + 组合优化(入库) '''
        for symbol, task_uid in task_uids.items():
            orig_params = self._apply_pair_config(symbol)
            rm = ResMonitor()
            opt_uid = self.gen_ordered_uid()
            rm.start_timer_cpu(opt_uid)
            rm.start_timer('total')
            opt_run_mgr = OptRunDataManager(db_path=self.db_path) 
            opt_run_mgr.add_data(OptRunData(opt_uid, task_uid, symbol))
            opt_run_mgr.set_bgn_run(opt_uid)
            ''' ========================== 0.本轮环境初始化 =========================== '''
            return_col = 'ret_open' if TRADE_ON_OPEN else 'ret'
            
            def get_scope_data(scope, df_train, df_test, df_all, df_all_r, df_out_r):
                if scope == 'in':
                    return df_train
                elif scope == 'out':
                    return df_test
                elif scope == 'all':
                    return df_all
                elif scope == 'all_r':
                    return df_all_r
                elif scope == 'out_r':
                    return df_out_r
                else:
                    raise ValueError("Invalid scope")
            ''' ========================== 1.1 读L2因子库 =========================== ''' 
            rm.start_timer('load')  # >> monitor setting...      
            f_L2_mgr = FactorL2DataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_f_'+from_L2)
            pj_L2_mgr = FactorL2DataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_pj_'+from_L2)
            # load所有L2的因子(经过半自动筛选后剩余的优秀单因子)
            f_L2_list = f_L2_mgr.load_all_factors(scope='in') # L2库中所有因子提取(可能是多批次选出的)
            pj_L2_list = pj_L2_mgr.load_all_factors(scope='in')
            # 去重/排序   
            f_L2_list = list(sorted(set(f_L2_list)))
            pj_L2_list = list(sorted(set(pj_L2_list)))
            ''' ==================== 1.2 读库组成因子值数据大宽表 ====================== '''
            # 1. 大宽表: [date(index) ret fct1 fct2 ...] 
            # ----- datetime64[us] 如'2005-02-23 09:45:00'
            fctval_mgr = FactorValueMgr(self.fctval_base_dir,self.fctval_file_max_cols)
            f_L2_df = fctval_mgr.load_factor_values_by_cols(task_uid, [return_col] + f_L2_list)
            pj_L2_df = fctval_mgr.load_factor_values_by_cols(task_uid+'_pj', [return_col] + pj_L2_list)
            opt_run_mgr.update_field(opt_uid, 'cost_load', rm.end_timer('load'))
            ''' ======================  2.特征选择(组合优化问题)  =========================== '''
            rm.start_timer('opt')
            # TODO: ta_ad(CORR60, CLOSE3, b_aroon_14, SUMP5) 2次算出来的metric不一样
            ''' ====================== 2.1: 分层随机抽样(不回放)算法 - 适合30左右个因子的各种组合 ===================== '''
            # 分层(1/10), 每层组合数不超过(30: 防过拟合), 跨层交叉组合数不超过(60)
            # combo_generator = XLayerComboGenerator(df=f_lib, num_layers=4, seed=None)
            ''' ====================== 2.2: 遗传算法解决特征选择(最优组合)问题 =========================== '''
            # NOTE: 裁剪训练集
            split_idx = int(len(f_L2_df)*self.task.split_perc)
            big_df_train = f_L2_df[f_L2_list].iloc[:split_idx]
            ret_df_train = f_L2_df[return_col].iloc[:split_idx]
            # 特征选择问题GA求解
            combo_generator = GAComboGenerator(self.modeler)
            combo_mgr = ComboMgr(combo_generator)
            num_runs = 2  # 每个核运行多少次
            all_combos = [] # 用于存储所有运行结果的列表
            # 特征选择算法执行前 记录状态 并打开监控开关
            opt_method = 'DEAP + '+ str(self.modeler.model_strats)
            opt_params = 'METRIC_PARAMS_DEFAULT[{}] + None'.format(symbol) # 优化算法参数 + 装配模型参数
            opt_run_mgr.update_field(opt_uid, 'opt_method', opt_method)
            opt_run_mgr.update_field(opt_uid, 'opt_params', opt_params)
            opt_run_mgr.update_field(opt_uid, 'factor_size', len(f_L2_list))
            # 多进程..                
            with Pool(processes=self.processes) as pool:
                results = pool.starmap(combo_mgr.gen_best_combos, 
                                        [(big_df_train, ret_df_train, METRIC_PARAMS_DEFAULT[symbol]) 
                                            for _ in range(num_runs)]
                                       )
            all_combos = [item for sublist in results for item in sublist]
            unique_combos = []
            seen_combos = set()
            for combo in all_combos:  # 去重（基于expr_combo）
                if tuple(combo['expr_combo']) not in seen_combos:
                    seen_combos.add(tuple(combo['expr_combo']))
                    unique_combos.append(combo)
            sorted_unique_combos = sorted(unique_combos, key=lambda x: x['fitness'], reverse=True)
            combo_df = pd.DataFrame(sorted_unique_combos)
            opt_run_mgr.update_field(opt_uid, 'cost_opt', rm.end_timer('opt'))
            ''' ====================== 3.保存特征选择结果和其回测的评估绩效指标 ======================= '''
            # ========= 初始化本节操作的环境
            rm.start_timer('save')
            opt_result_mgr = OptResultDataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_optresult')
            day_bars, ann_days, fixed_return, fee_rate, weights, is_ret_open = METRIC_PARAMS_DEFAULT[symbol]
            min_fitness = sorted_unique_combos[-1]['fitness'] # 加入pj后与之比较 -- fit函数用的是wfa
            expr_combos = combo_df['expr_combo'].values.tolist()
            pj_temp = pj_L2_list + [None]
            combos_with_pj = [(combo, p) for combo in expr_combos for p in pj_temp]
            opt_result_data_list = []
            ret_se_train_list = []
            ret_se_test_list = []
            ret_se_all_list = []
            ret_se_all_roll_list = []
            ret_se_test_roll_list = []
            cost_se_train_list = []
            cost_se_test_list = []
            cost_se_all_list = []
            cost_se_all_roll_list = []
            cost_se_test_roll_list = []
            combo_ret_df_train = pd.DataFrame() # [date] combo1_ret combo2_ret ...
            combo_cost_df_train = pd.DataFrame() # [date] combo1_cost combo2_cost ...
            combo_ret_df_test = pd.DataFrame() # [date] combo1_ret combo2_ret ...
            combo_cost_df_test = pd.DataFrame() # [date] combo1_cost combo2_cost ...
            combo_ret_df_all = pd.DataFrame() # [date] combo1_ret combo2_ret ...
            combo_cost_df_all = pd.DataFrame() # [date] combo1_cost combo2_cost ...
            combo_ret_df_all_roll = pd.DataFrame() # [date] combo1_ret combo2_ret ...
            combo_cost_df_all_roll = pd.DataFrame() # [date] combo1_cost combo2_cost ...
            combo_ret_df_test_roll = pd.DataFrame() # [date] combo1_ret combo2_ret ...
            combo_cost_df_test_roll = pd.DataFrame() # [date] combo1_cost combo2_cost ...
            df_ratios_train_list = []
            df_ratios_test_list = []
            df_ratios_all_list = []
            df_ratios_all_roll_list = []
            df_ratios_test_roll_list = []
            combo_df_ratios_train = pd.DataFrame() # [expr] sr, cost_sr, tot_ret, ..., saw_score
            combo_df_ratios_test = pd.DataFrame() # [expr] sr, cost_sr, tot_ret, ..., saw_score
            combo_df_ratios_all = pd.DataFrame() # [expr] sr, cost_sr, tot_ret, ..., saw_score
            combo_df_ratios_all_roll = pd.DataFrame() # [expr] sr, cost_sr, tot_ret, ..., saw_score
            combo_df_ratios_test_roll = pd.DataFrame() # [expr] sr, cost_sr, tot_ret, ..., saw_score
            # ======== 逐次加入pj 评测效果是否提升 ========
            for (a_combo, a_pj) in combos_with_pj:
                # =========== 数据集组装 + 训练/测试集 切分
                if a_pj is not None:
                    combined_df_all = pd.concat([f_L2_df[return_col], f_L2_df[a_combo], pj_L2_df[a_pj]],axis=1)
                else:
                    combined_df_all = pd.concat([f_L2_df[return_col], f_L2_df[a_combo]],axis=1)

                combined_df_train = combined_df_all.iloc[:split_idx]
                combined_df_test = combined_df_all.iloc[split_idx:]
                if split_idx < 1000*2:
                    raise ValueError(f"split_idx的值{split_idx} 小于滚动窗口的大小{1000*2},无法创建滚动测试集")
                combined_df_test_roll = combined_df_all.iloc[split_idx - 1000*2: ] # 测试集样本外滚动: 最终结果参考
                # =========== wfa组装训练集, 计算该组合的绩效指标
                ret_se_train, cost_se_train = self.modeler.wfa_ensemble(combined_df_train, # 滚动训练集
                                                                        1000, fee_rate,is_ret_open)  
                df_ratios_train = calc_stats_RC(ret_se_train, cost_se_train, 
                                                day_bars, ann_days, fixed_return) # 计算组合的绩效指标
                df_ratios_with_score_train = stats_saw_score(df_ratios_train, weights) # 计算组合的评分
                # ============= NOTE: 这部分提前至此, 马上判断来优化算力
                in_sr = df_ratios_with_score_train['sr'].iloc[0] # 训练集wfa(与min_fitness保持一致)
                if a_pj is not None:       
                    # ============= 评估该组合加入pj的效果 try2: sr only; try1: saw评分 -- 不够直观
                    if in_sr < min_fitness: # 加入pj后效果不好 直接看下一个for
                        continue
                # =========== pure预测测试集, 计算该组合的绩效指标 -- NOTE: 不要再用pure_enamble了
                ret_se_test, cost_se_test = self.modeler.pure_predict(combined_df_test,  # pure装配测试集
                                                            fee_rate, is_ret_open)
                df_ratios_test = calc_stats_RC(ret_se_test, cost_se_test, 
                                                day_bars, ann_days, fixed_return) # 计算组合的绩效指标
                df_ratios_with_score_test = stats_saw_score(df_ratios_test, weights) # 计算组合的评分 
                # =========== wfa组装全集, 计算该组合的绩效指标
                ret_se_all_roll, cost_se_all_roll = self.modeler.wfa_ensemble(combined_df_all, 
                                                                    1000, fee_rate, is_ret_open)
                df_ratios_all_roll = calc_stats_RC(ret_se_all_roll, cost_se_all_roll, 
                                                day_bars, ann_days, fixed_return) # 计算组合的绩效指标
                df_ratios_with_score_all_roll = stats_saw_score(df_ratios_all_roll, weights) # 计算组合的评分
                # =========== wfa训练+pure测试 拼接全集, 计算该组合的绩效指标
                ret_se_all = pd.concat([ret_se_train, ret_se_test])
                cost_se_all = pd.concat([cost_se_train, cost_se_test])
                df_ratios_all = calc_stats_RC(ret_se_all, cost_se_all,
                                                day_bars, ann_days, fixed_return) # 计算组合的绩效指标
                df_ratios_with_score_all = stats_saw_score(df_ratios_all, weights) # 计算组合的评分
                # =========== wfa测试集(样本外) , 计算该组合的绩效指标 ---- 最终参考!
                ret_se_test_roll, cost_se_test_roll = self.modeler.wfa_ensemble(combined_df_test_roll,
                                                                    1000, fee_rate, is_ret_open)
                df_ratios_test_roll = calc_stats_RC(ret_se_test_roll, cost_se_test_roll, 
                                                day_bars, ann_days, fixed_return) # 计算组合的绩效指标
                df_ratios_with_score_test_roll = stats_saw_score(df_ratios_test_roll, weights) # 计算组合的评分
                # ========= 当前因子组合装配模型后的yhat series图形化: eq + hist
                # 观察后除去: 1.跳变的(400采样点); 2.分布怪异的(全量数据)
                save_name = symbol+'_'+task_uid+'_combo_outwfa_yhat_'+opt_uid
                save_path = Path(self.fctpic_combo_dir).joinpath(save_name)
                self.modeler.show_yhat_as_combo_normed(combined_df_test_roll[return_col], compr_col=[return_col], 
                                    compr_show_cum = True, save_path = str(save_path)) # 当前yhat保存在类变量y_hat_as_combo_normed
                # NOTE: 存的是out_r的wfa过程最后一次的model -- 最逼近真实
                if a_pj is not None:       
                    model_name = "_".join([symbol, str(self.modeler.model_strats), # 品种_算法名_C数_Ssr_ordereduid.pkl
                                        'C'+str(len(a_combo)+1), 'inSr'+str(round(in_sr,2)), 
                                        self.gen_ordered_uid()]) + ".pkl" 
                    a_combo_str = json.dumps(a_combo + a_pj)  # 未来用json.load(a_combo_str) 还原回list
                else: # 保存优化组合本身的结果
                    model_name = "_".join([symbol, str(self.modeler.model_strats), # 品种_算法名_C数_Ssr_ordereduid.pkl
                                        'C'+str(len(a_combo)), 'inSr'+str(round(in_sr,2)), 
                                        self.gen_ordered_uid()]) + ".pkl" 
                    a_combo_str = json.dumps(a_combo)  # 未来用json.load(a_combo_str) 还原回list
                self.modeler.save(model_name) 
                # ============== 组装ret/cost矩阵, 以及df_ratios矩阵拼接: 为绩效图形化而用
                ret_se_train.name = a_combo_str
                ret_se_test.name = a_combo_str
                ret_se_all.name = a_combo_str
                ret_se_all_roll.name = a_combo_str
                ret_se_test_roll.name = a_combo_str
                cost_se_train.name = a_combo_str
                cost_se_test.name = a_combo_str
                cost_se_all.name = a_combo_str
                cost_se_all_roll.name = a_combo_str
                cost_se_test_roll.name = a_combo_str
                ret_se_train_list.append(ret_se_train)
                ret_se_test_list.append(ret_se_test)
                ret_se_all_list.append(ret_se_all)
                ret_se_all_roll_list.append(ret_se_all_roll)
                ret_se_test_roll_list.append(ret_se_test_roll)
                cost_se_train_list.append(cost_se_train)
                cost_se_test_list.append(cost_se_test)
                cost_se_all_list.append(cost_se_all)
                cost_se_all_roll_list.append(cost_se_all_roll)
                cost_se_test_roll_list.append(cost_se_test_roll)
                # set_index
                df_ratios_with_score_train.set_index(pd.Index([a_combo_str]), inplace=True)
                df_ratios_with_score_test.set_index(pd.Index([a_combo_str]), inplace=True)
                df_ratios_with_score_all.set_index(pd.Index([a_combo_str]), inplace=True)
                df_ratios_with_score_all_roll.set_index(pd.Index([a_combo_str]), inplace=True)
                df_ratios_with_score_test_roll.set_index(pd.Index([a_combo_str]), inplace=True)
                df_ratios_train_list.append(df_ratios_with_score_train)
                df_ratios_test_list.append(df_ratios_with_score_test)
                df_ratios_all_list.append(df_ratios_with_score_all)
                df_ratios_all_roll_list.append(df_ratios_with_score_all_roll)
                df_ratios_test_roll_list.append(df_ratios_with_score_test_roll)
                # ============== 优化结果入库: optresult
                for scope in ['in', 'out', 'all', 'all_r', 'out_r']:
                    
                    df_scope = get_scope_data(scope, df_ratios_with_score_train,
                                                    df_ratios_with_score_test,
                                                    df_ratios_with_score_all,
                                                    df_ratios_with_score_all_roll,
                                                    df_ratios_with_score_test_roll
                                            )      
                    opt_result_data = OptResultData(
                        model_file=model_name, combination=a_combo_str, 
                        task_uid=task_uid, opt_uid=opt_uid, scope=scope,
                        sr = round(df_scope['sr'].iloc[0],3),
                        cost_sr = round(df_scope['cost_sr'].iloc[0],3),
                        tot_ret = round(df_scope['tot_ret'].iloc[0],3),
                        ann_ret = round(df_scope['ann_ret'].iloc[0],3),
                        ann_cost = round(df_scope['ann_cost'].iloc[0],3),
                        ann_std = round(df_scope['ann_std'].iloc[0],3),
                        mdd = round(df_scope['mdd'].iloc[0],3),
                        bgn_mdd = df_scope['bgn_mdd'].iloc[0],
                        end_mdd = df_scope['end_mdd'].iloc[0],
                        avg_dd = round(df_scope['avg_dd'].iloc[0],3),
                        monthly_skew = round(df_scope['monthly_skew'].iloc[0],3),
                        lower_tail = round(df_scope['lower_tail'].iloc[0],3),
                        upper_tail = round(df_scope['upper_tail'].iloc[0],3),
                        profit_factor = round(df_scope['profit_factor'].iloc[0],3),
                        calmar = round(df_scope['calmar'].iloc[0],3),
                        trade_times = int(df_scope['trade_times'].iloc[0]),
                        win_rate = round(df_scope['win_rate'].iloc[0],3),
                        saw_score = round(df_scope['saw_score'].iloc[0],3),
                        topsis_score = 0
                    )
                    opt_result_data_list.append(opt_result_data)
            opt_result_mgr.add_data_list(opt_result_data_list) # 一次性写库
            # NOTE: 所有optresult表数据大排名, 不仅只有本次的opt结果
            for scope in ['in', 'out', 'all', 'all_r', 'out_r']:
                optresult_list: List[OptResultData] = opt_result_mgr.fetch_all_opt_results(scope = scope)
                optresult_df_all = opt_result_mgr.to_in_topsis_df(optresult_list)
                optresult_df_all = stats_topsis_score(optresult_df_all, col_prefix='', strat_weights=None)
                opt_result_mgr.update_topsis_by_df(optresult_df_all)
            # ============ 封装ret/cost总矩阵 + 封装df_ratios总矩阵
            combo_ret_df_train = pd.concat( ret_se_train_list, axis=1)
            combo_ret_df_test = pd.concat( ret_se_test_list, axis=1)
            combo_ret_df_all = pd.concat( ret_se_all_list, axis=1)
            combo_ret_df_all_roll = pd.concat( ret_se_all_roll_list, axis=1)
            combo_ret_df_test_roll = pd.concat( ret_se_test_roll_list, axis=1)
            combo_cost_df_train = pd.concat( cost_se_train_list, axis=1)
            combo_cost_df_test = pd.concat( cost_se_test_list, axis=1)
            combo_cost_df_all = pd.concat( cost_se_all_list, axis=1)
            combo_cost_df_all_roll = pd.concat( cost_se_all_roll_list, axis=1)
            combo_cost_df_test_roll = pd.concat( cost_se_test_roll_list, axis=1)
            combo_df_ratios_train = pd.concat(df_ratios_train_list, axis=0)
            combo_df_ratios_test = pd.concat(df_ratios_test_list, axis=0)
            combo_df_ratios_all = pd.concat(df_ratios_all_list, axis=0)
            combo_df_ratios_all_roll = pd.concat(df_ratios_all_roll_list, axis=0)
            combo_df_ratios_test_roll = pd.concat(df_ratios_test_roll_list, axis=0)
            # # 保存为 CSV 文件
            # combo_ret_df_train.to_csv('combo_ret_df_train.csv', index=True)
            # combo_ret_df_test.to_csv('combo_ret_df_test.csv', index=True)
            # combo_ret_df_all.to_csv('combo_ret_df_all.csv', index=True)
            # combo_ret_df_all_roll.to_csv('combo_ret_df_all_roll.csv', index=True)
            # combo_cost_df_train.to_csv('combo_cost_df_train.csv', index=True)
            # combo_cost_df_test.to_csv('combo_cost_df_test.csv', index=True)
            # combo_cost_df_all.to_csv('combo_cost_df_all.csv', index=True)
            # combo_cost_df_all_roll.to_csv('combo_cost_df_all_roll.csv', index=True)
            # combo_df_ratios_train.to_csv('combo_df_ratios_train.csv', index=True)
            # combo_df_ratios_test.to_csv('combo_df_ratios_test.csv', index=True)
            # combo_df_ratios_all.to_csv('combo_df_ratios_all.csv', index=True)
            # combo_df_ratios_all_roll.to_csv('combo_df_ratios_all_roll.csv', index=True)
            ''' ====================== 4.绩效数据图形化保存 ========================= '''
            # ========= 初始化: 参数提取, 数据加载 ========
            day_bars = METRIC_PARAMS_DEFAULT[symbol].day_bars
            close_df = fctval_mgr.load_factor_values_by_cols(task_uid, ['close'])
            pj_close_df = fctval_mgr.load_factor_values_by_cols(task_uid+'_pj', ['close'])
            assert close_df.index.equals(pj_close_df.index)
            assert (close_df['close'] == pj_close_df['close']).all()
            # ========= 数据处理: benchmark数据日度化 ========
            close_df = close_df.loc[combo_ret_df_all.index.min():combo_ret_df_all.index.max()]
            day_combo_ret_df_all = to_day_pct_ret(combo_ret_df_all,day_bars=day_bars).dropna(how='all')
            day_combo_cost_df_all = to_day_pct_ret(combo_cost_df_all,day_bars=day_bars).dropna(how='all')
            day_combo_ret_df_all_roll = to_day_pct_ret(combo_ret_df_all_roll,day_bars=day_bars).dropna(how='all')
            day_combo_ret_df_test_roll = to_day_pct_ret(combo_ret_df_test_roll,day_bars=day_bars).dropna(how='all')
            day_combo_cost_df_all_roll = to_day_pct_ret(combo_cost_df_all_roll,day_bars=day_bars).dropna(how='all')
            day_combo_cost_df_test_roll = to_day_pct_ret(combo_cost_df_test_roll,day_bars=day_bars).dropna(how='all')
            day_close_df = to_day_close(close_df,day_bars=day_bars).dropna(how='all')
            bench_se = day_close_df['close']/day_close_df['close'].shift(1) -1
            boundary_day = combo_ret_df_train.index[-1].date()
            self.modeler.show_comboperf(day_combo_ret_df_all, day_combo_cost_df_all,
                                        day_combo_ret_df_all_roll, day_combo_cost_df_all_roll,
                                        day_combo_ret_df_test_roll, day_combo_cost_df_test_roll,
                                        benchmark=bench_se,
                                        df_ratios_in=combo_df_ratios_train,
                                        df_ratios_out=combo_df_ratios_test,
                                        df_ratios_all=combo_df_ratios_all,
                                        df_ratios_all_r = combo_df_ratios_all_roll,
                                        df_ratios_out_r = combo_df_ratios_test_roll,
                                        boundary_day = boundary_day,
                                        batch_uid=opt_uid, # 本次组合优化的uid批次号
                                        save_path=self.fctpic_combo_dir)
                        
            # 计时结束
            opt_run_mgr.update_field(opt_uid, 'cost_save', rm.end_timer('save'))
            opt_run_mgr.update_field(opt_uid, 'cost_total', rm.end_timer('total'))
            opt_run_mgr.update_field(opt_uid, 'cpu_usage', rm.end_timer_cpu(opt_uid))
            opt_run_mgr.update_field(opt_uid, 'memory_usage', rm.log_memory_usage())
            opt_run_mgr.set_end_run(opt_uid)
            # ========= 恢复task全局参数
            self._restore_global_config(orig_params)
            
            # TODO:特征选择的evaluate中未来加"宏观", 宏观不参与gp挖掘
            # 特征选择返回combo列表 但不会存模型文件 和 绩效指标; 接下来半自动看排名topn的表现(加pj)并存入optresult和模型文件保存; gen总体topsis; 个别的(模型文件+combo)逐一看详细绩效情况(比如滚动xx); - 半自动选出前几名组合取并集且逐一看单因子滚动情况后得到本期因子池(每半年?)  后续就观察上述池里的单因子的具体情况(每周?每月?)不断调整本期入选因子然后装配模型跑实盘
    
    def analyze(self):
        ''' 读优化组合 + 分析展示 '''
        pass
                # ''' =============================  1.装配; 2.分析  =============================== '''
                # model_file = '{}_{}_model_ols_{}.pkl'.format(symbol, run_uid, datetime.now().strftime('%y%m%d_%H'))
                # model_path = DATA_DIR_MODEL.joinpath(model_file)
                # modeler = FactorsModeler(LinearRegressionModel(), model_path)
                # perf_df_all, tot_nav_df_all = modeler.ensemble_and_analyze(symbol, self.task.fee_rate,
                #                                                         a_fcts_final_df[f1+pj+['ret']], 
                #                                                         self.y_raw_dfs[symbol], 
                #                                                         self.task.split_perc,f1,pj)
                # # 保持混淆pj的所有perf
                # file_name1 = '{}_{}_f{}_pj{}_comb_perf_{}.csv'.format(symbol, run_uid, f_len, pj_len, datetime.now().strftime('%y%m%d_%H'))
                # path_name1 = DATA_DIR_FACTOR.joinpath(file_name1)
                # perf_df_all.to_csv(path_name1, encoding='utf_8_sig')
                # file_name2 = '{}_{}_f{}_pj{}_comb_nav_{}.csv'.format(symbol, run_uid, f_len, pj_len, datetime.now().strftime('%y%m%d_%H'))
                # path_name2 = DATA_DIR_FACTOR.joinpath(file_name2)
                # tot_nav_df_all.to_csv(path_name2, encoding='utf_8_sig')
                # # # NOTE: fcts_norm绘图 -- final因子集
                # # # import jzal_pro.utils.factor_utils as plt_u
                # # # a_fcts_final_df['close'] = x_train['close']
                # # # plt_u.plot_hist(a_fcts_final_df,f1)
                
    def L2group(self, task_uids: dict, infunc_id: str):
        ''' =============   用"训练集"来进行群验证筛选, 入L2group库 ================= '''
        self.log("读库 + 读因子值 + 因子群验证筛选(训练集) + 入L2group库 + 图形保存: start... ")
        for symbol, task_uid in task_uids.items():
            # ====== task应用pair个性化参数
            orig_params = self._apply_pair_config(symbol)
            # ========= 运行时监控信息统计
            rm = ResMonitor()
            opt_uid = self.gen_ordered_uid()
            rm.start_timer_cpu(opt_uid)
            rm.start_timer('total')
            opt_run_mgr = OptRunDataManager(db_path=self.db_path) 
            opt_run_mgr.add_data(OptRunData(opt_uid, task_uid, symbol))
            opt_run_mgr.set_bgn_run(opt_uid)
            
            return_col = 'ret_open' if TRADE_ON_OPEN else 'ret'
            ''' ========================== 读L2因子库 =========================== ''' 
            rm.start_timer('load') # ========= 运行时监控信息统计
            f_L2_mgr = FactorL2DataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_f_'+infunc_id)
            pj_L2_mgr = FactorL2DataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_pj_'+infunc_id)
            f_L2_list = f_L2_mgr.load_all_factors(scope='in')
            pj_L2_list = pj_L2_mgr.load_all_factors(scope='in')
            if len(f_L2_list) != len(set(f_L2_list)) or len(pj_L2_list) != len(set(pj_L2_list)):
                self.log('L2因子库中有重复数据.. 待检查..')
                return
                
            ''' ========================== 读库组成因子值数据大宽表 ==================== ''' 
            fctval_mgr = FactorValueMgr(self.fctval_base_dir,self.fctval_file_max_cols)
            f_L2_df = fctval_mgr.load_factor_values_by_cols(task_uid, [return_col] + f_L2_list)
            pj_L2_df = fctval_mgr.load_factor_values_by_cols(task_uid+'_pj', [return_col] + pj_L2_list)
            
            # 确保所有数据使用相同的索引
            common_index = f_L2_df.index.intersection(self.y_norm_dfs[symbol].index)
            f_L2_df = f_L2_df.loc[common_index]
            pj_L2_df = pj_L2_df.loc[common_index]
            
            # 计算训练集截止点
            split_idx = int(len(f_L2_df)*self.task.split_perc)
            train_index = f_L2_df.index[:split_idx]
            
            # 截取训练数据
            f_L2_df = f_L2_df.loc[train_index].copy()
            pj_L2_df = pj_L2_df.loc[train_index].copy()
            y = self.y_norm_dfs[symbol].loc[train_index].copy()
            y_raw_t = self.y_raw_t_return_dfs[symbol].loc[train_index].copy()
            
            # 获取标准化的label
            opt_run_mgr.update_field(opt_uid, 'cost_load', rm.end_timer('load'))
            
            ''' ====================== L2筛选: 群验证(rankic+corr) ============================ '''
            
            rm.start_timer('opt')
            f = self._examine_random_groups(f_L2_df, y, y_raw_t, 
                                        symbol=symbol, 
                                        opt_uid=opt_uid,
                                        group_size=20, 
                                        window=2000, 
                                        min_period=1000)  # 正确
            pj = self._examine_random_groups(pj_L2_df, y, y_raw_t, 
                                        symbol=symbol,
                                        opt_uid=opt_uid,
                                        group_size=20, 
                                        window=2000, 
                                        min_period=1000)  # split_idx已在pj_L2_df中截断
            
            # ========= 运行时监控信息统计
            opt_params = 'G_size=20, ic_top=40%, corr_top=20%'
            opt_run_mgr.update_field(opt_uid, 'opt_method', 'L2group')
            opt_run_mgr.update_field(opt_uid, 'opt_params', opt_params)
            opt_run_mgr.update_field(opt_uid, 'factor_size', f_L2_df.shape[1]+pj_L2_df.shape[1])
            opt_run_mgr.update_field(opt_uid, 'cost_opt', rm.end_timer('opt'))
            
            ''' ====================== 入L2库 =========================== '''
            rm.start_timer('save')
            f_L2_mgr = FactorL2DataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_f_L2group')
            pj_L2_mgr = FactorL2DataMgr(db_path=self.db_path, table_name=symbol+'_'+task_uid+'_pj_L2group')
            f_data_list = [
                FactorL2Data(expr=e, task_uid=task_uid, opt_uid=opt_uid, scope='in')
                for e in f
            ]
            f_L2_mgr.add_factor_list(f_data_list)
            
            pj_data_list = [
                FactorL2Data(expr=e, task_uid=task_uid, opt_uid=opt_uid, scope='in')
                for e in pj
            ]
            pj_L2_mgr.add_factor_list(pj_data_list)
            
            ''' ====================== 因子值图形化保存 =========================== '''
            save_name = symbol+'_'+task_uid+'_L2group_f'
            save_path = Path(self.fctpic_base_dir).joinpath(save_name)
            self.modeler.show_fctval(f_L2_df[f+[return_col]], compr_col=[return_col], 
                                compr_show_cum=True, save_path=str(save_path))
            
            save_name = symbol+'_'+task_uid+'_L2group_pj'
            save_path = Path(self.fctpic_base_dir).joinpath(save_name)
            self.modeler.show_fctval(pj_L2_df[pj+[return_col]], compr_col=[return_col], 
                                compr_show_cum=True, save_path=str(save_path))
            
            # ========= 运行时监控信息统计
            opt_run_mgr.update_field(opt_uid, 'cost_save', rm.end_timer('save'))
            opt_run_mgr.update_field(opt_uid, 'cost_total', rm.end_timer('total'))
            opt_run_mgr.update_field(opt_uid, 'cpu_usage', rm.end_timer_cpu(opt_uid))
            opt_run_mgr.update_field(opt_uid, 'memory_usage', rm.log_memory_usage())
            opt_run_mgr.set_end_run(opt_uid)
            # ======== task恢复到全局参数配置
            self._restore_global_config(orig_params)
            
        self.log("读库 + 读因子值 + 因子群验证筛选(训练集) + 入L2group库 + 图形保存: complete... ")
        while True:
            staircase_factors = input("Group: 1.查看因子值图 2.在L2库中删除异常因子 3.保存数据库 4.输入'CONTINUE'继续: ")
            if staircase_factors.lower() == 'continue':
                break
        return

    def _examine_random_groups(self, df: pd.DataFrame, # 总体因子值矩阵(样本内)
                            y: pd.Series,   # 样本内标准化label
                            y_raw_t: pd.Series,  # 样本内t期收益率(没有norm过)
                            symbol: str, opt_uid: str,
                            group_size: int = 20, 
                            window: int = 2000, 
                            min_period: int = 1000):
        """群随机分组择优函数"""
        from tqdm import tqdm
        
        factors = [col for col in df.columns if col not in ['ret', 'ret_open']]
        
        # 添加检查
        if not factors:
            self.log(f"[{symbol}] 错误: 没有可用的因子列")
            return []
            
        if len(factors) < group_size:
            self.log(f"[{symbol}] 警告: 因子数量({len(factors)})小于组大小({group_size}), 调整组大小")
            group_size = len(factors)
            
        groups = []
        ''' ========================== 随机生成不重叠组 =========================== '''
        # 计算可以生成的不重叠组数
        num_groups = len(factors) // group_size
        remaining_factors = factors.copy()
        # 存储每个组合的仓位序列
        pos_matrix = np.zeros((len(df), num_groups))
        
        if num_groups == 0:
            self.log(f"[{symbol}] 错误: 因子数量不足以形成组合")
            return []
            
        print(f"\n[{symbol}] Stage 1/3: 生成{num_groups}个随机组合并装模...")
        # 随机生成不重叠的因子组合
        for i in tqdm(range(num_groups), desc="Random Groups"):
            if len(remaining_factors) < group_size:
                break    
            # 从剩余因子中随机抽取
            group = np.random.choice(remaining_factors, size=group_size, replace=False)
            groups.append(group)
            # 从剩余因子池中移除已使用的因子
            remaining_factors = [f for f in remaining_factors if f not in group]
            # 使用modeler对group进行装配
            group_df = df[list(group)]
            X = group_df.values
            if len(X.shape) == 1:
                X = X.reshape(-1, 1)
            self.modeler.model_strats.fit_model(X, y)
            y_pred = self.modeler.model_strats.predict(X)
            pos_np = eu.norm(y_pred, params=NORM_PARAMS_POS)
            pos_matrix[:, i] = pos_np.ravel()
            
        # 如果只有一个组合，直接返回该组的因子
        if num_groups == 1:
            return list(groups[0])
        
        ''' ======================== 每组装模算rankic排序择优 ========================= '''
        print(f"[{symbol}] Stage 2/3: Rankic和相关性筛选...")
        pos_df = pd.DataFrame(pos_matrix, index=df.index, 
                                columns=[f'group_{i}' for i in range(num_groups)])
        top_groups, _ = FctsGPMiner.select_by_rolling_rankic(
            pos_df, 
            pd.DataFrame(y), # 将Series转为DataFrame
            pd.Series(y_raw_t),
            window=window, min_period=min_period,
            top_pct=0.4, pj_pct=0.2, # 根据排序选前40%的groups
            symbol=symbol, g_uid=opt_uid
        )
        ''' ================== rankic择优组之间的相关性,再次排序优选 ================== '''
        top_group_cols = [col for col in pos_df.columns if col in top_groups.index]
        top_pos_df = pos_df[top_group_cols]
        # 计算相关性矩阵(仅上三角)
        corr_matrix = top_pos_df.corr().where(np.triu(np.ones(top_pos_df.corr().shape), k=1).astype(bool))
        # 计算每个组合的平均相关性
        avg_corrs = {}
        for col in top_group_cols:
            # 获取该列与其他列的相关系数(排除自身)
            corrs = corr_matrix[col].dropna()
            avg_corrs[col] = np.mean(np.abs(corrs)) if len(corrs) > 0 else 0
        # 按平均相关性排序
        sorted_groups = sorted(avg_corrs.items(), key=lambda x: x[1])
        n_select = max(1, int(len(sorted_groups) * 0.2))
        final_groups = sorted_groups[:n_select]                    
        # 获取最终选中的组对应的因子
        selected_factors = set()
        for group_name, _ in final_groups:
            group_idx = int(group_name.split('_')[1])
            selected_factors.update(groups[group_idx])                    
            
        # 添加检查
        if not selected_factors:
            self.log(f"[{symbol}] 警告: 相关性筛选后没有因子被选中，返回原始组的所有因子")
            # 返回第一组的所有因子
            if groups:
                return list(groups[0])
            return []
            
        ''' ======== 因子影响性: 优选的因子集合轮询逐个剔除影响小的因子 ============== '''
        print(f"[{symbol}] Stage 3/3: 评估{len(selected_factors)}个因子的影响性...")
        current_factors = list(selected_factors).copy()
        impact_threshold = 0.05  # 影响度阈值
        update_base_threshold = 3  # 每移除3个因子更新一次base
        removed_count = 0
        # 初始base计算
        X_base = df[current_factors].values
        self.modeler.model_strats.fit_model(X_base, y)
        base_y_pred = self.modeler.model_strats.predict(X_base)
        base_pos = eu.norm(base_y_pred, params=NORM_PARAMS_POS)
        base_rankic = pu.calc_rolling_rankic(
            base_pos.reshape(-1,1),
            np.array(y_raw_t.values),
            window=window,
            min_periods=min_period
        ).ravel()
        base_score = np.nanmean(np.abs(base_rankic))

        round_count = 1
        # 预热JIT
        pu.warmup_jit_functions()
        while True:
            redundant_in_round = []
            # 评估每个因子
            for fct in tqdm(current_factors, desc=f"Round {round_count}"):
                remain_fcts = [f for f in current_factors if f != fct]
                X_remain = df[remain_fcts].values
                self.modeler.model_strats.fit_model(X_remain, y)
                y_pred = self.modeler.model_strats.predict(X_remain)
                pos = eu.norm(y_pred, params=NORM_PARAMS_POS)
                rankic = pu.calc_rolling_rankic(
                    pos.reshape(-1,1),
                    np.array(y_raw_t.values),
                    window=window,
                    min_periods=min_period
                ).ravel()
                score = np.nanmean(np.abs(rankic))
                
                if abs(base_score - score) / base_score < impact_threshold:
                    redundant_in_round.append(fct)
            
            if not redundant_in_round:  # 如果这轮没有发现冗余因子则退出
                print(f"[{symbol}] 完成! 最终保留{len(current_factors)}个因子")
                break
                
            # 移除本轮发现的冗余因子
            print(f"[{symbol}] Round {round_count}: 发现{len(redundant_in_round)}个冗余因子")
            current_factors = [f for f in current_factors if f not in redundant_in_round]
            removed_count += len(redundant_in_round)
            round_count += 1
            
            # 每移除3个因子更新一次base
            if removed_count >= update_base_threshold:
                print(f"[{symbol}] 更新base score...")
                X_base = df[current_factors].values
                self.modeler.model_strats.fit_model(X_base, y)
                base_y_pred = self.modeler.model_strats.predict(X_base)
                base_pos = eu.norm(base_y_pred, params=NORM_PARAMS_POS)
                base_rankic = pu.calc_rolling_rankic(
                    base_pos.reshape(-1,1),
                    np.array(y_raw_t.values),
                    window=window,
                    min_periods=min_period
                ).ravel()
                base_score = np.nanmean(np.abs(base_rankic))
                removed_count = 0  # 重置计数器

        return sorted(current_factors)

    def _apply_pair_config(self, symbol_period: str) -> dict:
        """应用品种专属配置并返回原始参数(如果有修改)
        Returns:
            dict: 被修改的原始参数字典,如果没有修改则返回空字典
        """
        orig_params = {}
        
        if symbol_period in self.task.pair_configs:
            # 保存原始参数
            orig_params = {
                'fee_rate': self.task.fee_rate,
                'split_perc': self.task.split_perc, 
                'free_rate': self.task.free_rate,
                't_delay': self.task.t_delay,
                'start_date': self.task.start_date,
                'end_date': self.task.end_date
            }
            
            # 应用专属配置
            pair_config = self.task.pair_configs[symbol_period]
            self.task.fee_rate = pair_config.get('fee_rate', self.task.fee_rate)
            self.task.split_perc = pair_config.get('split_perc', self.task.split_perc)
            self.task.free_rate = pair_config.get('free_rate', self.task.free_rate)
            self.task.t_delay = pair_config.get('t_delay', self.task.t_delay)
            self.task.start_date = pair_config.get('start_date', self.task.start_date)
            self.task.end_date = pair_config.get('end_date', self.task.end_date)
            
        return orig_params
    
    def _restore_global_config(self, orig_params: dict):
        """恢复品种专属配置为原始参数
        
        Args:
            orig_params: 原始参数字典,包含需要恢复的参数值
        """
        if orig_params:
            self.task.fee_rate = orig_params['fee_rate']
            self.task.split_perc = orig_params['split_perc']
            self.task.free_rate = orig_params['free_rate']
            self.task.t_delay = orig_params['t_delay']
            self.task.start_date = orig_params['start_date']
            self.task.end_date = orig_params['end_date']
    
if __name__ == '__main__':
    # 示例数据
    data = {
        'date': pd.date_range(start='2005-01-01', periods=20000, freq='15T'),
        'ret': np.random.randn(20000),
        'fct1': np.random.randn(20000),
        'fct2': np.random.randn(20000),
        'fct3': np.random.randn(20000)
    }
    fctval_df = pd.DataFrame(data).set_index('date')
    model_path = DB_DIR_FACTOR.joinpath('factor_models')
    model_strat = LinearRegressionModel()
    modeler = FactorsModeler(model_strat, str(model_path))
    ret_df, cost_df = modeler.wfa_ensemble_as_single(fctval_df, step_bars=1000, fee_rate=0.001)
    from jzal_pro.utils import calc_stats_RC
    df_ratios = calc_stats_RC(ret_df, cost_df, 16, 252, 0.03)
    df_ratios = stats_saw_score(df_ratios)
    # print(ret_df)
    # print(df_ratios[['mdd','bgn_mdd','end_mdd']])
    
    
 