from loguru import logger
import psutil
from datetime import datetime
from typing import Optional
import time
import random
# import sys
# from pathlib import Path
# path_root = Path(__file__).parent.absolute().parent
# if str(path_root) not in sys.path:
#     sys.path.append(str(path_root))
from config import SETTING_TZ
# logger.add("logs/myapp_{time}.log", level="INFO", format="{time} - {name} - {level} - {message}", rotation="1 week", compression="zip")

class BaseObj:
    def __init__(self) -> None:
        pass
    
    @classmethod
    def log(cls, txt: str, level: str = "INFO"):
        # logger.add("logs/myapp_{time}.log", level="INFO", format="{time} - {name} - {level} - {message}", rotation="1 week", compression="zip")
        if level.upper() == "DEBUG":
            logger.debug('%s: %s' % (cls.__class__.__name__, txt))
        elif level.upper() == "INFO":
            logger.info('%s: %s' % (cls.__class__.__name__, txt))
        elif level.upper() == "WARNING":
            logger.warning('%s: %s' % (cls.__class__.__name__, txt))
        elif level.upper() == "ERROR":
            logger.error('%s: %s' % (cls.__class__.__name__, txt))
        elif level.upper() == "CRITICAL":
            logger.critical('%s: %s' % (cls.__class__.__name__, txt))
        else:
            raise ValueError(f"Unsupported log level: {level}")
    def gen_ordered_uid(self) -> str:
        # 考虑时区，以UTC为基础， 返回16位uid（字符串）
        now = datetime.now(SETTING_TZ)
        timestamp = int(now.timestamp() * 1000)  # 毫秒级时间戳
        unique_part = str(random.randint(100, 999))  # 简化的3位随机数增加唯一性
        # 组合时间戳和唯一部分，用于哈希
        uid = f"{timestamp}{unique_part}"
        return uid    
        
class ResMonitor(BaseObj):
    def __init__(self):
        super().__init__()
        self.start_times = {}
        self.start_cpu_usage = {}  # 新增：存储开始时的CPU使用情况
        self.log("Resource monitor started")

    def _get_memory_usage(self):
        process = psutil.Process()
        mem_info = process.memory_info()
        return mem_info.rss / 10**6  # 返回当前的内存使用量（MB）

    def _get_cpu_usage(self):
        process = psutil.Process()
        cpu_times = process.cpu_times()
        return cpu_times.user, cpu_times.system  # 返回当前的CPU使用量（用户时间和系统时间）

    def _calculate_diff(self, resource_type, current_value):
        """计算资源（CPU或内存）的增量。假设start_times中记录了开始时刻的时间戳。"""
        if resource_type not in ['cpu', 'memory']:
            raise ValueError("resource_type must be 'cpu' or 'memory'")
        start_time = self.start_times.pop(resource_type, time.time())
        delta_time = time.time() - start_time
        if resource_type == 'cpu':
            # 对于CPU时间，直接计算差值可能不准确，因为CPU时间会持续累加，此处仅示意处理
            return current_value - self._get_cpu_usage()[0] if delta_time > 0 else 0
        elif resource_type == 'memory':
            return current_value - self._get_memory_usage()
        return 0
    
    def start_timer_cpu(self, label: str):
        ''' 起始 - 统计一段代码的cpu时间 '''
        self.start_cpu_usage['label'] = self._get_cpu_usage()

    def end_timer_cpu(self, label: str) -> Optional[str]:
        ''' 结束 - 统计一段代码的cpu时间 '''
        current_cpu_usage = self._get_cpu_usage()
        start_cpu_usage = self.start_cpu_usage.pop(label, (0, 0))
        diff_cpu_usage = (current_cpu_usage[0] - start_cpu_usage[0], current_cpu_usage[1] - start_cpu_usage[1])
        return f"{diff_cpu_usage[0]:.2f}s user, {diff_cpu_usage[1]:.2f}s system"
    
    def log_memory_usage(self, message: str = '') -> str:
        process = psutil.Process()
        mem_info = process.memory_info()
        self.log(f"{message} - Memory usage: {mem_info.rss / 10**6:.2f}MB")
        return f"{mem_info.rss / 10**6:.2f}MB"

    def log_cpu_usage(self, message: str = '') -> str:
        process = psutil.Process()
        cpu_times = process.cpu_times()
        self.log(f"{message} - CPU usage: {cpu_times.user:.2f}s user, {cpu_times.system:.2f}s system")
        return f"{cpu_times.user:.2f}s user, {cpu_times.system:.2f}s system"

    def start_timer(self, label):
        self.start_times[label] = time.time()

    def end_timer(self, label) -> Optional[str]:
        end_time = time.time()
        elapsed_time = end_time - self.start_times.pop(label, end_time)
        self.log(f"{label} took {elapsed_time:.2f} seconds")
        return self._to_time_format(elapsed_time)
        
    def _to_time_format(self, value) -> Optional[str]:
        """将秒数转换为xx:xx:xx格式，或CPU使用率转换为时间表示"""
        if value is None:
            return None
        hours, remainder = divmod(int(value), 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        
if __name__ == '__main__':
    print(BaseObj().gen_ordered_uid())
    rm = ResMonitor()
    rm.start_timer("test")
    rm.end_timer("test")
    rm.start_timer("test2")
    rm.end_timer("test2")
    rm.start_timer("test3")
    rm.end_timer("test3")
    rm.log_memory_usage("test")