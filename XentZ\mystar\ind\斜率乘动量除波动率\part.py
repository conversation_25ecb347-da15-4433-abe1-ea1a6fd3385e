
#!/usr/bin/env python
# -*- coding:utf-8 -*-

from hikyuu import *

author = "admin"
version = "20250418"

def part(se: Indicator = CLOSE(), period: int = 63, momentum_window: int = 21) -> Indicator:
    """
    趋势评分指标：(年化收益率 × 动量) / 波动率
    
    通过计算价格的线性回归,结合动量和波动率,综合评估趋势的强度和方向。
    指标值越高表示上升趋势越强,值越低表示下降趋势越强。
    
    计算方法:
    1. 对收盘价取对数,进行线性回归计算斜率
    2. 计算斜率的年化收益率
    3. 计算动量(过去n天的收益率)
    4. 计算波动率(年化)
    5. 将年化收益率与动量相乘后除以波动率得到趋势评分
    
    参数:
        se: 输入数据,默认为收盘价
        period: 计算窗口长度,默认63天
        momentum_days: 动量计算天数,默认21天
    """
    # ================== 1. 计算斜率/R2 ==================
    log_c = LN(se)
    slope = SLOPE(log_c, period)
    Y_Avg = MA(log_c, period)
    X_Sum_Sq = (POW(period, 3) - period) / 12
    ESS = POW(slope, 2) * X_Sum_Sq
    TSS = SUM(POW(log_c - Y_Avg, 2), period)
    r_squared = IF(TSS > 0, ESS / TSS, 0)
    
    # ================== 2. 计算斜率的年化收益率 ==================
    daily_growth = EXP(slope) - 1
    annualized_returns = POW(1 + daily_growth, 252) - 1
    
    # ================== 3. 计算动量 ==================
    momentum = ROCP(se, momentum_window)
    
    # ================== 4. 计算波动率 ==================
    # 计算对数收益率
    ret = log_c - REF(log_c, 1)
    # 计算年化波动率
    volatility = STD(ret, period) * SQRT(252)

    # ================== 6. 计算风险调整收益率 ==================
    risk_adjusted_return = IF(volatility == 0, 0, annualized_returns / (volatility + 0.000001))
        
  # ================== 7. 计算最终趋势评分 ==================
    score = (risk_adjusted_return * r_squared) + (0.3 * momentum)
    score = (score - MA(score, period)) / STDEV(score, period) # zscore
    # 处理初始值
    result = DISCARD(score, period)
    result.name = "斜率乘动量除波动率"
    
    return result
    

if __name__ == "__main__":
    # 执行 testall 命令时，会多传入一个参数，防止测试时间过长
    # 比如如果在测试代码中执行了绘图操作，可以打开下面的注释代码
    # 此时执行 testall 命令时，将直接返回
    if len(sys.argv) > 1:
        ind = part()
        print(ind)
        exit(0)

    import sys
    if sys.platform == 'win32':
        import os
        os.system('chcp 65001')

    # 仅加载测试需要的数据，请根据需要修改
    options = {
        'stock_list': ['sz000001'],
        'ktype_list': ['day'],
        'preload_num': {'day_max': 100000},
        'load_history_finance': False,
        'load_weight': False,
        'start_spot': False,
        'spot_worker_num': 1,
    }
    load_hikyuu(**options)
    
    stks = tuple([sm[code] for code in options['stock_list']])
    
    # 请在下方编写测试代码
    ind = part(period=63)
    k = stks[0].get_kdata(Query(-3000))
    # ZSCORE(ind(k),True).plot()
    ind(k).plot()
    # 显示图形
    import matplotlib.pylab as plt
    plt.show()    
