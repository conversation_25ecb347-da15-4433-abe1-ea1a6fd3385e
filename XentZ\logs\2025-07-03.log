2025-07-03 00:36 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-07-03 00:36 | ERROR    | BaseObj.calc_expr_df_all: ts_delay(close, -1) / close - 1错误: Traceback (most recent call last):
  File "D:\myquant\XentZ\datafeed\expr_funcs\expr.py", line 22, in calc_expr
    se = eval(expr)
         ^^^^^^^^^^
  File "<string>", line 1, in <module>
NameError: name 'ts_delay' is not defined. Did you mean: 'ts_elu1'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\myquant\XentZ\datafeed\features\feature_utils.py", line 51, in calc_expr_df_all
    se = calc_expr(df, field)
         ^^^^^^^^^^^^^^^^^^^^
  File "D:\myquant\XentZ\datafeed\expr_funcs\expr.py", line 32, in calc_expr
    raise NameError('{}——eval异常'.format(expr))
NameError: ts_delay(df["close"], -1) / df["close"] - 1——eval异常

2025-07-03 00:36 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 00:36 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-07-03 00:36 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-07-03 00:36 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 00:36 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-07-03 00:36 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-07-03 00:36 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 00:36 | DEBUG    | BaseObj.normalize_df_all:   标签列(0): []
2025-07-03 00:36 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-07-03 00:36 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-07-03 00:36 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-07-03 00:36 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['tr_ma10', 'OPEN0', 'CLOSE4', 'R_3', 'STD20', 'R_4', 'HIGH3', 'JZ014_troc_mul3_9', 'BETA5', 'JZ008_18']...
2025-07-03 00:36 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 00:36 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 174 (基础7+归一化2+保持0+标签0+筛选后特征165)
2025-07-03 00:36 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 174
2025-07-03 00:36 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-07-03 00:36 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 171 列(不含品种列)
2025-07-03 00:36 | WARNING  | FeatSelection.corr_filter: 未找到label_开头的目标列
2025-07-03 00:36 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 171 个特征中保留了 83 个特征 (base2keep: 4, 冗余筛选: 79, 相关性阈值=0.85, 聚类数=80)
2025-07-03 00:36 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-07-03 00:36 | WARNING  | FeatSelection.xgb_importance_analysis: [SH510050] 未找到目标列
2025-07-03 00:36 | WARNING  | BaseObj.plot_importance_comparison: 未找到重要性数据
2025-07-03 00:36 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: reports\features\complete_analysis.png
2025-07-03 00:36 | WARNING  | BaseObj.save_importance_table: 没有有效的重要性数据可保存
2025-07-03 00:44 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-07-03 00:44 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 00:44 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-07-03 00:44 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-07-03 00:44 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 00:44 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-07-03 00:44 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-07-03 00:44 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 00:44 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 00:44 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-07-03 00:44 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-07-03 00:44 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-07-03 00:44 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['MA10', 'QTLD10', 'JZ014_trends_mul2_21', 'CLOSE1', 'JZ014_troc_mul2_9', 'BETA30', 'JZ011_34', 'V_1', 'JZ014_trends_mul3_55', 'JZ014_troc_mul3_14']...
2025-07-03 00:44 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 00:44 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-07-03 00:44 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-07-03 00:44 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-07-03 00:44 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-07-03 00:44 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-07-03 00:44 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-07-03 00:44 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-07-03 00:44 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征70
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(70个)，跳过相关性过滤
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 70), y形状: (1043,)
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 58
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-07-03 00:44 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=12, 饱和法=11, 平坦法=6 -> 选择=11
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=11, 最大R²=0.2606
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 58
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-07-03 00:44 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=10, 饱和法=9, 平坦法=9 -> 选择=9
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=9, 最大R²=0.2515
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 58
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-07-03 00:44 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=7, 饱和法=6, 平坦法=6 -> 选择=6
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2528
2025-07-03 00:44 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-07-03 00:44 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-07-03 00:44 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: reports\features\importance_comparison.png
2025-07-03 00:44 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: reports\features\marginal_r2_gain.png
2025-07-03 00:44 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: reports\features\marginal_r2_weight.png
2025-07-03 00:44 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: reports\features\marginal_r2_cover.png
2025-07-03 00:44 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: reports\features\complete_analysis.png
2025-07-03 00:44 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: reports\features\importance_tables.csv
2025-07-03 00:57 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-07-03 00:57 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 00:57 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-07-03 00:57 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-07-03 00:57 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 00:57 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-07-03 00:57 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-07-03 00:57 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 00:57 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 00:57 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-07-03 00:57 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-07-03 00:57 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-07-03 00:57 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['R_5', 'JZ008_55', 'JZ014_trends_mul3_34', 'JZ014_trends_mul2_55', 'OPEN1', 'JZ011_14', 'R_2', 'JZ014_troc_mul3_21', 'JZ011_55', 'VSUMP_60']...
2025-07-03 00:57 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 00:57 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-07-03 00:57 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-07-03 00:57 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-07-03 00:57 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-07-03 00:57 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-07-03 00:57 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-07-03 00:57 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-07-03 00:57 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征70
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(70个)，跳过相关性过滤
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 70), y形状: (1043,)
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 58
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-07-03 00:57 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=12, 饱和法=11, 平坦法=6 -> 选择=11
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=11, 最大R²=0.2606
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 58
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-07-03 00:57 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=18, 饱和法=31, 平坦法=11 -> 选择=18
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=18, 最大R²=0.2527
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 58
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-07-03 00:57 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=7, 饱和法=6, 平坦法=6 -> 选择=6
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2528
2025-07-03 00:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-07-03 00:57 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-07-03 00:57 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\features\importance_comparison.png
2025-07-03 00:57 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_gain.png
2025-07-03 00:57 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_weight.png
2025-07-03 00:57 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_cover.png
2025-07-03 00:57 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\features\complete_analysis.png
2025-07-03 00:57 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\features\importance_tables.csv
2025-07-03 00:58 | INFO     | FeatSelection.select_features: 选择特征: 10个 + base2keep: 4个 + 标签: 1个
2025-07-03 10:41 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-07-03 10:42 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 10:42 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-07-03 10:42 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-07-03 10:42 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 10:42 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-07-03 10:42 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-07-03 10:42 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 10:42 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 10:42 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-07-03 10:42 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-07-03 10:42 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-07-03 10:42 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['VSUMN_60', 'JZ014_troc_mul3_55', 'STD20', 'JZ014_trends_mul2_34', 'MIN5', 'LOW0', 'R_4', 'V_4', 'QTLD10', 'JZ008_9']...
2025-07-03 10:42 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 10:42 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-07-03 10:42 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-07-03 10:42 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-07-03 10:42 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-07-03 10:42 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-07-03 10:42 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-07-03 10:42 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-07-03 10:42 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征69
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(69个)，跳过相关性过滤
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1045, 69), y形状: (1045,)
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-07-03 10:42 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=7, 饱和法=7, 平坦法=13 -> 选择=7
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=7, 最大R²=0.2526
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-07-03 10:42 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=10, 饱和法=14, 平坦法=7 -> 选择=10
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=10, 最大R²=0.2527
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-07-03 10:42 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=9, 饱和法=8, 平坦法=10 -> 选择=9
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=9, 最大R²=0.2473
2025-07-03 10:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-07-03 10:42 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-07-03 10:42 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\features\importance_comparison.png
2025-07-03 10:42 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_gain.png
2025-07-03 10:42 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_weight.png
2025-07-03 10:42 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_cover.png
2025-07-03 10:42 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\features\complete_analysis.png
2025-07-03 10:42 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\features\importance_tables.csv
2025-07-03 10:44 | INFO     | FeatSelection.select_features: 选择特征: 11个 + base2keep: 4个 + 标签: 1个
2025-07-03 10:50 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-07-03 10:50 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 10:50 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-07-03 10:50 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-07-03 10:50 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 10:50 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-07-03 10:50 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-07-03 10:50 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 10:50 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 10:50 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-07-03 10:50 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-07-03 10:50 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-07-03 10:50 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['VSUMP_60', 'JZ012_34', 'HIGH1', 'JZ014_trends_mul3_34', 'JZ004_30', 'BETA5', 'MIN5', 'KSFT', 'JZ011_9', 'KLOW']...
2025-07-03 10:50 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 10:50 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-07-03 10:50 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-07-03 10:50 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-07-03 10:50 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-07-03 10:50 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-07-03 10:50 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-07-03 10:50 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-07-03 10:50 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征69
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(69个)，跳过相关性过滤
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1045, 69), y形状: (1045,)
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-07-03 10:50 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=7, 饱和法=7, 平坦法=13 -> 选择=7
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=7, 最大R²=0.2526
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-07-03 10:50 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=18, 饱和法=17, 平坦法=7 -> 选择=17
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=17, 最大R²=0.2524
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-07-03 10:50 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=9, 饱和法=8, 平坦法=10 -> 选择=9
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=9, 最大R²=0.2473
2025-07-03 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-07-03 10:50 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-07-03 10:50 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\features\importance_comparison.png
2025-07-03 10:50 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_gain.png
2025-07-03 10:50 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_weight.png
2025-07-03 10:50 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_cover.png
2025-07-03 10:50 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\features\complete_analysis.png
2025-07-03 10:50 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\features\importance_tables.csv
2025-07-03 11:27 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-07-03 11:27 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 11:27 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-07-03 11:27 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-07-03 11:27 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 11:27 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-07-03 11:27 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-07-03 11:27 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 11:27 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 11:27 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-07-03 11:27 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-07-03 11:27 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-07-03 11:27 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['HIGH3', 'LOW3', 'smadiff5', 'QTLU5', 'KLEN', 'roc_02', 'STD5', 'JZ014_trends_mul3_34', 'JZ004_58', 'JZ014_trends_mul2_55']...
2025-07-03 11:27 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 11:27 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-07-03 11:27 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-07-03 11:27 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-07-03 11:27 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-07-03 11:27 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-07-03 11:27 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-07-03 11:27 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-07-03 11:27 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征69
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(69个)，跳过相关性过滤
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1045, 69), y形状: (1045,)
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-07-03 11:27 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=7, 饱和法=7, 平坦法=13 -> 选择=7
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=7, 最大R²=0.2526
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-07-03 11:27 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=15, 饱和法=14, 平坦法=16 -> 选择=15
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=15, 最大R²=0.2543
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-07-03 11:27 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=9, 饱和法=8, 平坦法=10 -> 选择=9
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=9, 最大R²=0.2473
2025-07-03 11:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-07-03 11:27 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-07-03 11:27 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\features\importance_comparison.png
2025-07-03 11:27 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_gain.png
2025-07-03 11:27 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_weight.png
2025-07-03 11:27 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_cover.png
2025-07-03 11:27 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\features\complete_analysis.png
2025-07-03 11:27 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\features\importance_tables.csv
2025-07-03 11:28 | INFO     | FeatSelection.select_features: 选择特征: 10个 + base2keep: 4个 + 标签: 1个
2025-07-03 11:33 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-07-03 11:33 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 11:33 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-07-03 11:33 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-07-03 11:33 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 11:33 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-07-03 11:33 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-07-03 11:33 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 11:33 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 11:33 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-07-03 11:33 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-07-03 11:33 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-07-03 11:33 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['MA10', 'LOW2', 'roc_05', 'STD60', 'JZ004_120', 'KMID', 'JZ014_trends_mul2_55', 'MAX10', 'BETA30', 'JZ004_6']...
2025-07-03 11:33 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 11:33 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-07-03 11:33 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-07-03 11:33 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-07-03 11:33 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-07-03 11:33 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-07-03 11:33 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-07-03 11:33 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-07-03 11:33 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-07-03 11:35 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-07-03 11:35 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 11:35 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-07-03 11:35 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-07-03 11:35 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 11:35 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-07-03 11:35 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-07-03 11:35 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 11:35 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 11:35 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-07-03 11:35 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-07-03 11:35 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-07-03 11:35 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ004_30', 'JZ004_120', 'CLOSE3', 'b_atr_60', 'V_4', 'R_4', 'BETA30', 'JZ004_20', 'HIGH2', 'JZ008_18']...
2025-07-03 11:35 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 11:35 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-07-03 11:35 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-07-03 11:35 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-07-03 11:35 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-07-03 11:35 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-07-03 11:35 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-07-03 11:35 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-07-03 11:35 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征69
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(69个)，跳过相关性过滤
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1045, 69), y形状: (1045,)
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-07-03 11:35 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=7, 饱和法=7, 平坦法=13 -> 选择=7
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=7, 最大R²=0.2526
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-07-03 11:35 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=10, 饱和法=23, 平坦法=7 -> 选择=10
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=10, 最大R²=0.2527
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-07-03 11:35 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=9, 饱和法=8, 平坦法=10 -> 选择=9
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=9, 最大R²=0.2487
2025-07-03 11:35 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-07-03 11:35 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-07-03 11:35 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\features\importance_comparison.png
2025-07-03 11:35 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_gain.png
2025-07-03 11:35 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_weight.png
2025-07-03 11:36 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_cover.png
2025-07-03 11:36 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\features\complete_analysis.png
2025-07-03 11:36 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\features\importance_tables.csv
2025-07-03 11:39 | INFO     | FeatSelection.select_features: 选择特征: 15个 + base2keep: 4个 + 标签: 1个
2025-07-03 11:41 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-07-03 11:41 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 11:41 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-07-03 11:41 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-07-03 11:41 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 11:41 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-07-03 11:41 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-07-03 11:41 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 11:41 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 11:41 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-07-03 11:41 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-07-03 11:41 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-07-03 11:41 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['OPEN2', 'JZ008_18', 'b_macd_hist', 'KLEN', 'JZ014_trends_mul3_55', 'R_0', 'R_5', 'JZ014_troc_mul2_14', 'JZ004_10', 'HIGH1']...
2025-07-03 11:41 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 11:41 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-07-03 11:41 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-07-03 11:41 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-07-03 11:41 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-07-03 11:42 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-07-03 11:42 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-07-03 11:42 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-07-03 11:42 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征69
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(69个)，跳过相关性过滤
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1045, 69), y形状: (1045,)
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-07-03 11:42 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=7, 饱和法=7, 平坦法=13 -> 选择=7
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=7, 最大R²=0.2526
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-07-03 11:42 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=28, 饱和法=27, 平坦法=30 -> 选择=28
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=28, 最大R²=0.2557
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-07-03 11:42 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=9, 饱和法=8, 平坦法=10 -> 选择=9
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=9, 最大R²=0.2487
2025-07-03 11:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-07-03 11:42 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-07-03 11:42 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\features\importance_comparison.png
2025-07-03 11:42 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_gain.png
2025-07-03 11:42 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_weight.png
2025-07-03 11:42 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_cover.png
2025-07-03 11:42 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\features\complete_analysis.png
2025-07-03 11:42 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\features\importance_tables.csv
2025-07-03 11:42 | INFO     | FeatSelection.select_features: 选择特征: 10个 + base2keep: 4个 + 标签: 1个
2025-07-03 11:52 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-07-03 11:52 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 11:52 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-07-03 11:52 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-07-03 11:52 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 11:52 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-07-03 11:52 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-07-03 11:52 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 11:52 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 11:52 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-07-03 11:52 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-07-03 11:52 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-07-03 11:52 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['R_0', 'HIGH2', 'LOW2', 'JZ004_6', 'STD30', 'JZ014_trends_mul3_55', 'JZ014_troc_mul2_34', 'b_atr_14', 'MAX10', 'smadiff5']...
2025-07-03 11:52 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 11:52 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-07-03 11:52 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-07-03 11:52 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-07-03 11:52 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-07-03 11:52 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-07-03 11:52 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-07-03 11:52 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-07-03 11:52 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征69
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(69个)，跳过相关性过滤
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1045, 69), y形状: (1045,)
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-07-03 11:52 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=7, 饱和法=7, 平坦法=13 -> 选择=7
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=7, 最大R²=0.2526
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-07-03 11:52 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=15, 饱和法=14, 平坦法=7 -> 选择=14
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=14, 最大R²=0.2552
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-07-03 11:52 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=9, 饱和法=8, 平坦法=10 -> 选择=9
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=9, 最大R²=0.2473
2025-07-03 11:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-07-03 11:52 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-07-03 11:52 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\features\importance_comparison.png
2025-07-03 11:52 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_gain.png
2025-07-03 11:52 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_weight.png
2025-07-03 11:53 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_cover.png
2025-07-03 11:53 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\features\complete_analysis.png
2025-07-03 11:53 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\features\importance_tables.csv
2025-07-03 11:53 | INFO     | FeatSelection.select_features: 选择特征: 10个 + base2keep: 4个 + 标签: 1个
2025-07-03 11:53 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-07-03 11:53 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 11:53 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-07-03 11:53 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-07-03 11:53 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 11:53 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-07-03 11:53 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-07-03 11:53 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 11:53 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 11:53 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-07-03 11:53 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-07-03 11:53 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-07-03 11:53 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ014_troc_mul3_55', 'JZ014_troc_mul2_21', 'smadiff10', 'JZ004_98', 'JZ014_trends_mul2_34', 'JZ011_21', 'JZ008_89', 'b_macd_wt_hist', 'JZ014_trends_mul3_34', 'R_6']...
2025-07-03 11:53 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 11:53 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-07-03 11:53 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-07-03 11:53 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-07-03 11:53 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-07-03 11:53 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-07-03 11:53 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-07-03 11:53 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-07-03 11:53 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-07-03 11:53 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征69
2025-07-03 11:53 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(69个)，跳过相关性过滤
2025-07-03 11:53 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-07-03 11:53 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1045, 69), y形状: (1045,)
2025-07-03 11:53 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-07-03 11:53 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-07-03 11:53 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-07-03 11:53 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-07-03 11:53 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-07-03 11:53 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 11:53 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-07-03 11:53 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=7, 饱和法=7, 平坦法=13 -> 选择=7
2025-07-03 11:53 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=7, 最大R²=0.2526
2025-07-03 11:53 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-07-03 11:53 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-07-03 11:53 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-07-03 11:53 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-07-03 11:53 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 11:53 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-07-03 11:54 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=17, 饱和法=16, 平坦法=7 -> 选择=16
2025-07-03 11:54 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=16, 最大R²=0.2539
2025-07-03 11:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-07-03 11:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-07-03 11:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-07-03 11:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-07-03 11:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 11:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-07-03 11:54 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=9, 饱和法=8, 平坦法=10 -> 选择=9
2025-07-03 11:54 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=9, 最大R²=0.2487
2025-07-03 11:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-07-03 11:54 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-07-03 11:54 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\features\importance_comparison.png
2025-07-03 11:54 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_gain.png
2025-07-03 11:54 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_weight.png
2025-07-03 11:54 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_cover.png
2025-07-03 11:54 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\features\complete_analysis.png
2025-07-03 11:54 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\features\importance_tables.csv
2025-07-03 11:54 | INFO     | FeatSelection.select_features: 选择特征: 14个 + base2keep: 4个 + 标签: 1个
2025-07-03 11:55 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-07-03 11:55 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 11:55 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-07-03 11:55 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-07-03 11:55 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 11:55 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-07-03 11:55 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-07-03 11:55 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 11:55 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 11:55 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-07-03 11:55 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-07-03 11:55 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-07-03 11:55 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['b_macd_wt_hist', 'QTLU10', 'JZ008_89', 'OPEN3', 'OPEN1', 'JZ012_60', 'tr_ma20', 'STD10', 'KSFT', 'JZ004_20']...
2025-07-03 11:55 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 11:55 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-07-03 11:55 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-07-03 11:55 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-07-03 11:55 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-07-03 11:55 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-07-03 11:55 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-07-03 11:55 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-07-03 11:55 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征69
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(69个)，跳过相关性过滤
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1045, 69), y形状: (1045,)
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-07-03 11:55 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=7, 饱和法=7, 平坦法=13 -> 选择=7
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=7, 最大R²=0.2526
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-07-03 11:55 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=13, 饱和法=18, 平坦法=17 -> 选择=17
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=17, 最大R²=0.2587
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-07-03 11:55 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=9, 饱和法=8, 平坦法=10 -> 选择=9
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=9, 最大R²=0.2487
2025-07-03 11:55 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-07-03 11:55 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-07-03 11:55 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\features\importance_comparison.png
2025-07-03 11:55 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_gain.png
2025-07-03 11:55 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_weight.png
2025-07-03 11:55 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_cover.png
2025-07-03 11:55 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\features\complete_analysis.png
2025-07-03 11:55 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\features\importance_tables.csv
2025-07-03 11:55 | INFO     | FeatSelection.select_features: 选择特征: 16个 + base2keep: 4个 + 标签: 1个
2025-07-03 12:11 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-07-03 12:11 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 12:11 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-07-03 12:11 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-07-03 12:11 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 12:11 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-07-03 12:11 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-07-03 12:11 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 12:11 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 12:11 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-07-03 12:11 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-07-03 12:11 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-07-03 12:11 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ014_troc_mul2_14', 'R_0', 'LOW1', 'HIGH3', 'JZ008_9', 'JZ011_9', 'V_2', 'tr_index', 'JZ014_troc_mul2_9', 'tr_ma5']...
2025-07-03 12:11 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 12:11 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-07-03 12:11 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-07-03 12:11 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-07-03 12:11 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-07-03 12:11 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-07-03 12:11 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-07-03 12:11 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-07-03 12:11 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征69
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(69个)，跳过相关性过滤
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1045, 69), y形状: (1045,)
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-07-03 12:11 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=7, 饱和法=7, 平坦法=13 -> 选择=7
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=7, 最大R²=0.2526
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-07-03 12:11 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=10, 饱和法=15, 平坦法=7 -> 选择=10
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=10, 最大R²=0.2524
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 53
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-07-03 12:11 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=9, 饱和法=8, 平坦法=10 -> 选择=9
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=9, 最大R²=0.2473
2025-07-03 12:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-07-03 12:11 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-07-03 12:11 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\features\importance_comparison.png
2025-07-03 12:11 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_gain.png
2025-07-03 12:11 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_weight.png
2025-07-03 12:11 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\features\marginal_r2_cover.png
2025-07-03 12:11 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\features\complete_analysis.png
2025-07-03 12:11 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\features\importance_tables.csv
2025-07-03 12:11 | INFO     | FeatSelection.select_features: 选择特征: 10个 + base2keep: 4个 + 标签: 1个
2025-07-03 14:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:03 | INFO     | BaseObj.<module>: 🚀 开始因子挖掘流程...
2025-07-03 14:03 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:03 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250703_L0_982897 took 0.10 seconds (00:00:00)
2025-07-03 14:03 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250703_L0_982897 CPU usage: 0.19s user, 0.05s system
2025-07-03 14:03 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250703_L0_982897 memory delta: 6.77MB
2025-07-03 14:03 | ERROR    | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250703_L0_982897 failed with error: None
2025-07-03 14:03 | INFO     | ResMonitor.end_timer: global_process took 0.12 seconds (00:00:00)
2025-07-03 14:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:31 | INFO     | BaseObj.<module>: 🚀 开始因子挖掘流程...
2025-07-03 14:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:31 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 14:31 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-07-03 14:31 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-07-03 14:31 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 14:31 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-07-03 14:31 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-07-03 14:31 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 14:31 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 14:31 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(10): 10个
2025-07-03 14:31 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 10 列
2025-07-03 14:31 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-07-03 14:31 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 14:31 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 16 (基础5+归一化0+保持0+标签1+筛选后特征10)
2025-07-03 14:31 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 16
2025-07-03 14:31 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-07-03 14:31 | INFO     | BaseObj.<module>: 📋 发现 1 个预测目标: ['label_1']
2025-07-03 14:31 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250703_L0_961147 took 0.23 seconds (00:00:00)
2025-07-03 14:31 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250703_L0_961147 CPU usage: 0.25s user, 0.08s system
2025-07-03 14:31 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250703_L0_961147 memory delta: 13.05MB
2025-07-03 14:31 | ERROR    | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250703_L0_961147 failed with error: "'DynaBox' object has no attribute 'runnum'"
2025-07-03 14:31 | INFO     | ResMonitor.end_timer: global_process took 0.24 seconds (00:00:00)
2025-07-03 14:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:38 | INFO     | BaseObj.<module>: 🚀 开始因子挖掘流程...
2025-07-03 14:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:38 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 14:38 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-07-03 14:38 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-07-03 14:38 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 14:38 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-07-03 14:38 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-07-03 14:38 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 14:38 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 14:38 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(10): 10个
2025-07-03 14:38 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 10 列
2025-07-03 14:38 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-07-03 14:38 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 14:38 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 16 (基础5+归一化0+保持0+标签1+筛选后特征10)
2025-07-03 14:38 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 16
2025-07-03 14:38 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-07-03 14:38 | INFO     | BaseObj.<module>: 📋 发现 1 个预测目标: ['label_1']
2025-07-03 14:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:38 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-07-03 14:38 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-03 14:38 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.03s user, 0.00s system
2025-07-03 14:38 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 1.45MB
2025-07-03 14:38 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: init_depth should be a tuple with length two.
2025-07-03 14:38 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250703_L0_388998 took 0.26 seconds (00:00:00)
2025-07-03 14:38 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250703_L0_388998 CPU usage: 0.34s user, 0.14s system
2025-07-03 14:38 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250703_L0_388998 memory delta: 16.46MB
2025-07-03 14:38 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250703_L0_388998 completed successfully
2025-07-03 14:38 | INFO     | ResMonitor.end_timer: global_process took 0.28 seconds (00:00:00)
2025-07-03 14:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:42 | INFO     | BaseObj.<module>: 🚀 开始因子挖掘流程...
2025-07-03 14:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:42 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 14:42 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-07-03 14:42 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-07-03 14:42 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 14:42 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-07-03 14:42 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-07-03 14:42 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 14:42 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 14:42 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(10): 10个
2025-07-03 14:42 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 10 列
2025-07-03 14:42 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-07-03 14:42 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 14:42 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 16 (基础5+归一化0+保持0+标签1+筛选后特征10)
2025-07-03 14:42 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 16
2025-07-03 14:42 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-07-03 14:42 | INFO     | BaseObj.<module>: 📋 发现 1 个预测目标: ['label_1']
2025-07-03 14:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:42 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-07-03 14:42 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 12.51 seconds (00:00:12)
2025-07-03 14:42 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 7.34s user, 5.36s system
2025-07-03 14:42 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 525.53MB
2025-07-03 14:42 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-07-03 14:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:42 | INFO     | FactorLoader.get_fct_df: 开始计算5个因子表达式
2025-07-03 14:42 | INFO     | FactorLoader.get_fct_df: 返回结果: 5个指定因子
2025-07-03 14:42 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.03 seconds (00:00:00)
2025-07-03 14:42 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-07-03 14:42 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 0.08MB
2025-07-03 14:42 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-07-03 14:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:42 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-03 14:42 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.03s user, 0.00s system
2025-07-03 14:42 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 1.73MB
2025-07-03 14:42 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-07-03 14:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:42 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-03 14:42 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.00s user, 0.02s system
2025-07-03 14:42 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: -0.95MB
2025-07-03 14:42 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-07-03 14:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:43 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 31.05 seconds (00:00:31)
2025-07-03 14:43 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 29.70s user, 16.84s system
2025-07-03 14:43 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 716.41MB
2025-07-03 14:43 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-07-03 14:43 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:43 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-03 14:43 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-03 14:43 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-07-03 14:43 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.12MB
2025-07-03 14:43 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-07-03 14:43 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250703_L0_907231 took 43.98 seconds (00:00:43)
2025-07-03 14:43 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250703_L0_907231 CPU usage: 37.47s user, 22.38s system
2025-07-03 14:43 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250703_L0_907231 memory delta: 1258.94MB
2025-07-03 14:43 | ERROR    | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250703_L0_907231 failed with error: Cannot save file into a non-existent directory: 'D:\myquant\reports\XentZ\gplearn'
2025-07-03 14:43 | INFO     | ResMonitor.end_timer: global_process took 43.99 seconds (00:00:43)
2025-07-03 14:45 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:45 | INFO     | BaseObj.<module>: 🚀 开始因子挖掘流程...
2025-07-03 14:45 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:45 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 14:45 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-07-03 14:45 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-07-03 14:45 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 14:45 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-07-03 14:45 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-07-03 14:45 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 14:45 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 14:45 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(10): 10个
2025-07-03 14:45 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 10 列
2025-07-03 14:45 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-07-03 14:45 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 14:45 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 16 (基础5+归一化0+保持0+标签1+筛选后特征10)
2025-07-03 14:45 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 16
2025-07-03 14:45 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-07-03 14:45 | INFO     | BaseObj.<module>: 📋 发现 1 个预测目标: ['label_1']
2025-07-03 14:45 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:45 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-07-03 14:45 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 8.93 seconds (00:00:08)
2025-07-03 14:45 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.59s user, 0.11s system
2025-07-03 14:45 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 2.85MB
2025-07-03 14:45 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-07-03 14:45 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:45 | INFO     | FactorLoader.get_fct_df: 开始计算5个因子表达式
2025-07-03 14:45 | INFO     | FactorLoader.get_fct_df: 返回结果: 5个指定因子
2025-07-03 14:45 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.02 seconds (00:00:00)
2025-07-03 14:45 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.03s user, 0.00s system
2025-07-03 14:45 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 0.55MB
2025-07-03 14:45 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-07-03 14:45 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:45 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-03 14:45 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-07-03 14:45 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 0.03MB
2025-07-03 14:45 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-07-03 14:45 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:45 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-03 14:45 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-07-03 14:45 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: 0.00MB
2025-07-03 14:45 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-07-03 14:45 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:45 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 24.76 seconds (00:00:24)
2025-07-03 14:45 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 22.52s user, 1.05s system
2025-07-03 14:45 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 108.10MB
2025-07-03 14:45 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-07-03 14:45 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:45 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-03 14:45 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-03 14:45 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-07-03 14:45 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.05MB
2025-07-03 14:45 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-07-03 14:45 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-03 14:45 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-03 14:45 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:45 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-07-03 14:45 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-07-03 14:45 | INFO     | ResMonitor.end_timer: CALC_F_VALUES_SH510050 took 0.01 seconds (00:00:00)
2025-07-03 14:45 | INFO     | ResMonitor.end_timer_cpu: CALC_F_VALUES_SH510050 CPU usage: 0.02s user, 0.02s system
2025-07-03 14:45 | INFO     | ResMonitor.end_memory_monitor: CALC_F_VALUES_SH510050 memory delta: 0.07MB
2025-07-03 14:45 | INFO     | FactorMonitorContext.__exit__: CALC_F_VALUES_SH510050 completed successfully
2025-07-03 14:45 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:45 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_SH510050_20250703_L0_766338
2025-07-03 14:45 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_SH510050_20250703_L0_766338 耗时145.2ms
2025-07-03 14:45 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.15 seconds (00:00:00)
2025-07-03 14:45 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.03s user, 0.05s system
2025-07-03 14:45 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 5.05MB
2025-07-03 14:45 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-07-03 14:45 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:45 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250703_L0_766338_label_1_001 took 0.01 seconds (00:00:00)
2025-07-03 14:45 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250703_L0_766338_label_1_001 CPU usage: 0.02s user, 0.00s system
2025-07-03 14:45 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250703_L0_766338_label_1_001 memory delta: 0.02MB
2025-07-03 14:45 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250703_L0_766338_label_1_001 completed successfully
2025-07-03 14:45 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250703_L0_766338 took 34.39 seconds (00:00:34)
2025-07-03 14:45 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250703_L0_766338 CPU usage: 23.53s user, 1.27s system
2025-07-03 14:45 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250703_L0_766338 memory delta: 122.59MB
2025-07-03 14:45 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250703_L0_766338 completed successfully
2025-07-03 14:45 | INFO     | ResMonitor.end_timer: global_process took 34.41 seconds (00:00:34)
2025-07-03 14:56 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:56 | INFO     | BaseObj.<module>: 🚀 开始因子挖掘流程...
2025-07-03 14:56 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:56 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 14:56 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-07-03 14:56 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-07-03 14:56 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 14:56 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-07-03 14:56 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-07-03 14:56 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 14:56 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 14:56 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(10): 10个
2025-07-03 14:56 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 10 列
2025-07-03 14:56 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-07-03 14:56 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 14:56 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 16 (基础5+归一化0+保持0+标签1+筛选后特征10)
2025-07-03 14:56 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 16
2025-07-03 14:56 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-07-03 14:56 | INFO     | BaseObj.<module>: 📋 发现 1 个预测目标: ['label_1']
2025-07-03 14:56 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:56 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-07-03 14:56 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 8.53 seconds (00:00:08)
2025-07-03 14:56 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.67s user, 0.11s system
2025-07-03 14:56 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 4.96MB
2025-07-03 14:56 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-07-03 14:56 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:56 | INFO     | FactorLoader.get_fct_df: 开始计算5个因子表达式
2025-07-03 14:56 | INFO     | FactorLoader.get_fct_df: 返回结果: 5个指定因子
2025-07-03 14:56 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.02 seconds (00:00:00)
2025-07-03 14:56 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-07-03 14:56 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 0.02MB
2025-07-03 14:56 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-07-03 14:56 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:56 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-03 14:56 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-07-03 14:56 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 0.03MB
2025-07-03 14:56 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-07-03 14:56 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:56 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-03 14:56 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-07-03 14:56 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: 0.00MB
2025-07-03 14:56 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-07-03 14:56 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:56 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 26.81 seconds (00:00:26)
2025-07-03 14:56 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 22.95s user, 1.47s system
2025-07-03 14:56 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 107.42MB
2025-07-03 14:56 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-07-03 14:56 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:56 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-03 14:56 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-07-03 14:56 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-07-03 14:56 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.05MB
2025-07-03 14:56 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-07-03 14:56 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-03 14:56 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-03 14:56 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:56 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-07-03 14:56 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-07-03 14:56 | INFO     | ResMonitor.end_timer: CALC_F_VALUES_SH510050 took 0.02 seconds (00:00:00)
2025-07-03 14:56 | INFO     | ResMonitor.end_timer_cpu: CALC_F_VALUES_SH510050 CPU usage: 0.02s user, 0.00s system
2025-07-03 14:56 | INFO     | ResMonitor.end_memory_monitor: CALC_F_VALUES_SH510050 memory delta: 0.02MB
2025-07-03 14:56 | INFO     | FactorMonitorContext.__exit__: CALC_F_VALUES_SH510050 completed successfully
2025-07-03 14:56 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:56 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_SH510050_20250703_L0_037819
2025-07-03 14:56 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_SH510050_20250703_L0_037819 耗时72.3ms
2025-07-03 14:56 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.08 seconds (00:00:00)
2025-07-03 14:56 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.06s user, 0.02s system
2025-07-03 14:56 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 4.94MB
2025-07-03 14:56 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-07-03 14:56 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250703_L0_037819 took 35.85 seconds (00:00:35)
2025-07-03 14:56 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250703_L0_037819 CPU usage: 24.00s user, 1.62s system
2025-07-03 14:56 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250703_L0_037819 memory delta: 122.68MB
2025-07-03 14:56 | ERROR    | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250703_L0_037819 failed with error: name 'factorzoo' is not defined
2025-07-03 14:56 | INFO     | ResMonitor.end_timer: global_process took 35.86 seconds (00:00:35)
2025-07-03 14:57 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:57 | INFO     | BaseObj.<module>: 🚀 开始因子挖掘流程...
2025-07-03 14:57 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:57 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 14:57 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-07-03 14:57 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-07-03 14:57 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 14:57 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-07-03 14:57 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-07-03 14:57 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 14:57 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 14:57 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(10): 10个
2025-07-03 14:57 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 10 列
2025-07-03 14:57 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-07-03 14:57 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 14:57 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 16 (基础5+归一化0+保持0+标签1+筛选后特征10)
2025-07-03 14:57 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 16
2025-07-03 14:57 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-07-03 14:57 | INFO     | BaseObj.<module>: 📋 发现 1 个预测目标: ['label_1']
2025-07-03 14:57 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:57 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-07-03 14:57 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 11.89 seconds (00:00:11)
2025-07-03 14:57 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.69s user, 0.03s system
2025-07-03 14:57 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 5.23MB
2025-07-03 14:57 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-07-03 14:57 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:57 | INFO     | FactorLoader.get_fct_df: 开始计算5个因子表达式
2025-07-03 14:57 | INFO     | FactorLoader.get_fct_df: 返回结果: 5个指定因子
2025-07-03 14:57 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.02 seconds (00:00:00)
2025-07-03 14:57 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-07-03 14:57 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 0.02MB
2025-07-03 14:57 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-07-03 14:57 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:57 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-07-03 14:57 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-07-03 14:57 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 0.03MB
2025-07-03 14:57 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-07-03 14:57 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:57 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-03 14:57 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-07-03 14:57 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: 0.00MB
2025-07-03 14:57 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-07-03 14:57 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:58 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 30.46 seconds (00:00:30)
2025-07-03 14:58 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 23.95s user, 1.45s system
2025-07-03 14:58 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 106.79MB
2025-07-03 14:58 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-07-03 14:58 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:58 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-03 14:58 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-07-03 14:58 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-07-03 14:58 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.05MB
2025-07-03 14:58 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-07-03 14:58 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-03 14:58 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-03 14:58 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:58 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-07-03 14:58 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-07-03 14:58 | INFO     | ResMonitor.end_timer: CALC_F_VALUES_SH510050 took 0.01 seconds (00:00:00)
2025-07-03 14:58 | INFO     | ResMonitor.end_timer_cpu: CALC_F_VALUES_SH510050 CPU usage: 0.00s user, 0.00s system
2025-07-03 14:58 | INFO     | ResMonitor.end_memory_monitor: CALC_F_VALUES_SH510050 memory delta: 0.00MB
2025-07-03 14:58 | INFO     | FactorMonitorContext.__exit__: CALC_F_VALUES_SH510050 completed successfully
2025-07-03 14:58 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:58 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_SH510050_20250703_L0_745831
2025-07-03 14:58 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_SH510050_20250703_L0_745831 耗时54.6ms
2025-07-03 14:58 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.06 seconds (00:00:00)
2025-07-03 14:58 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.06s user, 0.00s system
2025-07-03 14:58 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 4.87MB
2025-07-03 14:58 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-07-03 14:58 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 14:58 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250703_L0_745831_label_1_001 took 0.02 seconds (00:00:00)
2025-07-03 14:58 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250703_L0_745831_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-07-03 14:58 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250703_L0_745831_label_1_001 memory delta: 0.05MB
2025-07-03 14:58 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250703_L0_745831_label_1_001 completed successfully
2025-07-03 14:58 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250703_L0_745831 took 42.91 seconds (00:00:42)
2025-07-03 14:58 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250703_L0_745831 CPU usage: 25.00s user, 1.53s system
2025-07-03 14:58 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250703_L0_745831 memory delta: 122.43MB
2025-07-03 14:58 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250703_L0_745831 completed successfully
2025-07-03 14:58 | INFO     | ResMonitor.end_timer: global_process took 42.92 seconds (00:00:42)
2025-07-03 15:13 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-03 15:13 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-03 15:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 15:13 | INFO     | ResMonitor.end_timer: L1_CORR_FILTER_1751526800216542 took 0.01 seconds (00:00:00)
2025-07-03 15:13 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_FILTER_1751526800216542 CPU usage: 0.00s user, 0.00s system
2025-07-03 15:13 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_FILTER_1751526800216542 memory delta: 0.01MB
2025-07-03 15:13 | ERROR    | MonitorContext.__exit__: L1_CORR_FILTER_1751526800216542 failed with error: "'DynaBox' object has no attribute 'corr'"
2025-07-03 15:14 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-03 15:14 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-03 15:14 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 15:14 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250703_L0_037819/L0
2025-07-03 15:14 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250703_L0_037819/L0 耗时232.8ms
2025-07-03 15:14 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250703_L0_745831/L0
2025-07-03 15:14 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250703_L0_745831/L0 耗时24.9ms
2025-07-03 15:14 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250703_L0_766338/L0
2025-07-03 15:14 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250703_L0_766338/L0 耗时17.3ms
2025-07-03 15:14 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250630_L0_085450/L0
2025-07-03 15:14 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250630_L0_085450/L0 耗时18.0ms
2025-07-03 15:14 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 15:14 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-03 15:14 | INFO     | ResMonitor.end_timer: L1_CORR_SH510050_label_1 took 0.02 seconds (00:00:00)
2025-07-03 15:14 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_SH510050_label_1 CPU usage: 0.00s user, 0.02s system
2025-07-03 15:14 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_SH510050_label_1 memory delta: 0.31MB
2025-07-03 15:14 | INFO     | FactorMonitorContext.__exit__: L1_CORR_SH510050_label_1 completed successfully
2025-07-03 15:14 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 15:14 | INFO     | ResMonitor.end_timer: PERSIST_L1_SH510050_label_1 took 0.00 seconds (00:00:00)
2025-07-03 15:14 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L1_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-03 15:14 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L1_SH510050_label_1 memory delta: 0.01MB
2025-07-03 15:14 | ERROR    | FactorMonitorContext.__exit__: PERSIST_L1_SH510050_label_1 failed with error: "'DynaBox' object has no attribute 'corr'"
2025-07-03 15:14 | INFO     | ResMonitor.end_timer: L1_CORR_FILTER_1751526859333294 took 0.41 seconds (00:00:00)
2025-07-03 15:14 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_FILTER_1751526859333294 CPU usage: 0.19s user, 0.08s system
2025-07-03 15:14 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_FILTER_1751526859333294 memory delta: 16.18MB
2025-07-03 15:14 | INFO     | MonitorContext.__exit__: L1_CORR_FILTER_1751526859333294 completed successfully
2025-07-03 19:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:10 | INFO     | BaseObj.<module>: 🚀 开始因子挖掘流程...
2025-07-03 19:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:10 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 19:10 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-07-03 19:10 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-07-03 19:10 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 19:10 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-07-03 19:10 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-07-03 19:10 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 19:10 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 19:10 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(10): 10个
2025-07-03 19:10 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 10 列
2025-07-03 19:10 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-07-03 19:10 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 19:10 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 16 (基础5+归一化0+保持0+标签1+筛选后特征10)
2025-07-03 19:10 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 16
2025-07-03 19:10 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-07-03 19:10 | INFO     | BaseObj.<module>: 📋 发现 1 个预测目标: ['label_1']
2025-07-03 19:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:10 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-07-03 19:11 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 13.12 seconds (00:00:13)
2025-07-03 19:11 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.72s user, 0.12s system
2025-07-03 19:11 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 4.75MB
2025-07-03 19:11 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-07-03 19:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:11 | INFO     | FactorLoader.get_fct_df: 开始计算5个因子表达式
2025-07-03 19:11 | INFO     | FactorLoader.get_fct_df: 返回结果: 5个指定因子
2025-07-03 19:11 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.02 seconds (00:00:00)
2025-07-03 19:11 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.03s user, 0.00s system
2025-07-03 19:11 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 0.02MB
2025-07-03 19:11 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-07-03 19:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:11 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-03 19:11 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-07-03 19:11 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 0.03MB
2025-07-03 19:11 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-07-03 19:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:11 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-03 19:11 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-07-03 19:11 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: 0.00MB
2025-07-03 19:11 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-07-03 19:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:11 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 38.27 seconds (00:00:38)
2025-07-03 19:11 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 30.81s user, 1.66s system
2025-07-03 19:11 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 107.73MB
2025-07-03 19:11 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-07-03 19:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:11 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-03 19:11 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.02 seconds (00:00:00)
2025-07-03 19:11 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-07-03 19:11 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.05MB
2025-07-03 19:11 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-07-03 19:11 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-03 19:11 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-03 19:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:11 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-07-03 19:11 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-07-03 19:11 | INFO     | ResMonitor.end_timer: CALC_F_VALUES_SH510050 took 0.02 seconds (00:00:00)
2025-07-03 19:11 | INFO     | ResMonitor.end_timer_cpu: CALC_F_VALUES_SH510050 CPU usage: 0.02s user, 0.00s system
2025-07-03 19:11 | INFO     | ResMonitor.end_memory_monitor: CALC_F_VALUES_SH510050 memory delta: 0.02MB
2025-07-03 19:11 | INFO     | FactorMonitorContext.__exit__: CALC_F_VALUES_SH510050 completed successfully
2025-07-03 19:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:11 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_SH510050_20250703_L0_153156
2025-07-03 19:11 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_SH510050_20250703_L0_153156 耗时168.4ms
2025-07-03 19:11 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.17 seconds (00:00:00)
2025-07-03 19:11 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.03s user, 0.02s system
2025-07-03 19:11 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 5.22MB
2025-07-03 19:11 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-07-03 19:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:11 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250703_L0_153156_label_1_001 took 0.02 seconds (00:00:00)
2025-07-03 19:11 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250703_L0_153156_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-07-03 19:11 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250703_L0_153156_label_1_001 memory delta: 0.04MB
2025-07-03 19:11 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250703_L0_153156_label_1_001 completed successfully
2025-07-03 19:11 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250703_L0_153156 took 52.42 seconds (00:00:52)
2025-07-03 19:11 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250703_L0_153156 CPU usage: 31.95s user, 1.89s system
2025-07-03 19:11 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250703_L0_153156 memory delta: 123.40MB
2025-07-03 19:11 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250703_L0_153156 completed successfully
2025-07-03 19:11 | INFO     | ResMonitor.end_timer: global_process took 52.44 seconds (00:00:52)
2025-07-03 19:15 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:15 | INFO     | BaseObj.<module>: 🚀 开始因子挖掘流程...
2025-07-03 19:15 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:15 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 19:15 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-07-03 19:15 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-07-03 19:15 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 19:15 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-07-03 19:15 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-07-03 19:15 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 19:15 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 19:15 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(10): 10个
2025-07-03 19:15 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 10 列
2025-07-03 19:15 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-07-03 19:15 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 19:15 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 16 (基础5+归一化0+保持0+标签1+筛选后特征10)
2025-07-03 19:15 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 16
2025-07-03 19:15 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-07-03 19:15 | INFO     | BaseObj.<module>: 📋 发现 1 个预测目标: ['label_1']
2025-07-03 19:15 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:15 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-07-03 19:15 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 17.28 seconds (00:00:17)
2025-07-03 19:15 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.86s user, 0.12s system
2025-07-03 19:15 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 5.20MB
2025-07-03 19:15 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-07-03 19:15 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:15 | INFO     | FactorLoader.get_fct_df: 开始计算5个因子表达式
2025-07-03 19:15 | INFO     | FactorLoader.get_fct_df: 返回结果: 5个指定因子
2025-07-03 19:15 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.02 seconds (00:00:00)
2025-07-03 19:15 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-07-03 19:15 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 0.05MB
2025-07-03 19:15 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-07-03 19:15 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:15 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-03 19:15 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-07-03 19:15 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 0.03MB
2025-07-03 19:15 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-07-03 19:15 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:15 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-03 19:15 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-07-03 19:15 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: 0.00MB
2025-07-03 19:15 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-07-03 19:15 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:15 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 39.15 seconds (00:00:39)
2025-07-03 19:15 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 29.44s user, 2.02s system
2025-07-03 19:15 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 109.50MB
2025-07-03 19:15 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-07-03 19:15 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:15 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-03 19:15 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-07-03 19:15 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-07-03 19:15 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.05MB
2025-07-03 19:15 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-07-03 19:15 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-03 19:15 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-03 19:15 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:15 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-07-03 19:15 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-07-03 19:15 | INFO     | ResMonitor.end_timer: CALC_F_VALUES_SH510050 took 0.01 seconds (00:00:00)
2025-07-03 19:15 | INFO     | ResMonitor.end_timer_cpu: CALC_F_VALUES_SH510050 CPU usage: 0.02s user, 0.00s system
2025-07-03 19:15 | INFO     | ResMonitor.end_memory_monitor: CALC_F_VALUES_SH510050 memory delta: 0.02MB
2025-07-03 19:15 | INFO     | FactorMonitorContext.__exit__: CALC_F_VALUES_SH510050 completed successfully
2025-07-03 19:15 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:15 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_SH510050_20250703_L0_169671
2025-07-03 19:15 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_SH510050_20250703_L0_169671 耗时120.6ms
2025-07-03 19:15 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.12 seconds (00:00:00)
2025-07-03 19:15 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.03s user, 0.02s system
2025-07-03 19:15 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 5.22MB
2025-07-03 19:15 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-07-03 19:15 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:15 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250703_L0_169671_label_1_001 took 0.02 seconds (00:00:00)
2025-07-03 19:15 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250703_L0_169671_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-07-03 19:15 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250703_L0_169671_label_1_001 memory delta: 0.04MB
2025-07-03 19:15 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250703_L0_169671_label_1_001 completed successfully
2025-07-03 19:15 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250703_L0_169671 took 57.61 seconds (00:00:57)
2025-07-03 19:15 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250703_L0_169671 CPU usage: 30.64s user, 2.28s system
2025-07-03 19:15 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250703_L0_169671 memory delta: 125.43MB
2025-07-03 19:15 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250703_L0_169671 completed successfully
2025-07-03 19:15 | INFO     | ResMonitor.end_timer: global_process took 57.65 seconds (00:00:57)
2025-07-03 19:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:19 | INFO     | BaseObj.<module>: 🚀 开始因子挖掘流程...
2025-07-03 19:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:19 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-03 19:19 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-07-03 19:19 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-07-03 19:19 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-03 19:19 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-07-03 19:19 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-07-03 19:19 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-03 19:19 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-03 19:19 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(10): 10个
2025-07-03 19:19 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 10 列
2025-07-03 19:19 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-07-03 19:19 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-03 19:19 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 16 (基础5+归一化0+保持0+标签1+筛选后特征10)
2025-07-03 19:19 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 16
2025-07-03 19:19 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-07-03 19:19 | INFO     | BaseObj.<module>: 📋 发现 1 个预测目标: ['label_1']
2025-07-03 19:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:19 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-07-03 19:19 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 9.04 seconds (00:00:09)
2025-07-03 19:19 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.55s user, 0.06s system
2025-07-03 19:19 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 5.52MB
2025-07-03 19:19 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-07-03 19:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:19 | INFO     | FactorLoader.get_fct_df: 开始计算5个因子表达式
2025-07-03 19:19 | INFO     | FactorLoader.get_fct_df: 返回结果: 5个指定因子
2025-07-03 19:19 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-03 19:19 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-07-03 19:19 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 0.02MB
2025-07-03 19:19 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-07-03 19:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:19 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-03 19:19 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-07-03 19:19 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 0.03MB
2025-07-03 19:19 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-07-03 19:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:19 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-03 19:19 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-07-03 19:19 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: 0.00MB
2025-07-03 19:19 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-07-03 19:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:20 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 24.96 seconds (00:00:24)
2025-07-03 19:20 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 21.91s user, 1.20s system
2025-07-03 19:20 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 107.52MB
2025-07-03 19:20 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-07-03 19:20 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:20 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-03 19:20 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-03 19:20 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-07-03 19:20 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.05MB
2025-07-03 19:20 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-07-03 19:20 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-03 19:20 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-03 19:20 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:20 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-07-03 19:20 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-07-03 19:20 | INFO     | ResMonitor.end_timer: CALC_F_VALUES_SH510050 took 0.01 seconds (00:00:00)
2025-07-03 19:20 | INFO     | ResMonitor.end_timer_cpu: CALC_F_VALUES_SH510050 CPU usage: 0.00s user, 0.00s system
2025-07-03 19:20 | INFO     | ResMonitor.end_memory_monitor: CALC_F_VALUES_SH510050 memory delta: 0.00MB
2025-07-03 19:20 | INFO     | FactorMonitorContext.__exit__: CALC_F_VALUES_SH510050 completed successfully
2025-07-03 19:20 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:20 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_SH510050_20250703_L0_580670
2025-07-03 19:20 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_SH510050_20250703_L0_580670 耗时54.5ms
2025-07-03 19:20 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.06 seconds (00:00:00)
2025-07-03 19:20 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.05s user, 0.02s system
2025-07-03 19:20 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 5.21MB
2025-07-03 19:20 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-07-03 19:20 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:20 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250703_L0_580670_label_1_001 took 0.01 seconds (00:00:00)
2025-07-03 19:20 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250703_L0_580670_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-07-03 19:20 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250703_L0_580670_label_1_001 memory delta: 0.04MB
2025-07-03 19:20 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250703_L0_580670_label_1_001 completed successfully
2025-07-03 19:20 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250703_L0_580670 took 34.53 seconds (00:00:34)
2025-07-03 19:20 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250703_L0_580670 CPU usage: 22.81s user, 1.36s system
2025-07-03 19:20 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250703_L0_580670 memory delta: 123.74MB
2025-07-03 19:20 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250703_L0_580670 completed successfully
2025-07-03 19:20 | INFO     | ResMonitor.end_timer: global_process took 34.54 seconds (00:00:34)
2025-07-03 19:30 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-03 19:30 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-03 19:30 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:30 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: L1_CORR_SH510050_label_1_20250701_288141/L1
2025-07-03 19:30 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: L1_CORR_SH510050_label_1_20250701_288141/L1 耗时261.8ms
2025-07-03 19:30 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:30 | INFO     | ResMonitor.end_timer: L2_LASSO_SH510050_label_1 took 0.06 seconds (00:00:00)
2025-07-03 19:30 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_SH510050_label_1 CPU usage: 0.06s user, 0.00s system
2025-07-03 19:30 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_SH510050_label_1 memory delta: 0.31MB
2025-07-03 19:30 | INFO     | FactorMonitorContext.__exit__: L2_LASSO_SH510050_label_1 completed successfully
2025-07-03 19:30 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:30 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_LASSO_SH510050_label_1_20250703_648705
2025-07-03 19:30 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_LASSO_SH510050_label_1_20250703_648705 耗时43.5ms
2025-07-03 19:30 | INFO     | ResMonitor.end_timer: PERSIST_L2_LASSO_SH510050_label_1 took 0.05 seconds (00:00:00)
2025-07-03 19:30 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_LASSO_SH510050_label_1 CPU usage: 0.03s user, 0.02s system
2025-07-03 19:30 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_LASSO_SH510050_label_1 memory delta: 2.24MB
2025-07-03 19:30 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_LASSO_SH510050_label_1 completed successfully
2025-07-03 19:30 | INFO     | ResMonitor.end_timer: L2_LASSO_FILTER_1751542211648705 took 0.47 seconds (00:00:00)
2025-07-03 19:30 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_FILTER_1751542211648705 CPU usage: 0.11s user, 0.12s system
2025-07-03 19:30 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_FILTER_1751542211648705 memory delta: 16.49MB
2025-07-03 19:30 | INFO     | MonitorContext.__exit__: L2_LASSO_FILTER_1751542211648705 completed successfully
2025-07-03 19:38 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-03 19:38 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-03 19:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:38 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: L1_CORR_SH510050_label_1_20250701_288141/L1
2025-07-03 19:38 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: L1_CORR_SH510050_label_1_20250701_288141/L1 耗时312.8ms
2025-07-03 19:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:38 | INFO     | ResMonitor.end_timer: L2_LASSO_SH510050_label_1 took 0.07 seconds (00:00:00)
2025-07-03 19:38 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_SH510050_label_1 CPU usage: 0.05s user, 0.02s system
2025-07-03 19:38 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_SH510050_label_1 memory delta: 0.46MB
2025-07-03 19:38 | INFO     | FactorMonitorContext.__exit__: L2_LASSO_SH510050_label_1 completed successfully
2025-07-03 19:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:38 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_LASSO_SH510050_label_1_20250703_382139
2025-07-03 19:38 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_LASSO_SH510050_label_1_20250703_382139 耗时49.6ms
2025-07-03 19:38 | INFO     | ResMonitor.end_timer: PERSIST_L2_LASSO_SH510050_label_1 took 0.05 seconds (00:00:00)
2025-07-03 19:38 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_LASSO_SH510050_label_1 CPU usage: 0.02s user, 0.00s system
2025-07-03 19:38 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_LASSO_SH510050_label_1 memory delta: 2.17MB
2025-07-03 19:38 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_LASSO_SH510050_label_1 completed successfully
2025-07-03 19:38 | INFO     | ResMonitor.end_timer: L2_LASSO_FILTER_1751542688382139 took 0.55 seconds (00:00:00)
2025-07-03 19:38 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_FILTER_1751542688382139 CPU usage: 0.16s user, 0.11s system
2025-07-03 19:38 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_FILTER_1751542688382139 memory delta: 16.85MB
2025-07-03 19:38 | INFO     | MonitorContext.__exit__: L2_LASSO_FILTER_1751542688382139 completed successfully
2025-07-03 19:42 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-03 19:42 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-03 19:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:42 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: L1_CORR_SH510050_label_1_20250701_288141/L1
2025-07-03 19:42 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: L1_CORR_SH510050_label_1_20250701_288141/L1 耗时268.7ms
2025-07-03 19:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:42 | INFO     | ResMonitor.end_timer: L2_LASSO_SH510050_label_1 took 0.05 seconds (00:00:00)
2025-07-03 19:42 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_SH510050_label_1 CPU usage: 0.05s user, 0.00s system
2025-07-03 19:42 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_SH510050_label_1 memory delta: 0.26MB
2025-07-03 19:42 | INFO     | FactorMonitorContext.__exit__: L2_LASSO_SH510050_label_1 completed successfully
2025-07-03 19:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-03 19:42 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_LASSO_SH510050_label_1_20250703_848537
2025-07-03 19:42 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_LASSO_SH510050_label_1_20250703_848537 耗时35.9ms
2025-07-03 19:42 | INFO     | ResMonitor.end_timer: PERSIST_L2_LASSO_SH510050_label_1 took 0.04 seconds (00:00:00)
2025-07-03 19:42 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_LASSO_SH510050_label_1 CPU usage: 0.00s user, 0.02s system
2025-07-03 19:42 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_LASSO_SH510050_label_1 memory delta: 2.17MB
2025-07-03 19:42 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_LASSO_SH510050_label_1 completed successfully
2025-07-03 19:43 | INFO     | ResMonitor.end_timer: L2_LASSO_FILTER_1751542971848537 took 22.59 seconds (00:00:22)
2025-07-03 19:43 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_FILTER_1751542971848537 CPU usage: 0.09s user, 0.06s system
2025-07-03 19:43 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_FILTER_1751542971848537 memory delta: 16.54MB
2025-07-03 19:43 | INFO     | MonitorContext.__exit__: L2_LASSO_FILTER_1751542971848537 completed successfully
