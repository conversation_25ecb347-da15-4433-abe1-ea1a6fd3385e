from itertools import combinations, product
from typing import Dict,List
import numpy as np
import pandas as pd
from .combo_mgr import ComboGenerator

class XLayerComboGenerator(ComboGenerator):
    ''' 分层随机抽样(不放回): 支持跨层+同层 '''
    # init: 因子df(因子expr,metric(其实就是sharpe值)), 分层网格(如10%分层,根据metric值排序分层,代码考虑因子数量很小的情况, 或者层内因子数不均衡情况)
    # generate: 生成分层组合,返回各个组合的列表; 同层用最优分配法 每次抽取样本总数 不超过因子总数的10%; 如何结合跨层组合的一些算法?
    # 假设外部调用都存在, ComboGenerator中有log方法用来格式化打印信息self.log('txt')
    def __init__(self, df: pd.DataFrame, num_layers: int = 5, layer_size: int = None, seed: int = 42): # 层数不宜太多
        ''' df: [expr, metric] ''' 
        super().__init__()
        self.seed = seed
        self.df = df.sort_values(by='metric', ascending=False).reset_index(drop=True)  # 根据metric排序
        self.num_layers = num_layers
        self.layer_size = layer_size or (len(self.df) // num_layers)
        self.layers = self._create_layers()
        self.selected_factors = self._select_factors()
            
    def _create_layers(self) -> Dict[int, List[str]]:
        layers = {}
        for i in range(self.num_layers):
            start = i * self.layer_size
            end = min((i + 1) * self.layer_size, len(self.df))
            layers[i] = self.df.iloc[start:end]['expr'].tolist()
        return layers

    def _compute_variance(self, factors: List[str]) -> float:
        """计算方差"""
        sharpe_ratios = self.df.loc[self.df['expr'].isin(factors), 'metric']
        if sharpe_ratios.empty:
            return 0.0
        
        finite_sharpe_ratios = sharpe_ratios[np.isfinite(sharpe_ratios)]
        if finite_sharpe_ratios.empty:
            return 0.0
        if len(finite_sharpe_ratios) < 2:
            return 0.0
        v = finite_sharpe_ratios.var(skipna=True)
        return float(v)
    
    # def _select_factors_simple(self) -> Dict[int, List[str]]:
    #     """每层等数目随机抽取 -- 暂时不用 """
    #     np.random.seed(42)
    #     total_factors = len(self.df)
    #     num_per_layer = min(int(total_factors * 0.1 / self.num_layers), min(len(v) for v in self.layers.values()))
    #     selected_factors = {}
    #     for layer_id, factors in self.layers.items():
    #         selected_factors[layer_id] = np.random.choice(factors, size=num_per_layer, replace=False)
    #     return selected_factors
    
    def _select_factors(self) -> Dict[int, List[str]]:
        """最优分配法: 考虑每层的方差/成本"""
        np.random.seed(self.seed)
        # 计算每个层的方差
        layer_variances = {}
        layer_variances = {layer_id: self._compute_variance(factors) for layer_id, factors in self.layers.items()}
        # 检查方差是否为0，并设置一个非常小的值来避免除以0
        for layer_id in layer_variances:
            if layer_variances[layer_id] == 0:
                layer_variances[layer_id] = 1e-10  # 可以用一个非常小的值代替0
        # 打印检查点
        print("layers:", self.layers)
        print("Layer Variances:", layer_variances)
        
        # 计算每个层的相对成本（在这个例子中，假设成本相同，因此省略）
        relative_costs = {layer_id: 1 for layer_id in layer_variances.keys()}
        # 计算每个层的大小
        layer_sizes = {layer_id: len(factors) for layer_id, factors in self.layers.items()}
        # 打印检查点
        print("Layer Sizes:", layer_sizes)
        # 总样本量
        total_sample_size = int(len(self.df) * 1) # NOTE: 暂定总因子数的10%, 未来设置为1-2%防止过拟合
        # 最优分配
        optimal_allocations = {}
        total_weight = sum(np.sqrt(layer_variances[layer_id]) * np.sqrt(relative_costs[layer_id]) * np.sqrt(layer_sizes[layer_id])
                           for layer_id in layer_variances.keys())
        
        # 检查总权重是否有效
        if np.isnan(total_weight):
            print("Total Weight is NaN")
        else:
            print("Total Weight:", total_weight)
    
        for layer_id in layer_variances.keys():
            weight = np.sqrt(layer_variances[layer_id]) * np.sqrt(relative_costs[layer_id]) * np.sqrt(layer_sizes[layer_id])
            optimal_allocations[layer_id] = round((weight / total_weight) * total_sample_size)
        print(optimal_allocations)
        # 从每层中选择因子
        selected_factors = {}
        for layer_id, num_samples in optimal_allocations.items():
            factors = self.layers[layer_id]
            selected_factors[layer_id] = np.random.choice(factors, size=min(num_samples, len(factors)), replace=False) # 随机不放回
        return selected_factors

    def generate(self) -> List[List[str]]:
        """Generates all possible combinations of factors across and within layers. """
        # TODO: 加入评估函数并阶段性保存结果 -- 改用deap, 此处暂时不去实现
        all_combinations = []

        # 生成层内组合
        for layer_id, factors in self.selected_factors.items():
            for r in range(1, len(factors) + 1):
                all_combinations.extend([comb for comb in combinations(factors, r)])

        # 生成跨层组合
        cross_layer_combinations = list(product(*self.selected_factors.values()))
        for c in cross_layer_combinations:
            for r in range(1, len(c) + 1):
                all_combinations.extend([comb for comb in combinations(c, r)])

        # 去除重复的组合并返回结果
        return list(set(map(tuple, all_combinations)))
        
if __name__ == '__main__':
    # 创建示例DataFrame
    np.random.seed(42)  # 为了可重复性
    factor_names = [f'Factor_{i}' for i in range(1, 6)]
    sharpe_ratios = np.random.rand(5)  # 生成随机的夏普比率
    df = pd.DataFrame({
        'expr': factor_names,
        'metric': sharpe_ratios
    })
    # 实例化XLayerComboGenerator并生成组合
    g = XLayerComboGenerator(df, num_layers=3, layer_size=2)
    combinations = g.generate()
    # 打印生成的组合
    print("Generated Combinations:")
    for i, combo in enumerate(combinations, start=1):
        print(f"Combination {i}: {combo}")