import streamlit as st
import akshare as ak
import plotly.express as px
import pandas as pd
import time
from datetime import datetime
import threading

# 初始化全局锁
akshare_lock = threading.Lock()

def get_realtime_concept_data():
    """获取实时概念板块数据"""
    with akshare_lock:
        df = ak.stock_board_concept_em_spot().rename(columns={
            '最新价': '收盘价',
            '涨跌幅': '区间涨跌幅',
            '成交额': '总成交额（亿）',
            '换手率': '日均换手率'
        })
        df['总成交额（亿）'] = df['总成交额（亿）'] / 1e8
        return df[['板块名称', '收盘价', '区间涨跌幅', '总成交额（亿）', '日均换手率']]

def main():
    st.set_page_config(
        page_title="实时概念板块分析",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 侧边栏控件
    with st.sidebar:
        st.header("刷新设置")
        auto_refresh = st.checkbox("自动刷新", value=True)
        refresh_interval = st.slider(
            "刷新间隔(秒)", 
            min_value=5, 
            max_value=60, 
            value=30,
            step=5
        )
        
        st.header("可视化设置")
        color_scale = st.selectbox(
            "配色方案",
            options=['RdYlGn_r', 'BrBG_r', 'PiYG_r', 'RdBu_r'],
            index=0
        )
        size_metric = st.selectbox(
            "板块规模指标",
            options=['总成交额（亿）', '日均换手率'],
            index=0
        )
    
    # 主显示区
    placeholder = st.empty()
    
    last_update = st.sidebar.empty()
    status_text = st.sidebar.empty()
    
    while True:
        with st.spinner('获取实时数据中...'):
            try:
                concept_df = get_realtime_concept_data()
                
                if not concept_df.empty:
                    # 概念板块热力图
                    fig = px.treemap(
                        concept_df,
                        path=['板块名称'],
                        values=size_metric,
                        color='区间涨跌幅',
                        color_continuous_scale=color_scale,
                        range_color=[concept_df['区间涨跌幅'].min(), concept_df['区间涨跌幅'].max()],
                        hover_data={
                            '收盘价': ':.2f',
                            '区间涨跌幅': ':.2f%',
                            '总成交额（亿）': ':.1f',
                            '日均换手率': ':.2f%'
                        },
                        height=800
                    )
                    fig.update_layout(
                        margin=dict(t=40, l=0, r=0, b=0),
                        title={
                            'text': f"实时概念板块表现 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                            'y': 0.95,
                            'x': 0.5
                        },
                    )
                    
                    with placeholder.container():
                        st.plotly_chart(fig, use_container_width=True)
                        
                        # 数据表格
                        with st.expander("查看详细数据排名"):
                            st.dataframe(
                                concept_df.sort_values(by='区间涨跌幅', ascending=False),
                                column_config={
                                    "板块名称": st.column_config.TextColumn(width="large"),
                                    "区间涨跌幅": st.column_config.NumberColumn(format="▁+%.2f%%"),
                                    "总成交额（亿）": st.column_config.NumberColumn(format="%.1f 亿"),
                                    "日均换手率": st.column_config.NumberColumn(format="%.2f%%")
                                },
                                height=400,
                                hide_index=True
                            )
                    
                    last_update.success(f"最后更新时间: {datetime.now().strftime('%H:%M:%S')}")
                else:
                    st.error("获取数据失败，请稍后重试")
                    
            except Exception as e:
                status_text.error(f"数据获取异常: {str(e)}")
                
        if not auto_refresh:
            break
            
        time.sleep(refresh_interval)

if __name__ == "__main__":
    main()
