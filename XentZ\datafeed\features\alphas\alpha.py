from dynaconf import Dynaconf

# 🎯 新配置体系：Alpha类不直接读取配置，而是通过参数接收
# from config.settings import get_label_single_tdelay, get_label_single_names, get_label_multiple_tdelay, get_label_multiple_names

class AlphaBase:
    '''基类: 定义lables等'''
    def __init__(self, **kwargs):
        pass

    def get_a_label(self, tdelay: int = 1, names: list = ['label_1']):
        """
        获取单个标签
        
        Args:
            tdelay (int): 标签的时间延迟，默认1
            names (list): 标签名称，默认['label_1']
        
        Returns:
            tuple: (表达式列表, 名称列表)
        """
        exprs = [f"ts_delay(close, {-tdelay}) / close - 1"]
        return exprs, names

    def get_labels(self, tdelay: list = [1, 5, 10, 20], 
                     names: list = ['label_1', 'label_5', 'label_10', 'label_20']):
        """
        获取多个时间延迟的标签
        
        Args:
            tdelay (list): 标签的时间延迟列表
            names (list): 标签名称列表
            
        Returns:
            tuple: (表达式列表, 名称列表)
        """
        exprs = [f"ts_delay(close, {-d}) / close - 1" for d in tdelay]
        return exprs, names

class AlphaOrigin(AlphaBase):
    ''' 初级特征汇集 '''
    def get_exprs_names(self):
        exprs = []
        names = []
        # names = ['open', 'high', 'low', 'close', 'volume'] # 初尝试, 不要再加了,否则会报错!
        # exprs = ['open', 'high', 'low', 'close', 'volume']
        # 0、量价基础数据组合 - 视为OLHCV的扩展
        names += ['R_0','R_1','R_2','R_3','R_4','R_5','R_6','V_0','V_1','V_2','V_3','V_4']
        exprs += ['(high-low)/close','(high-open)/close','(high-close)/close','(close-open)/close','(close-low)/close', '(high-open)/open', '(open-low)/open',
                  '(high-low)/volume','(high-open)/volume','(high-close)/volume','(close-open)/volume','(close-low)/volume']
        # 1、ma类
        names += ['sma5', 'sma10', 'sma20', 'smadiff5', 'smadiff10', 'smadiff20']
        exprs += ['sma(close,5)', 'sma(close,10)', 'sma(close,20)', '(sma5/close)-1', 
                             '(sma10/close)-1', '(sma20/close)-1']
        # 2、bollinger band类
        names += ['bb_up_20','bb_min_20', 'bb_dn_20', 'bb_up_80','bb_min_80', 'bb_dn_80']
        exprs += ['bbands_up(close,20)','bbands_mid(close,20)','bbands_dn(close,20)','bbands_up(close,80)','bbands_mid(close,80)','bbands_dn(close,80)']
        # 3、sar因子
        names += ['sar_index', 'sar_r']
        exprs += ['sar_index(high,low)', 'sar_r(sar_index,close)']
        # 4、aroon
        names += ['b_aroon_14']
        exprs += ['aroon(high,low,14)']
        # 5、CCI
        names += ['b_cci_14', 'b_cci_25', 'b_cci_55']
        exprs += ['cci(high,low,close,14)', 'cci(high,low,close,25)', 'cci(high,low,close,55)']
        # 6、CMO
        names += ['b_cmo_14', 'b_cmo_25']
        exprs += ['cmo(close,14)', 'cmo(close,25)']
        # 7、MFI
        names += ['b_mfi_14']
        exprs += ['mfi(high,low,close,volume,14)']
        # 8、MOM
        names += ['b_mom_14', 'b_mom_25']
        exprs += ['mom(close,14)', 'mom(close,25)']
        # 9、PPO
        names += ['b_ppo_12_26']
        exprs += ['ppo(close,12,26,0)']
        # # 10、AD
        # names += ['b_ad_index', 'b_adosc_3_10']
        # exprs += ['ad_index(high,low,close,volume)', 'adosc(high,low,close,volume,3,10)']
        # # 11、OBV
        # names += ['b_obv']
        # exprs += ['obv(close,volume)']
        # 12、ATR
        names += ['b_atr_14', 'b_atr_25', 'b_atr_60']
        exprs += ['atr(high,low,close,14)', 'atr(high,low,close,25)', 'atr(high,low,close,60)']
        names += ['tr_index', 'tr_ma5', 'tr_ma10', 'tr_ma20']
        exprs += ['tr_index(high,low,close)', 'tr_ma(tr_index,close,5)', 'tr_ma(tr_index,close,10)', 'tr_ma(tr_index,close,20)']
        # 13、KD
        names += ['b_kdj_k', 'b_kdj_d', 'b_kdj_j']
        exprs += ['kdj_k(high,low,close)', 'kdj_d(high,low,close)', 'kdj_j(b_kdj_k,b_kdj_d)']
        # 14、MACD 
        names += ['b_macd_dif','b_macd_dea','b_macd_hist']
        exprs += ['macd_dif(close)','macd_dea(close)','macd_hist(close)']
        # 15. volume_weighted_MACD
        names += ['b_macd_wt_dif','b_macd_wt_dea','b_macd_wt_hist']
        exprs += ['macd_wt_dif(close,volume)','macd_wt_dea(close,volume)','macd_wt_hist(close,volume)']
        # 16、RSI index
        names += ['b_rsi_6','b_rsi_12','b_rsi_25']
        exprs += ['ta_rsi(close,6)','ta_rsi(close,12)','ta_rsi(close,25)']
        # 17、VWAP
        bars_daily = 16
        windows = [x * bars_daily for x in [3, 5, 8, 21, 32]]
        names += ["b_vwap_%d" % d for d in windows]
        exprs += ["rl_vwap(high,low,close,volume, %d)" % d for d in windows]
        
        return exprs, names
