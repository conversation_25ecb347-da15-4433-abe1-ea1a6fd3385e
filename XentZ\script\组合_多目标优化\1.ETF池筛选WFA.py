from hikyuu import *
from hikyuu.interactive import *
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import argparse
from datetime import datetime, timedelta

from datafeed.hku_dataloader import HKUDataloader
from config import REPORTS_DIR

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

''' =========  基本参数集中配置   ========= '''
# 添加参数配置
DEFAULT_CONFIG = {
    'start_date': '20200101',      # 起始日期
    'end_date': None,              # 结束日期，None表示当前日期
    'lookback_period': 90,        # 回看期(一年),但实际返回数据为交易日的数据len
    'long_window': 30,            # <= lookback_period 规则再平滑(交易概念);过大引起报错!
    'short_window': 21,            # AMO/波动率等的短期平滑窗口(交易概念)
    'step_days': 10,               # 步长(月),与GA的wfa测试保持一致
    'max_etfs': 10,                # 最大ETF数量
    'min_etfs': 8,                 # 最小ETF数量
    'corr_threshold': 0.5,         # 相关性阈值
    'min_volume': 10000,           # 最小日均成交额(万元)
    'min_volatility': 0.01,         # 最小波动率(去量纲后)
    'trading_days': 252            # 不同于回看天数设置, 相对固定, 代表一年实际交易天数
}

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='ETF滚动筛选工具')
    parser.add_argument('--start_date', type=str, help='起始日期，格式YYYYMMDD')
    parser.add_argument('--end_date', type=str, help='结束日期，格式YYYYMMDD')
    parser.add_argument('--lookback_period', type=int, help='回看期(天数)')
    parser.add_argument('--step_days', type=int, help='步长(天数)')
    parser.add_argument('--max_etfs', type=int, help='最大ETF数量')
    parser.add_argument('--min_etfs', type=int, help='最小ETF数量')
    parser.add_argument('--corr_threshold', type=float, help='相关性阈值')
    parser.add_argument('--min_volume', type=float, help='最小日均成交额(万元)')
    parser.add_argument('--min_volatility', type=float, help='最小波动率(去量纲后)')
    
    args = parser.parse_args()
    
    # 创建配置字典，只包含非None的参数
    config = DEFAULT_CONFIG.copy()
    for key, value in vars(args).items():
        if value is not None:
            config[key] = value
    
    # 如果end_date为None，设置为当前日期
    if config['end_date'] is None:
        config['end_date'] = datetime.now().strftime('%Y%m%d')
    
    return config

def calculate_diversity_score(corr_matrix, etf):
    """计算ETF的多样性得分，使用改进的方法"""
    corrs = corr_matrix[etf].drop(etf)  # 排除自身
    
    # 1. 计算绝对相关性
    abs_corrs = corrs.abs()
    avg_abs_corr = abs_corrs.mean()
    
    # 2. 计算负相关资产比例 (特别有价值的对冲资产)
    neg_corr_ratio = (corrs < -0.2).mean()
    
    # 3. 计算高相关资产比例 (潜在冗余)
    high_corr_ratio = (abs_corrs > 0.7).mean()
    
    # 综合多样性指标 (值越高越好)
    diversity_score = (1 - avg_abs_corr) * 0.6 + neg_corr_ratio * 0.3 + (1 - high_corr_ratio) * 0.1
    
    return diversity_score, avg_abs_corr, neg_corr_ratio, high_corr_ratio

def normalize_metric(values, method='minmax', center=None, scale=1.0):
    
    values = np.array(values)
    
    if method == 'minmax':
        min_val, max_val = min(values), max(values)
        if max_val > min_val:
            return (values - min_val) / (max_val - min_val)
        return np.ones_like(values) * 0.5
    
    elif method == 'sigmoid':
        if center is None:
            center = np.mean(values)
        return 1 / (1 + np.exp(-(values - center) / scale))
    
    elif method == 'zscore':
        mean, std = np.mean(values), np.std(values)
        if std > 0:
            z_scores = (values - mean) / std
            # 将z-score转换到[0,1]范围，假设z-score在[-3,3]范围内
            return (z_scores + 3) / 6
        return np.ones_like(values) * 0.5
    
    elif method == 'rank':
        # 排名标准化，简单且对异常值不敏感
        ranks = np.argsort(np.argsort(values))
        return ranks / (len(ranks) - 1)
    
    elif method == 'hybrid':
        # 混合方法：对极端值使用排名，对中间值使用sigmoid
        ranks = np.argsort(np.argsort(values)) / (len(values) - 1)
        
        # 计算sigmoid值
        if center is None:
            center = np.mean(values)
        sigmoid = 1 / (1 + np.exp(-(values - center) / scale))
        
        # 混合：极端值(前10%和后10%)使用排名，中间值使用sigmoid
        result = np.copy(sigmoid)
        extreme_mask = (ranks < 0.1) | (ranks > 0.9)
        result[extreme_mask] = ranks[extreme_mask]
        
        return result
    
    return values  # 默认返回原始值

def do_select(returns_df, lookback_period=252, 
              max_etfs=15, min_etfs=8, corr_threshold=0.7):
    """
    returns_df: ETF收益率DataFrame
    lookback_period: 回看期(默认一年)
    max_etfs: 最大ETF数量
    min_etfs: 最小ETF数量
    corr_threshold: 相关性阈值，高于此值的ETF对将被筛选
    """    
    # 计算相关性矩阵
    corr_matrix = returns_df.corr()
    # 计算每个ETF的关键指标
    etf_metrics = {}
    # 收集所有指标的值，用于后续标准化
    all_returns = []
    all_sharpes = []
    all_drawdowns = []
    all_diversity_scores = []
    
    for etf in returns_df.columns:
        # 计算年化收益率
        annual_return = returns_df[etf].mean() * 252
        all_returns.append(annual_return)
        
        # 计算年化波动率
        annual_vol = returns_df[etf].std() * np.sqrt(252)
        
        # 计算最大回撤
        cum_returns = (1 + returns_df[etf]).cumprod()
        running_max = cum_returns.cummax()
        drawdown = (cum_returns / running_max) - 1
        max_drawdown = drawdown.min()
        all_drawdowns.append(max_drawdown)
        
        # 计算夏普比率 (假设无风险利率为3%)
        sharpe = (annual_return - 0.03) / annual_vol if annual_vol > 0 else 0
        all_sharpes.append(sharpe)
        
        # 计算改进的多样性指标
        diversity_score, avg_abs_corr, neg_corr_ratio, high_corr_ratio = calculate_diversity_score(corr_matrix, etf)
        all_diversity_scores.append(diversity_score)
        
        etf_metrics[etf] = {
            'return': annual_return,
            'volatility': annual_vol,
            'max_drawdown': max_drawdown,
            'sharpe': sharpe,
            'diversity_score': diversity_score,
            'avg_abs_corr': avg_abs_corr,
            'neg_corr_ratio': neg_corr_ratio,
            'high_corr_ratio': high_corr_ratio
        }
    # 对夏普比率使用sigmoid，因为我们希望强调中等表现和优秀表现的差异
    capped_sharpes = [max(s, -3) for s in all_sharpes]  # 将极端负值截断在-3
    sharpe_norm = normalize_metric(capped_sharpes, method='sigmoid', center=0, scale=0.7)
    drawdown_norm = 1 - normalize_metric(all_drawdowns, method='minmax')
    diversity_norm = normalize_metric(all_diversity_scores, method='minmax')
    # 计算标准化后的评分
    for i, etf in enumerate(etf_metrics.keys()):
        etf_metrics[etf]['norm_diversity'] = diversity_norm[i]
        etf_metrics[etf]['norm_sharpe'] = sharpe_norm[i]
        etf_metrics[etf]['norm_drawdown'] = drawdown_norm[i]
        # 综合评分 - 所有指标都在[0,1]范围内
        if all_sharpes[i] < 0:
            # 负夏普比率时，降低夏普在评分中的权重，增加多样性权重
            score = 0.6 * diversity_norm[i] + 0.2 * sharpe_norm[i] + 0.2 * drawdown_norm[i]
        else:
            # 正夏普比率时，使用原始权重
            score = 0.5 * diversity_norm[i] + 0.3 * sharpe_norm[i] + 0.2 * drawdown_norm[i]
        etf_metrics[etf]['score'] = score
        # etf_metrics[etf]['score'] = diversity_norm[i]
    
    # 第一步：按多样性得分排序，选择前max_etfs*5个ETF作为候选
    sorted_by_diversity = sorted(etf_metrics.items(), 
                                key=lambda x: x[1]['diversity_score'], 
                                reverse=True)
    
    candidate_etfs = [item[0] for item in 
                      sorted_by_diversity[:min(max_etfs*5, len(sorted_by_diversity))]]
    
    # 第二步：使用贪心算法选择最终ETF池
    selected_etfs = []
    # 首先选择多样性最高的ETF
    selected_etfs.append(candidate_etfs[0])
    # 贪心选择剩余ETF
    while len(selected_etfs) < max_etfs and len(candidate_etfs) > 0:
        best_etf = None
        best_score = float('-inf')
        
        for etf in candidate_etfs:
            if etf in selected_etfs:
                continue
            
            # 计算与已选ETF的相关系数
            corrs = [corr_matrix.loc[etf, selected] for selected in selected_etfs]
            max_corr = max(corrs)
            
            # 如果相关性太高，跳过
            if max_corr > corr_threshold:
                continue
            
            # 使用预先计算的标准化评分
            score = etf_metrics[etf]['score']
            
            if score > best_score:
                best_score = score
                best_etf = etf
        
        # 如果找不到合适的ETF，或已达到最小要求，则停止
        if best_etf is None:
            if len(selected_etfs) >= min_etfs:
                break
            else:
                # 放宽相关性限制，继续寻找
                corr_threshold += 0.1
                continue
        
        # 添加最佳ETF到选择列表
        selected_etfs.append(best_etf)
    # selected_etfs = returns_df.columns # 粗筛情况
    # 打印选择结果
    print(f"\n为轮动策略选择的{len(selected_etfs)}个ETF池:")
    print("代码\t综合得分\t年化收益\t波动率\t最大回撤\t夏普比率\t多样性\t负相关比例")
    for etf in selected_etfs:
        metrics = etf_metrics[etf]
        print(f"{etf}\t{metrics['score']:.4f}\t{metrics['return']:.2%}\t{metrics['volatility']:.2%}\t"
              f"{metrics['max_drawdown']:.2%}\t{metrics['sharpe']:.2f}\t"
              f"{metrics['diversity_score']:.2f}\t{metrics['neg_corr_ratio']:.2f}")
    
    # 计算并打印选中ETF之间的相关性矩阵
    sub_matrix = corr_matrix.loc[selected_etfs, selected_etfs]
    print("\n选中ETF之间的相关性矩阵:")
    print(sub_matrix)
    
    # 计算平均相关系数
    corr_values = []
    for i in range(len(selected_etfs)):
        for j in range(i+1, len(selected_etfs)):
            corr_values.append(sub_matrix.iloc[i, j])
    
    avg_corr = sum(corr_values) / len(corr_values) if corr_values else 0
    
    print(f"\n选中ETF的平均相关系数: {avg_corr:.4f}")
    
    return selected_etfs, etf_metrics, avg_corr
# 新增函数：在指定日期筛选ETF
def select_etf_at_date(current_date, config, next_date=None):
    """在指定日期筛选ETF并加载数据"""
    # 计算上市日期要求
    min_listing_date = (current_date - timedelta(days=config['lookback_period'])).strftime('%Y-%m-%d')
    
    # 构建查询对象
    hku_datetime = Datetime(current_date.year, current_date.month, current_date.day)
    # 计算开始日期 (当前日期减去回看期)
    start_datetime = current_date - timedelta(days=config['lookback_period'])
    hku_start_datetime = Datetime(start_datetime.year, start_datetime.month, start_datetime.day)
    query = Query(hku_start_datetime, hku_datetime, recover_type=Query.EQUAL_BACKWARD)
    # print(MA(AMO(s.get_kdata(query)),n=252)[-1] for s in sm if s.type == (constant.STOCKTYPE_ETF)),exit()
    # ETF初筛(专家经验)
    stks_1 = [s for s in sm if s.type == (constant.STOCKTYPE_ETF) and
            s.start_datetime <= Datetime(min_listing_date)] # 每次先满足日期要求,确保足够数据
    stks = []
    for s in stks_1:
        try:
            kdata = s.get_kdata(query)
            if len(kdata) < config['long_window']:  # 确保有足够的数据
                print(f"数据不足: {s.market_code}")
                continue
                
            amo_ma = MA(AMO(kdata), n=config['long_window'])
            if len(amo_ma) == 0 or amo_ma[-1] < config['min_volume']:
                continue
                
            close_data = CLOSE(kdata)
            ma_close = MA(close_data, n=config['short_window'])
            stdev_close = STDEV(close_data, n=config['short_window'])
            if len(ma_close) == 0 or len(stdev_close) == 0:
                continue
                
            volatility_ind = MA(stdev_close/ma_close, n=config['long_window'])
            if len(volatility_ind) == 0 or volatility_ind[-1] <= config['min_volatility']:
                continue
                
            stks.append(s)
        except Exception as e:
            print(f"处理{s.market_code}时出错: {e}")   
    
    print(f'{len(stks)}只ETF进入初筛...')
    # ETF综合筛(corr/shp/mdd综合评分)
    returns_dict = {}
    for stk in stks:
        try:
            kdata = stk.get_kdata(query)  # 获取回看期数据
            close = pd.Series([k.close for k in kdata], index=[k.datetime for k in kdata])
            returns = close.pct_change().dropna()
            returns_dict[stk.market_code] = returns
        except Exception as e:
            print(f"处理{stk.market_code}时出错: {e}")
            
    returns_df = pd.DataFrame(returns_dict)
    
    # 使用相关性筛选算法选择ETF
    selected_etfs, etf_metrics, avg_corr = do_select(
        returns_df,
        lookback_period=config['lookback_period'],
        max_etfs=config['max_etfs'],
        min_etfs=config['min_etfs'],
        corr_threshold=config['corr_threshold']
    )

    # 输出筛选结果
    print("\n筛选后的ETF列表:")
    for code in selected_etfs:
        stk = sm[code]
        print(f"{code} {stk.name}")

    print(f"筛选前ETF数量: {len(stks)}")
    print(f"筛选后ETF数量: {len(selected_etfs)}")
    
    # 加载完整历史数据，包括下一个分段的数据
    historical_start_date = (current_date - timedelta(days=config['lookback_period'] * 2)).strftime('%Y%m%d')
    
    # 如果提供了下一个日期，则使用它作为结束日期，否则使用当前日期
    if next_date:
        end_date = next_date.strftime('%Y%m%d')
    else:
        # 如果没有提供下一个日期，则使用当前日期加上步长作为结束日期
        end_date = (current_date + timedelta(days=config['step_days']+1)).strftime('%Y%m%d')
    
    # 调用HKUDataloader的get方法获取透视图
    full_returns_df = HKUDataloader.get(selected_etfs, col='close', 
                                start_date=historical_start_date, end_date=end_date,
                                freq='D', recover='EQUAL_BACKWARD')
    # 计算日收益率
    full_returns_df = full_returns_df.pct_change().dropna()
    
    return selected_etfs, full_returns_df, etf_metrics, avg_corr

# 修改保存结果函数，添加日期信息到文件名
def save_results_with_date(selected_etfs, returns_df, 
                           etf_metrics, avg_corr, current_date, 
                           outdir, performance_data,
                           config):
    """保存筛选结果，文件名包含日期信息"""
    date_str = current_date.strftime('%Y%m%d')
    
    # 保存选中的ETF列表
    with open(os.path.join(outdir, f"selected_etfs_{date_str}.txt"), "w", encoding="utf-8") as f:
        for etf in selected_etfs:
            stk = sm[etf]
            f.write(f"{etf}, # {stk.name}\n")
    
    # 保存收益率数据
    returns_df.to_csv(os.path.join(outdir, f"etf_returns_{date_str}.csv"), encoding="utf-8-sig")
    
    # 保存ETF指标
    metrics_df = pd.DataFrame.from_dict({k: v for k, v in etf_metrics.items() if k in selected_etfs}, orient='index')
    metrics_df.to_csv(os.path.join(outdir, f"etf_metrics_{date_str}.csv"), encoding="utf-8-sig")
    
    # # 保存相关性矩阵
    # corr_matrix = returns_df[selected_etfs].corr()
    # corr_matrix.to_csv(os.path.join(outdir, f"correlation_matrix_{date_str}.csv"), encoding="utf-8-sig")
    
    # # 绘制相关性热图
    # plt.figure(figsize=(10, 8))
    # sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', vmin=-1, vmax=1, center=0)
    # plt.title(f"ETF相关性矩阵 ({date_str}, 平均相关系数: {avg_corr:.4f})")
    # plt.tight_layout()
    # plt.savefig(os.path.join(outdir, f"correlation_heatmap_{date_str}.png"), dpi=300)
    # plt.close()
    
    # 区分样本内(in)和样本外(out)数据
    # 样本内数据：当前日期之前的数据
    in_start_date = current_date - timedelta(days=config['lookback_period'])  # 假设样本内为1年
    in_mask = (returns_df.index >= in_start_date) & (returns_df.index <= current_date)
    returns_in = returns_df[in_mask]
    
    # 样本外数据：当前日期之前的所有数据
    out_mask = returns_df.index < in_start_date
    returns_out = returns_df[out_mask]
    
    # 计算等权重组合
    weights = np.ones(len(selected_etfs)) / len(selected_etfs)
    
    # 计算样本内净值
    if not returns_in.empty:
        port_in = (returns_in[selected_etfs] * weights).sum(axis=1)
        nav_in = (1 + port_in).cumprod()
        
        # 保存样本内净值数据
        # nav_in.to_csv(os.path.join(outdir, f"nav_equal_weight_in_{date_str}.csv"), encoding="utf-8-sig")
        
        # # 绘制样本内净值曲线
        # plt.figure(figsize=(12, 6))
        # nav_in.plot(title=f"样本内等权重净值曲线 ({in_start_date.strftime('%Y-%m-%d')}至{current_date.strftime('%Y-%m-%d')})")
        # plt.grid(True, linestyle='--', alpha=0.7)
        # plt.ylabel("净值")
        # plt.tight_layout()
        # plt.savefig(os.path.join(outdir, f"nav_equal_weight_in_{date_str}.png"), dpi=300)
        # plt.close()
        
        # 计算样本内绩效指标
        annual_return_in = port_in.mean() * 252
        annual_vol_in = port_in.std() * np.sqrt(252)
        sharpe_in = annual_return_in / annual_vol_in if annual_vol_in > 0 else 0
        max_drawdown_in = ((nav_in - nav_in.cummax()) / nav_in.cummax()).min()
    
    # 计算样本外净值
    if not returns_out.empty:
        port_out = (returns_out[selected_etfs] * weights).sum(axis=1)
        nav_out = (1 + port_out).cumprod()
        
        # # 保存样本外净值数据
        # nav_out.to_csv(os.path.join(outdir, f"nav_equal_weight_out_{date_str}.csv"), encoding="utf-8-sig")
        
        # # 绘制样本外净值曲线
        # plt.figure(figsize=(12, 6))
        # nav_out.plot(title=f"样本外等权重净值曲线 (至{in_start_date.strftime('%Y-%m-%d')})")
        # plt.grid(True, linestyle='--', alpha=0.7)
        # plt.ylabel("净值")
        # plt.tight_layout()
        # plt.savefig(os.path.join(outdir, f"nav_equal_weight_out_{date_str}.png"), dpi=300)
        # plt.close()
        
        # 计算样本外绩效指标
        annual_return_out = port_out.mean() * 252
        annual_vol_out = port_out.std() * np.sqrt(252)
        sharpe_out = annual_return_out / annual_vol_out if annual_vol_out > 0 else 0
        max_drawdown_out = ((nav_out - nav_out.cummax()) / nav_out.cummax()).min()
    
    # 绘制完整净值曲线(样本外+样本内)
    if not returns_out.empty and not returns_in.empty:
        plt.figure(figsize=(14, 7))
        
        # 调整样本内净值起点与样本外终点对齐
        scale_factor = nav_out.iloc[-1] if len(nav_out) > 0 else 1.0
        nav_in_scaled = nav_in / nav_in.iloc[0] * scale_factor
        
        # 创建完整的净值序列
        full_dates = list(nav_out.index) + list(nav_in.index)
        full_values = list(nav_out.values) + list(nav_in_scaled.values)
        nav_full = pd.Series(full_values, index=full_dates)
        
        # 绘制完整净值曲线
        nav_full.plot(label="等权重组合净值")
        plt.axvline(x=in_start_date, color='r', linestyle='--', 
                   label=f"样本内/样本外分界 ({in_start_date.strftime('%Y-%m-%d')})")
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.title("等权重组合完整净值曲线")
        plt.ylabel("净值")
        plt.tight_layout()
        plt.savefig(os.path.join(outdir, f"nav_equal_weight_full_{date_str}.png"), dpi=300)
        plt.close()
        
    segment_perf = {
        'date': date_str,
        'in_start_date': in_start_date.strftime('%Y-%m-%d'),
        'in_end_date': current_date.strftime('%Y-%m-%d'),
        'in_annual_return': annual_return_in,
        'in_annual_vol': annual_vol_in,
        'in_sharpe': sharpe_in,
        'in_max_drawdown': max_drawdown_in
    }
    
    if not returns_out.empty:
        segment_perf.update({
            'out_start_date': returns_out.index[0].strftime('%Y-%m-%d'),
            'out_end_date': in_start_date.strftime('%Y-%m-%d'),
            'out_annual_return': annual_return_out,
            'out_annual_vol': annual_vol_out,
            'out_sharpe': sharpe_out,
            'out_max_drawdown': max_drawdown_out
        })
    else:
        # 确保所有行都有相同的列，即使没有样本外数据
        segment_perf.update({
            'out_start_date': '',
            'out_end_date': '',
            'out_annual_return': float('nan'),
            'out_annual_vol': float('nan'),
            'out_sharpe': float('nan'),
            'out_max_drawdown': float('nan')
        })
    
    performance_data.append(segment_perf)
    
    return performance_data
    

# 修改主函数，实现滚动筛选
def main():
    # 解析配置参数
    config = parse_args()
    
    # 转换日期字符串为datetime对象
    start_date = datetime.strptime(config['start_date'], '%Y%m%d')
    end_date = datetime.strptime(config['end_date'], '%Y%m%d')
    
    # 设置基础输出目录 - 修正目录命名方式
    ts = datetime.now().strftime("%Y%m%d_%H%M")
    script_name = os.path.basename(__file__).split('.')[0]  # 动态获取脚本文件名
    base_outdir = REPORTS_DIR.joinpath(f"{script_name}_{ts}")  # 使用脚本名称作为目录前缀
    
    os.makedirs(base_outdir, exist_ok=True)
    
    # 保存配置信息
    with open(os.path.join(base_outdir, "config.txt"), "w", encoding="utf-8") as f:
        for key, value in config.items():
            f.write(f"{key}: {value}\n")
    
    # 初始化当前日期
    current_date = start_date
    
    # 记录所有分段的ETF池
    all_segments = []
    performance_data = []
    
    print(f"\n开始ETF滚动筛选，从 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}，步长 {config['step_days']} 天")
    
    # 滚动筛选ETF
    while current_date <= end_date:
        date_str = current_date.strftime('%Y%m%d')
        print(f"\n\n{'='*50}")
        print(f"处理日期: {date_str}")
        print(f"{'='*50}")
        
        # 直接在基础目录下保存，不创建额外的子目录
        segment_outdir = base_outdir

        # 计算下一个日期
        next_date = current_date + timedelta(days=config['step_days']+1)
        if next_date > end_date:
            next_date = None
                    
        # 在当前日期筛选ETF
        selected_etfs, returns_df, etf_metrics, avg_corr = select_etf_at_date(current_date, config, next_date)
        
        # 保存筛选结果，添加日期信息到文件名
        performance_data = save_results_with_date(selected_etfs, 
                                                  returns_df, etf_metrics,
                                                  avg_corr, current_date,
                                                  segment_outdir, performance_data,
                                                  config)
        
        # 记录当前分段信息
        all_segments.append({
            'date': date_str,
            'etfs': selected_etfs,
            'avg_corr': avg_corr
        })
        
        # 更新当前日期
        current_date += timedelta(days=config['step_days'])
    
    # 保存所有分段信息
    segments_df = pd.DataFrame(all_segments)
    segments_df.to_csv(os.path.join(base_outdir, "all_segments.csv"), index=False, encoding="utf-8-sig")
     
    # 在主函数结束前一次性写入所有绩效数据到CSV
    if performance_data:
        # 创建DataFrame并保存为CSV
        perf_df = pd.DataFrame(performance_data)
        
        # 重新排列列顺序，使样本内和样本外数据并排显示
        cols_order = [
            'date', 
            'in_start_date', 'in_end_date', 'out_start_date', 'out_end_date',
            'in_annual_return', 'out_annual_return',
            'in_annual_vol', 'out_annual_vol',
            'in_sharpe', 'out_sharpe',
            'in_max_drawdown', 'out_max_drawdown'
        ]
        
        # 确保所有列都存在
        for col in cols_order:
            if col not in perf_df.columns:
                perf_df[col] = float('nan')
                
        # 按指定顺序排列列
        perf_df = perf_df[cols_order]
        
        # 格式化百分比列
        for col in ['in_annual_return', 'out_annual_return', 'in_annual_vol', 'out_annual_vol', 'in_max_drawdown', 'out_max_drawdown']:
            if col in perf_df.columns:
                perf_df[col] = perf_df[col].apply(lambda x: f"{x:.2%}" if pd.notnull(x) else "")
        
        # 格式化夏普比率列
        for col in ['in_sharpe', 'out_sharpe']:
            if col in perf_df.columns:
                perf_df[col] = perf_df[col].apply(lambda x: f"{x:.2f}" if pd.notnull(x) else "")
        
        # 保存为CSV
        perf_df.to_csv(os.path.join(base_outdir, "all_segments_performance.csv"), index=False, encoding="utf-8-sig")

    print(f"\n\n滚动筛选完成，共处理 {len(all_segments)} 个时间段")
    print(f"结果保存在: {base_outdir}")
    
    return base_outdir

if __name__ == '__main__':
    main()

    
