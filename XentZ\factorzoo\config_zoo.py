#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FactorZoo 配置模块
包含FactorZoo系统的路径配置和常量定义
"""

from pathlib import Path

# 路径配置
FACTOR_ZOO_DIR = Path("D:/myquant/FZoo/")
FACTOR_ZOO_DB = FACTOR_ZOO_DIR / "database" / "factorzoo.sqlite"

# 主要配置信息
FACTOR_ZOO_CONFIG = {
    'database_path': str(FACTOR_ZOO_DB),
    'database_dir': str(FACTOR_ZOO_DIR),
    'factor_values_dir': str(FACTOR_ZOO_DIR / "factor_values"),
    'evaluations_dir': str(FACTOR_ZOO_DIR / "evaluations"),
    'models_dir': str(FACTOR_ZOO_DIR / "models"),
    'exports_dir': str(FACTOR_ZOO_DIR / "exports"),
    'temp_dir': str(FACTOR_ZOO_DIR / "temp"),
    'logs_dir': str(FACTOR_ZOO_DIR / "logs"),
}

# ✨ 新增：因子值存储详细配置
FACTOR_VALUE_STORAGE_CONFIG = {
    # 主存储路径
    'by_batch_dir': str(FACTOR_ZOO_DIR / "factor_values" / "by_batch"),
    'by_symbol_dir': str(FACTOR_ZOO_DIR / "factor_values" / "by_symbol"), 
    'cross_section_dir': str(FACTOR_ZOO_DIR / "factor_values" / "cross_section"),
    
    # 缓存路径
    'cache_dir': str(FACTOR_ZOO_DIR / "factor_values" / "cache"),
    'expression_hash_dir': str(FACTOR_ZOO_DIR / "factor_values" / "cache" / "expression_hash"),
    'hot_factors_dir': str(FACTOR_ZOO_DIR / "factor_values" / "cache" / "hot_factors"),
    'temp_calc_dir': str(FACTOR_ZOO_DIR / "factor_values" / "cache" / "temp_calculations"),
    
    # 索引路径
    'index_dir': str(FACTOR_ZOO_DIR / "factor_values" / "index"),
    
    # 存储格式配置
    'base_data_format': 'feather',        # 基础数据：feather格式，读写最快
    'factor_values_format': 'parquet',    # 因子值：parquet格式，压缩效果好
    'compression': 'zstd',                # 压缩算法：ZSTD平衡压缩比和速度
    'compression_level': 3,               # 压缩级别：平衡速度和压缩比
    
    # 分片配置
    'max_columns_per_file': 150,          # 单文件最大列数，超过自动分片
    'max_file_size_mb': 200,              # 单文件最大大小(MB)
    
    # 缓存配置
    'memory_cache_size_mb': 1024,         # 内存缓存大小(MB)
    'expression_cache_ttl_days': 7,       # 表达式缓存TTL(天)
    'auto_cleanup_days': 30,              # 自动清理过期文件(天)
}

# ✨ 新增：文件命名规范
FILE_NAMING_PATTERNS = {
    'batch_dir': "{batch_id}",
    'batch_metadata': "metadata.json",
    'base_data': "base_data.{format}",
    'factor_values': "L{level}_{stage}_factors.{format}",
    'performance_metrics': "performance_metrics.json",
    'factor_index': "factor_index.json",
    'expression_hash': "{hash_8chars}.pkl",
    'hash_index': "hash_index.json"
}

# ✨ 新增：因子值处理配置
FACTOR_PROCESSING_CONFIG = {
    # 管道步骤定义
    'pipeline_steps': {
        'L0': {'name': 'selected_factors', 'description': 'GP挖掘+初筛因子(core输出)'},
        'L0_pj': {'name': 'selected_factors_pj', 'description': 'GP挖掘+初筛因子(pj类)'},
        'L1': {'name': 'raw_factors', 'description': 'GP原始挖掘因子'},
        'L2': {'name': 'filtered_factors', 'description': '筛选后因子'},
        'L3': {'name': 'final_factors', 'description': '最终入选因子'},
        'L4': {'name': 'portfolio_factors', 'description': '组合应用因子'}
    },
    
    # 支持的数据类型
    'supported_frequencies': ['1min', '5min', '15min', '30min', '1h', '1d', '1w', '1m'],
    'supported_symbols': ['stock', 'etf', 'future', 'crypto'],
    
    # 质量控制
    'min_valid_days': 60,                 # 最少有效交易日
    'max_missing_ratio': 0.1,             # 最大缺失值比例
    'outlier_detection': True,            # 是否启用异常值检测
}

# 数据库表配置
TABLE_CONFIGS = {
    'factors': {
        'required_fields': ['factor_id', 'factor_name', 'factor_expression'],
        'optional_fields': ['batch_id', 'target_label', 'pipeline_step']
    },
    'factor_batches': {
        'required_fields': ['batch_id', 'batch_name', 'creation_tool'],
        'optional_fields': ['source_symbols', 'generation_params']
    }
}

# 批次ID生成规则
BATCH_ID_PATTERNS = {
    'time_series': "GP_{symbol}_{start_date}_{end_date}_{frequency}_{timestamp}",
    'cross_section': "GP_{universe_id}_{start_date}_{end_date}_{frequency}_{timestamp}"
} 