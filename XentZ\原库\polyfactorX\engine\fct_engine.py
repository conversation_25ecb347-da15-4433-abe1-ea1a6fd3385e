import numpy as np
import pandas as pd
import time
from concurrent.futures import ProcessPoolExecutor, as_completed

import sys
from pathlib import Path

path_root = Path(__file__).parent.absolute().parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
from config import DATA_DIR_FACTOR, DATA_DIR_CSVS
from engine.modelers.fct_model import FactorModeler
from tasks.task_base import TaskFactor
import warnings
warnings.filterwarnings('ignore')

class FactorsTestEngine:
    def __init__(self, task:TaskFactor):  # 初始化
        self.task = task
        self.a_df = pd.DataFrame()
        self.fct_names = []
        self.final_df = pd.DataFrame()
        self._init_env()
        
    def _init_env(self):
        self.a_df, self.fct_names = self.task.load_a_symbol_fcts_direct() # a_df: date-tdate-close-fcts(多因子)
        # 单因子/单品种 装入一堆模型m
        self.modelers = [FactorModeler(self.task.a_symbol, fct_name, self.a_df[['tdate', 'close', 'open', fct_name]], 
                                       self.task.freq, self.task.split_dt, self.task.n_days) for fct_name in self.fct_names]
        # n_days非常重要,5~6比较好
    def run(self):
        with ProcessPoolExecutor(max_workers=self.task.job_num) as executor:
            results = {executor.submit(self._iter_func, m): m for m in self.modelers} # 通过这种方式，把我们的行情数据和因子数据进行一一对应
            for r in as_completed(results):
                try:
                    self.final_df = pd.concat([self.final_df, r.result()])
                except Exception as exception:
                    print(exception)
        
        # final_frame.to_csv('E:/Factor_Work_K/12_fct_reinforcement/result_reinforcement_0702.csv', encoding='utf-8-sig') # change
    def _iter_func(self, m: FactorModeler):
        # df['fct'] = fct_series.values # df: date-tdate-close-fct
        # ind_frame = m.backtest() 
        # print('frequency: {}\nfct_name: {}\n'.format(freq, col_name))
        # print(ind_frame)
        # print('\n')
        # print('夏普比率（样本外）：{}\n\n'.format(ind_frame.loc['样本外', '夏普比率']))  # 输出样本外夏普比率
        # #print('frequency: {} fct_name: {} 夏普比率（样本外）：{}\n\n'.format(freq, col_name, ind_frame.loc['样本外', '夏普比率']))
        # # 最终输出因子成绩的部分在这儿，可以输出一个只有sharpe的集合，然后对因子进行排序
        # # 向表格中添加参数列
        # #param_str = freq + '-' + col_name # 这一行不让他再加freq，保留最原始的因子名称
        # ind_frame['params'] = m.a_fct_name
        m.backtest()
        return None

def Fct_Analysis():
    task = TaskFactor()
    task.name = '单品种多因子分析'
    task.start_date = '20151223' #'20050223'
    task.end_date = '20170630'# '20221130'
    task.split_dt = '20161230'
    task.job_num = 8    # 8, 设置并行核数 设置为-1，将会调用电脑所有进程
    task.freq = '15'  # '15'min, 周期频率
    task.n_days = 1
    task.a_symbol = '510050.SH'
    task.a_fcts_file = 'fct_compare_0702.csv'
    return task
    
if __name__ == '__main__':
    start_time = time.time()
    e = FactorsTestEngine(Fct_Analysis())
    e.run()
    end_time = time.time()
    print('Time cost:=======', end_time - start_time)
    # e.analysis(console=False)      
 