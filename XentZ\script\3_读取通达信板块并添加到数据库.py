# %%
from hikyuu.interactive import *

# %%
##################修改这里的信息并执行即可导入板块############
#通达信板块文件名
tdx_bk= r'/home/<USER>/finance/tdx_data/T0002/blocknew/JJCG5~+BXCG1Y.blk'
#板块分类，名称
bk_category='自定义板块'
bk_name='上市一年以上刚子增仓'

# %%
def read_ebk(file_path):
    processed_lines = []
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()
            for line in lines:
                line = line.strip()
                if line :
                    first_digit = line[0]
                    if first_digit == '0':
                        first_digit = 'sz'
                    elif first_digit == '1':
                        first_digit = 'sh'
                  
                    new_line = first_digit + line[1:]
                    processed_lines.append(new_line)
    except FileNotFoundError:
        print(f"未找到文件: {file_path}")
    except Exception as e:
        print(f"发生错误: {e}")   
    return processed_lines

# %%
# 查询该板块是否存在，存在则清空，不存在则创建
bks=sm.get_block(bk_category,bk_name)
if len(bks)>0:#板块存在
    sm.remove_block(bks)
new_bk=Block(bk_category,bk_name)


# %%
stocklist = read_ebk(tdx_bk)
if stocklist:
    for s in stocklist:
        if not new_bk.add(s):
            print(s)
len(stocklist)

# %%
stks = [s for s in new_bk]
len(stks)

# %%
#存入数据库
sm.add_block(new_bk)
sm.get_block(bk_category,bk_name) 

# %%
len(sm.get_block(bk_category,bk_name) )


