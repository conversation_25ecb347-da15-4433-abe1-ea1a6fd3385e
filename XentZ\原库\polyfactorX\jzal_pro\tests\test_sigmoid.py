import numpy as np
import matplotlib.pyplot as plt


def sigmoid(x):
    return 1 / (1 + np.exp(-x))

def scaled_sigmoid(x, start, end):
    """当`x`落在`[start,end]`区间时，函数值为[0,1]且在该区间有较
    好的响应灵敏度
    """
    n = np.abs(start - end)

    score = 2/(1 + np.exp(-np.log(40_000)*
                    (x - start - n)/n + np.log(5e-3)))
    return score/2

# # 生成均匀分布的数据
# uniform_data = np.random.uniform(low=-5, high=5, size=10000)

# # Sigmoid 归一化
# normalized_uniform_data = sigmoid(uniform_data)

# # 绘制原始数据和归一化后的数据分布
# plt.figure(figsize=(12, 6))

# plt.subplot(1, 2, 1)
# plt.hist(uniform_data, bins=50, color='blue', alpha=0.7)
# plt.title('Original Data Distribution (Uniform)')

# plt.subplot(1, 2, 2)
# plt.hist(normalized_uniform_data, bins=50, color='green', alpha=0.7)
# plt.title('Sigmoid Normalized Data Distribution (Uniform Input)')

# plt.show()

# # 生成指数分布的数据
# exponential_data = np.random.exponential(scale=1, size=10000)

# # Sigmoid 归一化
# normalized_exponential_data = sigmoid(exponential_data)

# # 绘制原始数据和归一化后的数据分布
# plt.figure(figsize=(12, 6))

# plt.subplot(1, 2, 1)
# plt.hist(exponential_data, bins=50, color='blue', alpha=0.7)
# plt.title('Original Data Distribution (Exponential)')

# plt.subplot(1, 2, 2)
# plt.hist(normalized_exponential_data, bins=50, color='green', alpha=0.7)
# plt.title('Sigmoid Normalized Data Distribution (Exponential Input)')

# plt.show()

# 生成双峰分布的数据
bimodal_data = np.concatenate([
    np.random.normal(loc=-2, scale=0.5, size=5000),
    np.random.normal(loc=3, scale=0.5, size=5000)
])

# Sigmoid 归一化
normalized_bimodal_data = sigmoid(bimodal_data)

# 绘制原始数据和归一化后的数据分布
plt.figure(figsize=(12, 6))

plt.subplot(1, 2, 1)
plt.hist(bimodal_data, bins=50, color='blue', alpha=0.7)
plt.title('Original Data Distribution (Bimodal)')

plt.subplot(1, 2, 2)
plt.hist(normalized_bimodal_data, bins=50, color='green', alpha=0.7)
plt.title('Sigmoid Normalized Data Distribution (Bimodal Input)')

plt.show()