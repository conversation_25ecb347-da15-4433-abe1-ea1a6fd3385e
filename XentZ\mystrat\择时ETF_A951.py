#!/usr/bin/env python
# -*- coding: utf-8 -*-

from hikyuu import *
from hikyuu.interactive import *
import matplotlib.pyplot as plt

if __name__ == "__main__":
    # =============== 1. 策略参数设置 ===============
    start_date = Datetime(20200101) # 回测起始时间
    end_date = Datetime(20231231) # 样本内
    fstd = 1.0
    activate_percent = 0.10
    trailing_percent = 0.03
    BOLL_LEN = 78
    LKB_PERIOD = 252 # 日均成交额计算窗口
    MA_PERIOD = 21 # 波动率计算窗口
    initial_cash = 100000
    cost_func = get_part(f"mystar.other.tc_etf")

    # =============== 2. end_date回看交易日历处理 ==============
    sm = StockManager.instance() 
    temp_query = Query(end_date - TimeDelta(days=LKB_PERIOD*2), end_date+TimeDelta(days=10))  
    trading_days = sm.get_trading_calendar(temp_query,market='SH') 
    # 找到end_date在交易日历的位置
    end_pos = -1  
    for i, date in enumerate(trading_days):  
        if date >= end_date:  
            end_pos = i  
            break
    if end_pos >= 0 and end_pos >= LKB_PERIOD:  
        lkb_start_date = trading_days[end_pos - LKB_PERIOD]   
        qry_sel = Query(lkb_start_date, end_date, Query.DAY, Query.EQUAL_BACKWARD)
    else:
        hku_error("交易日历中没有足够的历史数据") 
    # =============== 3. ETF品种池海选 ==============
    # 1.ETF;2.上市日期不晚于回测起始时间
    stks = [s for s in sm if s.type == (constant.STOCKTYPE_ETF) and 
            s.start_datetime <= start_date] # 上市日期不晚于回测起始时间
    # 3.日均1亿额; 4.大于1%波动;
    stks = [s for s in stks if 
            MA(AMO(s.get_kdata(qry_sel)), n=MA_PERIOD)[-1] >= 10000 and # 日均1亿额
            MA(STDEV(CLOSE(s.get_kdata(qry_sel)), n=MA_PERIOD)/MA(CLOSE(s.get_kdata(qry_sel)), n=MA_PERIOD), n=LKB_PERIOD)[-1] > 0.01 # 1%波动(去量纲后)
            ] # 1%波动(去量纲后)
    # =============== 4. IND构建: BOLL ==============
    ind_bl = get_part('mystar.ind.EMA布林', band=fstd)
    ind_mb = RESULT(ind_bl, 0); ind_mb.name = "Middle Band"
    ind_ub = RESULT(ind_bl, 1); ind_bl.name = "Upper Band"
    ind_lb = RESULT(ind_bl, 2); ind_lb.name = "Lower Band"
    # end_date = Datetime(20210930)
    qry_test = Query(start_date, end_date, ktype=Query.DAY, recover_type=Query.EQUAL_BACKWARD)
    # qry_test = Query(-100,ktype=Query.MIN60)
    # qry_test = Query(-1000)
    k_test = stks[0].get_kdata(qry_test)
    ind_mb(k_test).plot(new=False)
    ind_ub(k_test).plot(new=False)
    ind_lb(k_test).plot(new=False)
    
    # =============== 5. SYS构建 ==============    
    sg1 = SG_Buy(CLOSE()>ind_ub)
    sg2 = SG_Sell(CLOSE()<ind_lb)
    tp = get_part('mystar.st.lamp_trail', 
                  activate_percent=activate_percent, 
                  trailing_percent=trailing_percent,
                  debug_if=True)
    sg = sg1 + sg2
    tm = crtTM(date=start_date, init_cash=initial_cash, cost_func=cost_func)
    mm = MM_FixedPercent(0.9)
    sys = SYS_Simple(tm=tm, sg=sg, mm=mm, tp=tp)
    sys.set_param("sell_delay", False)
    sys.set_param("ignore_sell_sg", False)  # 不忽略卖出信号
    sys.set_param("tp_delay_n", 0)  # 立即执行止盈
    # sys.set_param("trace", True)
    sys.run(stks[0], qry_test)
    sys.plot(new=False)
    
    # 运行系统后
    trades = sys.tm.get_trade_list()
    # 或者转换为DataFrame查看
    import pandas as pd
    trades_df = pd.DataFrame([
        {
            '日期': t.datetime,
            '代码': t.stock.code,
            '名称': t.stock.name,
            '业务': t.business,
            '计划价格': t.plan_price,
            '实际价格': t.real_price,
            '数量': t.number,
            '来源': t.part
        } for t in trades
    ])
    trades_df.to_csv('trades.csv', index=False)
    # 获取止盈价格时序数据
    tp_ind = tp.tp_price_to_ind(stks[0].market_code)
    tp_high_se = tp.get_highest_price_series(stks[0].market_code)
    # print(tp_high_se),exit()
    # print(tp_ind),exit()
    # tp_ind.plot(new=False,color='orange', linestyle='--')
    
    plt.show()