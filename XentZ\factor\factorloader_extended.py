#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FactorLoader扩展版本
集成FactorZoo因子值存储，提供混合加载模式
"""

import pandas as pd
import numpy as np
from typing import List, Optional, Dict, Union, Tuple
import time
from common.cls_base import BaseObj
from datafeed.expr_funcs.expr import calc_expr
from datafeed.features.feature_utils import FeatPreprocessing
from config.settings import get_norm_params
from factor.factorloader import FactorLoader
from factorzoo import factorstore


class FactorLoaderExtended(FactorLoader):
    """
    FactorLoader的扩展版本，集成因子值缓存功能
    
    主要新增功能：
    1. 优先从FactorZoo缓存加载因子值
    2. 缓存未命中时回退到实时计算
    3. 混合加载模式（部分缓存+部分实时）
    4. 性能监控和统计
    """
    
    def __init__(self):
        super().__init__()
        self.cache_stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'total_requests': 0,
            'cache_hit_rate': 0.0,
            'avg_cache_load_time': 0.0,
            'avg_calc_time': 0.0
        }
    
    @staticmethod
    def get_fct_df_hybrid(base_df: pd.DataFrame,
                          model_type: str = 'robust',
                          norm_type: str = 'X',
                          fcts: List[str] = None,
                          batch_id: Optional[str] = None,
                          pipeline_step: str = 'L2',
                          prefer_cache: bool = True,
                          cache_threshold: float = 0.7) -> pd.DataFrame:
        """
        混合模式加载因子数据：优先缓存，回退计算
        
        Args:
            base_df: 基础数据DataFrame
            model_type: 归一化模型类型
            norm_type: 归一化类型
            fcts: 因子表达式列表
            batch_id: 优先使用的批次ID
            pipeline_step: 管道步骤 ('L1', 'L2', 'L3', 'L4')
            prefer_cache: 是否优先使用缓存
            cache_threshold: 缓存命中率阈值，低于此值时使用实时计算
            
        Returns:
            pd.DataFrame: 因子值DataFrame
        """
        if base_df is None or base_df.empty:
            FactorLoaderExtended.log("输入的base_df为空", level="ERROR")
            return pd.DataFrame()
        
        fcts = fcts or []
        if not fcts:
            FactorLoaderExtended.log("因子表达式列表为空", level="WARNING")
            return base_df.copy()
        
        start_time = time.time()
        
        # 1. 尝试缓存加载
        if prefer_cache:
            cached_df = FactorLoaderExtended._try_cache_load(
                base_df, fcts, batch_id, pipeline_step, cache_threshold
            )
            
            if not cached_df.empty:
                load_time = time.time() - start_time
                FactorLoaderExtended.log(
                    f"缓存加载成功: {cached_df.shape} 耗时{load_time*1000:.1f}ms", 
                    level="INFO"
                )
                return cached_df
        
        # 2. 回退到实时计算
        FactorLoaderExtended.log("回退到实时计算模式", level="INFO")
        calc_df = FactorLoader.get_fct_df(
            base_df=base_df,
            model_type=model_type,
            norm_type=norm_type,
            fcts=fcts
        )
        
        calc_time = time.time() - start_time
        FactorLoaderExtended.log(
            f"实时计算完成: {calc_df.shape} 耗时{calc_time*1000:.1f}ms", 
            level="INFO"
        )
        
        return calc_df
    
    @staticmethod
    def _try_cache_load(base_df: pd.DataFrame,
                        fcts: List[str],
                        batch_id: Optional[str],
                        pipeline_step: str,
                        cache_threshold: float) -> pd.DataFrame:
        """尝试从缓存加载因子"""
        try:
            # 方案1: 使用指定批次ID
            if batch_id:
                return FactorLoaderExtended._load_from_batch(
                    base_df, fcts, batch_id, pipeline_step
                )
            
            # 方案2: 自动查找最佳批次
            return FactorLoaderExtended._load_from_best_batch(
                base_df, fcts, pipeline_step, cache_threshold
            )
            
        except Exception as e:
            FactorLoaderExtended.log(f"缓存加载失败: {e}", level="WARNING")
            return pd.DataFrame()
    
    @staticmethod
    def _load_from_batch(base_df: pd.DataFrame,
                         fcts: List[str],
                         batch_id: str,
                         pipeline_step: str) -> pd.DataFrame:
        """从指定批次加载因子"""
        FactorLoaderExtended.log(f"尝试从批次 {batch_id} 加载因子", level="INFO")
        
        # 加载批次数据
        cached_base, cached_factors = factorstore.load_batch_data(
            batch_id=batch_id,
            pipeline_step=pipeline_step,
            factor_names=fcts
        )
        
        if cached_factors.empty:
            FactorLoaderExtended.log(f"批次 {batch_id} 中未找到指定因子", level="WARNING")
            return pd.DataFrame()
        
        # 检查时间索引兼容性
        return FactorLoaderExtended._align_time_series(
            base_df, cached_base, cached_factors
        )
    
    @staticmethod
    def _load_from_best_batch(base_df: pd.DataFrame,
                              fcts: List[str],
                              pipeline_step: str,
                              cache_threshold: float) -> pd.DataFrame:
        """自动查找最佳批次并加载因子"""
        
        # 获取可用批次
        available_batches = factorstore.get_available_batches()
        
        if not available_batches:
            FactorLoaderExtended.log("未找到可用的因子批次", level="WARNING")
            return pd.DataFrame()
        
        FactorLoaderExtended.log(f"发现 {len(available_batches)} 个可用批次", level="INFO")
        
        # 评估每个批次的匹配度
        best_batch = None
        best_score = 0.0
        
        for batch_id in available_batches[-5:]:  # 检查最近5个批次
            score = FactorLoaderExtended._evaluate_batch_compatibility(
                base_df, fcts, batch_id, pipeline_step
            )
            
            if score > best_score and score >= cache_threshold:
                best_batch = batch_id
                best_score = score
        
        if best_batch:
            FactorLoaderExtended.log(
                f"选择最佳批次: {best_batch} (匹配度: {best_score:.2f})", 
                level="INFO"
            )
            return FactorLoaderExtended._load_from_batch(
                base_df, fcts, best_batch, pipeline_step
            )
        else:
            FactorLoaderExtended.log(
                f"未找到满足阈值 {cache_threshold} 的批次", 
                level="WARNING"
            )
            return pd.DataFrame()
    
    @staticmethod
    def _evaluate_batch_compatibility(base_df: pd.DataFrame,
                                      fcts: List[str],
                                      batch_id: str,
                                      pipeline_step: str) -> float:
        """评估批次与当前请求的兼容性"""
        try:
            # 获取批次信息
            batch_info = factorstore.get_batch_info(batch_id)
            if not batch_info:
                return 0.0
            
            # 加载少量数据进行评估
            cached_base, cached_factors = factorstore.load_batch_data(
                batch_id=batch_id,
                pipeline_step=pipeline_step
            )
            
            if cached_factors.empty:
                return 0.0
            
            # 计算匹配度
            available_factors = set(cached_factors.columns)
            requested_factors = set(fcts)
            
            # 因子匹配度 (50% 权重)
            factor_match = len(available_factors & requested_factors) / len(requested_factors)
            
            # 时间重叠度 (30% 权重)
            time_overlap = FactorLoaderExtended._calculate_time_overlap(
                base_df.index, cached_base.index
            )
            
            # 数据新鲜度 (20% 权重)  
            freshness = FactorLoaderExtended._calculate_freshness(batch_info)
            
            # 综合评分
            score = factor_match * 0.5 + time_overlap * 0.3 + freshness * 0.2
            
            FactorLoaderExtended.log(
                f"批次 {batch_id[-10:]} 评分: {score:.3f} "
                f"(因子:{factor_match:.2f}, 时间:{time_overlap:.2f}, 新鲜度:{freshness:.2f})", 
                level="DEBUG"
            )
            
            return score
            
        except Exception as e:
            FactorLoaderExtended.log(f"评估批次 {batch_id} 时出错: {e}", level="WARNING")
            return 0.0
    
    @staticmethod
    def _calculate_time_overlap(index1: pd.Index, index2: pd.Index) -> float:
        """计算时间索引重叠度"""
        if len(index1) == 0 or len(index2) == 0:
            return 0.0
        
        overlap = len(index1.intersection(index2))
        total = len(index1.union(index2))
        
        return overlap / total if total > 0 else 0.0
    
    @staticmethod
    def _calculate_freshness(batch_info: Dict) -> float:
        """计算批次新鲜度"""
        try:
            creation_time = batch_info.get('creation_time')
            if not creation_time:
                return 0.5  # 默认中等新鲜度
            
            from datetime import datetime
            if isinstance(creation_time, str):
                creation_dt = pd.to_datetime(creation_time)
            else:
                creation_dt = creation_time
            
            now = datetime.now()
            age_hours = (now - creation_dt).total_seconds() / 3600
            
            # 24小时内最新鲜，逐渐衰减
            if age_hours <= 24:
                return 1.0
            elif age_hours <= 168:  # 一周内
                return 0.8
            elif age_hours <= 720:  # 一个月内
                return 0.6
            else:
                return 0.3
            
        except Exception:
            return 0.5
    
    @staticmethod
    def _align_time_series(base_df: pd.DataFrame,
                          cached_base: pd.DataFrame,
                          cached_factors: pd.DataFrame) -> pd.DataFrame:
        """对齐时间序列数据"""
        try:
            # 找到重叠的时间范围
            overlap_index = base_df.index.intersection(cached_factors.index)
            
            if len(overlap_index) == 0:
                FactorLoaderExtended.log("缓存数据与请求数据时间范围无重叠", level="WARNING")
                return pd.DataFrame()
            
            # 检查重叠度是否足够
            overlap_ratio = len(overlap_index) / len(base_df.index)
            if overlap_ratio < 0.8:
                FactorLoaderExtended.log(
                    f"时间重叠度过低: {overlap_ratio:.2f}", 
                    level="WARNING"
                )
                return pd.DataFrame()
            
            # 返回重叠时间范围的因子数据
            aligned_factors = cached_factors.loc[overlap_index]
            
            FactorLoaderExtended.log(
                f"时间对齐完成: {aligned_factors.shape} (重叠度: {overlap_ratio:.2f})", 
                level="INFO"
            )
            
            return aligned_factors
            
        except Exception as e:
            FactorLoaderExtended.log(f"时间序列对齐失败: {e}", level="ERROR")
            return pd.DataFrame()
    
    @classmethod
    def get_available_factor_batches(cls, symbol: Optional[str] = None) -> List[Dict]:
        """获取可用的因子批次列表"""
        try:
            available_batches = factorstore.get_available_batches(symbol=symbol)
            
            batch_list = []
            for batch_id in available_batches:
                batch_info = factorstore.get_batch_info(batch_id)
                if batch_info:
                    batch_summary = {
                        'batch_id': batch_id,
                        'creation_time': batch_info.get('creation_time'),
                        'symbol': batch_info.get('symbol', 'unknown'),
                        'frequency': batch_info.get('frequency', 'unknown'),
                        'factor_counts': batch_info.get('factor_counts', {}),
                        'date_range': batch_info.get('date_range', 'unknown')
                    }
                    batch_list.append(batch_summary)
            
            return batch_list
            
        except Exception as e:
            cls.log(f"获取因子批次列表失败: {e}", level="ERROR")
            return []
    
    @classmethod 
    def preview_batch_factors(cls, 
                              batch_id: str, 
                              pipeline_step: str = 'L2',
                              max_factors: int = 10) -> Dict:
        """预览批次中的因子信息"""
        try:
            # 加载批次元数据
            batch_info = factorstore.get_batch_info(batch_id)
            
            # 加载因子数据
            base_data, factors = factorstore.load_batch_data(
                batch_id=batch_id,
                pipeline_step=pipeline_step
            )
            
            if factors.empty:
                return {
                    'error': f'批次 {batch_id} 的 {pipeline_step} 层无因子数据'
                }
            
            # 构建预览信息
            preview_info = {
                'batch_id': batch_id,
                'pipeline_step': pipeline_step,
                'batch_meta': batch_info,
                'data_shape': factors.shape,
                'time_range': {
                    'start': str(factors.index.min()),
                    'end': str(factors.index.max()),
                    'count': len(factors.index)
                },
                'factor_preview': list(factors.columns[:max_factors]),
                'total_factors': len(factors.columns)
            }
            
            if len(factors.columns) > max_factors:
                preview_info['more_factors'] = len(factors.columns) - max_factors
            
            # 添加因子统计
            factor_stats = {}
            for col in factors.columns[:5]:  # 统计前5个因子
                factor_stats[col] = {
                    'mean': float(factors[col].mean()),
                    'std': float(factors[col].std()),
                    'min': float(factors[col].min()),
                    'max': float(factors[col].max()),
                    'null_count': int(factors[col].isnull().sum())
                }
            
            preview_info['factor_stats'] = factor_stats
            
            return preview_info
            
        except Exception as e:
            cls.log(f"预览批次 {batch_id} 失败: {e}", level="ERROR")
            return {'error': str(e)}
    
    @classmethod
    def search_factors_by_expression(cls, 
                                     expression_pattern: str,
                                     pipeline_step: str = 'L2',
                                     max_results: int = 20) -> List[Dict]:
        """根据表达式模式搜索因子"""
        try:
            available_batches = factorstore.get_available_batches()
            
            results = []
            for batch_id in available_batches:
                try:
                    _, factors = factorstore.load_batch_data(
                        batch_id=batch_id,
                        pipeline_step=pipeline_step
                    )
                    
                    if factors.empty:
                        continue
                    
                    # 简单的模式匹配
                    matching_factors = [
                        col for col in factors.columns 
                        if expression_pattern.lower() in col.lower()
                    ]
                    
                    if matching_factors:
                        batch_info = factorstore.get_batch_info(batch_id)
                        
                        for factor_name in matching_factors[:max_results]:
                            results.append({
                                'batch_id': batch_id,
                                'factor_name': factor_name,
                                'pipeline_step': pipeline_step,
                                'symbol': batch_info.get('symbol', 'unknown'),
                                'creation_time': batch_info.get('creation_time'),
                                'data_shape': factors.shape
                            })
                            
                            if len(results) >= max_results:
                                break
                    
                    if len(results) >= max_results:
                        break
                        
                except Exception as e:
                    cls.log(f"搜索批次 {batch_id} 时出错: {e}", level="WARNING")
                    continue
            
            return results
            
        except Exception as e:
            cls.log(f"因子表达式搜索失败: {e}", level="ERROR")
            return []


# 向后兼容的别名
get_fct_df_from_factorzoo = FactorLoaderExtended.get_fct_df_hybrid


def demo_extended_usage():
    """演示扩展功能的使用方法"""
    print("🔧 FactorLoaderExtended 使用演示")
    print("=" * 50)
    
    # 1. 查看可用批次
    print("\n📋 查看可用的因子批次:")
    available_batches = FactorLoaderExtended.get_available_factor_batches()
    
    for i, batch in enumerate(available_batches[:3], 1):
        print(f"  {i}. {batch['batch_id']}")
        print(f"     品种: {batch['symbol']}")
        print(f"     创建时间: {batch['creation_time']}")
        print(f"     因子数量: {batch['factor_counts']}")
    
    if available_batches:
        # 2. 预览批次因子
        latest_batch = available_batches[-1]['batch_id']
        print(f"\n🔍 预览最新批次: {latest_batch}")
        
        preview = FactorLoaderExtended.preview_batch_factors(
            batch_id=latest_batch,
            pipeline_step='L2',
            max_factors=5
        )
        
        if 'error' not in preview:
            print(f"  数据形状: {preview['data_shape']}")
            print(f"  时间范围: {preview['time_range']['start']} ~ {preview['time_range']['end']}")
            print(f"  因子预览: {preview['factor_preview']}")
        
        # 3. 搜索特定因子
        print(f"\n🔎 搜索包含 'GP_L2' 的因子:")
        search_results = FactorLoaderExtended.search_factors_by_expression(
            expression_pattern="GP_L2",
            pipeline_step='L2',
            max_results=5
        )
        
        for result in search_results[:3]:
            print(f"  - {result['factor_name']} (批次: {result['batch_id'][-10:]})")
    
    print(f"\n💡 使用建议:")
    print(f"  1. 优先使用 get_fct_df_hybrid() 进行混合加载")
    print(f"  2. 设置合适的 cache_threshold 平衡性能和准确性")
    print(f"  3. 定期清理过期的因子批次数据")


if __name__ == "__main__":
    demo_extended_usage() 