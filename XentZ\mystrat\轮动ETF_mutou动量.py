#!/usr/bin/env python
# -*- coding: utf-8 -*-

from hikyuu import *

if __name__ == "__main__":
    # =============== 1. 策略参数设置 ===============
    etf_codes = get_part('mystar.other.etfs全球资产01@m')
    start_date = Datetime(20241001)
    end_date = None
    initial_cash = 10000
    rebalance_days = 1
    period = 63 # 回看周期
    momentum_window = 21
    top_n = 3
    cost_func = get_part(f"mystar.other.tc_etf")
    mm = MM_FixedPercent(0.9)
    # mm = get_part(f"mystar.mm.etf_fixed_percent", p=0.9,
    #               max_trade_number=5000000)
    sp = SP_FixedValue(0.002) # 2跳
    # ==================== 2. 系统参数配置 ===================
    options = {
        "stock_list": etf_codes,
        "ktype_list": ["day"],
        "load_history_finance": False,
        "load_weight": False,
        "start_spot": False,
        "spot_worker_num": 1,
    }
    load_hikyuu(**options)
    # =============== 3. 创建模拟交易账户/指标/交易系统 ===============
    tm = crtTM(date=start_date, init_cash=initial_cash, cost_func=cost_func)
    stks = tuple([sm[code] for code in options["stock_list"]])
    ind = get_part('mystar.ind.斜率乘动量除波动率',
                   se=CLOSE, period=period, 
                   momentum_window=momentum_window)
    # ind = get_part('mystar.ind.斜率年化乘R2',
    #                se=CLOSE, period=period)
    
    sys = SYS_Simple()
    sys.set_param("buy_delay", False)
    sys.set_param("sell_delay", False)
    sys.tm = tm
    sys.mm = mm
    sys.sp = sp
    sys.st = ST_FixedPercent(0.02)
    sg0 = SG_Cycle()
    sys.sg = sg0
    # =============== 4. 创建选择器/资产分配器/投资组合 ===============
    se = SE_MultiFactor([ind], topn=top_n, 
                        ref_stk = sm['sz159915'],
                        mode="MF_EqualWeight")
    se.add_stock_list(stks,sys)
    af = AF_EqualWeight()
    # af = AF_FixedWeightList([1.0,1.0,1.0])
    # af.set_param("adjust_running_sys",False)
    af.set_param("auto_adjust_weight", False)
    pf = PF_Simple(tm=tm, af=af, se=se, adjust_cycle=rebalance_days,
                    adjust_mode="query", delay_to_trading_day=True)
    # pf.set_param("trace", True)
    # =============== 5. 指定测试区间并运行回测 ===============
    query = Query(start_date, end_date, 
                  ktype=Query.DAY,recover_type=Query.BACKWARD)
    # query = Query(start_date, end_date)
    pf.run(query)
    # =============== 6. 结果保存与展示 ======================
    pf.tm.tocsv(os.path.dirname(os.path.abspath(__file__)))
    pf.performance()
    import matplotlib.pyplot as plt
    plt.show()
    