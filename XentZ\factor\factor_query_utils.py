#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
因子查询和批量处理工具模块
提供L2阶段因子查询、数据加载、批量处理等功能
采用过程式编程风格，注重执行效率和逻辑严谨性
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
from pathlib import Path
import time
import json

from common.cls_base import BaseObj
from factorzoo import factorzoo, factorstore


class FactorQueryManager(BaseObj):
    """因子查询管理器"""
    
    def __init__(self):
        """初始化因子查询管理器"""
        super().__init__()
        self.log("因子查询管理器初始化完成", "INFO")
    
    def query_l2_passed_factors(self, query_params: Dict) -> List[Dict]:
        """
        查询L2阶段通过的因子
        
        流程：
        1. 构建查询过滤条件
        2. 调用FactorZoo搜索接口
        3. 按品种分组统计
        4. 应用排除规则
        
        Args:
            query_params: 查询参数字典
                - source_pipeline_step: 源管道步骤，默认'L2'
                - factor_limit_per_batch: 每批次因子数量限制
                - status_filter: 状态过滤列表
                - exclude_symbols: 排除的品种列表
                
        Returns:
            List[Dict]: 因子信息列表
        """
        self.log("开始查询L2阶段通过的因子", "DEBUG")
        start_time = time.time()
        
        # 构建查询过滤条件
        filters = {
            'pipeline_step': query_params.get('source_pipeline_step', 'L2'),
            'status': query_params.get('status_filter', ['L2_PASSED']),
            'factor_limit_per_batch': query_params.get('factor_limit_per_batch', 100)
        }
        
        # 调用FactorZoo搜索接口
        try:
            factors = factorzoo.search_factors(
                filters=filters,
                limit=query_params.get('factor_limit_per_batch', 100)
            )
        except Exception as e:
            self.log(f"FactorZoo查询失败: {str(e)}", "ERROR")
            return []
        
        if not factors:
            self.log("未找到符合条件的因子", "WARNING")
            return []
        
        # 应用排除规则
        exclude_symbols = query_params.get('exclude_symbols', [])
        if exclude_symbols:
            filtered_factors = []
            for factor in factors:
                symbols = factor.get('symbols', [])
                if isinstance(symbols, list):
                    # 检查是否有交集
                    if not any(sym in exclude_symbols for sym in symbols):
                        filtered_factors.append(factor)
                else:
                    # 单个symbol的情况
                    if symbols not in exclude_symbols:
                        filtered_factors.append(factor)
            factors = filtered_factors
        
        # 按品种分组统计
        symbol_stats = self._analyze_factor_distribution(factors)
        
        elapsed_time = time.time() - start_time
        self.log(f"查询完成: 找到{len(factors)}个因子，耗时{elapsed_time:.2f}秒", "DEBUG")
        self.log(f"品种分布: {symbol_stats}", "DEBUG")
        
        return factors
    
    def _analyze_factor_distribution(self, factors: List[Dict]) -> Dict[str, int]:
        """分析因子在不同品种上的分布"""
        symbol_counts = {}
        
        for factor in factors:
            symbols = factor.get('symbols', 'unknown')
            if isinstance(symbols, list) and symbols:
                symbol = symbols[0]  # 取第一个品种
            else:
                symbol = str(symbols) if symbols else 'unknown'
            
            symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1
        
        return symbol_counts
    
    def load_factor_and_price_data(self, factor_info: Dict, date_range: Optional[Tuple[str, str]] = None) -> Tuple[pd.Series, pd.Series]:
        """
        加载因子值和价格数据，所有参数在此统一解构
        """
        factor_id = factor_info.get('factor_id', 'unknown')
        self.log(f"开始加载因子数据: {factor_id}", "DEBUG")

        # 统一解构参数
        batch_id = factor_info.get('batch_id')
        factor_name = factor_info.get('factor_name') or factor_info.get('factor_id')
        pipeline_step = factor_info.get('pipeline_step', 'L2')
        symbols = factor_info.get('symbols', [])
        if isinstance(symbols, str):
            try:
                symbols = json.loads(symbols)
            except Exception:
                symbols = [symbols]
        # 统一解析date_range
        if date_range is None:
            dr = factor_info.get('date_ranges')
            if dr:
                if isinstance(dr, str):
                    try:
                        dr = json.loads(dr)
                    except Exception:
                        dr = None
                if isinstance(dr, dict) and 'start' in dr and 'end' in dr:
                    date_range = (dr['start'], dr['end'])

        try:
            # 1. 加载因子值数据
            factor_series = self._load_factor_values(batch_id, factor_name, pipeline_step, date_range)
            if factor_series.empty:
                self.log(f"因子值数据为空: {factor_id}", "WARNING")
                return pd.Series(), pd.Series()
            # 2. 加载价格数据
            price_series = self._load_price_returns(symbols, date_range)
            if price_series.empty:
                self.log(f"价格数据为空: {factor_id}", "WARNING")
                return pd.Series(), pd.Series()
            # 3. 数据对齐
            factor_aligned, price_aligned = self._align_data(factor_series, price_series)
            self.log(f"数据加载完成: {factor_id}, 数据点数: {len(factor_aligned)}", "DEBUG")
            return factor_aligned, price_aligned
        except Exception as e:
            self.log(f"数据加载失败: {factor_id}, 错误: {str(e)}", "ERROR")
            return pd.Series(), pd.Series()

    def _load_factor_values(self, batch_id: Optional[str], factor_name: str, pipeline_step: str, date_range: Optional[Tuple[str, str]] = None) -> pd.Series:
        """加载因子值数据，只接收已解构参数"""
        if batch_id and factor_name:
            try:
                _, factors_df = factorstore.load_batch_data(
                    batch_id=batch_id,
                    pipeline_step=pipeline_step,
                    factor_names=[factor_name]
                )
                if not factors_df.empty and factor_name in factors_df.columns:
                    factor_series = factors_df[factor_name].dropna()
                    if date_range:
                        start_date, end_date = date_range
                        factor_series = factor_series.loc[start_date:end_date]
                    return factor_series
            except Exception as e:
                self.log(f"缓存加载失败: {str(e)}", "DEBUG")
        # 回退到实时计算（如果有表达式）
        # 这里省略，保持原逻辑
        return pd.Series()

    def _load_price_returns(self, symbols: list, date_range: Optional[Tuple[str, str]] = None) -> pd.Series:
        """加载价格收益率数据，只接收已解构参数"""
        symbol = None
        if isinstance(symbols, list) and symbols:
            symbol = symbols[0]
        elif isinstance(symbols, str):
            symbol = symbols
        if not symbol:
            self.log("无法确定品种信息", "WARNING")
            return pd.Series()
        # 这里实现价格数据加载逻辑时，直接用date_range过滤
        self.log(f"需要实现价格数据加载: {symbol}", "DEBUG")
        return pd.Series()
    
    def _align_data(self, factor_series: pd.Series, 
                   price_series: pd.Series) -> Tuple[pd.Series, pd.Series]:
        """数据对齐和清洗"""
        if factor_series.empty or price_series.empty:
            return pd.Series(), pd.Series()
        
        # 按索引对齐
        common_index = factor_series.index.intersection(price_series.index)
        if len(common_index) == 0:
            return pd.Series(), pd.Series()
        
        factor_aligned = factor_series.loc[common_index]
        price_aligned = price_series.loc[common_index]
        
        # 移除包含NaN的行
        valid_mask = factor_aligned.notna() & price_aligned.notna()
        factor_aligned = factor_aligned[valid_mask]
        price_aligned = price_aligned[valid_mask]
        
        return factor_aligned, price_aligned


class BatchProcessor(BaseObj):
    """批量处理器"""
    
    def __init__(self, max_workers: int = 4, chunk_size: int = 50):
        """
        初始化批量处理器
        
        Args:
            max_workers: 最大并行工作进程数
            chunk_size: 批处理块大小
        """
        super().__init__()
        self.max_workers = max_workers
        self.chunk_size = chunk_size
        self.log(f"批量处理器初始化完成: max_workers={max_workers}, chunk_size={chunk_size}", "INFO")
    
    def process_factors_batch(self, factors: List[Dict], 
                             process_func: callable,
                             progress_callback: Optional[callable] = None) -> List[Any]:
        """
        批量处理因子列表
        
        流程：
        1. 将因子列表分块
        2. 顺序处理每个块（避免资源竞争）
        3. 收集处理结果
        4. 报告进度
        
        Args:
            factors: 因子信息列表
            process_func: 处理函数，接受单个因子信息，返回处理结果
            progress_callback: 进度回调函数
            
        Returns:
            List[Any]: 处理结果列表
        """
        if not factors:
            self.log("因子列表为空", "WARNING")
            return []
        
        self.log(f"开始批量处理: {len(factors)}个因子", "INFO")
        start_time = time.time()
        
        results = []
        total_factors = len(factors)
        
        # 分块处理
        for i in range(0, total_factors, self.chunk_size):
            chunk = factors[i:i + self.chunk_size]
            chunk_start_time = time.time()
            
            # 顺序处理块内因子
            chunk_results = []
            for j, factor in enumerate(chunk):
                try:
                    result = process_func(factor)
                    chunk_results.append(result)
                    
                    # 进度回调
                    if progress_callback:
                        progress = (i + j + 1) / total_factors
                        progress_callback(progress, factor.get('factor_id', 'unknown'))
                        
                except Exception as e:
                    self.log(f"处理因子失败: {factor.get('factor_id', 'unknown')}, 错误: {str(e)}", "ERROR")
                    chunk_results.append(None)
            
            results.extend(chunk_results)
            
            chunk_time = time.time() - chunk_start_time
            self.log(f"块处理完成: {len(chunk)}个因子, 耗时{chunk_time:.2f}秒", "DEBUG")
        
        total_time = time.time() - start_time
        success_count = sum(1 for r in results if r is not None)
        
        self.log(f"批量处理完成: 成功{success_count}/{total_factors}, 总耗时{total_time:.2f}秒", "INFO")
        
        return results
    
    def create_progress_reporter(self, log_interval: int = 10) -> callable:
        """创建进度报告器"""
        last_log_time = [0]  # 使用列表避免闭包问题
        
        def progress_callback(progress: float, current_factor: str):
            current_time = time.time()
            if current_time - last_log_time[0] >= log_interval:
                self.log(f"处理进度: {progress*100:.1f}%, 当前因子: {current_factor}", "INFO")
                last_log_time[0] = current_time
        
        return progress_callback


# 全局实例
factor_query_manager = FactorQueryManager()
batch_processor = BatchProcessor()
