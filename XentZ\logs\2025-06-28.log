2025-06-28 18:25 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-28 18:25 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 18:25 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 18:25 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-28 18:25 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-28 18:25 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 18:25 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-28 18:25 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-28 18:25 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 18:25 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 18:25 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-28 18:25 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-28 18:25 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-28 18:25 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['BETA30', 'V_4', 'tr_index', 'STD5', 'JZ014_troc_mul2_9', 'V_0', 'JZ014_troc_mul3_14', 'OPEN3', 'JZ008_89', 'JZ004_10']...
2025-06-28 18:25 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 18:25 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-28 18:25 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-28 18:25 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-28 18:25 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-28 18:25 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-28 18:25 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-28 18:25 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-28 18:25 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征70
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(70个)，跳过相关性过滤
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 70), y形状: (1043,)
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 58
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-28 18:25 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=12, 饱和法=11, 平坦法=6 -> 选择=11
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=11, 最大R²=0.2606
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 58
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-28 18:25 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=10, 饱和法=14, 平坦法=9 -> 选择=10
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=10, 最大R²=0.2528
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 58
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-28 18:25 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=7, 饱和法=6, 平坦法=6 -> 选择=6
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2528
2025-06-28 18:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-28 18:25 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-28 18:25 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-28 18:25 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_gain.png
2025-06-28 18:25 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_weight.png
2025-06-28 18:25 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_cover.png
2025-06-28 18:25 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-28 18:25 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-28 18:26 | INFO     | FeatSelection.select_features: 选择特征: 14个 + base2keep: 4个 + 标签: 1个
2025-06-28 18:26 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:26 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 18:26 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 18:26 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 18:26 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 18:26 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 18:26 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 18:26 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 18:26 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 18:26 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 18:26 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 18:26 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 18:26 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 18:26 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 18:26 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 18:26 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 18:26 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 18:26 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:26 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 18:26 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 18:26 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 18:26 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: -2.09MB
2025-06-28 18:26 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 18:26 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:26 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 18:26 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 18:26 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 18:26 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 1.78MB
2025-06-28 18:26 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 18:26 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:26 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 18:26 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.00 seconds (00:00:00)
2025-06-28 18:26 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.00s user, 0.02s system
2025-06-28 18:26 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.45MB
2025-06-28 18:26 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 18:26 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_333113 took 0.05 seconds (00:00:00)
2025-06-28 18:26 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_333113 CPU usage: 0.08s user, 0.05s system
2025-06-28 18:26 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_333113 memory delta: 12.10MB
2025-06-28 18:26 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_333113 completed successfully
2025-06-28 18:32 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-28 18:32 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-28 18:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:39 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 18:39 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 18:39 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 18:39 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 18:39 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 18:39 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 18:39 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 18:39 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 18:39 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 18:39 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 18:39 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 18:39 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 18:39 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 18:39 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 18:39 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 18:39 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 18:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:39 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 18:39 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 18:39 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 18:39 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 0.05MB
2025-06-28 18:39 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 18:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:39 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 18:39 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 18:39 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 18:39 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: -0.18MB
2025-06-28 18:39 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 18:39 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:39 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 18:39 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.00 seconds (00:00:00)
2025-06-28 18:39 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-28 18:39 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: -0.78MB
2025-06-28 18:39 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 18:39 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_446347 took 0.05 seconds (00:00:00)
2025-06-28 18:39 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_446347 CPU usage: 0.03s user, 0.05s system
2025-06-28 18:39 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_446347 memory delta: 10.71MB
2025-06-28 18:39 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_446347 completed successfully
2025-06-28 18:44 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:44 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 18:44 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 18:44 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 18:44 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 18:44 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 18:44 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 18:44 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 18:44 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 18:44 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 18:44 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 18:44 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 18:44 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 18:44 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 18:44 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 18:44 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 18:44 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 18:44 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:44 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 18:44 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 18:44 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 18:44 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: -1.63MB
2025-06-28 18:44 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 18:44 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:44 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 18:44 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 18:44 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 18:44 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 0.42MB
2025-06-28 18:44 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 18:44 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:44 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 18:44 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.00 seconds (00:00:00)
2025-06-28 18:44 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-28 18:44 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: -0.83MB
2025-06-28 18:44 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 18:44 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_348436 took 0.05 seconds (00:00:00)
2025-06-28 18:44 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_348436 CPU usage: 0.06s user, 0.05s system
2025-06-28 18:44 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_348436 memory delta: 11.76MB
2025-06-28 18:44 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_348436 completed successfully
2025-06-28 18:47 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:47 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 18:47 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 18:47 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 18:47 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 18:47 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 18:47 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 18:47 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 18:47 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 18:47 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 18:47 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 18:47 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 18:47 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 18:47 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 18:47 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 18:47 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 18:47 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 18:47 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:47 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 18:47 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 18:47 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 18:47 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: -0.85MB
2025-06-28 18:47 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 18:47 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:47 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 18:47 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 18:47 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 18:47 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 0.28MB
2025-06-28 18:47 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 18:47 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:47 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 18:47 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.00 seconds (00:00:00)
2025-06-28 18:47 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.05s user, 0.00s system
2025-06-28 18:47 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-28 18:47 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 18:47 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_804397 took 0.05 seconds (00:00:00)
2025-06-28 18:47 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_804397 CPU usage: 0.09s user, 0.00s system
2025-06-28 18:47 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_804397 memory delta: 12.31MB
2025-06-28 18:47 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_804397 completed successfully
2025-06-28 18:53 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:53 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 18:53 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 18:53 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 18:53 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 18:53 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 18:53 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 18:53 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 18:53 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 18:53 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 18:53 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 18:53 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 18:53 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 18:53 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 18:53 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 18:53 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 18:53 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 18:53 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:53 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 18:53 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 3.92 seconds (00:00:03)
2025-06-28 18:53 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 3.72s user, 2.02s system
2025-06-28 18:53 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 551.36MB
2025-06-28 18:53 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: input array type is not double
2025-06-28 18:53 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:53 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 18:53 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 3.77 seconds (00:00:03)
2025-06-28 18:53 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 3.78s user, 1.66s system
2025-06-28 18:53 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 415.23MB
2025-06-28 18:53 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: input array type is not double
2025-06-28 18:53 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 18:53 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 18:53 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 3.67 seconds (00:00:03)
2025-06-28 18:53 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 2.25s user, 1.12s system
2025-06-28 18:53 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 225.68MB
2025-06-28 18:53 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: input array type is not double
2025-06-28 18:53 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_189292 took 11.40 seconds (00:00:11)
2025-06-28 18:53 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_189292 CPU usage: 9.80s user, 4.84s system
2025-06-28 18:53 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_189292 memory delta: 1208.23MB
2025-06-28 18:53 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_189292 completed successfully
2025-06-28 19:04 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:04 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 19:04 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 19:04 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 19:04 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 19:04 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 19:04 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 19:04 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 19:04 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 19:04 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 19:04 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 19:04 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 19:04 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 19:04 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 19:04 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 19:04 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 19:04 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 19:04 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:04 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:04 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 3.86 seconds (00:00:03)
2025-06-28 19:04 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 3.38s user, 2.58s system
2025-06-28 19:04 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 566.53MB
2025-06-28 19:04 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: input array type is not double
2025-06-28 19:04 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:04 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:04 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 3.66 seconds (00:00:03)
2025-06-28 19:04 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 3.56s user, 1.97s system
2025-06-28 19:04 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 424.08MB
2025-06-28 19:04 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: input array type is not double
2025-06-28 19:04 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:04 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:04 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 3.60 seconds (00:00:03)
2025-06-28 19:04 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 2.03s user, 1.22s system
2025-06-28 19:04 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 218.80MB
2025-06-28 19:04 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: input array type is not double
2025-06-28 19:04 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_355496 took 11.17 seconds (00:00:11)
2025-06-28 19:04 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_355496 CPU usage: 9.00s user, 5.80s system
2025-06-28 19:04 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_355496 memory delta: 1219.72MB
2025-06-28 19:04 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_355496 completed successfully
2025-06-28 19:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:09 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 19:09 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 19:09 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 19:09 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 19:09 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 19:09 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 19:09 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 19:09 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 19:09 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 19:09 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 19:09 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 19:09 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 19:09 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 19:09 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 19:09 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 19:09 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 19:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:09 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:09 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 3.96 seconds (00:00:03)
2025-06-28 19:09 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 3.66s user, 2.30s system
2025-06-28 19:09 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 539.73MB
2025-06-28 19:09 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: input array type is not double
2025-06-28 19:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:09 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:09 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 3.87 seconds (00:00:03)
2025-06-28 19:09 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 4.06s user, 1.78s system
2025-06-28 19:09 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 433.05MB
2025-06-28 19:09 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: input array type is not double
2025-06-28 19:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:09 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:09 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 4.43 seconds (00:00:04)
2025-06-28 19:09 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 2.39s user, 1.19s system
2025-06-28 19:09 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 239.61MB
2025-06-28 19:09 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: input array type is not double
2025-06-28 19:09 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_863940 took 12.31 seconds (00:00:12)
2025-06-28 19:09 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_863940 CPU usage: 10.19s user, 5.28s system
2025-06-28 19:09 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_863940 memory delta: 1223.66MB
2025-06-28 19:09 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_863940 completed successfully
2025-06-28 19:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:10 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 19:10 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 19:10 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 19:10 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 19:10 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 19:10 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 19:10 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 19:10 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 19:10 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 19:10 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 19:10 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 19:10 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 19:10 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 19:10 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 19:10 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 19:10 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 19:10 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:10 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:11 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 3.99 seconds (00:00:03)
2025-06-28 19:11 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 3.53s user, 2.53s system
2025-06-28 19:11 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 565.87MB
2025-06-28 19:11 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: input array type is not double
2025-06-28 19:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:11 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:11 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 4.16 seconds (00:00:04)
2025-06-28 19:11 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 4.17s user, 1.98s system
2025-06-28 19:11 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 428.66MB
2025-06-28 19:11 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: input array type is not double
2025-06-28 19:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:11 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:11 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 4.54 seconds (00:00:04)
2025-06-28 19:11 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 2.59s user, 1.39s system
2025-06-28 19:11 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 217.92MB
2025-06-28 19:11 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: input array type is not double
2025-06-28 19:11 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_546377 took 12.74 seconds (00:00:12)
2025-06-28 19:11 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_546377 CPU usage: 10.38s user, 5.94s system
2025-06-28 19:11 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_546377 memory delta: 1225.18MB
2025-06-28 19:11 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_546377 completed successfully
2025-06-28 19:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:11 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 19:11 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 19:11 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 19:11 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 19:11 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 19:11 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 19:11 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 19:11 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 19:11 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 19:11 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 19:11 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 19:11 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 19:11 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 19:11 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 19:11 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 19:11 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 19:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:11 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:11 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 4.11 seconds (00:00:04)
2025-06-28 19:11 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 3.55s user, 2.53s system
2025-06-28 19:11 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 567.32MB
2025-06-28 19:11 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: input array type is not double
2025-06-28 19:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:11 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:11 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 4.54 seconds (00:00:04)
2025-06-28 19:11 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 4.50s user, 2.62s system
2025-06-28 19:11 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 412.76MB
2025-06-28 19:11 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: input array type is not double
2025-06-28 19:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:11 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:11 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 4.55 seconds (00:00:04)
2025-06-28 19:11 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 2.88s user, 1.39s system
2025-06-28 19:11 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 234.36MB
2025-06-28 19:11 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: input array type is not double
2025-06-28 19:11 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_830600 took 13.92 seconds (00:00:13)
2025-06-28 19:11 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_830600 CPU usage: 11.06s user, 6.69s system
2025-06-28 19:11 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_830600 memory delta: 1236.99MB
2025-06-28 19:11 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_830600 completed successfully
2025-06-28 19:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:13 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 19:13 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 19:13 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 19:13 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 19:13 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 19:13 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 19:13 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 19:13 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 19:13 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 19:13 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 19:13 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 19:13 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 19:13 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 19:13 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 19:13 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 19:13 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 19:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:13 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:13 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 19:13 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:13 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 0.14MB
2025-06-28 19:13 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:13 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:13 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 19:13 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:13 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: -1.03MB
2025-06-28 19:13 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:13 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:13 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-28 19:13 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:13 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: -0.63MB
2025-06-28 19:13 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:13 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_132795 took 0.15 seconds (00:00:00)
2025-06-28 19:13 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_132795 CPU usage: 0.11s user, 0.08s system
2025-06-28 19:13 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_132795 memory delta: 31.54MB
2025-06-28 19:13 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_132795 completed successfully
2025-06-28 19:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:16 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 19:16 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 19:16 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 19:16 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 19:16 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 19:16 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 19:16 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 19:16 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 19:16 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 19:16 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 19:16 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 19:16 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 19:16 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 19:16 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 19:16 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 19:16 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 19:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:16 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:16 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 19:16 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:16 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 0.05MB
2025-06-28 19:16 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:16 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:16 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 19:16 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:16 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: -0.92MB
2025-06-28 19:16 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:16 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:16 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.00 seconds (00:00:00)
2025-06-28 19:16 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:16 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: -1.95MB
2025-06-28 19:16 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:16 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_634872 took 0.17 seconds (00:00:00)
2025-06-28 19:16 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_634872 CPU usage: 0.14s user, 0.12s system
2025-06-28 19:16 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_634872 memory delta: 34.37MB
2025-06-28 19:16 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_634872 completed successfully
2025-06-28 19:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:19 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 19:19 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 19:19 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 19:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:19 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:19 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 3.97 seconds (00:00:03)
2025-06-28 19:19 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 3.33s user, 2.70s system
2025-06-28 19:19 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 578.92MB
2025-06-28 19:19 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: input array type is not double
2025-06-28 19:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:19 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:19 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 3.83 seconds (00:00:03)
2025-06-28 19:19 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 4.02s user, 1.75s system
2025-06-28 19:19 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 427.60MB
2025-06-28 19:19 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: input array type is not double
2025-06-28 19:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:19 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:19 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 3.65 seconds (00:00:03)
2025-06-28 19:19 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 2.30s user, 0.86s system
2025-06-28 19:19 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 207.52MB
2025-06-28 19:19 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: input array type is not double
2025-06-28 19:19 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_735355 took 12.24 seconds (00:00:12)
2025-06-28 19:19 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_735355 CPU usage: 9.78s user, 5.45s system
2025-06-28 19:19 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_735355 memory delta: 1233.42MB
2025-06-28 19:19 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_735355 completed successfully
2025-06-28 19:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:19 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 19:19 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 19:19 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 19:19 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 19:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:19 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:19 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 3.99 seconds (00:00:03)
2025-06-28 19:19 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 3.22s user, 2.92s system
2025-06-28 19:19 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 575.68MB
2025-06-28 19:19 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: input array type is not double
2025-06-28 19:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:19 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:19 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 3.79 seconds (00:00:03)
2025-06-28 19:19 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 4.14s user, 1.72s system
2025-06-28 19:19 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 428.38MB
2025-06-28 19:19 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: input array type is not double
2025-06-28 19:19 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:19 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:19 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 4.26 seconds (00:00:04)
2025-06-28 19:19 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 2.14s user, 1.52s system
2025-06-28 19:19 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 219.09MB
2025-06-28 19:19 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: input array type is not double
2025-06-28 19:19 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_853166 took 12.78 seconds (00:00:12)
2025-06-28 19:19 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_853166 CPU usage: 9.62s user, 6.17s system
2025-06-28 19:19 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_853166 memory delta: 1246.59MB
2025-06-28 19:19 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_853166 completed successfully
2025-06-28 19:21 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-28 19:21 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-28 19:21 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 simple_test_batch: Missing optional dependency 'pyarrow'.  Use pip or conda to install pyarrow.
2025-06-28 19:21 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-28 19:21 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-28 19:21 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 GP_510300.SH_20240101_20240331_1d_20250628192146: Missing optional dependency 'pyarrow'.  Use pip or conda to install pyarrow.
2025-06-28 19:21 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 GP_510500.SH_20240101_20240331_1d_20250628192146: Missing optional dependency 'pyarrow'.  Use pip or conda to install pyarrow.
2025-06-28 19:21 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 GP_159985.SZ_20240101_20240331_1d_20250628192146: Missing optional dependency 'pyarrow'.  Use pip or conda to install pyarrow.
2025-06-28 19:21 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628192146/L2
2025-06-28 19:21 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628192146/L2 耗时0.0ms
2025-06-28 19:21 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628192146/L3
2025-06-28 19:21 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628192146/L3 耗时0.0ms
2025-06-28 19:21 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628192146/L4
2025-06-28 19:21 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628192146/L4 耗时0.0ms
2025-06-28 19:21 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510300.SH_20240101_20240331_1d_20250628192146/L4
2025-06-28 19:21 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510300.SH_20240101_20240331_1d_20250628192146/L4 耗时0.0ms
2025-06-28 19:21 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628192146/L1
2025-06-28 19:21 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628192146/L1 耗时0.0ms
2025-06-28 19:21 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628192146/L1
2025-06-28 19:21 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628192146/L1 耗时0.0ms
2025-06-28 19:21 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628192146/L3
2025-06-28 19:21 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628192146/L3 耗时0.0ms
2025-06-28 19:22 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-28 19:22 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-28 19:22 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 test_optimized_batch: Missing optional dependency 'pyarrow'.  Use pip or conda to install pyarrow.
2025-06-28 19:22 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 perf_test_batch: Missing optional dependency 'pyarrow'.  Use pip or conda to install pyarrow.
2025-06-28 19:23 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:23 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 19:23 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 19:23 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 19:23 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 19:23 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 19:23 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 19:23 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 19:23 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 19:23 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 19:23 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 19:23 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 19:23 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 19:23 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 19:23 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 19:23 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 19:23 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 19:23 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:23 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:23 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 3.91 seconds (00:00:03)
2025-06-28 19:23 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 3.31s user, 2.58s system
2025-06-28 19:23 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 573.04MB
2025-06-28 19:23 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: input array type is not double
2025-06-28 19:23 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:23 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:23 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 3.81 seconds (00:00:03)
2025-06-28 19:23 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 3.66s user, 1.94s system
2025-06-28 19:23 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 425.24MB
2025-06-28 19:23 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: input array type is not double
2025-06-28 19:23 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:23 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:23 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 3.62 seconds (00:00:03)
2025-06-28 19:23 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 2.05s user, 1.30s system
2025-06-28 19:23 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 222.90MB
2025-06-28 19:23 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: input array type is not double
2025-06-28 19:23 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_009505 took 12.50 seconds (00:00:12)
2025-06-28 19:23 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_009505 CPU usage: 9.12s user, 5.91s system
2025-06-28 19:23 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_009505 memory delta: 1246.52MB
2025-06-28 19:23 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_009505 completed successfully
2025-06-28 19:30 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:30 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 19:30 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 19:30 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 19:30 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 19:30 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 19:30 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 19:30 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 19:30 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 19:30 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 19:30 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 19:30 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 19:30 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 19:30 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 19:30 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 19:30 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 19:30 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 19:30 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:30 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:30 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 3.87 seconds (00:00:03)
2025-06-28 19:30 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 3.34s user, 2.27s system
2025-06-28 19:30 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 575.11MB
2025-06-28 19:30 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: input array type is not double
2025-06-28 19:30 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:30 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:30 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 3.80 seconds (00:00:03)
2025-06-28 19:30 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 4.16s user, 1.62s system
2025-06-28 19:30 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 432.99MB
2025-06-28 19:30 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: 'y_raw'
2025-06-28 19:30 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:30 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:30 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 3.59 seconds (00:00:03)
2025-06-28 19:30 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 2.16s user, 1.09s system
2025-06-28 19:30 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 216.76MB
2025-06-28 19:30 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: input array type is not double
2025-06-28 19:30 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_319221 took 11.99 seconds (00:00:11)
2025-06-28 19:30 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_319221 CPU usage: 9.83s user, 5.09s system
2025-06-28 19:30 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_319221 memory delta: 1244.70MB
2025-06-28 19:30 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_319221 completed successfully
2025-06-28 19:33 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:33 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 19:33 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 19:33 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 19:33 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 19:33 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 19:33 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 19:33 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 19:33 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 19:33 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 19:33 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 19:33 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 19:33 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 19:33 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 19:33 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 19:33 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 19:33 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 19:33 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:33 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:33 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 19:33 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:33 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 2.15MB
2025-06-28 19:33 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:33 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:33 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:33 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 19:33 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:33 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: -1.54MB
2025-06-28 19:33 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:33 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:33 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:33 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.00 seconds (00:00:00)
2025-06-28 19:33 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:33 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: -0.44MB
2025-06-28 19:33 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:33 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_466716 took 0.17 seconds (00:00:00)
2025-06-28 19:33 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_466716 CPU usage: 0.09s user, 0.20s system
2025-06-28 19:33 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_466716 memory delta: 35.25MB
2025-06-28 19:33 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_466716 completed successfully
2025-06-28 19:35 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-28 19:35 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-28 19:35 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 simple_test_batch: Missing optional dependency 'pyarrow'.  Use pip or conda to install pyarrow.
2025-06-28 19:36 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-28 19:36 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-28 19:36 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 GP_510300.SH_20240101_20240331_1d_20250628193603: Missing optional dependency 'pyarrow'.  Use pip or conda to install pyarrow.
2025-06-28 19:36 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 GP_510500.SH_20240101_20240331_1d_20250628193603: Missing optional dependency 'pyarrow'.  Use pip or conda to install pyarrow.
2025-06-28 19:36 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 GP_159985.SZ_20240101_20240331_1d_20250628193603: Missing optional dependency 'pyarrow'.  Use pip or conda to install pyarrow.
2025-06-28 19:36 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628193603/L2
2025-06-28 19:36 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628193603/L2 耗时1.0ms
2025-06-28 19:36 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628193603/L3
2025-06-28 19:36 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628193603/L3 耗时0.5ms
2025-06-28 19:36 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628193603/L4
2025-06-28 19:36 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628193603/L4 耗时0.5ms
2025-06-28 19:36 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628192146/L4
2025-06-28 19:36 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628192146/L4 耗时0.5ms
2025-06-28 19:36 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628193603/L1
2025-06-28 19:36 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628193603/L1 耗时1.0ms
2025-06-28 19:36 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628193603/L1
2025-06-28 19:36 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628193603/L1 耗时0.7ms
2025-06-28 19:36 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628193603/L3
2025-06-28 19:36 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628193603/L3 耗时0.0ms
2025-06-28 19:36 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-28 19:36 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-28 19:36 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-28 19:36 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-28 19:36 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 fixed_test_batch: Missing optional dependency 'pyarrow'.  Use pip or conda to install pyarrow.
2025-06-28 19:38 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-28 19:38 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-28 19:38 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 simple_test_batch: Missing optional dependency 'pyarrow'.  Use pip or conda to install pyarrow.
2025-06-28 19:43 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:43 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 19:43 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 19:43 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 19:43 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 19:43 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 19:43 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 19:43 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 19:43 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 19:43 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 19:43 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 19:43 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 19:43 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 19:43 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 19:43 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 19:43 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 19:43 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 19:43 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:43 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:43 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 19:43 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:43 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: -0.75MB
2025-06-28 19:43 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:43 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:43 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:43 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 19:43 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:43 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 0.34MB
2025-06-28 19:43 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:43 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:43 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:43 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.00 seconds (00:00:00)
2025-06-28 19:43 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:43 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.13MB
2025-06-28 19:43 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:43 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_606948 took 0.15 seconds (00:00:00)
2025-06-28 19:43 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_606948 CPU usage: 0.09s user, 0.11s system
2025-06-28 19:43 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_606948 memory delta: 34.11MB
2025-06-28 19:43 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_606948 completed successfully
2025-06-28 19:45 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:45 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 19:45 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 19:45 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 19:45 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 19:45 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 19:45 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 19:45 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 19:45 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 19:45 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 19:45 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 19:45 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 19:45 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 19:45 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 19:45 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 19:45 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 19:45 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 19:45 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:45 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:45 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 19:45 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:45 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 0.82MB
2025-06-28 19:45 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:45 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:45 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:45 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 19:45 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:45 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: -1.05MB
2025-06-28 19:45 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:45 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:45 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:45 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.00 seconds (00:00:00)
2025-06-28 19:45 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:45 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 1.13MB
2025-06-28 19:45 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:45 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_874568 took 0.15 seconds (00:00:00)
2025-06-28 19:45 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_874568 CPU usage: 0.17s user, 0.08s system
2025-06-28 19:45 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_874568 memory delta: 31.21MB
2025-06-28 19:45 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_874568 completed successfully
2025-06-28 19:46 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-28 19:46 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-28 19:46 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 simple_test_batch: Missing optional dependency 'pyarrow'.  Use pip or conda to install pyarrow.
2025-06-28 19:46 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-28 19:46 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-28 19:46 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 GP_510300.SH_20240101_20240331_1d_20250628194631: Missing optional dependency 'pyarrow'.  Use pip or conda to install pyarrow.
2025-06-28 19:46 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 GP_510500.SH_20240101_20240331_1d_20250628194631: Missing optional dependency 'pyarrow'.  Use pip or conda to install pyarrow.
2025-06-28 19:46 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 GP_159985.SZ_20240101_20240331_1d_20250628194631: Missing optional dependency 'pyarrow'.  Use pip or conda to install pyarrow.
2025-06-28 19:46 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628194631/L2
2025-06-28 19:46 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628194631/L2 耗时0.0ms
2025-06-28 19:46 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628194631/L3
2025-06-28 19:46 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628194631/L3 耗时0.0ms
2025-06-28 19:46 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628194631/L4
2025-06-28 19:46 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628194631/L4 耗时0.0ms
2025-06-28 19:46 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628193603/L4
2025-06-28 19:46 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628193603/L4 耗时0.0ms
2025-06-28 19:46 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628194631/L1
2025-06-28 19:46 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628194631/L1 耗时0.0ms
2025-06-28 19:46 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628194631/L1
2025-06-28 19:46 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628194631/L1 耗时0.0ms
2025-06-28 19:46 | WARNING  | FactorValueManager._optimized_disk_load: 未找到文件: GP_510500.SH_20240101_20240331_1d_20250628194631/L3
2025-06-28 19:46 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_510500.SH_20240101_20240331_1d_20250628194631/L3 耗时0.0ms
2025-06-28 19:46 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-28 19:46 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-28 19:46 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-28 19:46 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-28 19:46 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 fixed_test_batch: Missing optional dependency 'pyarrow'.  Use pip or conda to install pyarrow.
2025-06-28 19:48 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:48 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 19:48 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 19:48 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 19:48 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 19:48 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 19:48 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 19:48 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 19:48 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 19:48 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 19:48 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 19:48 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 19:48 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 19:48 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 19:48 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 19:48 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 19:48 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 19:48 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:48 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:48 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 19:48 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:48 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: -0.54MB
2025-06-28 19:48 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:48 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:48 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:48 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 19:48 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:48 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 1.28MB
2025-06-28 19:48 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:48 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:48 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:48 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.00 seconds (00:00:00)
2025-06-28 19:48 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:48 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 1.25MB
2025-06-28 19:48 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:48 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_227207 took 0.15 seconds (00:00:00)
2025-06-28 19:48 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_227207 CPU usage: 0.16s user, 0.09s system
2025-06-28 19:48 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_227207 memory delta: 34.64MB
2025-06-28 19:48 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_227207 completed successfully
2025-06-28 19:58 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:58 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 19:58 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 19:58 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 19:58 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 19:58 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 19:58 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 19:58 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 19:58 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 19:58 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 19:58 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 19:58 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 19:58 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 19:58 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 19:58 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 19:58 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 19:58 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 19:58 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:58 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:58 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 19:58 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:58 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 1.65MB
2025-06-28 19:58 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:58 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:58 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:58 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 19:58 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:58 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 0.75MB
2025-06-28 19:58 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:58 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:58 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:58 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.00 seconds (00:00:00)
2025-06-28 19:58 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:58 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 1.79MB
2025-06-28 19:58 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:58 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_466270 took 0.16 seconds (00:00:00)
2025-06-28 19:58 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_466270 CPU usage: 0.11s user, 0.09s system
2025-06-28 19:58 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_466270 memory delta: 34.28MB
2025-06-28 19:58 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_466270 completed successfully
2025-06-28 19:59 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:59 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 19:59 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 19:59 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 19:59 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 19:59 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 19:59 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 19:59 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 19:59 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 19:59 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 19:59 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 19:59 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 19:59 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 19:59 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 19:59 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 19:59 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 19:59 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 19:59 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:59 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:59 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 19:59 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:59 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 0.02MB
2025-06-28 19:59 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:59 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:59 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:59 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 19:59 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:59 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 2.04MB
2025-06-28 19:59 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:59 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 19:59 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 19:59 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.00 seconds (00:00:00)
2025-06-28 19:59 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-28 19:59 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: -1.67MB
2025-06-28 19:59 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 19:59 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_353218 took 0.15 seconds (00:00:00)
2025-06-28 19:59 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_353218 CPU usage: 0.08s user, 0.06s system
2025-06-28 19:59 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_353218 memory delta: 34.07MB
2025-06-28 19:59 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_353218 completed successfully
2025-06-28 20:04 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:04 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 20:04 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 20:04 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 20:04 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 20:04 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 20:04 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 20:04 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 20:04 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 20:04 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 20:04 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 20:04 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 20:04 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 20:04 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 20:04 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 20:04 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 20:04 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 20:04 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:04 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:04 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 20:04 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:04 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: -0.80MB
2025-06-28 20:04 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 20:04 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:04 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:04 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 20:04 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:04 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-28 20:04 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 20:04 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:04 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:04 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.00 seconds (00:00:00)
2025-06-28 20:04 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:04 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: -0.11MB
2025-06-28 20:04 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 20:04 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_365115 took 0.16 seconds (00:00:00)
2025-06-28 20:04 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_365115 CPU usage: 0.19s user, 0.09s system
2025-06-28 20:04 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_365115 memory delta: 34.93MB
2025-06-28 20:04 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_365115 completed successfully
2025-06-28 20:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:09 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 20:09 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 20:09 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 20:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:09 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:09 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 20:09 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:09 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 0.25MB
2025-06-28 20:09 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 20:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:09 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:09 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 20:09 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:09 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 1.66MB
2025-06-28 20:09 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 20:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:09 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:09 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-28 20:09 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:09 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.53MB
2025-06-28 20:09 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 20:09 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_011541 took 0.16 seconds (00:00:00)
2025-06-28 20:09 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_011541 CPU usage: 0.14s user, 0.14s system
2025-06-28 20:09 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_011541 memory delta: 33.44MB
2025-06-28 20:09 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_011541 completed successfully
2025-06-28 20:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:09 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 20:09 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 20:09 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 20:09 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 20:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:09 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:09 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 20:09 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:09 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 1.12MB
2025-06-28 20:09 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 20:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:09 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:09 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 20:09 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:09 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 0.50MB
2025-06-28 20:09 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 20:09 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:09 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:09 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.00 seconds (00:00:00)
2025-06-28 20:09 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:09 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-28 20:09 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 20:09 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_588171 took 0.15 seconds (00:00:00)
2025-06-28 20:09 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_588171 CPU usage: 0.14s user, 0.05s system
2025-06-28 20:09 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_588171 memory delta: 29.63MB
2025-06-28 20:09 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_588171 completed successfully
2025-06-28 20:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:11 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 20:11 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 20:11 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 20:11 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 20:11 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 20:11 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 20:11 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 20:11 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 20:11 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 20:11 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 20:11 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 20:11 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 20:11 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 20:11 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 20:11 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 20:11 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 20:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:11 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:11 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 20:11 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:11 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 1.25MB
2025-06-28 20:11 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 20:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:11 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:11 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 20:11 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:11 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 0.01MB
2025-06-28 20:11 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 20:11 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:11 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:11 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.00 seconds (00:00:00)
2025-06-28 20:11 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:11 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 2.07MB
2025-06-28 20:11 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 20:11 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_252285 took 0.15 seconds (00:00:00)
2025-06-28 20:11 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_252285 CPU usage: 0.14s user, 0.08s system
2025-06-28 20:11 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_252285 memory delta: 33.10MB
2025-06-28 20:11 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_252285 completed successfully
2025-06-28 20:15 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:15 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 20:15 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 20:15 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 20:15 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 20:15 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 20:15 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 20:15 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 20:15 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 20:15 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 20:15 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 20:15 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 20:15 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 20:15 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 20:15 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 20:15 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 20:15 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 20:15 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:15 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:15 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 20:15 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:15 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: -1.45MB
2025-06-28 20:15 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 20:15 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:15 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:15 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 20:15 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:15 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 0.49MB
2025-06-28 20:15 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 20:15 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:15 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:15 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.00 seconds (00:00:00)
2025-06-28 20:15 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:15 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.62MB
2025-06-28 20:15 | ERROR    | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 failed with error: 'super' object has no attribute '__sklearn_tags__'
2025-06-28 20:15 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_805598 took 0.15 seconds (00:00:00)
2025-06-28 20:15 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_805598 CPU usage: 0.06s user, 0.14s system
2025-06-28 20:15 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_805598 memory delta: 34.74MB
2025-06-28 20:15 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_805598 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 20:16 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 20:16 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 20:16 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 20:16 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 20:16 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 20:16 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 20:16 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 20:16 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 20:16 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 20:16 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 20:16 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 20:16 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 20:16 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 20:16 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 20:16 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 4.08 seconds (00:00:04)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 3.78s user, 2.28s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 549.95MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-28 20:16 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.05 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.06s user, 0.05s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 9.71MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.02 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.02s user, 0.02s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 5.23MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.03s user, 0.00s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: -0.24MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 9.47 seconds (00:00:09)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 13.42s user, 3.33s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 772.04MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-28 20:16 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.06MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.62 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.47s user, 0.03s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 2.04MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-28 20:16 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 0.04 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 0.05s user, 0.00s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.02 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.03s user, 0.00s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.03s user, 0.00s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 0.07 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 0.06s user, 0.00s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: 7.72MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-28 20:16 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 0.02 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 0.02s user, 0.00s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.58 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.42s user, 0.06s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.05MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-28 20:16 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 0.05 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 0.05s user, 0.00s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.03 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.03s user, 0.00s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.02s user, 0.00s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 0.08 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 0.06s user, 0.00s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: 11.71MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-28 20:16 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 0.02 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 0.02s user, 0.00s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-28 20:16 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-28 20:16 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-28 20:16 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: CALC_F_VALUES_SH510050 took 0.00 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: CALC_F_VALUES_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: CALC_F_VALUES_SH510050 memory delta: 0.00MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: CALC_F_VALUES_SH510050 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | FactorLoader.get_fct_df: 开始计算2个因子表达式
2025-06-28 20:16 | INFO     | FactorLoader.get_fct_df: 返回结果: 2个指定因子
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: CALC_PJ_VALUES_SH510050 took 0.00 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: CALC_PJ_VALUES_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: CALC_PJ_VALUES_SH510050 memory delta: -0.00MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: CALC_PJ_VALUES_SH510050 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 GP_SH510050_20250628_L0_691874: Missing optional dependency 'pyarrow'.  Use pip or conda to install pyarrow.
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.00 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 0.00MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:16 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250628_L0_691874_label_1_001 took 0.49 seconds (00:00:00)
2025-06-28 20:16 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250628_L0_691874_label_1_001 CPU usage: 0.02s user, 0.00s system
2025-06-28 20:16 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250628_L0_691874_label_1_001 memory delta: 0.17MB
2025-06-28 20:16 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250628_L0_691874_label_1_001 completed successfully
2025-06-28 20:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:17 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250628_L0_PJ_691874_label_1_001 took 0.53 seconds (00:00:00)
2025-06-28 20:17 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250628_L0_PJ_691874_label_1_001 CPU usage: 0.00s user, 0.02s system
2025-06-28 20:17 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250628_L0_PJ_691874_label_1_001 memory delta: 0.00MB
2025-06-28 20:17 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250628_L0_PJ_691874_label_1_001 completed successfully
2025-06-28 20:17 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:17 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250628_L0_PJ_691874_label_1_002 took 0.43 seconds (00:00:00)
2025-06-28 20:17 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250628_L0_PJ_691874_label_1_002 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:17 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250628_L0_PJ_691874_label_1_002 memory delta: 0.00MB
2025-06-28 20:17 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250628_L0_PJ_691874_label_1_002 completed successfully
2025-06-28 20:17 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_691874 took 30.03 seconds (00:00:30)
2025-06-28 20:17 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_691874 CPU usage: 19.02s user, 6.06s system
2025-06-28 20:17 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_691874 memory delta: 1393.63MB
2025-06-28 20:17 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_691874 completed successfully
2025-06-28 20:17 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:17 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 20:17 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 20:17 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 20:17 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 20:17 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 20:17 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 20:17 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 20:17 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 20:17 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 20:17 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 20:17 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 20:17 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 20:17 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 20:17 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 20:17 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 20:17 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 20:17 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:17 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:17 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 4.14 seconds (00:00:04)
2025-06-28 20:17 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 4.17s user, 2.23s system
2025-06-28 20:17 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 576.77MB
2025-06-28 20:17 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-28 20:17 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:17 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-28 20:17 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-28 20:17 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.05 seconds (00:00:00)
2025-06-28 20:17 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.08s user, 0.02s system
2025-06-28 20:17 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 7.31MB
2025-06-28 20:17 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-28 20:17 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:17 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.02 seconds (00:00:00)
2025-06-28 20:17 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.05s user, 0.05s system
2025-06-28 20:17 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 6.36MB
2025-06-28 20:17 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-28 20:17 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:17 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-06-28 20:17 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:17 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: -1.11MB
2025-06-28 20:17 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-28 20:17 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 9.52 seconds (00:00:09)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 13.03s user, 3.09s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 779.51MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-28 20:18 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.05MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.60 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.44s user, 0.11s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 1.98MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-28 20:18 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 0.05 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 0.05s user, 0.00s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.03 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.02s user, 0.00s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.01 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.02s user, 0.00s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 0.07 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 0.20s user, 0.19s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: 13.05MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-28 20:18 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 0.02 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.64 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.50s user, 0.05s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.05MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-28 20:18 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 0.06 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 0.05s user, 0.00s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: 0.25MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.03 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.03s user, 0.00s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.02s user, 0.00s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 0.07 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 0.06s user, 0.00s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: 1.49MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-28 20:18 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 0.00 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 0.02s user, 0.00s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-28 20:18 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-28 20:18 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-28 20:18 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: CALC_F_VALUES_SH510050 took 0.00 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: CALC_F_VALUES_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: CALC_F_VALUES_SH510050 memory delta: 0.00MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: CALC_F_VALUES_SH510050 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | FactorLoader.get_fct_df: 开始计算2个因子表达式
2025-06-28 20:18 | INFO     | FactorLoader.get_fct_df: 返回结果: 2个指定因子
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: CALC_PJ_VALUES_SH510050 took 0.01 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: CALC_PJ_VALUES_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: CALC_PJ_VALUES_SH510050 memory delta: 0.00MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: CALC_PJ_VALUES_SH510050 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | ERROR    | FactorValueManager.save_batch_data: 保存批次数据失败 GP_SH510050_20250628_L0_549404: 'L0_pj'
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.05 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.03s user, 0.00s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 5.37MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250628_L0_549404_label_1_001 took 0.00 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250628_L0_549404_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250628_L0_549404_label_1_001 memory delta: 0.01MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250628_L0_549404_label_1_001 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250628_L0_PJ_549404_label_1_001 took 0.00 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250628_L0_PJ_549404_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250628_L0_PJ_549404_label_1_001 memory delta: 0.00MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250628_L0_PJ_549404_label_1_001 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250628_L0_PJ_549404_label_1_002 took 0.00 seconds (00:00:00)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250628_L0_PJ_549404_label_1_002 CPU usage: 0.00s user, 0.02s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250628_L0_PJ_549404_label_1_002 memory delta: 0.00MB
2025-06-28 20:18 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250628_L0_PJ_549404_label_1_002 completed successfully
2025-06-28 20:18 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_549404 took 27.66 seconds (00:00:27)
2025-06-28 20:18 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_549404 CPU usage: 19.27s user, 5.94s system
2025-06-28 20:18 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_549404 memory delta: 1419.82MB
2025-06-28 20:18 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_549404 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-28 20:21 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-28 20:21 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-28 20:21 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-28 20:21 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-28 20:21 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-28 20:21 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-28 20:21 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-28 20:21 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-28 20:21 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-28 20:21 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-28 20:21 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-28 20:21 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-28 20:21 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-28 20:21 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-28 20:21 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 4.16 seconds (00:00:04)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 3.53s user, 2.47s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 590.73MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-28 20:21 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.05 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.12s user, 0.02s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 6.86MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.03 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.05s user, 0.02s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 2.00MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: -1.68MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 9.34 seconds (00:00:09)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 13.00s user, 2.89s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 775.98MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-28 20:21 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.06MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 0.61 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 0.42s user, 0.05s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 2.16MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-28 20:21 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 0.05 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 0.05s user, 0.00s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: 0.01MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.03 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.03s user, 0.00s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.01 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 0.07 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 0.12s user, 0.03s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: 6.55MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-28 20:21 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 0.00 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 0.60 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 0.48s user, 0.02s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 0.25MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-28 20:21 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 0.05 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 0.05s user, 0.00s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.02 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.03s user, 0.00s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.01 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.02s user, 0.00s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: 0.02MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 0.06 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 0.06s user, 0.00s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: 4.19MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-28 20:21 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 0.02 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-28 20:21 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-28 20:21 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-28 20:21 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: CALC_F_VALUES_SH510050 took 0.00 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: CALC_F_VALUES_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: CALC_F_VALUES_SH510050 memory delta: 0.00MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: CALC_F_VALUES_SH510050 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | FactorLoader.get_fct_df: 开始计算2个因子表达式
2025-06-28 20:21 | INFO     | FactorLoader.get_fct_df: 返回结果: 2个指定因子
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: CALC_PJ_VALUES_SH510050 took 0.01 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: CALC_PJ_VALUES_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: CALC_PJ_VALUES_SH510050 memory delta: 0.00MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: CALC_PJ_VALUES_SH510050 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_SH510050_20250628_L0_607982
2025-06-28 20:21 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_SH510050_20250628_L0_607982 耗时105.8ms
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.11 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.03s user, 0.00s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 5.93MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250628_L0_607982_label_1_001 took 0.00 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250628_L0_607982_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250628_L0_607982_label_1_001 memory delta: 0.01MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250628_L0_607982_label_1_001 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250628_L0_PJ_607982_label_1_001 took 0.02 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250628_L0_PJ_607982_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250628_L0_PJ_607982_label_1_001 memory delta: 0.00MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250628_L0_PJ_607982_label_1_001 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250628_L0_PJ_607982_label_1_002 took 0.00 seconds (00:00:00)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250628_L0_PJ_607982_label_1_002 CPU usage: 0.00s user, 0.00s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250628_L0_PJ_607982_label_1_002 memory delta: 0.00MB
2025-06-28 20:21 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250628_L0_PJ_607982_label_1_002 completed successfully
2025-06-28 20:21 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250628_L0_607982 took 28.69 seconds (00:00:28)
2025-06-28 20:21 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250628_L0_607982 CPU usage: 18.42s user, 5.64s system
2025-06-28 20:21 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250628_L0_607982 memory delta: 1428.26MB
2025-06-28 20:21 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250628_L0_607982 completed successfully
