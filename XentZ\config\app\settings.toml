# XentZ 框架应用配置
# 这个文件只包含驱动框架运行的核心、长期稳定的配置
# 具体的研究任务参数应该放在 config/tasks/ 目录下

# ============================== 系统路径配置 ============================== #
# 所有路径配置统一在此管理，作为整个系统的基础
[paths]
# 主要数据存储目录
data_root = "D:/myquant/datahist/"
# 报告输出目录  
reports_root = "D:/myquant/reports/XentZ"
# 因子动物园根目录
factorzoo_root = "D:/myquant/FZoo/"
# 日志存储目录
logs_root = "logs"

# 子目录配置 (相对于上述根目录)
[paths.subdirs]
hku_data = "hku"           # HKU数据子目录 (相对于 data_root)
cache = "cache"            # 缓存子目录 (相对于 data_root)
factor_db = "databases"    # 因子数据库子目录 (相对于 factorzoo_root)
factor_values = "values"   # 因子值存储子目录 (相对于 factorzoo_root)

# ============================== 因子动物园 (FactorZoo) ============================== #
[factorzoo]
# 因子值存储格式
factor_value_format = "parquet"  # parquet, feather, csv
# 因子缓存策略
cache_strategy = "lru"  # lru, fifo, none
# 数据库文件名 (位于 factorzoo_root/subdirs.factor_db/ 下)
database_filename = "factor_zoo.sqlite"

# 因子库缓存配置 (对应 factorzoo/config_cache.py 中的设置)
[factorzoo.cache]
enabled = true
max_cache_size_gb = 10  # 因子数据内存缓存上限

# ============================== 系统常量 ============================== #
[system_defaults]
# 这些是系统级的常量，很少需要修改
annual_trading_days = 252
seconds_per_day = 86400
default_timezone = "Asia/Shanghai"

# ============================== 计算基础设施 ============================== #
[compute]
# 默认并行处理核心数 (-1 表示使用所有可用核心)
default_n_jobs = -1
# 数值计算精度
float_precision = "float64"
# 向量化计算优化
vectorization_backend = "pandas"  # pandas, polars, dask

# ============================== 日志配置 ============================== #
[logging]
level = "INFO"  # DEBUG, INFO, WARNING, ERROR
# 日志轮转配置
max_log_size_mb = 100
backup_count = 5

# ============================== 支持的算法类型注册表 ============================== #
# 注意: 这里只注册算法的"类型"，不包含具体参数
# 具体参数应该在各个任务的配置文件中指定

[supported_algorithms.missing]
# 支持的缺失值处理算法类型
available_methods = ["ffill", "bfill", "mean", "median", "interpolate"]

[supported_algorithms.outliers]
# 支持的异常值处理算法类型
available_methods = ["mad_clip", "zscore_clip", "iqr_clip", "none"]

[supported_algorithms.normalization]
# 支持的标准化算法类型
available_methods = ["linear", "robust", "neutral", "none"]

[supported_algorithms.labeling]
# 支持的标签生成算法类型
available_methods = ["single_period", "multi_period", "custom"]