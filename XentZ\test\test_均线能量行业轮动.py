#!/usr/bin/env python
# -*- coding:utf-8 -*-

from hikyuu import *
import matplotlib.pyplot as plt

class ROCPSellCycleSignal(SignalBase):
    """周期信号, 支持轮内ROCP超过阀值卖出 (每天轮的意义不大)
    
    参数：
        rocp_days (int): ROCP计算天数
        take_profit (float): 止盈点,当ROCP大于此值时卖出
    """
    def __init__(self, rocp_days=5, take_profit=0.02, debug_if=False):
        super(ROCPSellCycleSignal, self).__init__("ROCPSellCycleSignal")
        # 设置参数
        self.set_param("rocp_days", rocp_days)  
        self.set_param("take_profit", take_profit)
        self.set_param("cycle", True)  # 标记为周期信号 --- 表示传入为切片数据
        self.set_param("alternate", False)  # 不要交替买卖
        self.set_param("debug_if",debug_if)
        
    def _clone(self):
        """复制函数，必须实现"""
        return ROCPSellCycleSignal(
            self.get_param("rocp_days"),
            self.get_param("take_profit")
        )
        
    def _calculate(self, k):
        """计算卖出信号"""
        if len(k) <= 0:
            return
                    
        # 获取参数
        rocp_days = self.get_param("rocp_days")
        take_profit = self.get_param("take_profit")
        debug_if = self.get_param("debug_if")
        
        # 获取股票和当前日期
        stock = k.get_stock()
        try:
            # 获取完整历史数据
            complete_k = self.to # 获得全量kdata
            # complete_k = stock.get_kdata(Query(-500))  # 获得足够长
            if complete_k.empty():
                return
            # 关键改进：遍历整个切片，确保所有交易日都被检查
            trace_head = f'[SG] {stock.code}_{stock.name}'
            hku_debug_if(debug_if,f'{trace_head} {k[0].datetime} - {k[-1].datetime}')
            for i in range(len(k)):
                # 获取当前交易日
                current_date = k[i].datetime
                # 在完整历史数据中找到对应位置
                current_pos = complete_k.get_pos(current_date)
                # 计算ROCP并判断
                if current_pos is not None and current_pos >= rocp_days:
                    current_rocp = (complete_k[current_pos].close - 
                                complete_k[current_pos-rocp_days].close) / complete_k[current_pos-rocp_days].close
                    
                    if i == 0: # 切片首个k
                        if current_rocp > take_profit: # 超过止盈点生成卖出信号
                            self._add_sell_signal(current_date) # TODO: 如果有持仓
                            hku_debug_if(debug_if,f'{trace_head} 切片首K ROCP:{current_rocp:.2f}>{take_profit:.2f} >>> {current_date}添加卖出sg后本轮休息')
                            # 本轮休息
                            return
                        else:
                            self._add_buy_signal(current_date)
                            hku_debug_if(debug_if,f'{trace_head} 切片首K ROCP:{current_rocp:.2f} >>> {current_date}首k添加买入sg')
                    else:
                        if current_rocp > take_profit: # 超过止盈点生成卖出信号
                            self._add_sell_signal(current_date) # TODO: 如果有持仓
                            hku_debug_if(debug_if,f'{trace_head} 切片内K ROCP:{current_rocp:.2f}>{take_profit:.2f} >>> {current_date}添加卖出sg后本轮休息')
                            return # 本轮休息
                        else:
                            hku_debug_if(debug_if,f'{trace_head} 切片内K ROCP:{current_rocp:.2f} >>> {current_date}无动作')
                else:
                    # 切片数据不足则直接买入
                    self._add_buy_signal(k[0].datetime)  # 周期开始日期
                    hku_debug_if(debug_if,f'{trace_head} 切片数据不足 >>> {current_date}首K添加买入sg')
        except Exception as e:
            print(f"计算ROCP卖出信号时发生错误: {e}")


def ts_rank(ind: Indicator, period: int = 9) -> Indicator:
    """
    真正灵活的时间序列百分比排名实现
    - 不使用for循环
    - 不硬编码周期
    - 支持任意period值
    - 完全在hikyuu指标体系内
    """
    # 计算窗口有效性
    valid_window = BARSCOUNT(ind) >= period
    
    # 核心思想：使用数学关系计算排名
    # 关键突破：基于移动最大值和最小值构建滑动窗口比较
    
    # 1. 计算当前值相对于窗口最大值和最小值的位置
    min_val = LLV(ind, period)
    max_val = HHV(ind, period)
    
    # 2. 使用线性插值估计排名百分比
    # 当前值在[min, max]范围内的相对位置
    rel_pos = (ind - min_val) / (max_val - min_val)
    
    # 3. 调整为更准确的百分比排名
    # 使用分布特性修正
    
    # 3.1 计算窗口内大于中位数的值的比例
    # 使用偏离均值的方式估计
    ma_val = MA(ind, period)
    above_ma = (ind > ma_val)
    ma_above_ratio = MA(above_ma, period)
    
    # 3.2 基于偏度修正排名
    # 使用窗口内的分布特性修正排名
    std_val = STD(ind, period)
    z_score = (ind - ma_val) / IF(std_val == 0, 0.0001, std_val)
    
    # 3.3 组合多种估计方法
    # 窗口内等值情况处理
    rank_est1 = rel_pos * 100  # 基于范围的估计
    rank_est2 = (ma_above_ratio + 0.5 * (ind == ma_val)) * 100  # 基于均值的估计
    
    # 4. 选择更准确的估计
    # 范围法在极端分布下不准确，均值法在正态分布下较准确
    equal_values = (max_val == min_val)
    final_rank = IF(equal_values, 50, rank_est2)
    
    # 5. 应用窗口有效性
    result = IF(valid_window, final_rank, 0)
    
    return result

def ma_energy(ind:Indicator, period: int = 20) -> Indicator:
    # 计算移动平均线
    ma = MA(ind, period) 
    # 计算均线的差分
    diff = ma - REF(ma, 1)
    # 确定方向
    direction = IF(diff > 0, 1, IF(diff < 0, -1, 0))
    direction = IF(ISNA(diff), 0, direction)
    # 方向变化点
    dir_change = direction != REF(direction, 1)
    # 使用COUNT计算序列位置
    days = COUNT(CVAL(1), 0)  # 创建一个从1开始递增的序列
    # 标记每个方向变化点的位置
    change_pos = IF(dir_change, days, 0)
    # 这相当于找到每个区间的起始位置
    last_change = HHV(change_pos, 0)
    # 计算自上次变化以来的周期数
    count = days - last_change
    # 处理方向为0的情况
    count = IF(direction == 0, 0, count)
    # 计算能量值：方向 × 累计计数
    energy = direction * count
    # 将MA无效位置的能量设为0
    energy = IF(ISNA(ma), 0, energy)
    print(energy)
    
    return energy

def my_func():
    # =============== 1. 策略参数设置 ===============
    # ETF列表
    etf_codes = [
        'sh512880',  # 证券ETF
        'sh512480',  # 半导体ETF
        'sh515030',  # 新能车
        'sz159996',  # 家电ETF
        'sh512170',  # 医药ETF
        'sh515080',  # 中证红利
    ]
    # 
    start_date = Datetime(20200101)
    end_date = None
    initial_cash = 200000
    rebalance_days = 1
    rocp_days = 18
    take_profit = 0.03
    top_n = 2
    cost_func = TC_FixedA2017()
    # ==================== 2. 系统参数配置 ===================
    options = {
        "stock_list": etf_codes,
        "ktype_list": ["day"],
        "load_history_finance": False,
        "load_weight": False,
        "start_spot": False,
        "spot_worker_num": 1,
    }
    load_hikyuu(**options)
    # =============== 3. 创建模拟交易账户/指标/交易系统 ===============
    tm = crtTM(date=start_date, init_cash=initial_cash, cost_func=cost_func)
    stks = tuple([sm[code] for code in options["stock_list"]])
    k0 = stks[0].get_kdata(Query(-500))
    ind_cn = ma_energy(CLOSE, 20)
    ind_se = MA(ts_rank(LOW,20),10)
    sys = SYS_Simple()
    sys.set_param("buy_delay", False)
    sys.set_param("sell_delay", False)
    sys.tm = tm
    sys.cn = CN_Bool(ind_cn>0)
    sys.mm = MM_FixedPercent(0.9)  # 使用90%的资金进行购买
    # sg0 = ROCPSellCycleSignal(rocp_days=rocp_days, 
    #                           take_profit=take_profit,
    #                           debug_if=True)
    # sys.sg = sg0
    sys.sg = SG_Cycle()
    # =============== 4. 创建选择器/资产分配器/投资组合 ===============
    se = SE_MultiFactor([ind_se], topn=top_n, 
                        ref_stk = sm['sz159915'],
                        mode="MF_EqualWeight")
    se.add_stock_list(stks,sys)
    af = AF_EqualWeight()
    pf = PF_Simple(tm=tm, af=af, se=se, adjust_cycle=rebalance_days,
                    adjust_mode="query", delay_to_trading_day=True)
    # pf.set_param("trace", True)
    # =============== 5. 指定测试区间并运行回测 ===============
    query = Query(start_date, end_date, 
                  ktype=Query.DAY, recover_type=Query.BACKWARD)
    query = Query(start_date, end_date)
    pf.run(query)
    # =============== 6. 结果保存与展示 ======================
    pf.tm.tocsv(os.path.dirname(os.path.abspath(__file__)))
    pf.performance()
    import matplotlib.pyplot as plt
    plt.show()
    # =============== 5. 实盘LIVE ===========================
    # 获取qmt订单代理
    # QMT_ACCOUNT = "********"  # 字符串
    # QMT_PATH = r'D:\p-installed\gjQMT_sim\userdata_mini'  # 自己的QMT交易终端地址
    # stragegy_name = "实盘的DEMO"
    # qmt_broker = get_part("star.other.broker_qmt",
    #                     account=QMT_ACCOUNT, path=QMT_PATH, stg_name=stragegy_name)
    # qmt_broker = crtOB(qmt_broker)
    

    # hku_info("策略执行************************")
    # my_pf = pf
    # run_in_strategy(pf=my_pf, query=Query(Datetime(********)),
    #                 broker=qmt_broker, cost_func=cost_func, other_brokers=[])

if __name__ == "__main__":
    my_func()
    etf_codes = [
        'sh512880',  # 证券ETF
        'sh512480',  # 半导体ETF
        'sh515030',  # 新能车
        'sz159996',  # 家电ETF
        'sh512170',  # 医药ETF
        'sh515080',  # 中证红利
    ]
    # s = Strategy(etf_codes,  [Query.DAY])
    # # 每交易日 14点52分 执行
    # s.run_daily_at(my_func, TimeDelta(0, 14, 52))
    # s.start()