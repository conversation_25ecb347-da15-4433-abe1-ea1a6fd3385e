2025-06-16 17:01 | DEBUG    | BaseObj.get_norm_params: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-16 17:01 | DEBUG    | BaseObj.get_norm_params: skip
2025-06-16 17:02 | DEBUG    | BaseObj.get_norm_params: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-16 17:02 | DEBUG    | BaseObj.get_norm_params: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-16 17:24 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-16 17:24 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-16 17:24 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-16 17:24 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-16 17:24 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-16 17:24 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-16 17:24 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-16 17:24 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-16 17:24 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-16 17:24 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-16 17:24 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-16 17:24 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 273 列
2025-06-16 17:24 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-16 17:24 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-16 17:24 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['b_macd_hist', 'CLOSE4', 'b_macd_wt_hist', 'JZ014_trends_mul3_34', 'MA5', 'JZ008_9', 'JZ014_troc_mul3_34', 'STD10', 'JZ011_14', 'HIGH4']...
2025-06-16 17:24 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-16 17:24 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-16 17:24 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-16 17:24 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-16 17:25 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-16 17:26 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-16 17:26 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-16 17:26 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-16 17:26 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-16 17:26 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-16 17:26 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-16 17:26 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-16 17:26 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-16 17:26 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-16 17:26 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-16 17:26 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 273 列
2025-06-16 17:26 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-16 17:26 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-16 17:26 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['HIGH3', 'R_3', 'CLOSE3', 'QTLU5', 'STD10', 'JZ011_34', 'JZ008_89', 'V_0', 'JZ012_34', 'OPEN0']...
2025-06-16 17:26 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-16 17:26 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-16 17:26 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-16 17:26 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-16 17:40 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-16 17:40 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-16 17:40 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-16 17:40 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-16 17:40 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-16 17:40 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-16 17:40 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-16 17:40 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-16 17:40 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-16 17:40 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-16 17:40 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-16 17:41 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 273 列
2025-06-16 17:41 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-16 17:41 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-16 17:41 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['QTLD10', 'tr_ma10', 'QTLU10', 'KUP', 'tr_index', 'BETA5', 'OPEN0', 'JZ014_troc_mul3_14', 'JZ004_98', 'LOW3']...
2025-06-16 17:41 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-16 17:41 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-16 17:41 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-16 17:41 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-16 17:41 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-16 17:41 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-16 17:41 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-16 17:41 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-16 17:41 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-16 17:42 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 148 -> 137
2025-06-16 17:42 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 137 个特征 (窗口=20)
2025-06-16 17:42 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-16 17:42 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 206 个特征中保留了 122 个特征 (base2keep: 4, 冗余筛选: 118, 相关性阈值=0.85, 聚类数=119)
2025-06-16 17:42 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-16 17:51 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-16 17:51 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-16 17:51 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-16 17:51 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-16 17:51 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-16 17:51 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-16 17:51 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-16 17:51 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-16 17:51 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-16 17:51 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-16 17:51 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-16 17:51 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-16 17:51 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-16 17:51 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['b_macd_hist', 'roc_02', 'HIGH1', 'tr_ma20', 'OPEN2', 'V_3', 'JZ011_34', 'HIGH0', 'JZ004_98', 'R_4']...
2025-06-16 17:51 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-16 17:51 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-16 17:51 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-16 17:51 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-16 17:51 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-16 17:51 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-16 17:51 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-16 17:52 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-16 17:52 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-16 18:03 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-16 18:03 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-16 18:03 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-16 18:03 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-16 18:03 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-16 18:03 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-16 18:03 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-16 18:03 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-16 18:03 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-16 18:03 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-16 18:03 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-16 18:03 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-16 18:03 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-16 18:03 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['R_0', 'STD5', 'JZ008_89', 'R_2', 'HIGH4', 'JZ014_trends_mul2_55', 'JZ004_80', 'V_2', 'BETA5', 'JZ011_34']...
2025-06-16 18:03 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-16 18:03 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-16 18:03 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-16 18:03 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-16 18:03 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-16 18:03 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-16 18:03 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-16 18:03 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-16 18:03 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-16 18:03 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 70 列, 1045 行
2025-06-16 18:06 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 290 -> 257
2025-06-16 18:06 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 257 个特征 (窗口=20)
2025-06-16 18:06 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-16 18:06 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 326 个特征中保留了 198 个特征 (base2keep: 4, 冗余筛选: 194, 相关性阈值=0.85, 聚类数=195)
2025-06-16 18:06 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
