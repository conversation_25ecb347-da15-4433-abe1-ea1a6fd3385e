import streamlit as st
from pathlib import Path
import sys

root = Path(__file__).parent.parent.parent
if str(root) not in sys.path:
    sys.path.append(str(root))

from gui.pages.utils import render_features_page

def main():
    """特征管理页面"""
    # 检查页面访问权限
    if st.session_state.get('current_page') != "特征管理":
        st.switch_page("Home.py")
        return
        
    # 确保任务对象存在
    if 'task_mining' not in st.session_state:
        st.error("请先完成任务配置")
        return
    
    # 从任务对象中读取已选品种
    symbol_pairs = getattr(st.session_state.task_mining, 'selected_pairs', [])
    
    # 渲染特征管理页面（内部可利用任务对象的配置）
    render_features_page(symbol_pairs)

if __name__ == "__main__":
    main() 