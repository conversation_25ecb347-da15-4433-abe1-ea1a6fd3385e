import pandas as pd
from common.cls_base import BaseObj

class FactorPersist(BaseObj):
    """ === 因子持久化业务逻辑, 调用factorzoo的功能入库以及持久化 === """
    def __init__(self):
        pass
    
    def persist_factor(self, factor_data: pd.DataFrame, factor_name: str, pipeline_step: str):
        pass
    
    def persist_factor_batch(self, factor_data: pd.DataFrame, factor_name: str, pipeline_step: str):
        pass


# TODO: 以下代码如何抽象为可复用函数? pj和f类 可分别各自调用即可 不要内部判断pj还是f类
            # 统计总因子数以决定是否入库
            grand_total = sum(
                len(factors)
                for symbol_data in discovered_factors.values()
                for factors in symbol_data.values()
            )
            if grand_total > 0:
                print(f"\n🏦 开始入库到FactorZoo...")
                
                # 从toml配置中读取数据源配置信息
                frequency = getattr(cfg_mine.data_source, 'freq', 'D') if hasattr(cfg_mine, 'data_source') else 'D'

                # 6.1 按照新批次概念：同品种+同周期+同起始日期 = 同批次
                # 处理datetime索引，可能是字符串格式
                datetime_values = df_all.index.get_level_values('datetime')
                if isinstance(datetime_values[0], str):
                    # 如果是字符串，先转换为datetime
                    datetime_values = pd.to_datetime(datetime_values)
                
                start_date = datetime_values.min().strftime('%Y%m%d')
                end_date = datetime_values.max().strftime('%Y%m%d')

                # ✨ 新增：因子值持久化到FactorValueManager
                print(f"\n💾 开始因子值持久化...")
                
                # 6.2 按品种持久化因子值
                total_persistence_success = 0
                total_persistence_failed = 0
                
                for symbol in symbols:
                    # 统计该品种的因子数量
                    symbol_factor_count = sum(len(discovered_factors[symbol][label]) for label in label_cols)
                    if symbol_factor_count == 0:
                        print(f"    ⚠️  品种 {symbol} 无因子，跳过持久化")
                        continue
                    
                    # 为每个品种创建因子值批次ID
                    symbol_batch_id = f"GP_{symbol}_{current_date}_L0_{global_uid[-6:]}"
                    print(f"\n📋 持久化品种因子值: {symbol_batch_id}")
                    
                    try:
                        # 获取该品种的基础数据
                        X_symbol = X_all.xs(symbol, level='symbol')
                        
                        # 智能检测可用的价格列 - 根据配置文件中的base2keep设置
                        available_price_cols = []
                        # 首先尝试配置文件中指定的基础列
                        preferred_cols = ['open', 'high', 'low', 'close']
                        for col in preferred_cols:
                            if col in df_all.columns:
                                available_price_cols.append(col)
                        
                        # 如果还有其他可能的价格相关列，也加入
                        for col in ['volume', 'amount', 'open_interest']:
                            if col in df_all.columns:
                                available_price_cols.append(col)
                        
                        # 使用可用的价格列作为基础数据
                        base_data_symbol = df_all[available_price_cols].xs(symbol, level='symbol')
                        print(f"    📊 使用基础数据列: {available_price_cols}")
                        
                        # 计算收益率列以匹配原库格式 (仅保留open, close, ret, ret_open)
                        if 'open' in base_data_symbol.columns and 'close' in base_data_symbol.columns:
                            # 计算收益率 - 与文件开始部分逻辑保持一致
                            base_data_symbol = base_data_symbol.copy()
                            
                            # ret: 收盘价收益率 (对应close.shift(-1) / close - 1)
                            base_data_symbol['ret'] = base_data_symbol['close'].shift(-1) / base_data_symbol['close'] - 1
                            # ret_open: 开盘价相对前一日收盘的收益率 (对应open / close.shift(1) - 1)
                            base_data_symbol['ret_open'] = base_data_symbol['open'] / base_data_symbol['open'].shift(1) - 1
                            
                            # 填充缺失值
                            base_data_symbol['ret'] = base_data_symbol['ret'].fillna(0)
                            base_data_symbol['ret_open'] = base_data_symbol['ret_open'].fillna(0)
                            
                            print(f"    📊 收益率计算完成: ret(close收益率), ret_open(开盘收益率)")
                        else:
                            print(f"    ⚠️  缺少open或close列，无法计算收益率，保持原格式")
                        
                        # 收集该品种所有标签的因子
                        all_symbol_factors = []      # f类因子
                        all_symbol_pj_factors = []   # pj类因子
                        factor_meta_info = {}
                        
                        for label_col in label_cols:
                            # 收集f类因子
                            if label_col in discovered_factors[symbol]:
                                factors = discovered_factors[symbol][label_col]
                                if factors:
                                    all_symbol_factors.extend(factors)
                                    # 记录每个因子对应的标签
                                    for factor in factors:
                                        factor_meta_info[str(factor)] = {
                                            'target_label': label_col,
                                            'symbol': symbol,
                                            'creation_method': 'gp_mining',
                                            'pipeline_step': 'L0',
                                            'factor_type': 'f'
                                        }
                        
                            # 收集pj类因子
                            pj_key = f"{label_col}_pj"
                            if pj_key in discovered_factors[symbol]:
                                pj_factors = discovered_factors[symbol][pj_key]
                                if pj_factors:
                                    all_symbol_pj_factors.extend(pj_factors)
                                    # 记录每个pj因子对应的标签
                                    for factor in pj_factors:
                                        factor_meta_info[str(factor)] = {
                                            'target_label': label_col,
                                            'symbol': symbol,
                                            'creation_method': 'gp_mining',
                                            'pipeline_step': 'L0',
                                            'factor_type': 'pj'
                                        }
                        
                        total_symbol_factors = len(all_symbol_factors) + len(all_symbol_pj_factors)
                        if total_symbol_factors > 0:
                            print(f"    🔄 计算因子值: f类{len(all_symbol_factors)}个 + pj类{len(all_symbol_pj_factors)}个")
                            
                            # 分别计算f类和pj类因子值
                            fct_values_df = pd.DataFrame()
                            pj_values_df = pd.DataFrame()
                            
                            if all_symbol_factors:
                                # 计算f类因子值
                                with FactorMonitorContext(f"CALC_F_VALUES_{symbol}", 
                                                           operation_type='factor_f_value_calculation', 
                                                           batch_id=symbol_batch_id,
                                                           symbol=symbol,
                                                           data_size=len(all_symbol_factors)) as calc_ctx:
                                    
                                    fct_values_df = FactorLoader.get_fct_df(
                                        X_symbol,
                                        model_type=cfg_mine.mine.norm.model,
                                        norm_type='X', 
                                        fcts=all_symbol_factors
                                    )
                                    # f类因子计算完成
                            
                            if all_symbol_pj_factors:
                                # 计算pj类因子值
                                with FactorMonitorContext(f"CALC_PJ_VALUES_{symbol}", 
                                                           operation_type='factor_pj_value_calculation', 
                                                           batch_id=symbol_batch_id,
                                                           symbol=symbol,
                                                           data_size=len(all_symbol_pj_factors)) as calc_ctx:
                                    
                                    pj_values_df = FactorLoader.get_fct_df(
                                        X_symbol,
                                        model_type=cfg_mine.mine.norm.model,
                                        norm_type='X', 
                                        fcts=all_symbol_pj_factors
                                    )
                                    # pj类因子计算完成
                            
                            if not fct_values_df.empty or not pj_values_df.empty:
                                # 准备分层持久化数据
                                factor_data_dict = {}
                                if not fct_values_df.empty:
                                    factor_data_dict['L0'] = fct_values_df      # L0阶段：f类因子
                                if not pj_values_df.empty:
                                    factor_data_dict['L0_pj'] = pj_values_df    # L0_pj阶段：pj类因子
                                
                                # 准备元数据
                                persistence_metadata = {
                                    'symbol': symbol,
                                    'date': current_date,
                                    'stage': 'L0',
                                    'total_factors': total_symbol_factors,
                                    'f_factors': len(all_symbol_factors),
                                    'pj_factors': len(all_symbol_pj_factors),
                                    'labels': list(label_cols),
                                    'factor_count_by_label': {
                                        label: len(discovered_factors[symbol].get(label, [])) + len(discovered_factors[symbol].get(f"{label}_pj", []))
                                        for label in label_cols
                                    },
                                    'mine_config': {
                                        'mine_runnum': n_runs_per_target,
                                        'job_num': job_num,
                                        'mine_norm_model': cfg_mine.mine.norm.model,
                                        'func_set_size': len(func_set),
                                    },
                                    'factor_meta_info': factor_meta_info,
                                    'data_range': {
                                        'start_date': start_date,
                                        'end_date': end_date,
                                        'frequency': frequency
                                    },
                                    'creation_time': datetime.now().isoformat(),
                                    'global_batch_id': batch_id
                                }
                                
                                # 执行因子值持久化
                                with FactorMonitorContext(f"PERSIST_{symbol}", 
                                                           operation_type='factor_value_persistence', 
                                                           batch_id=symbol_batch_id,
                                                           symbol=symbol,
                                                           data_size=fct_values_df.shape[0] * fct_values_df.shape[1]) as persist_ctx:
                                    
                                    persistence_success = factorstore.save_batch_data(
                                        batch_id=symbol_batch_id,
                                        base_data=base_data_symbol,
                                        factor_data_dict=factor_data_dict,
                                        metadata=persistence_metadata
                                    )
                                    
                                    if persistence_success:
                                        total_persistence_success += 1
                                        print(f"    ✅ 因子值持久化成功: f类{len(all_symbol_factors)}个 + pj类{len(all_symbol_pj_factors)}个")
                                        if not fct_values_df.empty:
                                            print(f"       f类数据维度: {fct_values_df.shape}")
                                            print(f"       时间范围: {fct_values_df.index.min()} ~ {fct_values_df.index.max()}")
                                        if not pj_values_df.empty:
                                            print(f"       pj类数据维度: {pj_values_df.shape}")
                                    else:
                                        total_persistence_failed += 1
                                        print(f"    ❌ 因子值持久化失败")
                            else:
                                print(f"    ⚠️  因子值计算结果为空")
                                total_persistence_failed += 1
                        else:
                            print(f"    ⚠️  该品种无有效因子")
                            
                    except Exception as e:
                        total_persistence_failed += 1
                        print(f"    ❌ 因子值持久化异常: {str(e)}")
                        import traceback
                        traceback.print_exc()
                
                # 6.3 因子值持久化结果统计
                print(f"\n📊 因子值持久化完成:")
                print(f"    ✅ 成功持久化: {total_persistence_success} 个品种")
                print(f"    ❌ 持久化失败: {total_persistence_failed} 个品种")
                if total_persistence_success + total_persistence_failed > 0:
                    success_rate = total_persistence_success / (total_persistence_success + total_persistence_failed) * 100
                    print(f"    📈 持久化成功率: {success_rate:.1f}%")
                
                # 6.4 验证持久化结果
                if total_persistence_success > 0:
                    print(f"\n🔍 验证持久化结果:")
                    try:
                        # 检查可用批次
                        available_batches = factorstore.get_available_batches(stage='L0', date=current_date)
                        print(f"    📋 今日L0批次: {len(available_batches)} 个")
                        
                        # 展示部分批次
                        for batch_id in available_batches[:3]:
                            batch_info = factorstore.get_batch_info(batch_id)
                            if batch_info:
                                print(f"    📁 {batch_id}: {batch_info.get('creation_time', 'Unknown')}")
                            else:
                                print(f"    📁 {batch_id}: 元数据不可用")
                        
                        if len(available_batches) > 3:
                            print(f"    ... 及其他 {len(available_batches) - 3} 个批次")
                            
                    except Exception as e:
                        print(f"    ⚠️  验证异常: {str(e)}")

                # 6.5 因子分类自动判断（FactorZoo入库用）
                def auto_classify_factor(expr_str: str) -> str:
                    expr_lower = expr_str.lower()
                    if any(word in expr_lower for word in ['volume', 'vol', 'amount']):
                        return 'PRICE_VOLUME'
                    elif any(word in expr_lower for word in ['ts_mean', 'ts_sum', 'ts_max', 'ts_min', 'rolling']):
                        return 'PRICE_TREND'
                    elif any(word in expr_lower for word in ['close', 'open', 'high', 'low']):
                        return 'PRICE_VOLUME'
                    else:
                        return 'PRICE_VOLUME'  # 默认分类
                
                # 6.6 按品种分别创建批次和入库因子
                total_success_count = 0
                total_failed_count = 0
                
                for symbol in symbols:
                    # 为每个品种创建独立批次 - 使用统一编码规范
                    symbol_batch_id = f"GP_{symbol}_{current_date}_L0_{global_uid[-6:]}"
                    symbol_pj_batch_id = f"GP_{symbol}_{current_date}_L0_PJ_{global_uid[-6:]}"
                    print(f"\n📋 创建品种批次: {symbol_batch_id} (f类) + {symbol_pj_batch_id} (pj类)")
                    
                    # 统计该品种的因子数量
                    symbol_factor_count = sum(len(discovered_factors[symbol].get(label, [])) for label in label_cols)
                    symbol_pj_count = sum(len(discovered_factors[symbol].get(f"{label}_pj", [])) for label in label_cols)
                    
                    if symbol_factor_count == 0 and symbol_pj_count == 0:
                        print(f"    ⚠️  品种 {symbol} 无因子，跳过批次创建")
                        continue
                    
                    # 创建f类因子批次记录
                    batch_success = False
                    pj_batch_success = False
                    
                    if symbol_factor_count > 0:
                        batch_success = factorzoo.create_batch(
                            batch_id=symbol_batch_id,
                            batch_name=f"GP挖掘L0_f类_{symbol}_{current_date}",
                            creation_tool="gplearn",
                            source_symbols=[symbol],
                            source_frequencies=[frequency],
                            source_date_ranges={
                                "start": datetime_values.min().strftime('%Y-%m-%d'),
                                "end": datetime_values.max().strftime('%Y-%m-%d')
                            },
                            generation_params={
                                "symbol": symbol,
                                "date": current_date,
                                "stage": "L0",
                                "factor_type": "f",
                                "mine_runnum": n_runs_per_target,
                                "job_num": job_num,
                                "func_set": func_set[:10],
                                "labels": list(label_cols),
                                "symbol_factor_count": symbol_factor_count,
                                "batch_encoding": "GP_{symbol}_{date}_L0_{uid}"
                            }
                        )
                    
                    # 创建pj类因子批次记录  
                    if symbol_pj_count > 0:
                        pj_batch_success = factorzoo.create_batch(
                            batch_id=symbol_pj_batch_id,
                            batch_name=f"GP挖掘L0_pj类_{symbol}_{current_date}",
                            creation_tool="gplearn",
                            source_symbols=[symbol],
                            source_frequencies=[frequency],
                            source_date_ranges={
                                "start": datetime_values.min().strftime('%Y-%m-%d'),
                                "end": datetime_values.max().strftime('%Y-%m-%d')
                            },
                            generation_params={
                                "symbol": symbol,
                                "date": current_date,
                                "stage": "L0",
                                "factor_type": "pj",
                                "mine_runnum": n_runs_per_target,
                                "job_num": job_num,
                                "func_set": func_set[:10],
                                "labels": list(label_cols),
                                "symbol_pj_count": symbol_pj_count,
                                "batch_encoding": "GP_{symbol}_{date}_L0_PJ_{uid}"
                            }
                        )
                    
                    print(f"    批次创建结果: f类{'✅' if batch_success else '❌'} + pj类{'✅' if pj_batch_success else '❌'}")
                    
                    # 分别入库f类和pj类因子
                    symbol_success = 0
                    symbol_failed = 0
                    symbol_pj_success = 0
                    symbol_pj_failed = 0
                    
                    # 入库f类因子
                    for label_col in label_cols:
                        if label_col in discovered_factors[symbol]:
                            factors = discovered_factors[symbol][label_col]
                            if not factors:
                                continue
                            
                            print(f"  📊 入库f类因子 {symbol}-{label_col}: {len(factors)} 个")
                            
                            for i, factor_expr in enumerate(factors):
                                factor_id = f"F_GP_{symbol_batch_id}_{label_col}_{i+1:03d}"
                                
                                # 使用因子专用监控上下文
                                with FactorMonitorContext(factor_id, 
                                                           operation_type='registration', 
                                                           batch_id=symbol_batch_id,
                                                           factor_id=factor_id,
                                                           symbol=symbol) as ctx:
                                    try:
                                        # 计算因子复杂度评分 - 基于表达式长度和操作符数量
                                        expr_str = str(factor_expr)
                                        complexity_score = min(100, len(expr_str) + expr_str.count('(') * 2 + expr_str.count(','))

                                        # 评估资源强度 - 基于复杂度
                                        if complexity_score < 30:
                                            resource_intensity = 'low'
                                        elif complexity_score < 60:
                                            resource_intensity = 'medium'
                                        else:
                                            resource_intensity = 'high'

                                        # 添加f类因子到FactorZoo - 包含性能数据
                                        add_success = factorzoo.add_factor(
                                            factor_id=factor_id,
                                            factor_name=f"GP_f_{symbol}_{label_col}_{i+1}",
                                            factor_expression=expr_str,
                                            factor_type="time_series",
                                            data_source_type="single_symbol",
                                            symbols=[symbol],
                                            frequencies=[frequency],
                                            date_ranges={
                                                "start": datetime_values.min().strftime('%Y-%m-%d'),
                                                "end": datetime_values.max().strftime('%Y-%m-%d')
                                            },
                                            creation_method="auto_generation",
                                            generation_tool="gplearn",
                                            pipeline_step="L0",
                                            pipeline_mode="auto_pipeline",
                                            primary_category=auto_classify_factor(expr_str),
                                            batch_id=symbol_batch_id,
                                            target_label=label_col,
                                            status="active",
                                            # 性能相关字段 - 顺带填充
                                            complexity_score=complexity_score,
                                            resource_intensity=resource_intensity
                                        )
                                        
                                        if add_success:
                                            symbol_success += 1
                                        else:
                                            symbol_failed += 1
                                            
                                    except Exception as e:
                                        symbol_failed += 1
                                        print(f"    ⚠️  f类因子 {factor_id} 入库失败: {str(e)}")
                    
                    # 入库pj类因子
                    for label_col in label_cols:
                        pj_key = f"{label_col}_pj"
                        if pj_key in discovered_factors[symbol]:
                            pj_factors = discovered_factors[symbol][pj_key]
                            if not pj_factors:
                                continue
                            
                            print(f"  📊 入库pj类因子 {symbol}-{label_col}: {len(pj_factors)} 个")
                            
                            for i, factor_expr in enumerate(pj_factors):
                                factor_id = f"F_GP_PJ_{symbol_pj_batch_id}_{label_col}_{i+1:03d}"
                                
                                # 使用因子专用监控上下文
                                with FactorMonitorContext(factor_id, 
                                                           operation_type='registration', 
                                                           batch_id=symbol_pj_batch_id,
                                                           factor_id=factor_id,
                                                           symbol=symbol) as ctx:
                                    try:
                                        # 计算因子复杂度评分 - 基于表达式长度和操作符数量
                                        expr_str = str(factor_expr)
                                        complexity_score = min(100, len(expr_str) + expr_str.count('(') * 2 + expr_str.count(','))

                                        # 评估资源强度 - 基于复杂度
                                        if complexity_score < 30:
                                            resource_intensity = 'low'
                                        elif complexity_score < 60:
                                            resource_intensity = 'medium'
                                        else:
                                            resource_intensity = 'high'

                                        # 添加pj类因子到FactorZoo - 包含性能数据
                                        add_success = factorzoo.add_factor(
                                            factor_id=factor_id,
                                            factor_name=f"GP_pj_{symbol}_{label_col}_{i+1}",
                                            factor_expression=expr_str,
                                            factor_type="time_series",
                                            data_source_type="single_symbol",
                                            symbols=[symbol],
                                            frequencies=[frequency],
                                            date_ranges={
                                                "start": datetime_values.min().strftime('%Y-%m-%d'),
                                                "end": datetime_values.max().strftime('%Y-%m-%d')
                                            },
                                            creation_method="auto_generation",
                                            generation_tool="gplearn",
                                            pipeline_step="L0",
                                            pipeline_mode="auto_pipeline",
                                            primary_category=auto_classify_factor(expr_str),
                                            batch_id=symbol_pj_batch_id,
                                            target_label=label_col,
                                            status="active",
                                            # 性能相关字段 - 顺带填充
                                            complexity_score=complexity_score,
                                            resource_intensity=resource_intensity
                                        )
                                        
                                        if add_success:
                                            symbol_pj_success += 1
                                        else:
                                            symbol_pj_failed += 1
                                            
                                    except Exception as e:
                                        symbol_pj_failed += 1
                                        print(f"    ⚠️  pj类因子 {factor_id} 入库失败: {str(e)}")
                    
                    # 更新f类因子批次的统计
                    if batch_success and symbol_success > 0:
                        try:
                            with factorzoo.get_connection() as conn:
                                cursor = conn.cursor()
                                cursor.execute("""
                                    UPDATE factor_batches 
                                    SET total_generated = ?, l0_count = ?, end_time = ?
                                    WHERE batch_id = ?
                                """, (symbol_factor_count, symbol_success, datetime.now(), symbol_batch_id))
                                conn.commit()
                            print(f"    ✅ f类批次统计已更新: 成功{symbol_success}个，失败{symbol_failed}个")
                        except Exception as e:
                            print(f"    ⚠️  f类批次统计更新失败: {str(e)}")
                    
                    # 更新pj类因子批次的统计
                    if pj_batch_success and symbol_pj_success > 0:
                        try:
                            with factorzoo.get_connection() as conn:
                                cursor = conn.cursor()
                                cursor.execute("""
                                    UPDATE factor_batches 
                                    SET total_generated = ?, l0_count = ?, end_time = ?
                                    WHERE batch_id = ?
                                """, (symbol_pj_count, symbol_pj_success, datetime.now(), symbol_pj_batch_id))
                                conn.commit()
                            print(f"    ✅ pj类批次统计已更新: 成功{symbol_pj_success}个，失败{symbol_pj_failed}个")
                        except Exception as e:
                            print(f"    ⚠️  pj类批次统计更新失败: {str(e)}")
                    
                    total_success_count += symbol_success + symbol_pj_success
                    total_failed_count += symbol_failed + symbol_pj_failed
                
                # 6.7 全局入库结果统计
                print(f"\n📊 FactorZoo整体入库完成:")
                print(f"    ✅ 成功入库: {total_success_count} 个因子（f类+pj类）")
                print(f"    ❌ 入库失败: {total_failed_count} 个因子")
                if total_success_count + total_failed_count > 0:
                    print(f"    📈 入库成功率: {total_success_count/(total_success_count+total_failed_count)*100:.1f}%")
                
                # 统计创建的批次数（f类+pj类）
                f_batches = len([s for s in symbols if sum(len(discovered_factors[s].get(l, [])) for l in label_cols) > 0])
                pj_batches = len([s for s in symbols if sum(len(discovered_factors[s].get(f"{l}_pj", [])) for l in label_cols) > 0])
                total_batches = f_batches + pj_batches
                print(f"    🗂️  创建批次数: {total_batches} 个（f类{f_batches} + pj类{pj_batches}）")
                
            else:
                print(f"\n🏦 无因子需要入库")