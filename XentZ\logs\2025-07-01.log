2025-07-01 11:43 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-01 11:43 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-01 11:43 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 11:43 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250630_L0_085450/L0
2025-07-01 11:43 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250630_L0_085450/L0 耗时231.1ms
2025-07-01 11:43 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 11:43 | INFO     | ResMonitor.end_timer: L2_LASSO_SH510050 took 0.01 seconds (00:00:00)
2025-07-01 11:43 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_SH510050 CPU usage: 0.00s user, 0.00s system
2025-07-01 11:43 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_SH510050 memory delta: 0.27MB
2025-07-01 11:43 | INFO     | FactorMonitorContext.__exit__: L2_LASSO_SH510050 completed successfully
2025-07-01 11:43 | INFO     | ResMonitor.end_timer: L2_LASSO_FILTER_1751341439436393 took 0.29 seconds (00:00:00)
2025-07-01 11:43 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_FILTER_1751341439436393 CPU usage: 0.03s user, 0.09s system
2025-07-01 11:43 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_FILTER_1751341439436393 memory delta: 13.73MB
2025-07-01 11:43 | INFO     | MonitorContext.__exit__: L2_LASSO_FILTER_1751341439436393 completed successfully
2025-07-01 12:55 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-01 12:55 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-01 12:55 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 12:55 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250630_L0_085450/L0
2025-07-01 12:55 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250630_L0_085450/L0 耗时307.6ms
2025-07-01 12:55 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 12:55 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-01 12:55 | INFO     | ResMonitor.end_timer: L1_CORR_SH510050_label_1 took 0.03 seconds (00:00:00)
2025-07-01 12:55 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_SH510050_label_1 CPU usage: 0.02s user, 0.00s system
2025-07-01 12:55 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_SH510050_label_1 memory delta: 0.36MB
2025-07-01 12:55 | INFO     | FactorMonitorContext.__exit__: L1_CORR_SH510050_label_1 completed successfully
2025-07-01 12:55 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 12:55 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L1_CORR_SH510050_label_1_20250701_027861
2025-07-01 12:55 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L1_CORR_SH510050_label_1_20250701_027861 耗时50.6ms
2025-07-01 12:55 | INFO     | ResMonitor.end_timer: PERSIST_L1_SH510050_label_1 took 0.05 seconds (00:00:00)
2025-07-01 12:55 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L1_SH510050_label_1 CPU usage: 0.02s user, 0.03s system
2025-07-01 12:55 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L1_SH510050_label_1 memory delta: 2.09MB
2025-07-01 12:55 | INFO     | FactorMonitorContext.__exit__: PERSIST_L1_SH510050_label_1 completed successfully
2025-07-01 12:55 | INFO     | ResMonitor.end_timer: L1_CORR_FILTER_1751345708027861 took 22.71 seconds (00:00:22)
2025-07-01 12:55 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_FILTER_1751345708027861 CPU usage: 0.16s user, 0.09s system
2025-07-01 12:55 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_FILTER_1751345708027861 memory delta: 16.41MB
2025-07-01 12:55 | INFO     | MonitorContext.__exit__: L1_CORR_FILTER_1751345708027861 completed successfully
2025-07-01 12:55 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-01 12:55 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-01 12:55 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 12:55 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250630_L0_085450/L0
2025-07-01 12:55 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250630_L0_085450/L0 耗时155.6ms
2025-07-01 12:55 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 12:55 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-01 12:55 | INFO     | ResMonitor.end_timer: L1_CORR_SH510050_label_1 took 0.01 seconds (00:00:00)
2025-07-01 12:55 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-01 12:55 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_SH510050_label_1 memory delta: 0.37MB
2025-07-01 12:55 | INFO     | FactorMonitorContext.__exit__: L1_CORR_SH510050_label_1 completed successfully
2025-07-01 12:55 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 12:55 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L1_CORR_SH510050_label_1_20250701_232923
2025-07-01 12:55 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L1_CORR_SH510050_label_1_20250701_232923 耗时24.9ms
2025-07-01 12:55 | INFO     | ResMonitor.end_timer: PERSIST_L1_SH510050_label_1 took 0.03 seconds (00:00:00)
2025-07-01 12:55 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L1_SH510050_label_1 CPU usage: 0.03s user, 0.00s system
2025-07-01 12:55 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L1_SH510050_label_1 memory delta: 2.09MB
2025-07-01 12:55 | INFO     | FactorMonitorContext.__exit__: PERSIST_L1_SH510050_label_1 completed successfully
2025-07-01 12:55 | INFO     | ResMonitor.end_timer: L1_CORR_FILTER_1751345758232923 took 0.28 seconds (00:00:00)
2025-07-01 12:55 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_FILTER_1751345758232923 CPU usage: 0.16s user, 0.12s system
2025-07-01 12:55 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_FILTER_1751345758232923 memory delta: 16.92MB
2025-07-01 12:55 | INFO     | MonitorContext.__exit__: L1_CORR_FILTER_1751345758232923 completed successfully
2025-07-01 13:08 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-01 13:08 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-01 13:08 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 13:08 | INFO     | ResMonitor.end_timer: L2_LASSO_FILTER_1751346493909395 took 0.00 seconds (00:00:00)
2025-07-01 13:08 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_FILTER_1751346493909395 CPU usage: 0.00s user, 0.00s system
2025-07-01 13:08 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_FILTER_1751346493909395 memory delta: 0.00MB
2025-07-01 13:08 | INFO     | MonitorContext.__exit__: L2_LASSO_FILTER_1751346493909395 completed successfully
2025-07-01 13:17 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-01 13:17 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-01 13:17 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 13:17 | INFO     | ResMonitor.end_timer: L1_CORR_FILTER_1751347046181658 took 0.00 seconds (00:00:00)
2025-07-01 13:17 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_FILTER_1751347046181658 CPU usage: 0.00s user, 0.00s system
2025-07-01 13:17 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_FILTER_1751347046181658 memory delta: 0.00MB
2025-07-01 13:17 | INFO     | MonitorContext.__exit__: L1_CORR_FILTER_1751347046181658 completed successfully
2025-07-01 13:20 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-01 13:20 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-01 13:20 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 13:20 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250630_L0_085450/L0
2025-07-01 13:20 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250630_L0_085450/L0 耗时306.6ms
2025-07-01 13:20 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 13:20 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-01 13:20 | INFO     | ResMonitor.end_timer: L1_CORR_SH510050_label_1 took 0.02 seconds (00:00:00)
2025-07-01 13:20 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_SH510050_label_1 CPU usage: 0.02s user, 0.00s system
2025-07-01 13:20 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_SH510050_label_1 memory delta: 0.37MB
2025-07-01 13:20 | INFO     | FactorMonitorContext.__exit__: L1_CORR_SH510050_label_1 completed successfully
2025-07-01 13:20 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 13:20 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L1_CORR_SH510050_label_1_20250701_252531
2025-07-01 13:20 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L1_CORR_SH510050_label_1_20250701_252531 耗时52.8ms
2025-07-01 13:20 | INFO     | ResMonitor.end_timer: PERSIST_L1_SH510050_label_1 took 0.06 seconds (00:00:00)
2025-07-01 13:20 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L1_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-01 13:20 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L1_SH510050_label_1 memory delta: 1.85MB
2025-07-01 13:20 | INFO     | FactorMonitorContext.__exit__: PERSIST_L1_SH510050_label_1 completed successfully
2025-07-01 13:20 | INFO     | ResMonitor.end_timer: L1_CORR_FILTER_1751347253252531 took 0.49 seconds (00:00:00)
2025-07-01 13:20 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_FILTER_1751347253252531 CPU usage: 0.12s user, 0.06s system
2025-07-01 13:20 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_FILTER_1751347253252531 memory delta: 15.36MB
2025-07-01 13:20 | INFO     | MonitorContext.__exit__: L1_CORR_FILTER_1751347253252531 completed successfully
2025-07-01 13:21 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-01 13:21 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-01 13:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 13:21 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: L1_CORR_SH510050_label_1_20250701_252531/L1
2025-07-01 13:21 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: L1_CORR_SH510050_label_1_20250701_252531/L1 耗时81.8ms
2025-07-01 13:21 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 13:21 | INFO     | ResMonitor.end_timer: L2_LASSO_[_label_1 took 0.00 seconds (00:00:00)
2025-07-01 13:21 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_[_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-01 13:21 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_[_label_1 memory delta: 0.18MB
2025-07-01 13:21 | INFO     | FactorMonitorContext.__exit__: L2_LASSO_[_label_1 completed successfully
2025-07-01 13:21 | INFO     | ResMonitor.end_timer: L2_LASSO_FILTER_1751347278324268 took 0.12 seconds (00:00:00)
2025-07-01 13:21 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_FILTER_1751347278324268 CPU usage: 0.06s user, 0.03s system
2025-07-01 13:21 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_FILTER_1751347278324268 memory delta: 13.64MB
2025-07-01 13:21 | INFO     | MonitorContext.__exit__: L2_LASSO_FILTER_1751347278324268 completed successfully
2025-07-01 13:25 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-01 13:25 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-01 13:25 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 13:25 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: L1_CORR_SH510050_label_1_20250701_252531/L1
2025-07-01 13:25 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: L1_CORR_SH510050_label_1_20250701_252531/L1 耗时128.6ms
2025-07-01 13:25 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 13:25 | INFO     | ResMonitor.end_timer: L2_LASSO_SH510050_label_1 took 0.01 seconds (00:00:00)
2025-07-01 13:25 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_SH510050_label_1 CPU usage: 0.02s user, 0.00s system
2025-07-01 13:25 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_SH510050_label_1 memory delta: 0.19MB
2025-07-01 13:25 | INFO     | FactorMonitorContext.__exit__: L2_LASSO_SH510050_label_1 completed successfully
2025-07-01 13:25 | INFO     | ResMonitor.end_timer: L2_LASSO_FILTER_1751347502635334 took 0.25 seconds (00:00:00)
2025-07-01 13:25 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_FILTER_1751347502635334 CPU usage: 0.09s user, 0.06s system
2025-07-01 13:25 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_FILTER_1751347502635334 memory delta: 13.68MB
2025-07-01 13:25 | INFO     | MonitorContext.__exit__: L2_LASSO_FILTER_1751347502635334 completed successfully
2025-07-01 13:48 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-01 13:48 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-01 13:48 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 13:48 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250630_L0_085450/L0
2025-07-01 13:48 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250630_L0_085450/L0 耗时140.2ms
2025-07-01 13:48 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 13:48 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-01 13:48 | INFO     | ResMonitor.end_timer: L1_CORR_SH510050_label_1 took 0.01 seconds (00:00:00)
2025-07-01 13:48 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_SH510050_label_1 CPU usage: 0.02s user, 0.00s system
2025-07-01 13:48 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_SH510050_label_1 memory delta: 0.37MB
2025-07-01 13:48 | INFO     | FactorMonitorContext.__exit__: L1_CORR_SH510050_label_1 completed successfully
2025-07-01 13:48 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 13:48 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L1_CORR_SH510050_label_1_20250701_288141
2025-07-01 13:48 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L1_CORR_SH510050_label_1_20250701_288141 耗时28.5ms
2025-07-01 13:48 | INFO     | ResMonitor.end_timer: PERSIST_L1_SH510050_label_1 took 0.03 seconds (00:00:00)
2025-07-01 13:48 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L1_SH510050_label_1 CPU usage: 0.00s user, 0.02s system
2025-07-01 13:48 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L1_SH510050_label_1 memory delta: 2.13MB
2025-07-01 13:48 | INFO     | FactorMonitorContext.__exit__: PERSIST_L1_SH510050_label_1 completed successfully
2025-07-01 13:48 | INFO     | ResMonitor.end_timer: L1_CORR_FILTER_1751348930288141 took 0.26 seconds (00:00:00)
2025-07-01 13:48 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_FILTER_1751348930288141 CPU usage: 0.14s user, 0.05s system
2025-07-01 13:48 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_FILTER_1751348930288141 memory delta: 17.32MB
2025-07-01 13:48 | INFO     | MonitorContext.__exit__: L1_CORR_FILTER_1751348930288141 completed successfully
2025-07-01 13:49 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-01 13:49 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-01 13:49 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 13:49 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: L1_CORR_SH510050_label_1_20250701_288141/L1
2025-07-01 13:49 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: L1_CORR_SH510050_label_1_20250701_288141/L1 耗时83.3ms
2025-07-01 13:49 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 13:49 | INFO     | ResMonitor.end_timer: L2_LASSO_SH510050_label_1 took 0.00 seconds (00:00:00)
2025-07-01 13:49 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_SH510050_label_1 CPU usage: 0.02s user, 0.00s system
2025-07-01 13:49 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_SH510050_label_1 memory delta: 0.18MB
2025-07-01 13:49 | INFO     | FactorMonitorContext.__exit__: L2_LASSO_SH510050_label_1 completed successfully
2025-07-01 13:49 | INFO     | ResMonitor.end_timer: L2_LASSO_FILTER_1751348959896128 took 0.13 seconds (00:00:00)
2025-07-01 13:49 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_FILTER_1751348959896128 CPU usage: 0.06s user, 0.03s system
2025-07-01 13:49 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_FILTER_1751348959896128 memory delta: 13.83MB
2025-07-01 13:49 | INFO     | MonitorContext.__exit__: L2_LASSO_FILTER_1751348959896128 completed successfully
2025-07-01 13:53 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-01 13:53 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-01 13:53 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 13:53 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: L1_CORR_SH510050_label_1_20250701_288141/L1
2025-07-01 13:53 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: L1_CORR_SH510050_label_1_20250701_288141/L1 耗时109.7ms
2025-07-01 13:53 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 13:53 | INFO     | ResMonitor.end_timer: L2_LASSO_SH510050_label_1 took 0.00 seconds (00:00:00)
2025-07-01 13:53 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-01 13:53 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_SH510050_label_1 memory delta: 0.18MB
2025-07-01 13:53 | INFO     | FactorMonitorContext.__exit__: L2_LASSO_SH510050_label_1 completed successfully
2025-07-01 13:53 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-01 13:53 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_LASSO_SH510050_label_1_20250701_877942
2025-07-01 13:53 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_LASSO_SH510050_label_1_20250701_877942 耗时30.9ms
2025-07-01 13:53 | INFO     | ResMonitor.end_timer: PERSIST_L2_LASSO_SH510050_label_1 took 0.03 seconds (00:00:00)
2025-07-01 13:53 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_LASSO_SH510050_label_1 CPU usage: 0.03s user, 0.02s system
2025-07-01 13:53 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_LASSO_SH510050_label_1 memory delta: 2.30MB
2025-07-01 13:53 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_LASSO_SH510050_label_1 completed successfully
2025-07-01 13:53 | INFO     | ResMonitor.end_timer: L2_LASSO_FILTER_1751349207877942 took 0.25 seconds (00:00:00)
2025-07-01 13:53 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_FILTER_1751349207877942 CPU usage: 0.11s user, 0.12s system
2025-07-01 13:53 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_FILTER_1751349207877942 memory delta: 16.42MB
2025-07-01 13:53 | INFO     | MonitorContext.__exit__: L2_LASSO_FILTER_1751349207877942 completed successfully
2025-07-01 14:40 | INFO     | BaseObj.main: 📄 PDF文件: D:\JG-files\07_量化交易\@@知识库\遗传因子\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.pdf
2025-07-01 14:40 | INFO     | BaseObj.main: 📁 输出目录: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 准备将PDF转换为Markdown, 输出至: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.md
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: PDF共有 21 页, 开始逐页处理...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 1 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 2 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 3 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 4 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 5 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 6 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 7 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 8 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 9 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 10 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 11 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 12 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 13 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 14 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 15 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 16 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 17 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 18 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 19 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 20 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 正在处理第 21 页...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 所有页面处理完毕, 正在写入Markdown文件...
2025-07-01 14:40 | INFO     | BaseObj.pdf_to_md: 转换成功! ✨
2025-07-01 14:44 | INFO     | BaseObj.main: 📄 PDF文件: D:\JG-files\07_量化交易\@@知识库\遗传因子\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.pdf
2025-07-01 14:44 | INFO     | BaseObj.main: 📁 输出目录: d:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 准备将PDF转换为Markdown, 输出至: d:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.md
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: PDF共有 21 页, 开始逐页处理...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 1 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 2 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 3 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 4 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 5 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 6 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 7 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 8 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 9 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 10 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 11 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 12 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 13 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 14 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 15 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 16 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 17 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 18 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 19 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 20 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 正在处理第 21 页...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 所有页面处理完毕, 正在写入Markdown文件...
2025-07-01 14:44 | INFO     | BaseObj.pdf_to_md: 转换成功! ✨
2025-07-01 15:11 | WARNING  | BaseObj.main: citeds 目录下未找到 PDF
2025-07-01 15:41 | INFO     | BaseObj.main: 📄 PDF文件: D:\JG-files\07_量化交易\@@知识库\遗传因子\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.pdf
2025-07-01 15:41 | INFO     | BaseObj.main: 📁 输出目录: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525
2025-07-01 15:41 | INFO     | BaseObj.pdf_to_markdown: 开始解析 PDF……
2025-07-01 16:17 | INFO     | BaseObj.main: 📄 PDF文件: D:\JG-files\07_量化交易\@@知识库\遗传因子\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.pdf
2025-07-01 16:17 | INFO     | BaseObj.main: 📁 输出目录: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525
2025-07-01 16:17 | INFO     | BaseObj.pdf_to_markdown: 开始解析 PDF……
2025-07-01 16:17 | WARNING  | BaseObj.pdf_to_markdown: ⚠️ Poppler 未安装，回退到 strategy='fast' (不提取图片)
2025-07-01 16:21 | INFO     | BaseObj.main: 📄 PDF文件: D:\JG-files\07_量化交易\@@知识库\遗传因子\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.pdf
2025-07-01 16:21 | INFO     | BaseObj.main: 📁 输出目录: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525
2025-07-01 16:21 | INFO     | BaseObj.pdf_to_markdown: 开始解析 PDF……
2025-07-01 16:38 | INFO     | BaseObj.main: 📄 PDF文件: D:\JG-files\07_量化交易\@@知识库\遗传因子\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.pdf
2025-07-01 16:38 | INFO     | BaseObj.main: 📁 输出目录: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525
2025-07-01 16:38 | INFO     | BaseObj.pdf_to_markdown: 开始解析 PDF……
2025-07-01 16:46 | INFO     | BaseObj.main: 📄 PDF文件: D:\JG-files\07_量化交易\@@知识库\遗传因子\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.pdf
2025-07-01 16:46 | INFO     | BaseObj.main: 📁 输出目录: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525
2025-07-01 16:46 | INFO     | BaseObj.pdf_to_markdown: 开始解析 PDF……
2025-07-01 16:51 | INFO     | BaseObj.main: 📄 PDF文件: D:\JG-files\07_量化交易\@@知识库\遗传因子\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.pdf
2025-07-01 16:51 | INFO     | BaseObj.main: 📁 输出目录: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525
2025-07-01 16:51 | INFO     | BaseObj.pdf_to_markdown: 开始解析 PDF……
2025-07-01 16:51 | INFO     | BaseObj.pdf_to_markdown: 尝试 高精度策略(包含图片)...
2025-07-01 16:53 | WARNING  | BaseObj.pdf_to_markdown: ❌ 高精度策略(包含图片) 失败: 缺少 Tesseract OCR
2025-07-01 16:53 | INFO     | BaseObj.pdf_to_markdown: 尝试 自动策略(包含图片)...
2025-07-01 16:56 | WARNING  | BaseObj.pdf_to_markdown: ❌ 自动策略(包含图片) 失败: 缺少 Tesseract OCR
2025-07-01 16:56 | INFO     | BaseObj.pdf_to_markdown: 尝试 快速策略(不含图片)...
2025-07-01 16:56 | INFO     | BaseObj.pdf_to_markdown: ✅ 快速策略(不含图片) 成功
2025-07-01 17:33 | INFO     | BaseObj.main: 📄 PDF文件: D:\JG-files\07_量化交易\@@知识库\遗传因子\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.pdf
2025-07-01 17:33 | INFO     | BaseObj.main: 📁 输出目录: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525
2025-07-01 17:33 | INFO     | BaseObj.pdf_to_markdown: 开始解析 PDF……
2025-07-01 17:33 | INFO     | BaseObj.pdf_to_markdown: 尝试 高精度策略(包含图片)...
2025-07-01 17:35 | WARNING  | BaseObj.pdf_to_markdown: ❌ 高精度策略(包含图片) 失败: 缺少 Tesseract OCR
2025-07-01 17:35 | INFO     | BaseObj.pdf_to_markdown: 尝试 自动策略(包含图片)...
2025-07-01 17:37 | WARNING  | BaseObj.pdf_to_markdown: ❌ 自动策略(包含图片) 失败: 缺少 Tesseract OCR
2025-07-01 17:37 | INFO     | BaseObj.pdf_to_markdown: 尝试 快速策略(不含图片)...
2025-07-01 17:38 | INFO     | BaseObj.pdf_to_markdown: ✅ 快速策略(不含图片) 成功
2025-07-01 17:38 | INFO     | BaseObj.pdf_to_markdown: ✅ 已生成 Markdown: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.md
2025-07-01 17:38 | INFO     | BaseObj.main: ✨ 转换完成
2025-07-01 17:47 | INFO     | BaseObj.main: 📄 PDF文件: D:\JG-files\07_量化交易\@@知识库\遗传因子\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.pdf
2025-07-01 17:47 | INFO     | BaseObj.main: 📁 输出目录: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525
2025-07-01 17:47 | INFO     | BaseObj.pdf_to_markdown: 开始解析 PDF……
2025-07-01 17:47 | INFO     | BaseObj.pdf_to_markdown: 尝试 高精度策略(包含图片)...
2025-07-01 17:49 | WARNING  | BaseObj.pdf_to_markdown: ❌ 高精度策略(包含图片) 失败: 缺少 Tesseract OCR
2025-07-01 17:49 | INFO     | BaseObj.pdf_to_markdown: 尝试 自动策略(包含图片)...
2025-07-01 17:50 | WARNING  | BaseObj.pdf_to_markdown: ❌ 自动策略(包含图片) 失败: 缺少 Tesseract OCR
2025-07-01 17:50 | INFO     | BaseObj.pdf_to_markdown: 尝试 快速策略(不含图片)...
2025-07-01 17:50 | INFO     | BaseObj.pdf_to_markdown: ✅ 快速策略(不含图片) 成功
2025-07-01 17:50 | INFO     | BaseObj.pdf_to_markdown: ✅ 已生成 Markdown: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.md
2025-07-01 17:50 | INFO     | BaseObj.main: ✨ 转换完成
2025-07-01 17:57 | INFO     | BaseObj.main: 📄 PDF文件: D:\JG-files\07_量化交易\@@知识库\遗传因子\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.pdf
2025-07-01 17:57 | INFO     | BaseObj.main: 📁 输出目录: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525
2025-07-01 17:57 | INFO     | BaseObj.pdf_to_markdown: 开始提取PDF内容...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: PDF共有 21 页，开始提取内容...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 1 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 2 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 3 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 4 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 5 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 6 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 7 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 8 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 9 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 10 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 11 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 12 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 13 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 14 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 15 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 16 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 17 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 18 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 19 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 20 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 21 页...
2025-07-01 17:57 | ERROR    | BaseObj.pdf_to_markdown: 转换失败: document closed
2025-07-01 17:57 | INFO     | BaseObj.main: 📄 PDF文件: D:\JG-files\07_量化交易\@@知识库\遗传因子\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.pdf
2025-07-01 17:57 | INFO     | BaseObj.main: 📁 输出目录: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525
2025-07-01 17:57 | INFO     | BaseObj.pdf_to_markdown: 开始提取PDF内容...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: PDF共有 21 页，开始提取内容...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 1 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 2 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 3 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 4 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 5 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 6 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 7 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 8 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 9 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 10 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 11 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 12 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 13 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 14 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 15 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 16 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 17 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 18 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 19 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 20 页...
2025-07-01 17:57 | INFO     | BaseObj.extract_pdf_content: 处理第 21 页...
2025-07-01 17:57 | INFO     | BaseObj.pdf_to_markdown: 成功提取 1344 个元素
2025-07-01 17:57 | INFO     | BaseObj.pdf_to_markdown: ✅ 转换完成！
2025-07-01 17:57 | INFO     | BaseObj.pdf_to_markdown: 📄 Markdown: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.md
2025-07-01 17:57 | INFO     | BaseObj.pdf_to_markdown: 🖼️  提取图片: 44 张
2025-07-01 17:57 | INFO     | BaseObj.pdf_to_markdown: 📊 总元素数: 1344
2025-07-01 17:57 | INFO     | BaseObj.main: ✨ 任务完成
2025-07-01 18:39 | INFO     | BaseObj.main: 📄 PDF文件: D:\JG-files\07_量化交易\@@知识库\遗传因子\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.pdf
2025-07-01 18:39 | INFO     | BaseObj.main: 📁 输出目录: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525
2025-07-01 18:39 | INFO     | BaseObj.pdf_to_markdown: 开始提取PDF内容...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: PDF共有 21 页，开始提取内容...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 1 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 2 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 3 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 4 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 5 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 6 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 7 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 8 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 9 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 10 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 11 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 12 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 13 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 14 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 15 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 16 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 17 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 18 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 19 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 20 页...
2025-07-01 18:39 | INFO     | BaseObj.extract_pdf_content: 处理第 21 页...
2025-07-01 18:39 | INFO     | BaseObj.pdf_to_markdown: 成功提取 1344 个元素
2025-07-01 18:39 | INFO     | BaseObj.pdf_to_markdown: ✅ 转换完成！
2025-07-01 18:39 | INFO     | BaseObj.pdf_to_markdown: 📄 Markdown: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.md
2025-07-01 18:39 | INFO     | BaseObj.pdf_to_markdown: 🖼️  提取图片: 44 张
2025-07-01 18:39 | INFO     | BaseObj.pdf_to_markdown: 📊 总元素数: 1344
2025-07-01 18:39 | INFO     | BaseObj.main: ✨ 任务完成
2025-07-01 18:42 | INFO     | BaseObj.main: 📄 PDF文件: D:\JG-files\07_量化交易\@@知识库\遗传因子\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.pdf
2025-07-01 18:42 | INFO     | BaseObj.main: 📁 输出目录: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 开始提取PDF内容...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: PDF共有 21 页，开始提取内容...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 1 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 2 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 3 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 4 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 5 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 6 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 7 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 8 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 9 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 10 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 11 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 12 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 13 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 14 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 15 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 16 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 17 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 18 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 19 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 20 页...
2025-07-01 18:42 | INFO     | BaseObj.extract_pdf_content: 处理第 21 页...
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 成功提取 1344 个元素
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_1_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_2_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_2_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_3_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_3_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_4_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_4_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_5_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_5_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_6_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_6_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_7_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_7_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_8_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_8_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_9_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_9_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_10_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_10_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_11_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_11_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_12_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_12_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_13_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_13_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_14_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_14_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_15_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_15_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_16_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_16_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_17_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_17_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_18_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_18_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_19_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_19_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_20_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_20_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_21_img_1.jpeg
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_21_img_2.png
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: ✅ 转换完成！
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 📄 Markdown: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.md
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 🖼️  提取图片: 3 张
2025-07-01 18:42 | INFO     | BaseObj.pdf_to_markdown: 📊 总元素数: 1344
2025-07-01 18:42 | INFO     | BaseObj.main: ✨ 任务完成
2025-07-01 18:46 | INFO     | BaseObj.main: 🔍 检测到文档类型: research_report
2025-07-01 18:46 | INFO     | BaseObj.main: 📄 PDF文件: D:\JG-files\07_量化交易\@@知识库\遗传因子\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.pdf
2025-07-01 18:46 | INFO     | BaseObj.main: 📁 输出目录: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 开始提取PDF内容... (文档类型: research_report)
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: PDF共有 21 页，开始提取内容...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 1 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 2 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 3 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 4 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 5 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 6 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 7 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 8 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 9 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 10 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 11 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 12 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 13 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 14 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 15 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 16 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 17 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 18 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 19 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 20 页...
2025-07-01 18:46 | INFO     | BaseObj.extract_pdf_content: 处理第 21 页...
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 成功提取 1344 个元素
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_1_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_2_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_2_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_3_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_3_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_4_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_4_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_5_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_5_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_6_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_6_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_7_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_7_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_8_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_8_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_9_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_9_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_10_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_10_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_11_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_11_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_12_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_12_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_13_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_13_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_14_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_14_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_15_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_15_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_16_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_16_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_17_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_17_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_18_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_18_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_19_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_19_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_20_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_20_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_21_img_1.jpeg
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_21_img_2.png
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: ✅ 转换完成！
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 📄 Markdown: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.md
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 🖼️  提取图片: 3 张
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: 📊 总元素数: 1344
2025-07-01 18:46 | INFO     | BaseObj.pdf_to_markdown: ⚙️  使用配置: research_report
2025-07-01 18:46 | INFO     | BaseObj.main: ✨ 任务完成
2025-07-01 18:49 | INFO     | BaseObj.main: 🔍 检测到文档类型: research_report
2025-07-01 18:49 | INFO     | BaseObj.main: 📄 PDF文件: D:\JG-files\07_量化交易\@@知识库\遗传因子\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.pdf
2025-07-01 18:49 | INFO     | BaseObj.main: 📁 输出目录: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 开始提取PDF内容... (文档类型: research_report)
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: PDF共有 21 页，开始提取内容...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 1 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 2 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 3 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 4 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 5 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 6 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 7 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 8 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 9 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 10 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 11 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 12 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 13 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 14 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 15 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 16 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 17 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 18 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 19 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 20 页...
2025-07-01 18:49 | INFO     | BaseObj.extract_pdf_content: 处理第 21 页...
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 成功提取 1344 个元素
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_1_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_2_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_2_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_3_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_3_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_4_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_4_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_5_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_5_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_6_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_6_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_7_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_7_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_8_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_8_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_9_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_9_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_10_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_10_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_11_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_11_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_12_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_12_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_13_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_13_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_14_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_14_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_15_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_15_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_16_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_16_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_17_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_17_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_18_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_18_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_19_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_19_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_20_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_20_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_21_img_1.jpeg
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 跳过无效图片: page_21_img_2.png
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: ✅ 转换完成！
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 📄 Markdown: D:\myquant\XentZ\citeds\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525\华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.md
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 🖼️  提取图片: 3 张
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: 📊 总元素数: 1344
2025-07-01 18:49 | INFO     | BaseObj.pdf_to_markdown: ⚙️  使用配置: research_report
2025-07-01 18:49 | INFO     | BaseObj.main: ✨ 任务完成
