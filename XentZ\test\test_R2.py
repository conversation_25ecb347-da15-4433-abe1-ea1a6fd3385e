#!/usr/bin/env python
# -*- coding:utf-8 -*-

from hikyuu import *

def part(period: int = 25) -> Indicator:
    """
    R方 - 线性回归的决定系数
    
    用于衡量线性回归模型对数据的拟合程度，值越接近1表示拟合越好。
    
    参数：
        period: 计算窗口长度，默认25天
        
    返回：
        Indicator: R方值，范围在0-1之间
    """
    # 获取对数收盘价
    log_c = LN(CLOSE())
    
    # 计算线性回归斜率
    slope = SLOPE(log_c, period)
    
    # 计算Y均值
    Y_Avg = MA(log_c, period)
    
    # 计算X的方差总和（X取值为0到period-1）
    X_Sum_Sq = (POW(period, 3) - period) / 12
    
    # 回归平方和（ESS）
    ESS = POW(slope, 2) * X_Sum_Sq
    
    # 总平方和（TSS）
    TSS = SUM(POW(log_c - Y_Avg, 2), period)
    
    # 计算R²
    r_squared = IF(TSS > 0, ESS / TSS, 0)
    
    r_squared = DISCARD(r_squared,period)
    
    # 创建结果指标
    result = r_squared
    result.name = "R方"
    
    return result


if __name__ == "__main__":
    import os
    import sys
    
    if sys.platform == 'win32':
        os.system('chcp 65001')

    # 仅加载测试需要的数据
    options = {
        "stock_list": ["sh000001"],
        "ktype_list": ["day"],
        "load_history_finance": False,
        "load_weight": False,
        "start_spot": False,
        "spot_worker_num": 1,
    }
    load_hikyuu(**options)

    stk = sm[options['stock_list'][0]]
    k = stk.get_kdata(Query(-3000))
    ind = part()
    ind_result = ind(k)
    # ind_result.discard = 25
    
    print("First 5 values:", [ind_result[i] for i in range(min(30, len(ind_result)))])
    print("Last 5 values:", [ind_result[i] for i in range(max(0, len(ind_result)-5), len(ind_result))])
    
    ind_result.plot(label=f"{stk.name}R方", legend_on=True)

    import matplotlib.pyplot as plt
    plt.show()