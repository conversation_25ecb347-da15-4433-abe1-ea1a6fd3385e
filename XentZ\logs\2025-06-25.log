2025-06-25 17:35 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-25 17:35 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-25 17:35 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-25 17:35 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-25 17:35 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-25 17:35 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-25 17:35 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-25 17:35 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-25 17:35 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-25 17:35 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-25 17:35 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-25 17:35 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-25 17:35 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-25 17:35 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-25 17:35 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-25 17:35 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-25 17:35 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 17:35 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 17:35 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 17:47 | INFO     | FctsGPMiner.mine: start mining on TEST...
2025-06-25 17:47 | INFO     | FctsGPMiner.mine: start mining on TEST...
2025-06-25 17:51 | INFO     | FctsGPMiner.mine: start mining on TEST...
2025-06-25 18:06 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-25 18:06 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-25 18:06 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-25 18:06 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-25 18:06 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-25 18:06 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-25 18:06 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-25 18:06 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-25 18:06 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-25 18:06 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-25 18:06 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-25 18:06 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-25 18:06 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-25 18:06 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-25 18:06 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-25 18:06 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-25 18:06 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 18:06 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 18:06 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 18:20 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-25 18:20 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-25 18:20 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-25 18:20 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-25 18:20 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-25 18:20 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-25 18:20 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-25 18:20 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-25 18:20 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-25 18:20 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-25 18:20 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-25 18:20 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-25 18:20 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-25 18:20 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-25 18:20 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-25 18:20 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-25 18:20 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 18:20 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 18:21 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 18:23 | INFO     | FctsGPMiner.mine: start mining on TEST...
2025-06-25 18:24 | INFO     | FctsGPMiner.mine: start mining on TEST...
2025-06-25 18:26 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-25 18:26 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-25 18:26 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-25 18:26 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-25 18:26 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-25 18:26 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-25 18:26 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-25 18:26 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-25 18:26 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-25 18:26 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-25 18:26 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-25 18:26 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-25 18:26 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-25 18:26 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-25 18:26 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-25 18:26 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-25 18:26 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 18:26 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-25 18:26 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-25 18:26 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 18:27 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-25 18:27 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-25 18:27 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 18:27 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-25 18:27 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-25 18:49 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-25 18:49 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-25 18:49 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-25 18:49 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-25 18:49 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-25 18:49 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-25 18:49 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-25 18:49 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-25 18:49 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-25 18:49 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-25 18:49 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-25 18:49 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-25 18:49 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-25 18:49 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-25 18:49 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-25 18:49 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-25 18:49 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 18:49 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-25 18:49 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-25 18:49 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 18:49 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-25 18:49 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-25 18:49 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 18:49 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-25 18:50 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-25 19:15 | INFO     | FctsGPMiner.mine: start mining on TEST...
2025-06-25 19:19 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-25 19:19 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-25 19:19 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-25 19:19 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-25 19:19 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-25 19:19 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-25 19:19 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-25 19:19 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-25 19:19 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-25 19:19 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-25 19:19 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-25 19:19 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-25 19:19 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-25 19:19 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-25 19:19 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-25 19:19 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-25 19:19 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 19:19 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-25 19:19 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-25 19:20 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-25 19:20 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 19:20 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-25 19:20 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-25 19:20 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-25 19:20 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 19:20 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-25 19:20 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-25 19:20 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-25 19:29 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-25 19:29 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-25 19:29 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-25 19:29 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-25 19:29 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-25 19:29 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-25 19:29 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-25 19:29 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-25 19:29 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-25 19:29 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-25 19:29 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-25 19:29 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-25 19:29 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-25 19:29 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-25 19:29 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-25 19:29 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-25 19:29 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 19:29 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-25 19:29 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-25 19:30 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-25 19:30 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 19:30 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-25 19:30 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-25 19:30 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-25 19:30 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-25 19:30 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-25 19:30 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-25 19:30 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
