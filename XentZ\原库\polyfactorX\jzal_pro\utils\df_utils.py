from operator import index
import pandas as pd
import numpy as np
from datetime import datetime
from icecream import ic
import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent.parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
from datafeed.dataloader import Featherloader
from datafeed.expr import calc_expr
from config import DATA_DIR_FEATHER,DATA_DIR_FACTOR
from enums import NORM_PARAMS_DEFAULT
import jzal_pro.utils.expr_utils as eu

def load_a_symbol(fields=[], names=[], symbols=None, columns=None, start_date='20100101', 
                  end_date=datetime.now().strftime('%Y%m%d'), path=DATA_DIR_FEATHER.joinpath('etfs').resolve()):
    ''' 比如load bechmark品种 '''
    if columns is None:
        columns = ['close']  # date是索引 因此不会被校验到且会跟着被返回
    loader = Featherloader(path=path, symbols=symbols, columns=columns,
                          start_date=start_date, end_date=end_date)
    df = loader.load(fields=fields, names=names)
    df = df.loc[start_date: end_date]
    
    return df

def load_a_symbol_fcts(a_symbol:str, base_features_df:pd.DataFrame, 
                       fcts:list, fcts_files = None) -> pd.DataFrame:
    '''
        a_symbol        : 单品种, 如510050.SH_15, 含部分基础数据[index(date),OHLC,volume,...]
        base_features_df: 基础特征df, [OHLC,volume_norm, R_0, V_0, sma5,...] 除了OHLC都normed, 不含label_
        fcts            : 因子表达式list, 可是gp的结果list或手动编写
        fcts_value_files: 已保存的计算好的fcts_value文件list, 格式: symbol_fcts_value_日期_时间.csv, 这里只拼接[日期_时间,...]即可
        返回 : norm后的因子值df, 也代表因子pos时序; 只根据fcts和fcts_files提供的因子返回相应的norm列, 以及ret/ret_open列(不norm)
    '''
    df = base_features_df.copy()
    df.dropna(axis=0, how='any', inplace=True) # 删除缺失值的行(axis=0)
    cols_selected = []
    new_features = []
    try:
        for fct in fcts:
            if fct not in df.columns:
                se = pd.Series(calc_expr(df, fct))
                se = pd.Series(np.float32(eu.norm(se, params=NORM_PARAMS_DEFAULT)),index=df.index)
                new_features.append(se)
                cols_selected.append(fct)
        df = pd.concat([df] + new_features, axis=1)
        df.columns = list(base_features_df.columns) + cols_selected
    except Exception as e:
        print('load {} 之后对features统一norm计算异常:{}'.format(a_symbol,e))
    df = df.replace([np.inf, np.nan, -np.inf],0.0) 
    # NOTE:以fcts为准,去重fcts_files中同样的列 -- fcts_list总要优先被计算, fcts_files的优先级高的排在前面
    if fcts_files:
        for ff in fcts_files:
            fct_path = DATA_DIR_FACTOR.joinpath(a_symbol+'_fcts_norm_'+ff+'.csv')  # eg. '510050.SH_15_fcts_norm_240518_21.csv'
            ff_df = pd.read_csv(fct_path, index_col='date')
            if df.index.equals(ff_df.index):
                ff_df  = ff_df.drop(columns=ff_df.columns.intersection(df.columns))
                df = pd.concat([df, ff_df], axis=1)
                cols_selected.extend(list(ff_df.columns))
            else:
                print(f'Skipping file {ff} because indexes are not aligned.')
    # 增加df['ret']列, ret不是label不用t期 也不norm
    df['ret'] = df['close'].shift(-1) / df['close'] - 1
    cols_selected.append('ret')
    df['ret_open'] = df['open'].shift(-1) / df['open'] - 1
    cols_selected.append('ret_open')
    df = df.replace([np.inf, np.nan, -np.inf], 0.0)

    return df[cols_selected]

def gen_etime_close_df_divd_time(sym_path:Path, bgn_date, end_date):
    ''' 输入: 1)sym_path为path类型, 调用之前先行处理路径, 对应的数据格式为[etime, close, ... ]; 2)切片的开始日期/结束日期
        返回: ['etime', 'tdate', 'close', 'open']  不带索引
    '''
    # 读取数据
    kbars = pd.read_excel(sym_path)
    kbars['tdate'] = pd.to_datetime(kbars['etime']).dt.date # 这一段需要优化
    dt = pd.to_datetime(kbars['etime'], format='%Y-%m-%d %H:%M:%S.%f')
    kbars['etime'] = pd.Series([pd.Timestamp(x).round('s').to_pydatetime() for x in dt])
    kbars['label'] = '-1'
    # 根据区间开始和结束日期截取数据
    bgn_date = pd.to_datetime(bgn_date)
    end_date = pd.to_datetime(end_date)
    kbars['label'] = np.where((kbars['etime'] >= bgn_date) & (kbars['etime'] <= end_date), '1', '-1') # 向量化写法
    # 筛选数据并重置索引
    kbars = kbars[kbars['label'] == '1']
    kbars = kbars.reset_index(drop=True)
    etime_close_data = kbars[['etime', 'tdate', 'close', 'open']]
    etime_close_data = etime_close_data.reset_index(drop=True)
    return etime_close_data # df: etime-tdate-clase-open  更新加入open为了计算nav更真实

def check_bar_counts(pkl_data_path:str, date_to_print:int, bars_per_day:int = 16):
    """
    检查指定路径下的数据文件(.pkl), 确保每天有16根15分钟K, 并打印出不符合这一条件的日期及其K线数量。
    同时，打印出特定日期的详细数据.
    
    pkl_data_path: 数据文件的路径。>> df.index 为 datetime
    date_to_print: 需要打印详细数据的日期，格式为'YYYYMMDD'
    bars_per_day: 缺省16(15min A stock)
    """
    data = pd.read_pickle(pkl_data_path)
    data['NumDate'] = data.index.year * 10000 + data.index.month * 100 + data.index.day
    # 按日期分组并计算每天的K线数量
    num_xminbar = data.groupby('NumDate').count()
    # 打印非16根bar的日期及其bar的数量
    print(num_xminbar[num_xminbar['open'] != bars_per_day])
    # 打印特定日期的详细数据
    specific_data = data[data['NumDate'] == date_to_print]
    print(specific_data)
    
def pkl2ftr_with_check_bar(pkl_path:str, ftr_path:str, date_to_print:int, bars_per_day:int = 16):
    '''
    检查指定路径下的数据文件(.pkl), 确保每天有16根15分钟K, 并打印出不符合这一条件的日期及其K线数量。
    同时，打印出特定日期的详细数据.
    pkl_data_path: 数据文件的路径。>> df.index 为 datetime
    date_to_print: 需要打印详细数据的日期，格式为'YYYYMMDD'
    bars_per_day: 缺省16(15min A stock)
    ftr_path : check没问题则save to feather类型文件
    '''
    df = pd.read_pickle(pkl_path)
    data = df.copy()
    data['etime']=pd.to_datetime(data['etime'])
    data.set_index('etime',inplace=True)
    data['NumDate'] = data.index.year * 10000 + data.index.month * 100 + data.index.day
    # 按日期分组并计算每天的K线数量
    num_xminbar = data.groupby('NumDate').count()
    # 打印非16根bar的日期及其bar的数量
    temp = num_xminbar[num_xminbar['open'] != bars_per_day]
    # 打印特定日期的详细数据
    specific_data = data[data['NumDate'] == date_to_print]
    print(specific_data)
    if temp.empty:
        df.rename(columns={'etime': 'date'}, inplace=True)
        df.to_feather(ftr_path)
    else:
        print('数据校验失败!')

def  zero_by_bins(a_df, n_bins=20, zero_bins=4, algo_type=0) -> pd.DataFrame:
    ''' NOTE: 全局分箱 -- 有未来函数!
        a_df: 时序数据, 可以是 因子值 或 OHLC, [date] col1 col2 ...
        n_bins: 分箱数, 缺省为20
        zero_bins: 中间零信号区, 缺省为中间4个bins内的数据变更为0
        algo_type: 分箱方法 - 0百分比分箱/1等距分箱
        return: 在零信号区数据变为0(其他数据不变)后的 结果时序数据res_df, [date] col1 col2 ...
    '''
    # 确保日期列为索引
    if 'date' in a_df.columns:
        a_df.set_index('date', inplace=True)
    cols = a_df.columns

    # 根据分箱方法选择分箱策略
    if algo_type == 0:  # 百分比分箱(等频)
        print("binning: 百分比分箱")
    elif algo_type == 1:  # 等距分箱
        print("binning: 等距分箱")
    else:
        raise ValueError("algo_type 必须为0或1")
    
    res_df = a_df.copy()
    # 分箱处理
    for col in cols:
        if algo_type == 0:  # 百分比分箱
            _, bins = pd.qcut(a_df[col], q=n_bins, retbins=True, duplicates='drop')
        elif algo_type == 1:  # 等距分箱
            bins = np.linspace(start=a_df[col].min(), stop=a_df[col].max(), num=n_bins + 1)
        # 添加零信号区
        if zero_bins > 0:
            mid_bin = len(bins) // 2
            low_bin = mid_bin - zero_bins // 2
            high_bin = mid_bin + zero_bins // 2
            # 确定零信号区的边界
            lower_bound = bins[low_bin]
            upper_bound = bins[high_bin]
            # 将零信号区内的数据置为0
            res_df.loc[(a_df[col] >= lower_bound) & (a_df[col] <= upper_bound), col] = 0
            res_df[col] = np.where((a_df[col] >= lower_bound) & (a_df[col] <= upper_bound), 0, a_df[col])
    return res_df

# 设置随机种子以确保结果可重复
np.random.seed(42)

# 生成正态分布的随机数据
data = {
    'date': pd.date_range(start='2023-01-01', periods=100),
    'col1': np.random.normal(loc=0, scale=1, size=100),
    'col2': np.random.normal(loc=5, scale=2, size=100),
    'col3': np.random.normal(loc=-5, scale=1.5, size=100)
}

# 创建 DataFrame
a_df = pd.DataFrame(data)

# # 测试 `binning` 函数
# if __name__ == "__main__":
#     res_df_quantile = binning(a_df, n_bins=10, zero_bins=2, algo_type=0)
#     print("Quantile Binning Results:")
#     print(res_df_quantile)
#     res_df_quantile = pd.concat([a_df, res_df_quantile], axis=1)
#     # res_df_quantile.to_csv('res_df_quantile.csv')

#     # 使用等距分箱
#     res_df_frequency = binning(a_df, n_bins=10, zero_bins=2, algo_type=1)
#     print("\nFrequency Binning Results:")
#     print(res_df_frequency)
#     res_df_frequency = pd.concat([a_df, res_df_frequency], axis=1)
#     # res_df_frequency.to_csv('res_df_frequency.csv')
