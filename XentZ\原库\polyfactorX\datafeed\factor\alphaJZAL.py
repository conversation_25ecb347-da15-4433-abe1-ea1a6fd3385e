from .alpha import AlphaBase

class AlphaJZAL(AlphaBase):

    def get_exprs_names(self):
        exprs = []
        names = []
        # 001: Awesome Oscillator (AO)
        fast_window = [5, 10, 20]
        slow_window = [10,20, 30]
        names += [f'JZ001_{fast}_{slow}' for fast in fast_window for slow in slow_window if fast < slow] 
        exprs += ["sma(0.5*(high+low),%d) - sma(0.5*(high+low),%d)" % (fast, slow) for fast in fast_window for slow in slow_window if fast < slow]
        # 002: ER - 240723
        windows = [13, 21, 34, 55, 89]  # 不同的时间窗口长度
        names += ["JZ002_%d" % d for d in windows]
        exprs += [
            f"Abs(close - shift(close, {d-1})) / ({'+'.join(f'Abs(shift(close, {i}) - shift(close, {i-1}))' for i in range(1, d))})"
            for d in windows
        ]
        # # 003: BBAND扩展 - 240816 -- X
        # windows = [5, 13, 21, 34, 55]
        # names += [f'JZ003_20_{d}' for d in windows]
        # exprs += [f'((bb_up_20 - close)/(bb_up_20 - bb_dn_20) - (shift(bb_up_20, {d}) - shift(close, {d}))/(shift(bb_up_20,{d}) - shift(bb_dn_20, {d})))' for d in windows]
        
        # 004: SF_train 区间slope震荡过滤 - 240818
        windows = [6, 10, 20, 30, 58, 80, 98, 120] # 偶数
        names += [f"JZ004_{d}" for d in windows]
        exprs += [f"slope((max(high,{d}) + min(low,{d}))/2, {int(d/2)})" for d in windows]
        
        # 005: RSRS - 240823
        windows = [9, 18, 34, 55, 89, 144, 233]
        names += [f"JZ005_rs{d}" for d in windows]
        exprs += [f"slope_pair(high,low,{d})" for d in windows]
        
        # 006: delta(volume,N) - 240825
        windows = [9, 18, 34, 55, 89]
        names += [f"JZ006_{d}" for d in windows]
        exprs += [f"delta(volume,{d})" for d in windows]
        
        # 007: udvd(close, volume, N) - 240825
        windows = [9, 18, 34, 55, 89]
        names += [f"JZ007_{d}" for d in windows]
        exprs += [f"udvd(close,volume,{d})" for d in windows]
        
        # 008: udvd2: sma(R_5-R_6, N) - 240825
        windows = [9, 18, 34, 55, 89]
        names += [f"JZ008_{d}" for d in windows]
        exprs += [f"sma((R_5-R_6),{d})" for d in windows]

        # 009: rvi - 240825
        windows = [9, 14, 34, 55, 89]
        names += [f"JZ009_{d}" for d in windows]
        exprs += [f"rvi(close,{d})" for d in windows]

        # 010: 梅斯线(Mass Index) - 240825
        windows = [9, 14, 21, 34, 55]
        names += [f"JZ010_{d}" for d in windows]
        exprs += [f"massi(high,low,{d},{2*d})" for d in windows]

        # 011: EOM(Ease of Movement) - 240825
        windows = [9, 14, 21, 34, 55]
        names += [f"JZ011_{d}" for d in windows]
        exprs += [f"eom(high,low,volume,{d})" for d in windows]
        
        # 012: ORV(Opening Range Volume) - 240830
        n_days = [5, 9, 14, 21, 34, 60, 89]
        names += [f"JZ012_{d}" for d in n_days]
        exprs += [f"orv(volume,{d})" for d in n_days]
        
        # # 013: ORV(Opening Range Volume) - 240830  -- 没出现信号
        # n_days = [5, 9, 14, 21, 34, 60]
        # compr_ratio = [5, 10, 20, 30]
        # names += [f"JZ013_{d}_{c}" for d in n_days for c in compr_ratio]
        # exprs += [f"orv_index(volume,{d},{c})" for d in n_days for c in compr_ratio]
        
        # 014: high_pass_3 高通滤波 - John Ehlers - 240918
        fast_window = [9, 14, 21, 34, 55]
        
        names += [f"JZ014_trends_mul2_{d}" for d in fast_window]
        exprs += [f"hpass_trends(close, {d}, {2*d})" for d in fast_window]
        
        names += [f"JZ014_trends_mul3_{d}" for d in fast_window]
        exprs += [f"hpass_trends(close, {d}, {3*d})" for d in fast_window]
        
        names += [f"JZ014_troc_mul2_{d}" for d in fast_window]
        exprs += [f"hpass_troc(close, {d}, {2*d})" for d in fast_window]
        
        names += [f"JZ014_troc_mul3_{d}" for d in fast_window]
        exprs += [f"hpass_troc(close, {d}, {3*d})" for d in fast_window]
                
        return exprs, names