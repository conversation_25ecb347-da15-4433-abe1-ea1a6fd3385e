2025-06-20 00:05 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 60
2025-06-20 00:05 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 122
2025-06-20 00:08 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 00:08 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 00:08 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 00:08 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 00:08 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 00:08 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 00:08 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 00:08 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 00:08 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 00:08 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 00:08 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 00:08 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 00:08 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 00:08 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['R_2', 'BETA30', 'BETA60', 'b_atr_14', 'b_atr_25', 'OPEN0', 'JZ004_20', 'KSFT', 'HIGH0', 'JZ012_21']...
2025-06-20 00:08 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 00:08 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 00:08 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 00:08 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 00:08 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 00:08 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-20 00:08 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 00:08 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-20 00:08 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:08 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-20 00:08 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-20 00:08 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-20 00:08 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(67个)，跳过相关性过滤
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 67), y形状: (1043,)
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始训练模型...
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 模型训练完成
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始获取重要性...
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性字典长度: 36
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性键样例: ['JZ001_5_10', 'open', 'low']
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=6, 最大R²=0.2536
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始训练模型...
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 模型训练完成
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始获取重要性...
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性字典长度: 36
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性键样例: ['JZ001_5_10', 'open', 'low']
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=5, 最大R²=0.2559
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始训练模型...
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 模型训练完成
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始获取重要性...
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性字典长度: 36
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性键样例: ['JZ001_5_10', 'open', 'low']
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=7, 最大R²=0.2557
2025-06-20 00:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-20 00:08 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-20 00:08 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-20 00:08 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-20 00:08 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-20 00:09 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-20 00:13 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 00:13 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 00:13 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 00:13 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 00:13 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 00:13 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 00:13 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 00:13 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 00:13 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 00:13 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 00:13 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 00:13 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 00:13 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 00:13 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['b_atr_25', 'JZ014_trends_mul2_21', 'JZ014_troc_mul3_55', 'JZ008_89', 'MAX10', 'R_0', 'JZ004_30', 'KLOW', 'b_atr_60', 'JZ011_9']...
2025-06-20 00:13 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 00:13 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 00:13 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 00:13 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 00:13 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 00:13 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-20 00:13 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 00:13 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-20 00:13 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:13 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-20 00:13 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-20 00:13 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-20 00:13 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:14 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 00:14 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 00:14 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 00:14 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 00:14 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 00:14 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 00:14 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 00:14 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 00:14 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 00:14 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 00:14 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 00:14 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 00:14 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 00:14 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ014_trends_mul2_34', 'b_macd_wt_hist', 'smadiff5', 'JZ008_55', 'roc_02', 'JZ011_55', 'STD30', 'JZ004_6', 'JZ004_98', 'JZ008_9']...
2025-06-20 00:14 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 00:14 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 00:14 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 00:14 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 00:14 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 00:14 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-20 00:14 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 00:14 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-20 00:14 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:14 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-20 00:14 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-20 00:14 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-20 00:14 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(67个)，跳过相关性过滤
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 67), y形状: (1043,)
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=5, 最大R²=0.2577
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=5, 最大R²=0.2552
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=7, 最大R²=0.2537
2025-06-20 00:14 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-20 00:14 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-20 00:14 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-20 00:14 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-20 00:14 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-20 00:14 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-20 00:21 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 00:21 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 00:21 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 00:21 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 00:21 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 00:21 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 00:21 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 00:21 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 00:21 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 00:21 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 00:21 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 00:21 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 00:21 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 00:21 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ008_9', 'JZ011_21', 'b_atr_60', 'CLOSE2', 'STD5', 'OPEN1', 'VSUMP_60', 'OPEN0', 'b_atr_25', 'V_1']...
2025-06-20 00:21 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 00:21 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 00:21 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 00:21 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 00:21 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 00:21 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-20 00:21 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 00:21 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-20 00:21 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:21 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-20 00:21 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-20 00:21 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-20 00:21 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(67个)，跳过相关性过滤
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 67), y形状: (1043,)
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=5, 最大R²=0.2577
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=5, 最大R²=0.2556
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=4, 最大R²=0.2537
2025-06-20 00:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-20 00:21 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-20 00:21 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-20 00:21 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-20 00:21 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-20 00:22 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 00:22 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 00:22 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 00:22 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['STD60', 'BETA60', 'KUP', 'JZ004_30', 'JZ012_21', 'MA5', 'STD20', 'b_atr_60', 'KMID', 'JZ014_troc_mul3_14']...
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 00:22 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 00:22 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-20 00:22 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 00:22 | WARNING  | FeatSelection.xgb_importance_analysis: [Unknown] 未找到目标列
2025-06-20 00:22 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 00:22 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 00:22 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 00:22 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['BETA5', 'HIGH2', 'R_2', 'KUP', 'VSUMN_60', 'KLEN', 'V_0', 'STD30', 'JZ004_58', 'b_atr_25']...
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 00:22 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 00:22 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 00:22 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-20 00:22 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征15
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(15个)，跳过相关性过滤
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 15), y形状: (1043,)
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 15
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=5, 最大R²=0.1562
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 15
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=7, 最大R²=0.1611
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 15
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=7, 最大R²=0.1570
2025-06-20 00:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-20 00:22 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-20 00:23 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 00:23 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 00:23 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 00:23 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 00:23 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 00:23 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 00:23 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 00:23 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 00:23 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 00:23 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 00:23 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 00:23 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 00:23 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 00:23 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['V_1', 'JZ014_trends_mul2_34', 'LOW0', 'BETA60', 'CLOSE4', 'STD30', 'JZ004_10', 'KSFT', 'MA10', 'roc_02']...
2025-06-20 00:23 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 00:23 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 00:23 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 00:23 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 00:23 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 00:23 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-20 00:23 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征15
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(15个)，跳过相关性过滤
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 15), y形状: (1043,)
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 15
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=5, 最大R²=0.1562
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 15
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=7, 最大R²=0.1611
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 15
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=7, 最大R²=0.1570
2025-06-20 00:23 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-20 00:23 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-20 00:23 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 00:24 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 00:24 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 00:24 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 00:24 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 00:24 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 00:24 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 00:24 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 00:24 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 00:24 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 00:24 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 00:24 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 00:24 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 00:24 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['CLOSE2', 'JZ011_14', 'KMID', 'BETA20', 'STD30', 'CLOSE3', 'tr_ma5', 'V_1', 'LOW4', 'MA5']...
2025-06-20 00:24 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 00:24 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 00:24 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 00:24 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 00:24 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 00:24 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-20 00:24 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征15
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(15个)，跳过相关性过滤
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 15), y形状: (1043,)
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 15
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=5, 最大R²=0.1562
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 15
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=7, 最大R²=0.1611
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 15
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=7, 最大R²=0.1570
2025-06-20 00:24 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-20 00:24 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-20 00:24 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_gain.png
2025-06-20 00:24 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_weight.png
2025-06-20 00:24 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_cover.png
2025-06-20 00:24 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 00:25 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 00:25 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 00:25 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 00:25 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 00:25 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 00:25 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 00:25 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 00:25 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 00:25 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 00:25 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 00:25 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 00:25 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 00:25 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['R_3', 'JZ011_21', 'QTLU5', 'smadiff10', 'roc_05', 'KSFT', 'smadiff5', 'BETA10', 'b_atr_14', 'LOW0']...
2025-06-20 00:25 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 00:25 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 00:25 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 00:25 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 00:25 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 00:25 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-20 00:25 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 00:25 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-20 00:25 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:25 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-20 00:25 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-20 00:25 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-20 00:25 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(67个)，跳过相关性过滤
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 67), y形状: (1043,)
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=5, 最大R²=0.2577
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=5, 最大R²=0.2502
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=7, 最大R²=0.2537
2025-06-20 00:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-20 00:25 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-20 00:25 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-20 00:25 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-20 00:25 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-20 00:26 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 00:26 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 00:26 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 00:26 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 00:26 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 00:26 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 00:26 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 00:26 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 00:26 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 00:26 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 00:26 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 00:26 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 00:26 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 00:26 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['MIN10', 'MA5', 'JZ011_55', 'JZ008_18', 'JZ014_troc_mul3_34', 'R_3', 'JZ004_10', 'BETA30', 'JZ014_trends_mul2_55', 'JZ004_98']...
2025-06-20 00:26 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 00:26 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 00:26 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 00:26 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 00:26 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 00:26 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-20 00:26 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 00:26 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-20 00:26 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:26 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-20 00:26 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-20 00:26 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-20 00:26 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(67个)，跳过相关性过滤
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 67), y形状: (1043,)
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=5, 最大R²=0.2577
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=5, 最大R²=0.2554
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=7, 最大R²=0.2537
2025-06-20 00:26 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-20 00:26 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-20 00:26 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-20 00:26 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-20 00:26 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-20 00:27 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 00:27 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 00:27 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 00:27 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 00:27 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 00:27 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 00:27 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 00:27 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 00:27 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 00:27 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 00:27 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 00:27 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 00:27 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 00:27 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['R_1', 'JZ004_120', 'JZ004_20', 'JZ012_34', 'JZ014_trends_mul3_55', 'JZ004_80', 'KMID', 'JZ004_10', 'JZ014_troc_mul3_55', 'JZ011_34']...
2025-06-20 00:27 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 00:27 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 00:27 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 00:27 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 00:27 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 00:27 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-20 00:27 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 00:27 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-20 00:27 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:27 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-20 00:27 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-20 00:27 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-20 00:27 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(67个)，跳过相关性过滤
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 67), y形状: (1043,)
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=5, 最大R²=0.2577
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=5, 最大R²=0.2585
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=4, 最大R²=0.2537
2025-06-20 00:27 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-20 00:27 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-20 00:27 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-20 00:28 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-20 00:28 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-20 00:29 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 00:29 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 00:29 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 00:29 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 00:29 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 00:29 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 00:29 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 00:29 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 00:29 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 00:29 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 00:29 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 00:29 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 00:29 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 00:29 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['R_3', 'CLOSE3', 'QTLD10', 'JZ011_34', 'JZ004_98', 'JZ011_55', 'JZ014_troc_mul2_14', 'MAX10', 'MA5', 'JZ014_trends_mul2_55']...
2025-06-20 00:29 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 00:29 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 00:29 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 00:29 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 00:29 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 00:29 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-20 00:29 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 00:29 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-20 00:29 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:29 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-20 00:29 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-20 00:29 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-20 00:29 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(67个)，跳过相关性过滤
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 67), y形状: (1043,)
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=5, 最大R²=0.2577
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=6, 最大R²=0.2598
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=7, 最大R²=0.2537
2025-06-20 00:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-20 00:29 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-20 00:29 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-20 00:29 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_gain.png
2025-06-20 00:29 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_weight.png
2025-06-20 00:29 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_cover.png
2025-06-20 00:29 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-20 00:29 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-20 00:32 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 00:32 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 00:32 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 00:32 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 00:32 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 00:32 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 00:32 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 00:32 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 00:32 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 00:32 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 00:32 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 00:32 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 00:32 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 00:32 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ014_troc_mul3_9', 'OPEN3', 'JZ014_troc_mul3_34', 'LOW2', 'JZ014_troc_mul2_9', 'b_macd_hist', 'R_1', 'V_3', 'KLOW', 'JZ008_89']...
2025-06-20 00:32 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 00:32 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 00:32 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 00:32 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 00:32 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 00:32 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-20 00:32 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 00:32 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-20 00:32 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:32 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-20 00:32 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-20 00:32 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-20 00:32 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(67个)，跳过相关性过滤
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 67), y形状: (1043,)
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=5, 最大R²=0.2577
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=5, 最大R²=0.2571
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=7, 最大R²=0.2537
2025-06-20 00:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-20 00:32 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-20 00:32 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-20 00:32 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_gain.png
2025-06-20 00:32 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_weight.png
2025-06-20 00:32 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_cover.png
2025-06-20 00:32 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-20 00:32 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 数据诊断: 样本1000, 原始特征5
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(5个)，跳过相关性过滤
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析1种重要性类型: ['gain']
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] X形状: (1000, 5), y形状: (1000,)
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: gain
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 第1/3次运行...
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 第2/3次运行...
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 第3/3次运行...
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain Ensemble完成，平均3次运行，有效特征数: 3
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 开始后续处理...
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=3, 最大R²=0.9590
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] XGBoost重要性分析完成，共分析1种类型
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 数据诊断: 样本1000, 原始特征5
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(5个)，跳过相关性过滤
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析1种重要性类型: ['gain']
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] X形状: (1000, 5), y形状: (1000,)
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: gain
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 第1/3次运行...
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 第2/3次运行...
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 第3/3次运行...
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain Ensemble完成，平均3次运行，有效特征数: 3
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 开始后续处理...
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 负相关特征修正: 1个特征获得重要性提升
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=3, 最大R²=0.9590
2025-06-20 00:34 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] XGBoost重要性分析完成，共分析1种类型
2025-06-20 00:36 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 00:36 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 00:36 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 00:36 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 00:36 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 00:36 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 00:36 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 00:36 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 00:36 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 00:36 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 00:36 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 00:36 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 00:36 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 00:36 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ012_89', 'LOW0', 'R_4', 'KMID', 'R_3', 'b_macd_hist', 'JZ012_34', 'JZ014_trends_mul2_21', 'R_6', 'KUP']...
2025-06-20 00:36 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 00:36 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 00:36 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 00:36 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 00:36 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 00:36 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-20 00:36 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 00:36 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-20 00:36 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:36 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-20 00:36 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-20 00:36 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-20 00:36 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(67个)，跳过相关性过滤
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 67), y形状: (1043,)
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=5, 最大R²=0.2577
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=5, 最大R²=0.2559
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=7, 最大R²=0.2537
2025-06-20 00:36 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-20 00:36 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-20 00:36 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-20 00:36 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_gain.png
2025-06-20 00:37 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_weight.png
2025-06-20 00:37 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_cover.png
2025-06-20 00:37 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-20 00:37 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 数据诊断: 样本500, 原始特征10
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(10个)，跳过相关性过滤
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] X形状: (500, 10), y形状: (500,)
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: gain
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 第1/3次运行...
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 第2/3次运行...
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 第3/3次运行...
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain Ensemble完成，平均3次运行，有效特征数: 10
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 开始后续处理...
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 负相关特征修正: 7个特征获得重要性提升
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=4, 最大R²=0.8631
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: weight
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 第1/3次运行...
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 第2/3次运行...
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 第3/3次运行...
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight Ensemble完成，平均3次运行，有效特征数: 10
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 开始后续处理...
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 负相关特征修正: 7个特征获得重要性提升
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=4, 最大R²=0.8621
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: cover
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 第1/3次运行...
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 第2/3次运行...
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 第3/3次运行...
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover Ensemble完成，平均3次运行，有效特征数: 10
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 开始后续处理...
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 负相关特征修正: 7个特征获得重要性提升
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=4, 最大R²=0.8614
2025-06-20 00:38 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] XGBoost重要性分析完成，共分析3种类型
2025-06-20 00:38 | INFO     | FeatSelection._compare_importance_types: [Unknown] 特征对比: 共同特征0个
2025-06-20 00:41 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 00:41 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 00:41 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 00:41 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 00:41 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 00:41 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 00:41 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 00:41 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 00:41 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 00:41 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 00:41 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 00:41 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 00:41 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 00:41 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['V_1', 'CLOSE2', 'JZ008_9', 'JZ014_troc_mul3_21', 'VSUMP_60', 'LOW4', 'CLOSE4', 'V_4', 'LOW3', 'JZ008_34']...
2025-06-20 00:41 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 00:41 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 00:41 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 00:41 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 00:41 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 00:41 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-20 00:41 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 00:41 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-20 00:41 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:41 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-20 00:41 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-20 00:41 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-20 00:41 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(67个)，跳过相关性过滤
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 67), y形状: (1043,)
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-20 00:41 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=29, 饱和法=18, 平坦法=8 -> 选择=23
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=23, 最大R²=0.2577
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-20 00:41 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=32, 饱和法=30, 平坦法=8 -> 选择=31
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=31, 最大R²=0.2547
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-20 00:41 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=15, 饱和法=12, 平坦法=9 -> 选择=13
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=13, 最大R²=0.2537
2025-06-20 00:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-20 00:41 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-20 00:41 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-20 00:41 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_gain.png
2025-06-20 00:41 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_weight.png
2025-06-20 00:41 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_cover.png
2025-06-20 00:41 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-20 00:41 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-20 00:43 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 00:43 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 00:43 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 00:43 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 00:43 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 00:43 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 00:43 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 00:43 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 00:43 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 00:43 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 00:43 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 00:43 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 00:43 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 00:43 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['MIN5', 'R_4', 'tr_ma10', 'R_0', 'VSUMN_60', 'JZ004_80', 'b_atr_60', 'b_atr_25', 'JZ011_34', 'OPEN3']...
2025-06-20 00:43 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 00:43 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 00:43 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 00:43 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 00:43 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 00:43 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-20 00:43 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 00:43 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-20 00:43 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:43 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-20 00:43 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-20 00:43 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-20 00:43 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(67个)，跳过相关性过滤
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 67), y形状: (1043,)
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-20 00:43 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=8, 饱和法=18, 平坦法=8 -> 选择=8
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2577
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-20 00:43 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=13, 饱和法=12, 平坦法=14 -> 选择=13
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=13, 最大R²=0.2621
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-20 00:43 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=11, 饱和法=10, 平坦法=9 -> 选择=10
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=10, 最大R²=0.2537
2025-06-20 00:43 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-20 00:43 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-20 00:43 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-20 00:43 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_gain.png
2025-06-20 00:43 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_weight.png
2025-06-20 00:43 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_cover.png
2025-06-20 00:44 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-20 00:44 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-20 00:46 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 00:46 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 00:46 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 00:46 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 00:46 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 00:46 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 00:46 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 00:46 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 00:46 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 00:46 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 00:46 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 00:46 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 00:46 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 00:46 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['MA5', 'QTLD10', 'JZ014_trends_mul2_21', 'JZ008_34', 'STD10', 'JZ008_18', 'OPEN3', 'V_2', 'R_0', 'roc_02']...
2025-06-20 00:46 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 00:46 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 00:46 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 00:46 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 00:46 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 00:46 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-20 00:46 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 00:46 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-20 00:46 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:46 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-20 00:46 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-20 00:46 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-20 00:46 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(67个)，跳过相关性过滤
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 67), y形状: (1043,)
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-20 00:46 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=8, 饱和法=18, 平坦法=8 -> 选择=8
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2577
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-20 00:46 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=15, 饱和法=27, 平坦法=9 -> 选择=15
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=15, 最大R²=0.2580
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 56
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-20 00:46 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=11, 饱和法=28, 平坦法=9 -> 选择=11
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=11, 最大R²=0.2537
2025-06-20 00:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-20 00:46 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-20 00:46 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-20 00:46 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_gain.png
2025-06-20 00:47 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_weight.png
2025-06-20 00:47 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_cover.png
2025-06-20 00:47 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-20 00:47 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-20 12:19 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 12:19 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 12:19 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 12:19 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 12:19 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 12:19 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 12:19 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 12:19 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 12:19 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 12:19 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 12:19 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 12:19 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 12:19 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 12:19 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['BETA60', 'OPEN4', 'VSUMP_60', 'KLOW', 'STD10', 'OPEN2', 'R_2', 'LOW1', 'b_atr_14', 'KUP']...
2025-06-20 12:19 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 12:19 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 12:19 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 12:19 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 12:19 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 12:19 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-20 12:19 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 12:19 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-20 12:19 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 12:19 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 70 列, 1045 行
2025-06-20 12:22 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 290 -> 257
2025-06-20 12:22 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 257 个特征 (窗口=20)
2025-06-20 12:22 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-20 12:22 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 326 个特征中保留了 198 个特征 (base2keep: 4, 冗余筛选: 194, 相关性阈值=0.85, 聚类数=195)
2025-06-20 12:22 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征198
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余195个
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1045, 195), y形状: (1045,)
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 63
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-20 12:22 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=4, 饱和法=9, 平坦法=50 -> 选择=9
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=9, 最大R²=0.9061
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 63
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-20 12:22 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=4, 饱和法=9, 平坦法=23 -> 选择=9
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=9, 最大R²=0.9056
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 63
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-20 12:22 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=4, 饱和法=18, 平坦法=6 -> 选择=6
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.9076
2025-06-20 12:22 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-20 12:22 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-20 12:26 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-20 12:26 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_gain.png
2025-06-20 12:26 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_weight.png
2025-06-20 12:26 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_cover.png
2025-06-20 12:27 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-20 12:27 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-20 12:30 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 12:30 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 12:30 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 12:30 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 12:30 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 12:30 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 12:30 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 12:30 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 12:30 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 12:30 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 12:30 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 12:30 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 12:30 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 12:30 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ008_34', 'JZ014_troc_mul2_14', 'MIN10', 'LOW0', 'V_3', 'KLOW', 'smadiff10', 'R_0', 'KLEN', 'R_6']...
2025-06-20 12:30 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 12:30 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 12:30 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 12:30 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 12:30 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 12:30 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-20 12:30 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 12:30 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-20 12:30 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 12:30 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 70 列, 1045 行
2025-06-20 12:32 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 290 -> 257
2025-06-20 12:32 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 257 个特征 (窗口=20)
2025-06-20 12:32 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-20 12:32 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 326 个特征中保留了 198 个特征 (base2keep: 4, 冗余筛选: 194, 相关性阈值=0.85, 聚类数=195)
2025-06-20 12:32 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征198
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余195个
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1045, 195), y形状: (1045,)
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 63
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-20 12:32 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=4, 饱和法=9, 平坦法=50 -> 选择=9
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=9, 最大R²=0.9061
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 63
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-20 12:32 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=4, 饱和法=9, 平坦法=23 -> 选择=9
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=9, 最大R²=0.9040
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 63
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-20 12:32 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=4, 饱和法=18, 平坦法=6 -> 选择=6
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.9076
2025-06-20 12:32 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-20 12:32 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-20 12:33 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-20 12:33 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_gain.png
2025-06-20 12:33 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_weight.png
2025-06-20 12:33 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_cover.png
2025-06-20 12:33 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-20 12:34 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-20 12:47 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-20 12:47 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-20 12:47 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-20 12:47 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-20 12:47 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-20 12:47 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-20 12:47 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-20 12:47 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-20 12:47 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-20 12:47 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-20 12:47 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-20 12:47 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-20 12:47 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-20 12:47 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ014_trends_mul2_55', 'OPEN1', 'JZ011_9', 'STD60', 'JZ008_34', 'JZ014_trends_mul2_34', 'roc_05', 'JZ014_troc_mul2_34', 'JZ004_6', 'STD5']...
2025-06-20 12:47 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-20 12:47 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-20 12:47 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-20 12:47 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-20 12:47 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-20 12:47 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-20 12:47 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-20 12:47 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-20 12:47 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 12:47 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 70 列, 1045 行
2025-06-20 12:49 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 290 -> 257
2025-06-20 12:50 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 257 个特征 (窗口=20)
2025-06-20 12:50 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-20 12:50 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 326 个特征中保留了 198 个特征 (base2keep: 4, 冗余筛选: 194, 相关性阈值=0.85, 聚类数=195)
2025-06-20 12:50 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征198
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余195个
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1045, 195), y形状: (1045,)
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 63
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-20 12:50 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=4, 饱和法=9, 平坦法=50 -> 选择=9
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=9, 最大R²=0.9061
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 63
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-20 12:50 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=4, 饱和法=9, 平坦法=23 -> 选择=9
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=9, 最大R²=0.9033
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 63
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-20 12:50 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=4, 饱和法=18, 平坦法=6 -> 选择=6
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.9076
2025-06-20 12:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-20 12:50 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-20 12:50 | INFO     | FeatSelection.select_features: 选择特征: 9个 + base2keep: 4个 + 标签: 1个
2025-06-20 12:50 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-20 12:50 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_gain.png
2025-06-20 12:50 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_weight.png
2025-06-20 12:50 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_cover.png
2025-06-20 12:50 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-20 12:51 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
