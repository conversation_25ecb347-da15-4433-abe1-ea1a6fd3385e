from datetime import datetime
import backtrader as bt
import pandas as pd

# ======================================================================================================================
# HELPERS：帮助函数，输出各种analyzer结果
# ======================================================================================================================
def pretty_print(format, *args):
    print(format.format(*args))

def exists(object, *properties):
    for property in properties:
        if not property in object: return False
        object = object.get(property)
    return True

def printTradeAnalysis(analyzers, bech_close_df=None):
    format = "  {:<24} : {:<24}"
    NA     = '-'
    print('Backtesting Results')
    
    if hasattr(analyzers, 'pyfolio'):
        portfolio_stats = analyzers.getbyname('pyfolio')
        capital_rets, positions, transactions,_ = portfolio_stats.get_pf_items()
        
        orders = transactions.copy()
        orders.index = orders.index.tz_convert(None)
        print(orders)
        orders.drop(columns=['sid'], inplace=True)
        #orders.to_csv('ord4.csv', index=True, header=True)
        
        nav = (1 + capital_rets).cumprod()  # 净值
        nav.name = 'nav' # net asset value
        nav.index = nav.index.tz_convert(None)
        if bech_close_df is not None:
            bech_close_df = bech_close_df/bech_close_df[0] # bench_close_df是series对象
            df_all_nav = pd.concat([nav, bech_close_df], axis=1)
        else:
            df_all_nav = pd.DataFrame(nav)
        df_all_nav.dropna(inplace=True)
        df_all_nav = df_all_nav / df_all_nav.iloc[0]  # 第一行归一, 以防万一
        ''' df_all_nav >>
                        nav    510300.SH
        2014-01-03   0.992221   0.989171
        '''
        from . import perf_utils
        df_ratios,df_nav_peryear = perf_utils.calc_stats(df_all_nav)
        print(df_ratios)
        print('------------------------------')
        print(df_nav_peryear)
        print('------------------------------')
        
        import empyrical
        calmar = empyrical.calmar_ratio(capital_rets)
        nav_final = empyrical.cum_returns_final(capital_rets)+1
        pretty_print(format, 'Nav', '{}'.format(round(nav_final,2)))

    if hasattr(analyzers, 'returns'):
        pretty_print(format, 'CAGR', '{}%'.format(round(analyzers.returns.get_analysis()['rnorm100'],2)))
    if hasattr(analyzers, 'drawdown'):
        pretty_print(format, 'Drawdown', '{}%'.format(round(analyzers.drawdown.get_analysis()['max']['drawdown'],2)))
    if calmar:
        pretty_print(format, 'Calmar', round(calmar,3)) 
    if hasattr(analyzers, 'sharpe'):
        pretty_print(format, 'Sharpe', round(analyzers.sharpe.get_analysis()['sharperatio'],3))
    if hasattr(analyzers, 'vwr'):
        pretty_print(format, 'VRW', round(analyzers.vwr.get_analysis()['vwr'],3))
    if hasattr(analyzers, 'sqn'):
        sqn=round(analyzers.sqn.get_analysis()['sqn'],3)
        outs=''
        if 1.6 <= sqn < 2.0:
            outs='poor but tradable'
        elif  2.0 <= sqn < 2.5:
            outs = 'average'
        elif  2.5 <= sqn < 3.0:
            outs = 'good'
        elif  3.0 <= sqn < 5.0:
            outs = 'excellent'
        elif  5.0 <= sqn < 7.0:
            outs = 'superb'
        elif  7.0 <= sqn:
            outs = 'holy grail'
        else:
            outs= 'bad'
        pretty_print(format, 'SQN', '{} - {}'.format(sqn,outs))
    #if hasattr(analyzers, 'calmar'):
        #pretty_print(format, 'Calmar', round(analyzers.calmar.get_analysis()['Calmar'][-1],3))
    print('\n')
    
    if hasattr(analyzers, 'ta'):
        ta = analyzers.ta.get_analysis()

        openTotal         = ta.total.open          if exists(ta, 'total', 'open'  ) else None
        closedTotal       = ta.total.closed        if exists(ta, 'total', 'closed') else None
        wonTotal          = ta.won.total           if exists(ta, 'won',   'total' ) else None
        lostTotal         = ta.lost.total          if exists(ta, 'lost',  'total' ) else None
        wonPnlAverage     = ta.won.pnl.average     if exists(ta, 'won', 'pnl', 'average') else None
        lostPnlAverage    = ta.lost.pnl.average    if exists(ta, 'lost', 'pnl', 'average') else None

        streakWonLongest  = ta.streak.won.longest  if exists(ta, 'streak', 'won',  'longest') else None
        streakLostLongest = ta.streak.lost.longest if exists(ta, 'streak', 'lost', 'longest') else None

        pnlGrossTotal     = ta.pnl.gross.total     if exists(ta, 'pnl', 'gross', 'total'  ) else None
        pnlNetTotal       = ta.pnl.net.total       if exists(ta, 'pnl', 'net', 'total'  ) else None
        pnlNetAverage     = ta.pnl.net.average     if exists(ta, 'pnl', 'net', 'average') else None

        pretty_print(format, 'Open Positions', openTotal   or NA)
        pretty_print(format, 'Closed Trades',  closedTotal or NA)
        pretty_print(format, 'Winning Trades', wonTotal    or NA)
        pretty_print(format, 'Loosing Trades', lostTotal   or NA)
        print('\n')

        pretty_print(format, 'Longest Winning Streak',   streakWonLongest  or NA)
        pretty_print(format, 'Longest Loosing Streak',   streakLostLongest or NA)
        pretty_print(format, 'Win Rate (Win/closed)' ,  '{}%'.format(round((wonTotal / closedTotal) * 100,2) if wonTotal and closedTotal else NA))
        pretty_print(format, 'Win/Loss Rate'         ,  round((wonPnlAverage / abs(lostPnlAverage)),2) if wonTotal and closedTotal else NA)
        print('\n')

        pretty_print(format, 'Gross P/L',              '${}'.format(round(pnlGrossTotal, 2)) if pnlNetTotal   else NA)
        pretty_print(format, 'Fees',                   '${}'.format(round(pnlGrossTotal-pnlNetTotal, 2)) if pnlNetTotal   else NA)
        pretty_print(format, 'Net P/L',                '${}'.format(round(pnlNetTotal,   2)) if pnlNetTotal   else NA)
        pretty_print(format, 'Net P/L Avg. per trade', '${}'.format(round(pnlNetAverage, 2)) if pnlNetAverage else NA)
        print('\n')
        '''

        import pyfolio as pf
        pf.create_full_tear_sheet(
            returns,
            positions=positions,
            transactions=transactions)
        '''
        # self.cerebro.plot(volume=False)