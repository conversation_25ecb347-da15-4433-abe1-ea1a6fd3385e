import numpy as np
import pandas as pd
import time
import bottleneck as bn
import talib
import scipy.stats as stats

# 假设 NORM_PARAMS_DEFAULT 已经定义
# NORM_PARAMS_DEFAULT = (2000, 6, 0, 0, True, 0)
# NORM_PARAMS_DEFAULT = (2000, 2, 0, 0, False, 0)
NORM_PARAMS_DEFAULT = (2000, 2, 0, 1, False, 3)

# 原始的 norm 函数
def norm_original(x, params=NORM_PARAMS_DEFAULT):
    window, clip_num, log_method, MA_type, is_demean, algo_norm = params
    try:
        if isinstance(x, pd.Series):
            x = x.replace([np.inf, np.nan, -np.inf], 0.0).astype(np.float64)
        elif isinstance(x, np.ndarray):
            x = np.nan_to_num(x, nan=0.0, posinf=0.0, neginf=-0.0)
        else:
            raise ValueError("Unsupported input type. Expected numpy.ndarray or pandas.Series.")
        # 对数处理
        if log_method == 1:
            x = np.log1p(x)
        elif log_method == 2:
            abs_x = np.abs(x)
            mean_abs_x = np.nanmean(abs_x)
            x = np.sign(x) * np.log1p(abs_x) / np.log1p(mean_abs_x)
            x = np.nan_to_num(x)
        else:
            x = np.asarray(x)
        
        # 转换为DataFrame以便使用rolling
        factor_data = pd.DataFrame(x, columns=['factor'])
        factor_data = factor_data.replace([np.inf, np.nan, -np.inf], 0.0)
        
        factor_mean = factor_data.rolling(window=window, min_periods=1).mean()
        factor_std = factor_data.rolling(window=window, min_periods=1).std()
        factor_data = factor_data.replace([np.inf, np.nan, -np.inf], 0.0)
        
        if is_demean:
            factor_data_2 = factor_data - factor_mean
        else:
            factor_data_2 = factor_data
        
        # 避免除以0
        with np.errstate(divide='ignore', invalid='ignore'):
            if algo_norm == 0: # zscore
                factor_value = factor_data_2 / factor_std
                factor_value[factor_std == 0] = 0.0  # 处理除以0的情况
            elif algo_norm == 1: # divisor: L2norm
                l2norm = factor_data_2.rolling(window=window, min_periods=1).apply(
                    lambda x: np.sqrt(np.sum(x**2)), raw=True)
                factor_value = factor_data_2 / l2norm
                factor_value[l2norm == 0] = 0.0  # 处理除以0的情况
            elif algo_norm == 2: # MinMax
                min_val = factor_data_2.rolling(window=window, min_periods=1).min()
                max_val = factor_data_2.rolling(window=window, min_periods=1).max()
                factor_value = (factor_data_2 - min_val) / (max_val - min_val)
                factor_value[(max_val - min_val) == 0] = 0.0  # 处理除以0的情况
            else:
                print('norm()函数的algo_norm参数错误')
            factor_value[np.isnan(factor_value) | np.isinf(factor_value)] = 0
            
        if MA_type == 1:
            factor_value['factor'] = talib.MA(factor_value['factor'].to_numpy(), timeperiod=5, matype=1)
        
        factor_value = factor_value.replace([np.inf, np.nan, -np.inf],0.0)
        factor_value = factor_value.clip(-clip_num, clip_num)
        
        return factor_value['factor'].to_numpy().flatten() # 变一维数组
    
    except Exception as e:
        print('norm()计算异常:{}'.format(e))
        if isinstance(x, pd.Series):
            return x.values
        else:
            return x

# 优化后的 norm 函数
def norm_optimized(x, params=NORM_PARAMS_DEFAULT):
    """
    滚动归一化函数, 支持NumPy数组和Pandas Series作为输入。
    参数: 
    - x: 输入数据,可以是NumPy数组或Pandas Series。
    - window: 滚动窗口大小,默认2000。
    - clip_num: 归一化后数据裁剪的最大绝对值,默认2。
    - log_method: 对数处理方法,0为不对数处理, 1为正数对数化,2为保留正负数的对数化。
    - MA_type: 是否使用移动平均,1为使用,其他为不使用。
    - is_demean: 是否减去均值,默认False。
    - algo_norm: 归一化算法,0为z-score, 1为L2-norm, 2为MinMax, 3为鲁棒, 4为累计概率
    返回: 归一化后的数组。
    """
    window, clip_num, log_method, MA_type, is_demean, algo_norm = params
    try:
        # 统一处理NaN和inf值
        if isinstance(x, pd.Series):
            x = x.replace([np.inf, np.nan, -np.inf], 0.0).astype(np.float64)
        elif isinstance(x, np.ndarray):
            x = np.nan_to_num(x, nan=0.0, posinf=0.0, neginf=0.0)
        else:
            raise ValueError("Unsupported input type. Expected numpy.ndarray or pandas.Series.")

        # 对数处理
        if log_method == 1:
            x = np.log1p(x)
        elif log_method == 2:
            mean_abs_x = np.nanmean(np.abs(x))
            x = np.sign(x) * np.log1p(np.abs(x)) / np.log1p(mean_abs_x)
            x = np.nan_to_num(x)

        if isinstance(x, pd.Series):
            x = x.to_numpy()

        # 计算滚动统计量
        factor_mean = pd.Series(x).rolling(window=window, min_periods=1).mean().to_numpy()
        factor_std = pd.Series(x).rolling(window=window, min_periods=1).std().to_numpy()

        # 去均值操作
        if is_demean:
            x = x - factor_mean

        # 归一化处理
        with np.errstate(divide='ignore', invalid='ignore'):
            if algo_norm == 0:  # zscore -- 性能提升2倍
                factor_value = x / factor_std
                factor_value[factor_std == 0] = 0.0
            elif algo_norm == 1:  # divisor: L2norm --- 性能没变, [-1,1] 可保号
                l2norm = pd.Series(x).rolling(window=window, min_periods=1).apply(
                    lambda x: np.sqrt(np.sum(x**2)), raw=True).to_numpy()
                factor_value = x / l2norm
                factor_value[l2norm == 0] = 0.0
            elif algo_norm == 2:  # MinMax -- 性能提升2倍 [-0.5,0.5]
                min_val = pd.Series(x).rolling(window=window, min_periods=1).min().to_numpy()
                max_val = pd.Series(x).rolling(window=window, min_periods=1).max().to_numpy()
                factor_value = ((x - min_val) / (max_val - min_val)) - 0.5
                factor_value[(max_val - min_val) == 0] = 0.0
            elif algo_norm == 3: # robust [-0.5,0.5]
                median = np.median(x, axis=0)
                iqr = np.percentile(x, 75, axis=0) - np.percentile(x, 25, axis=0)
                x = (x - median) / iqr
                lower_bound = np.percentile(x, 1, axis=0) # 基于标准化后的x
                upper_bound = np.percentile(x, 99, axis=0)
                factor_value = np.clip((x - lower_bound) / (upper_bound - lower_bound), 0, 1)
                factor_value = factor_value - 0.5
                factor_value[(upper_bound - lower_bound) == 0] = 0.0 
            elif algo_norm == 4: # 累计概率
                factor_value = x / factor_std
                factor_value = stats.norm.cdf(factor_value)
                factor_value = factor_value - 0.5
            else:
                raise ValueError("Invalid value for algo_norm")

        # 剔除异常值
        factor_value[np.isnan(factor_value) | np.isinf(factor_value)] = 0.0
        factor_value = np.clip(factor_value, -clip_num, clip_num)
        
        # 移动平均处理
        if MA_type == 1:
            factor_value = talib.MA(factor_value, timeperiod=5, matype=1)
        
        factor_value = np.nan_to_num(factor_value)
            
        return factor_value.flatten()

    except Exception as e:
        print(f'norm()计算异常: {e}')
        return x if isinstance(x, np.ndarray) else x.values

def rolling_window(a, window):
    shape = a.shape[:-1] + (a.shape[-1] - window + 1, window)
    # strides = a.strides + (a.strides[-1],)
    return np.lib.stride_tricks.sliding_window_view(a, window_shape=(window,)).reshape(shape)

# 生成测试数据
np.random.seed(42)
data = np.random.randn(100000)
data[np.random.choice(100000, size=1000, replace=False)] = np.nan
data[np.random.choice(100000, size=1000, replace=False)] = np.inf
data[np.random.choice(100000, size=1000, replace=False)] = -np.inf

# # 测试原始函数
# start_time = time.time()
# result_original = norm_original(data)
# end_time = time.time()
# print(f"Original function time: {end_time - start_time} seconds")

# 测试优化后的函数
start_time = time.time()
result_optimized = norm_optimized(data)
end_time = time.time()
print(f"Optimized function time: {end_time - start_time} seconds")

# 比较结果
# assert np.allclose(result_original, result_optimized, equal_nan=True), "Results are not consistent"
# print("Results are consistent")

# print(result_original)
print(result_optimized)

import matplotlib.pyplot as plt
plt.subplot(1, 2, 2)
plt.hist(result_optimized, bins=500, alpha=0.5, label='Normalized Data')
plt.title('Normalized Data Distribution')
plt.legend()

plt.show()