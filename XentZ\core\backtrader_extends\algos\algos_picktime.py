from .algo_base import *
from loguru import logger
import pandas as pd


class PickTime: # 择时平仓规则,注意是"平仓"
    def __init__(self, rule_picktime):
        self.rule_picktime = rule_picktime

    def __call__(self, target): # 不做首选,建议在前面几轮选择后调用picktime
        df_bar = target.df_bar.copy()
        df_bar.loc[:,'signal'] = df_bar.eval(self.rule_picktime)
        bar = df_bar[df_bar['signal'] == True]
        select = target.temp['selected']
        for s in bar.index: # bar的index是symbol
            logger.info('择时信号显示，平仓{}。'.format(s))
            if s in select:
                select.remove(s)
        target.temp['selected'] = select
        return True
