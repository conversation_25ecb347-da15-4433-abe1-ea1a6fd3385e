2025-07-06 00:25 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-06 00:25 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-06 00:25 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-06 00:25 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-06 00:25 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=50
2025-07-06 00:25 | INFO     | QuantStatsReportGenerator.__init__: quantstats_lumi库导入成功
2025-07-06 00:25 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-06 00:25 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: D:\myquant\reports\XentZ
2025-07-06 00:25 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-06 00:25 | INFO     | L3WFAValidator.__init__: L3动态稳健性检验器初始化完成
2025-07-06 00:25 | INFO     | L3WFAValidator.run_validation_pipeline: 开始L3动态稳健性检验流程
2025-07-06 00:25 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤1: 加载配置参数
2025-07-06 00:25 | DEBUG    | L3WFAValidator._load_and_validate_config: 配置参数验证通过
2025-07-06 00:25 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤2: 查询L2阶段通过的因子
2025-07-06 00:25 | DEBUG    | FactorQueryManager.query_l2_passed_factors: 开始查询L2阶段通过的因子
2025-07-06 00:25 | DEBUG    | FactorQueryManager.query_l2_passed_factors: 查询完成: 找到1个因子，耗时0.00秒
2025-07-06 00:25 | DEBUG    | FactorQueryManager.query_l2_passed_factors: 品种分布: {'["SH510050"]': 1}
2025-07-06 00:25 | INFO     | L3WFAValidator._query_l2_factors: 查询到1个L2通过的因子
2025-07-06 00:25 | DEBUG    | L3WFAValidator._query_l2_factors: 品种分布: {'["SH510050"]': 1}
2025-07-06 00:25 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤3: 批量执行WFA验证
2025-07-06 00:25 | INFO     | BatchProcessor.process_factors_batch: 开始批量处理: 1个因子
2025-07-06 00:25 | DEBUG    | FactorQueryManager.load_factor_and_price_data: 开始加载因子数据: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-06 00:25 | WARNING  | FactorValueManager._basic_disk_load: 指定因子不存在于批次 L2_LASSO_SH510050_label_1_20250704_718779
2025-07-06 00:25 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: L2_LASSO_SH510050_label_1_20250704_718779/L2 耗时24.0ms
2025-07-06 00:25 | WARNING  | FactorQueryManager.load_factor_and_price_data: 因子值数据为空: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-06 00:25 | WARNING  | L3WFAValidator.validate_single_factor: 因子数据为空，跳过验证: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-06 00:25 | INFO     | BatchProcessor.progress_callback: 处理进度: 100.0%, 当前因子: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-06 00:25 | DEBUG    | BatchProcessor.process_factors_batch: 块处理完成: 1个因子, 耗时0.03秒
2025-07-06 00:25 | INFO     | BatchProcessor.process_factors_batch: 批量处理完成: 成功0/1, 总耗时0.03秒
2025-07-06 00:25 | INFO     | L3WFAValidator._batch_validate_factors: 批量验证完成: 0/1个因子验证成功
2025-07-06 00:25 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤4: 汇总验证结果
2025-07-06 00:25 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤5: 生成验证报告
2025-07-06 00:25 | INFO     | L3WFAValidator._generate_comprehensive_reports: 开始生成综合验证报告
2025-07-06 00:25 | WARNING  | BatchReportManager.generate_batch_reports: WFA结果为空，无法生成报告
2025-07-06 00:25 | INFO     | L3WFAValidator._generate_validation_report: 验证报告已生成: D:\myquant\reports\XentZ\L3_validation\L3_WFA_validation_report_20250706_002544.txt
2025-07-06 00:25 | WARNING  | L3WFAValidator._generate_comprehensive_reports: 批量报告生成失败: 无数据
2025-07-06 00:25 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤6: 更新因子状态
2025-07-06 00:25 | DEBUG    | L3WFAValidator._update_factor_status: 开始更新因子状态
2025-07-06 00:25 | INFO     | L3WFAValidator._update_factor_status: 因子状态更新完成: 0个因子
2025-07-06 00:25 | INFO     | L3WFAValidator.run_validation_pipeline: L3验证流程完成: 总耗时0.05秒
2025-07-06 00:26 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-06 00:26 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-06 00:26 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-06 00:26 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-06 00:26 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=50
2025-07-06 00:26 | INFO     | QuantStatsReportGenerator.__init__: quantstats_lumi库导入成功
2025-07-06 00:26 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-06 00:26 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: D:\myquant\reports\XentZ
2025-07-06 00:26 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-06 00:26 | INFO     | L3WFAValidator.__init__: L3动态稳健性检验器初始化完成
2025-07-06 00:26 | INFO     | L3WFAValidator.run_validation_pipeline: 开始L3动态稳健性检验流程
2025-07-06 00:26 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤1: 加载配置参数
2025-07-06 00:26 | DEBUG    | L3WFAValidator._load_and_validate_config: 配置参数验证通过
2025-07-06 00:26 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤2: 查询L2阶段通过的因子
2025-07-06 00:26 | DEBUG    | FactorQueryManager.query_l2_passed_factors: 开始查询L2阶段通过的因子
2025-07-06 00:26 | DEBUG    | FactorQueryManager.query_l2_passed_factors: 查询完成: 找到1个因子，耗时0.00秒
2025-07-06 00:26 | DEBUG    | FactorQueryManager.query_l2_passed_factors: 品种分布: {'["SH510050"]': 1}
2025-07-06 00:26 | INFO     | L3WFAValidator._query_l2_factors: 查询到1个L2通过的因子
2025-07-06 00:26 | DEBUG    | L3WFAValidator._query_l2_factors: 品种分布: {'["SH510050"]': 1}
2025-07-06 00:26 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤3: 批量执行WFA验证
2025-07-06 00:26 | INFO     | BatchProcessor.process_factors_batch: 开始批量处理: 1个因子
2025-07-06 00:26 | DEBUG    | FactorQueryManager.load_factor_and_price_data: 开始加载因子数据: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-06 00:26 | WARNING  | FactorValueManager._basic_disk_load: 指定因子不存在于批次 L2_LASSO_SH510050_label_1_20250704_718779
2025-07-06 00:26 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: L2_LASSO_SH510050_label_1_20250704_718779/L2 耗时24.8ms
2025-07-06 00:26 | WARNING  | FactorQueryManager.load_factor_and_price_data: 因子值数据为空: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-06 00:26 | WARNING  | L3WFAValidator.validate_single_factor: 因子数据为空，跳过验证: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-06 00:26 | INFO     | BatchProcessor.progress_callback: 处理进度: 100.0%, 当前因子: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-06 00:26 | DEBUG    | BatchProcessor.process_factors_batch: 块处理完成: 1个因子, 耗时0.03秒
2025-07-06 00:26 | INFO     | BatchProcessor.process_factors_batch: 批量处理完成: 成功0/1, 总耗时0.03秒
2025-07-06 00:26 | INFO     | L3WFAValidator._batch_validate_factors: 批量验证完成: 0/1个因子验证成功
2025-07-06 00:26 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤4: 汇总验证结果
2025-07-06 00:26 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤5: 生成验证报告
2025-07-06 00:26 | INFO     | L3WFAValidator._generate_comprehensive_reports: 开始生成综合验证报告
2025-07-06 00:26 | WARNING  | BatchReportManager.generate_batch_reports: WFA结果为空，无法生成报告
2025-07-06 00:26 | INFO     | L3WFAValidator._generate_validation_report: 验证报告已生成: D:\myquant\reports\XentZ\L3_validation\L3_WFA_validation_report_20250706_002605.txt
2025-07-06 00:26 | WARNING  | L3WFAValidator._generate_comprehensive_reports: 批量报告生成失败: 无数据
2025-07-06 00:26 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤6: 更新因子状态
2025-07-06 00:26 | DEBUG    | L3WFAValidator._update_factor_status: 开始更新因子状态
2025-07-06 00:26 | INFO     | L3WFAValidator._update_factor_status: 因子状态更新完成: 0个因子
2025-07-06 00:26 | INFO     | L3WFAValidator.run_validation_pipeline: L3验证流程完成: 总耗时0.05秒
2025-07-06 00:26 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-06 00:26 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-06 00:26 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-06 00:26 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-06 00:26 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=50
2025-07-06 00:26 | INFO     | QuantStatsReportGenerator.__init__: quantstats_lumi库导入成功
2025-07-06 00:26 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-06 00:26 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: D:\myquant\reports\XentZ
2025-07-06 00:26 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-06 00:26 | INFO     | L3WFAValidator.__init__: L3动态稳健性检验器初始化完成
2025-07-06 00:26 | INFO     | L3WFAValidator.run_validation_pipeline: 开始L3动态稳健性检验流程
2025-07-06 00:26 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤1: 加载配置参数
2025-07-06 00:26 | DEBUG    | L3WFAValidator._load_and_validate_config: 配置参数验证通过
2025-07-06 00:26 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤2: 查询L2阶段通过的因子
2025-07-06 00:26 | DEBUG    | FactorQueryManager.query_l2_passed_factors: 开始查询L2阶段通过的因子
2025-07-06 00:26 | DEBUG    | FactorQueryManager.query_l2_passed_factors: 查询完成: 找到1个因子，耗时0.00秒
2025-07-06 00:26 | DEBUG    | FactorQueryManager.query_l2_passed_factors: 品种分布: {'["SH510050"]': 1}
2025-07-06 00:26 | INFO     | L3WFAValidator._query_l2_factors: 查询到1个L2通过的因子
2025-07-06 00:26 | DEBUG    | L3WFAValidator._query_l2_factors: 品种分布: {'["SH510050"]': 1}
2025-07-06 00:26 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤3: 批量执行WFA验证
2025-07-06 00:26 | INFO     | BatchProcessor.process_factors_batch: 开始批量处理: 1个因子
2025-07-06 00:26 | DEBUG    | FactorQueryManager.load_factor_and_price_data: 开始加载因子数据: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-06 00:26 | WARNING  | FactorValueManager._basic_disk_load: 指定因子不存在于批次 L2_LASSO_SH510050_label_1_20250704_718779
2025-07-06 00:26 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: L2_LASSO_SH510050_label_1_20250704_718779/L2 耗时27.3ms
2025-07-06 00:26 | WARNING  | FactorQueryManager.load_factor_and_price_data: 因子值数据为空: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-06 00:26 | WARNING  | L3WFAValidator.validate_single_factor: 因子数据为空，跳过验证: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-06 00:26 | INFO     | BatchProcessor.progress_callback: 处理进度: 100.0%, 当前因子: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-06 00:26 | DEBUG    | BatchProcessor.process_factors_batch: 块处理完成: 1个因子, 耗时0.03秒
2025-07-06 00:26 | INFO     | BatchProcessor.process_factors_batch: 批量处理完成: 成功0/1, 总耗时0.03秒
2025-07-06 00:26 | INFO     | L3WFAValidator._batch_validate_factors: 批量验证完成: 0/1个因子验证成功
2025-07-06 00:26 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤4: 汇总验证结果
2025-07-06 00:26 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤5: 生成验证报告
2025-07-06 00:26 | INFO     | L3WFAValidator._generate_comprehensive_reports: 开始生成综合验证报告
2025-07-06 00:26 | WARNING  | BatchReportManager.generate_batch_reports: WFA结果为空，无法生成报告
2025-07-06 00:26 | INFO     | L3WFAValidator._generate_validation_report: 验证报告已生成: D:\myquant\reports\XentZ\L3_validation\L3_WFA_validation_report_20250706_002645.txt
2025-07-06 00:26 | WARNING  | L3WFAValidator._generate_comprehensive_reports: 批量报告生成失败: 无数据
2025-07-06 00:26 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤6: 更新因子状态
2025-07-06 00:26 | DEBUG    | L3WFAValidator._update_factor_status: 开始更新因子状态
2025-07-06 00:26 | INFO     | L3WFAValidator._update_factor_status: 因子状态更新完成: 0个因子
2025-07-06 00:26 | INFO     | L3WFAValidator.run_validation_pipeline: L3验证流程完成: 总耗时0.06秒
2025-07-06 00:27 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-06 00:27 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-06 00:27 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 00:27 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: L1_CORR_SH510050_label_1_20250704_187123/L1
2025-07-06 00:27 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: L1_CORR_SH510050_label_1_20250704_187123/L1 耗时81.7ms
2025-07-06 00:27 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 00:27 | INFO     | ResMonitor.end_timer: L2_LASSO_SH510050_label_1 took 0.02 seconds (00:00:00)
2025-07-06 00:27 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_SH510050_label_1 CPU usage: 0.02s user, 0.00s system
2025-07-06 00:27 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_SH510050_label_1 memory delta: 0.39MB
2025-07-06 00:27 | INFO     | FactorMonitorContext.__exit__: L2_LASSO_SH510050_label_1 completed successfully
2025-07-06 00:27 | INFO     | ResMonitor.end_timer: L2_LASSO_FILTER_1751732872704141 took 0.30 seconds (00:00:00)
2025-07-06 00:27 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_FILTER_1751732872704141 CPU usage: 0.06s user, 0.00s system
2025-07-06 00:27 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_FILTER_1751732872704141 memory delta: 15.86MB
2025-07-06 00:27 | ERROR    | MonitorContext.__exit__: L2_LASSO_FILTER_1751732872704141 failed with error: 'FactorValueManager' object has no attribute 'get_factor_value_manager'
    📍 file:///D:/myquant/XentZ/common/cls_base.py:311
    📚 调用栈:
      1. run_l2_lasso_filter_pipeline() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L2总体重要筛选.py:497
      2. <module>() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L2总体重要筛选.py:696

2025-07-06 00:29 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-06 00:29 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-06 00:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 00:29 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: L1_CORR_SH510050_label_1_20250704_187123/L1
2025-07-06 00:29 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: L1_CORR_SH510050_label_1_20250704_187123/L1 耗时23.0ms
2025-07-06 00:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 00:29 | INFO     | ResMonitor.end_timer: L2_LASSO_SH510050_label_1 took 0.02 seconds (00:00:00)
2025-07-06 00:29 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_SH510050_label_1 CPU usage: 0.02s user, 0.00s system
2025-07-06 00:29 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_SH510050_label_1 memory delta: 0.34MB
2025-07-06 00:29 | INFO     | FactorMonitorContext.__exit__: L2_LASSO_SH510050_label_1 completed successfully
2025-07-06 00:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 00:29 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_LASSO_SH510050_label_1_20250706_962637
2025-07-06 00:29 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_LASSO_SH510050_label_1_20250706_962637 耗时64.5ms
2025-07-06 00:29 | INFO     | ResMonitor.end_timer: PERSIST_L2_LASSO_SH510050_label_1 took 0.07 seconds (00:00:00)
2025-07-06 00:29 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_LASSO_SH510050_label_1 CPU usage: 0.00s user, 0.06s system
2025-07-06 00:29 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_LASSO_SH510050_label_1 memory delta: 3.01MB
2025-07-06 00:29 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_LASSO_SH510050_label_1 completed successfully
2025-07-06 00:29 | INFO     | ResMonitor.end_timer: L2_LASSO_FILTER_1751732952962637 took 0.24 seconds (00:00:00)
2025-07-06 00:29 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_FILTER_1751732952962637 CPU usage: 0.06s user, 0.06s system
2025-07-06 00:29 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_FILTER_1751732952962637 memory delta: 19.24MB
2025-07-06 00:29 | INFO     | MonitorContext.__exit__: L2_LASSO_FILTER_1751732952962637 completed successfully
2025-07-06 00:29 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-06 00:29 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-06 00:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 00:29 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_211730/L0
2025-07-06 00:29 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_211730/L0 耗时62.5ms
2025-07-06 00:29 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_277461/L0
2025-07-06 00:29 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_277461/L0 耗时66.5ms
2025-07-06 00:29 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_366378/L0
2025-07-06 00:29 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_366378/L0 耗时43.5ms
2025-07-06 00:29 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-07-06 00:29 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时39.4ms
2025-07-06 00:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 00:29 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-06 00:29 | INFO     | ResMonitor.end_timer: L1_CORR_SH510050_label_1 took 0.00 seconds (00:00:00)
2025-07-06 00:29 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-06 00:29 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_SH510050_label_1 memory delta: 0.23MB
2025-07-06 00:29 | INFO     | FactorMonitorContext.__exit__: L1_CORR_SH510050_label_1 completed successfully
2025-07-06 00:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 00:29 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L1_CORR_SH510050_label_1_20250706_980901
2025-07-06 00:29 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L1_CORR_SH510050_label_1_20250706_980901 耗时10.8ms
2025-07-06 00:29 | INFO     | ResMonitor.end_timer: PERSIST_L1_SH510050_label_1 took 0.01 seconds (00:00:00)
2025-07-06 00:29 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L1_SH510050_label_1 CPU usage: 0.02s user, 0.00s system
2025-07-06 00:29 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L1_SH510050_label_1 memory delta: 2.12MB
2025-07-06 00:29 | INFO     | FactorMonitorContext.__exit__: PERSIST_L1_SH510050_label_1 completed successfully
2025-07-06 00:29 | INFO     | ResMonitor.end_timer: L1_CORR_FILTER_1751732976980901 took 0.43 seconds (00:00:00)
2025-07-06 00:29 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_FILTER_1751732976980901 CPU usage: 0.08s user, 0.05s system
2025-07-06 00:29 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_FILTER_1751732976980901 memory delta: 22.08MB
2025-07-06 00:29 | INFO     | MonitorContext.__exit__: L1_CORR_FILTER_1751732976980901 completed successfully
2025-07-06 00:29 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-06 00:29 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-06 00:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 00:29 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: L1_CORR_SH510050_label_1_20250706_980901/L1
2025-07-06 00:29 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: L1_CORR_SH510050_label_1_20250706_980901/L1 耗时31.1ms
2025-07-06 00:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 00:29 | INFO     | ResMonitor.end_timer: L2_LASSO_SH510050_label_1 took 0.02 seconds (00:00:00)
2025-07-06 00:29 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_SH510050_label_1 CPU usage: 0.02s user, 0.00s system
2025-07-06 00:29 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_SH510050_label_1 memory delta: 0.48MB
2025-07-06 00:29 | INFO     | FactorMonitorContext.__exit__: L2_LASSO_SH510050_label_1 completed successfully
2025-07-06 00:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 00:29 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_LASSO_SH510050_label_1_20250706_019877
2025-07-06 00:29 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_LASSO_SH510050_label_1_20250706_019877 耗时8.0ms
2025-07-06 00:29 | INFO     | ResMonitor.end_timer: PERSIST_L2_LASSO_SH510050_label_1 took 0.01 seconds (00:00:00)
2025-07-06 00:29 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_LASSO_SH510050_label_1 CPU usage: 0.02s user, 0.00s system
2025-07-06 00:29 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_LASSO_SH510050_label_1 memory delta: 2.45MB
2025-07-06 00:29 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_LASSO_SH510050_label_1 completed successfully
2025-07-06 00:29 | INFO     | ResMonitor.end_timer: L2_LASSO_FILTER_1751732989019877 took 0.19 seconds (00:00:00)
2025-07-06 00:29 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_FILTER_1751732989019877 CPU usage: 0.05s user, 0.02s system
2025-07-06 00:29 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_FILTER_1751732989019877 memory delta: 18.84MB
2025-07-06 00:29 | INFO     | MonitorContext.__exit__: L2_LASSO_FILTER_1751732989019877 completed successfully
2025-07-06 00:30 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-06 00:30 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-06 00:30 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-06 00:30 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-06 00:30 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=50
2025-07-06 00:30 | INFO     | QuantStatsReportGenerator.__init__: quantstats_lumi库导入成功
2025-07-06 00:30 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-06 00:30 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: D:\myquant\reports\XentZ
2025-07-06 00:30 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-06 00:30 | INFO     | L3WFAValidator.__init__: L3动态稳健性检验器初始化完成
2025-07-06 00:30 | INFO     | L3WFAValidator.run_validation_pipeline: 开始L3动态稳健性检验流程
2025-07-06 00:30 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤1: 加载配置参数
2025-07-06 00:30 | DEBUG    | L3WFAValidator._load_and_validate_config: 配置参数验证通过
2025-07-06 00:30 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤2: 查询L2阶段通过的因子
2025-07-06 00:30 | DEBUG    | FactorQueryManager.query_l2_passed_factors: 开始查询L2阶段通过的因子
2025-07-06 00:30 | DEBUG    | FactorQueryManager.query_l2_passed_factors: 查询完成: 找到1个因子，耗时0.00秒
2025-07-06 00:30 | DEBUG    | FactorQueryManager.query_l2_passed_factors: 品种分布: {'["SH510050"]': 1}
2025-07-06 00:30 | INFO     | L3WFAValidator._query_l2_factors: 查询到1个L2通过的因子
2025-07-06 00:30 | DEBUG    | L3WFAValidator._query_l2_factors: 品种分布: {'["SH510050"]': 1}
2025-07-06 00:30 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤3: 批量执行WFA验证
2025-07-06 00:30 | INFO     | BatchProcessor.process_factors_batch: 开始批量处理: 1个因子
2025-07-06 00:30 | DEBUG    | FactorQueryManager.load_factor_and_price_data: 开始加载因子数据: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-06 00:30 | WARNING  | FactorValueManager._basic_disk_load: 指定因子不存在于批次 L2_LASSO_SH510050_label_1_20250704_718779
2025-07-06 00:30 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: L2_LASSO_SH510050_label_1_20250704_718779/L2 耗时29.0ms
2025-07-06 00:30 | WARNING  | FactorQueryManager.load_factor_and_price_data: 因子值数据为空: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-06 00:30 | WARNING  | L3WFAValidator.validate_single_factor: 因子数据为空，跳过验证: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-06 00:30 | INFO     | BatchProcessor.progress_callback: 处理进度: 100.0%, 当前因子: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-06 00:30 | DEBUG    | BatchProcessor.process_factors_batch: 块处理完成: 1个因子, 耗时0.03秒
2025-07-06 00:30 | INFO     | BatchProcessor.process_factors_batch: 批量处理完成: 成功0/1, 总耗时0.03秒
2025-07-06 00:30 | INFO     | L3WFAValidator._batch_validate_factors: 批量验证完成: 0/1个因子验证成功
2025-07-06 00:30 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤4: 汇总验证结果
2025-07-06 00:30 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤5: 生成验证报告
2025-07-06 00:30 | INFO     | L3WFAValidator._generate_comprehensive_reports: 开始生成综合验证报告
2025-07-06 00:30 | WARNING  | BatchReportManager.generate_batch_reports: WFA结果为空，无法生成报告
2025-07-06 00:30 | INFO     | L3WFAValidator._generate_validation_report: 验证报告已生成: D:\myquant\reports\XentZ\L3_validation\L3_WFA_validation_report_20250706_003005.txt
2025-07-06 00:30 | WARNING  | L3WFAValidator._generate_comprehensive_reports: 批量报告生成失败: 无数据
2025-07-06 00:30 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤6: 更新因子状态
2025-07-06 00:30 | DEBUG    | L3WFAValidator._update_factor_status: 开始更新因子状态
2025-07-06 00:30 | INFO     | L3WFAValidator._update_factor_status: 因子状态更新完成: 0个因子
2025-07-06 00:30 | INFO     | L3WFAValidator.run_validation_pipeline: L3验证流程完成: 总耗时0.05秒
2025-07-06 10:29 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-06 10:29 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-06 10:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 10:29 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: L1_CORR_SH510050_label_1_20250706_980901/L1
2025-07-06 10:29 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: L1_CORR_SH510050_label_1_20250706_980901/L1 耗时151.3ms
2025-07-06 10:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 10:29 | INFO     | ResMonitor.end_timer: L2_LASSO_SH510050_label_1 took 0.02 seconds (00:00:00)
2025-07-06 10:29 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_SH510050_label_1 CPU usage: 0.00s user, 0.02s system
2025-07-06 10:29 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_SH510050_label_1 memory delta: 0.32MB
2025-07-06 10:29 | INFO     | FactorMonitorContext.__exit__: L2_LASSO_SH510050_label_1 completed successfully
2025-07-06 10:29 | INFO     | ResMonitor.end_timer: L2_LASSO_FILTER_1751768983257554 took 0.51 seconds (00:00:00)
2025-07-06 10:29 | INFO     | ResMonitor.end_timer_cpu: L2_LASSO_FILTER_1751768983257554 CPU usage: 0.02s user, 0.03s system
2025-07-06 10:29 | INFO     | ResMonitor.end_memory_monitor: L2_LASSO_FILTER_1751768983257554 memory delta: 16.57MB
2025-07-06 10:29 | ERROR    | MonitorContext.__exit__: L2_LASSO_FILTER_1751768983257554 failed with error: None
    📍 file:///D:/myquant/XentZ/common/cls_base.py:311
    📚 调用栈:
      1. run_l2_lasso_filter_pipeline() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L2总体重要筛选.py:515
      2. <module>() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L2总体重要筛选.py:714

2025-07-06 12:13 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-06 12:13 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-06 12:13 | INFO     | DatabaseReset.__init__: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-07-06 12:13 | INFO     | DatabaseReset.__init__: Schema文件路径: d:\myquant\XentZ\script\数据_因子库脚本\schema.sql
2025-07-06 12:13 | INFO     | DatabaseReset.run_reset: ============================================================
2025-07-06 12:13 | INFO     | DatabaseReset.run_reset: 开始执行 XentZ 因子动物园数据库重置
2025-07-06 12:13 | INFO     | DatabaseReset.run_reset: ============================================================
2025-07-06 12:13 | INFO     | DatabaseReset.validate_files: 步骤1: 验证文件存在性...
2025-07-06 12:13 | INFO     | DatabaseReset.validate_files: Schema文件存在: d:\myquant\XentZ\script\数据_因子库脚本\schema.sql
2025-07-06 12:13 | INFO     | DatabaseReset.backup_database: 步骤2: 备份现有数据库...
2025-07-06 12:13 | INFO     | DatabaseReset.backup_database: 数据库已备份到: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250706_121315
2025-07-06 12:13 | INFO     | DatabaseReset.get_existing_tables: 步骤3: 获取现有表列表...
2025-07-06 12:13 | INFO     | DatabaseReset.get_existing_tables: 发现现有表 (11个): factor_batches, factor_categories, factor_evaluations, factor_performance_logs, factor_pool_members, factor_pools, factor_relationships, factor_tags, factor_values, factors, universes
2025-07-06 12:13 | INFO     | DatabaseReset.drop_all_tables: 步骤4: 删除现有表 (11个)...
2025-07-06 12:13 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_batches
2025-07-06 12:13 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_categories
2025-07-06 12:13 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_evaluations
2025-07-06 12:13 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_performance_logs
2025-07-06 12:13 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_pool_members
2025-07-06 12:13 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_pools
2025-07-06 12:13 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_relationships
2025-07-06 12:13 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_tags
2025-07-06 12:13 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_values
2025-07-06 12:13 | INFO     | DatabaseReset.drop_all_tables:   删除表: factors
2025-07-06 12:13 | INFO     | DatabaseReset.drop_all_tables:   删除表: universes
2025-07-06 12:13 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_active_factors
2025-07-06 12:13 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_batch_statistics
2025-07-06 12:13 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_factor_performance_summary
2025-07-06 12:13 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_performance_stats
2025-07-06 12:13 | INFO     | DatabaseReset.drop_all_tables: 成功删除 11 个表, 4 个视图, 0 个索引
2025-07-06 12:13 | INFO     | DatabaseReset.execute_schema: 步骤5: 执行schema.sql重建表结构...
2025-07-06 12:13 | INFO     | DatabaseReset.execute_schema: 读取schema文件，长度: 26533 字符
2025-07-06 12:13 | INFO     | DatabaseReset.execute_schema: Schema执行完成
2025-07-06 12:13 | INFO     | DatabaseReset.verify_tables: 步骤6: 验证表创建结果...
2025-07-06 12:13 | INFO     | DatabaseReset.verify_tables: 创建完成统计:
2025-07-06 12:13 | INFO     | DatabaseReset.verify_tables:   - 表 (11个): factor_batches, factor_categories, factor_evaluations, factor_performance_logs, factor_pool_members, factor_pools, factor_relationships, factor_tags, factor_values, factors, universes
2025-07-06 12:13 | INFO     | DatabaseReset.verify_tables:   - 视图 (4个): v_active_factors, v_batch_statistics, v_factor_performance_summary, v_performance_stats
2025-07-06 12:13 | INFO     | DatabaseReset.verify_tables:   - 索引 (32个): idx_evaluations_factor_id, idx_evaluations_method, idx_evaluations_period, idx_evaluations_sharpe, idx_factor_performance_logs_batch_id, idx_factor_performance_logs_memory, idx_factor_performance_logs_operation_type, idx_factor_performance_logs_start_time, idx_factor_performance_logs_total_time, idx_factor_tags_tag_name, idx_factor_tags_tag_type, idx_factor_values_date, idx_factor_values_factor_id, idx_factors_batch_id, idx_factors_computation_time, idx_factors_creation_date, idx_factors_creation_method, idx_factors_factor_type, idx_factors_generation_tool, idx_factors_market_type, idx_factors_pipeline_mode, idx_factors_pipeline_step, idx_factors_primary_category, idx_factors_resource_intensity, idx_factors_status, idx_factors_target_label, idx_factors_unique_full_context, idx_pool_members_factor_id, idx_pool_members_pool_id, idx_pool_members_status, idx_universes_current, idx_universes_effective_date
2025-07-06 12:13 | INFO     | DatabaseReset.verify_tables: 所有核心表创建成功
2025-07-06 12:13 | INFO     | DatabaseReset.insert_default_data: 步骤7: 插入默认数据...
2025-07-06 12:13 | INFO     | DatabaseReset.insert_default_data: 默认数据插入完成
2025-07-06 12:13 | INFO     | DatabaseReset.insert_default_data:   - 因子分类: 10 个
2025-07-06 12:13 | INFO     | DatabaseReset.insert_default_data:   - 股票池: 2 个
2025-07-06 12:13 | INFO     | DatabaseReset.run_reset: ============================================================
2025-07-06 12:13 | INFO     | DatabaseReset.run_reset: 数据库重置完成！
2025-07-06 12:13 | INFO     | DatabaseReset.run_reset: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-07-06 12:13 | INFO     | DatabaseReset.run_reset: 备份路径: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250706_121315
2025-07-06 12:13 | INFO     | DatabaseReset.run_reset: 创建统计: 11表 + 4视图 + 32索引
2025-07-06 12:13 | INFO     | DatabaseReset.run_reset: ============================================================
2025-07-06 12:13 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-06 12:13 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-06 12:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 12:13 | INFO     | BaseObj.<module>: 🚀 开始因子挖掘流程...全局批次ID: GP_MULTI_20250706_L0_716576...
2025-07-06 12:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 12:13 | INFO     | BaseObj.<module>: STEP01: 加载特征数据...
2025-07-06 12:13 | WARNING  | BaseObj.<module>: ⚠️  特征归一化模型为robust, 挖掘模型为linear, 将先norm特征, 再挖掘
2025-07-06 12:13 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-07-06 12:13 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-07-06 12:13 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-07-06 12:13 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-07-06 12:13 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-07-06 12:13 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-07-06 12:13 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-07-06 12:13 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-07-06 12:13 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(10): 10个
2025-07-06 12:13 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 10 列
2025-07-06 12:13 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-07-06 12:13 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-07-06 12:13 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 16 (基础5+归一化0+保持0+标签1+筛选后特征10)
2025-07-06 12:13 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 16
2025-07-06 12:13 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-07-06 12:13 | INFO     | BaseObj.<module>: STEP02: 延迟1个BAR=True, T期=1, 1 个预测目标: ['label_1']
2025-07-06 12:13 | INFO     | BaseObj.<module>: STEP03: 手续费=0.002, 无风险=0.03, 并行核数=27, 挖掘轮次=1
2025-07-06 12:13 | INFO     | BaseObj.<module>: STEP04: 挖掘品种: SH510050 | 目标: label_1
2025-07-06 12:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 12:13 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-07-06 12:14 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 10.99 seconds (00:00:10)
2025-07-06 12:14 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 6.39s user, 0.39s system
2025-07-06 12:14 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 6.73MB
2025-07-06 12:14 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-07-06 12:14 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 12:14 | INFO     | FactorLoader.get_fct_df: 开始计算11个因子表达式
2025-07-06 12:14 | INFO     | FactorLoader.get_fct_df: 返回结果: 11个指定因子
2025-07-06 12:14 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-06 12:14 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-07-06 12:14 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 0.66MB
2025-07-06 12:14 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-07-06 12:14 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 12:14 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-07-06 12:14 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-07-06 12:14 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 0.04MB
2025-07-06 12:14 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-07-06 12:14 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 12:14 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.00 seconds (00:00:00)
2025-07-06 12:14 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.02s user, 0.00s system
2025-07-06 12:14 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: 0.00MB
2025-07-06 12:14 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-07-06 12:14 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 12:14 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 8.61 seconds (00:00:08)
2025-07-06 12:14 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 7.50s user, 0.42s system
2025-07-06 12:14 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 96.36MB
2025-07-06 12:14 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-07-06 12:14 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 12:14 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-06 12:14 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-06 12:14 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 0.01 seconds (00:00:00)
2025-07-06 12:14 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 0.00s user, 0.00s system
2025-07-06 12:14 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.07MB
2025-07-06 12:14 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-07-06 12:14 | INFO     | BaseObj.<module>: STEP05: 结果统计并保持csv...
2025-07-06 12:14 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 12:14 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-07-06 12:14 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-07-06 12:14 | INFO     | ResMonitor.end_timer: CALC_F_VALUES_SH510050 took 0.00 seconds (00:00:00)
2025-07-06 12:14 | INFO     | ResMonitor.end_timer_cpu: CALC_F_VALUES_SH510050 CPU usage: 0.00s user, 0.00s system
2025-07-06 12:14 | INFO     | ResMonitor.end_memory_monitor: CALC_F_VALUES_SH510050 memory delta: 0.38MB
2025-07-06 12:14 | INFO     | FactorMonitorContext.__exit__: CALC_F_VALUES_SH510050 completed successfully
2025-07-06 12:14 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 12:14 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-07-06 12:14 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-07-06 12:14 | INFO     | ResMonitor.end_timer: CALC_PJ_VALUES_SH510050 took 0.00 seconds (00:00:00)
2025-07-06 12:14 | INFO     | ResMonitor.end_timer_cpu: CALC_PJ_VALUES_SH510050 CPU usage: 0.00s user, 0.00s system
2025-07-06 12:14 | INFO     | ResMonitor.end_memory_monitor: CALC_PJ_VALUES_SH510050 memory delta: 0.02MB
2025-07-06 12:14 | INFO     | FactorMonitorContext.__exit__: CALC_PJ_VALUES_SH510050 completed successfully
2025-07-06 12:14 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 12:14 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_SH510050_20250706_L0_716576
2025-07-06 12:14 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_SH510050_20250706_L0_716576 耗时42.8ms
2025-07-06 12:14 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.04 seconds (00:00:00)
2025-07-06 12:14 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.02s user, 0.00s system
2025-07-06 12:14 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 6.57MB
2025-07-06 12:14 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-07-06 12:14 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 12:14 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250706_L0_716576_label_1_001 took 0.03 seconds (00:00:00)
2025-07-06 12:14 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250706_L0_716576_label_1_001 CPU usage: 0.02s user, 0.00s system
2025-07-06 12:14 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250706_L0_716576_label_1_001 memory delta: 0.02MB
2025-07-06 12:14 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250706_L0_716576_label_1_001 completed successfully
2025-07-06 12:14 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-06 12:14 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250706_L0_PJ_716576_label_1_001 took 0.03 seconds (00:00:00)
2025-07-06 12:14 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250706_L0_PJ_716576_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-07-06 12:14 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250706_L0_PJ_716576_label_1_001 memory delta: 0.01MB
2025-07-06 12:14 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250706_L0_PJ_716576_label_1_001 completed successfully
2025-07-06 12:14 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250706_L0_716576 took 20.46 seconds (00:00:20)
2025-07-06 12:14 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250706_L0_716576 CPU usage: 14.05s user, 0.86s system
2025-07-06 12:14 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250706_L0_716576 memory delta: 116.71MB
2025-07-06 12:14 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250706_L0_716576 completed successfully
2025-07-06 12:14 | INFO     | ResMonitor.end_timer: global_process took 20.46 seconds (00:00:20)
