
#!/usr/bin/env python
# -*- coding:utf-8 -*-

from hikyuu import *

author = "admin"
version = "20250418"

class MM_ETF_FixedPercent(MoneyManagerBase):
    """
    ETF专用固定百分比风险模型，增加最大交易数量限制
    参数：
        p: 每笔交易占总资产的百分比，如0.02表示总资产的2%
        max_trade_number: 最大交易数量，默认None表示使用股票默认值
        min_unit: ETF最小交易单位，默认100
    """
    
    def __init__(self, p=0.02, max_trade_number=None, min_unit=100):
        super(MM_ETF_FixedPercent, self).__init__("MM_ETF_FixedPercent")
        self.set_param("p", p)  # 设置资金使用比例参数
        self.max_trade_number = max_trade_number
        self.min_unit = min_unit
        
    def _get_buy_num(self, datetime, stock, price, risk, part_from):
        """
        获取指定交易对象可买入的数量
        :param Datetime datetime: 交易时间
        :param Stock stock: 交易对象
        :param float price: 交易价格
        :param float risk: 每单位风险价格
        :param SystemPart part_from: 来源系统组件
        :return: 可买入数量
        """
        # 获取资金使用比例参数
        p = self.get_param("p")
        
        # 计算基础购买数量：现金 * 使用比例 / 风险
        cash = self.tm.cash(datetime, self.query.ktype)
        number = cash * p / risk
        
        # 调整为最小交易单位的整数倍
        number = (number // self.min_unit) * self.min_unit
        
        # 获取最大可交易数量并进行限制
        max_num = self.max_trade_number if self.max_trade_number is not None else stock.max_trade_number
        # 确保最大交易数量也是最小交易单位的整数倍
        max_num = (max_num // self.min_unit) * self.min_unit
        
        if number > max_num:
            number = max_num*0.9
    
        return number  # 返回值已经是最小交易单位的整数倍
        
    def _clone(self):
        """
        克隆操作
        """
        return MM_ETF_FixedPercent(
            p=self.get_param("p"),
            max_trade_number=self.max_trade_number,
            min_unit=self.min_unit
        )

def part(p=0.02, max_trade_number=None, min_unit=100) -> MoneyManagerBase:
    """
    创建ETF专用固定比例资金管理策略
    
    :param float p: 使用资金比例，如0.02表示使用2%的资金
    :param max_trade_number: 最大交易数量，默认None表示使用股票默认值
    :param min_unit: ETF最小交易单位，默认100
    :return: ETF资金管理策略实例
    """
    return MM_ETF_FixedPercent(p, max_trade_number, min_unit)
    

if __name__ == "__main__":
    # 执行 testall 命令时，会多传入一个参数，防止测试时间过长
    # 比如如果在测试代码中执行了绘图操作，可以打开下面的注释代码
    # 此时执行 testall 命令时，将直接返回
    if len(sys.argv) > 1:
        ind = part()
        print(ind)
        exit(0)

    import sys
    if sys.platform == 'win32':
        import os
        os.system('chcp 65001')

    # 仅加载测试需要的数据，请根据需要修改
    options = {
        'stock_list': ['sz000001'],
        'ktype_list': ['day'],
        'preload_num': {'day_max': 100000},
        'load_history_finance': False,
        'load_weight': False,
        'start_spot': False,
        'spot_worker_num': 1,
    }
    load_hikyuu(**options)
    
    stks = tuple([sm[code] for code in options['stock_list']])
    
    # 请在下方编写测试代码
    ind = part()
    print(ind)
    
    # 显示图形
    import matplotlib.pylab as plt
    plt.show()    
