from pathlib import Path
import pytz
import platform
system = platform.system()

if system == 'Windows':
    WORKDIR = Path(__file__).parent
else:
    WORKDIR = Path('/home/<USER>/myquant/btq_jzal')
    
SETTING_TZ = pytz.timezone('Asia/Shanghai')

CONFIG_DIR = WORKDIR.joinpath("config")

DATA_DIR = WORKDIR.joinpath("data")
DATA_DIR_CSVS = DATA_DIR.joinpath('csvs')
DATA_DIR_FEATHER = DATA_DIR.joinpath('feathers')
DATA_DIR_FACTOR = DATA_DIR.joinpath('factors')
DATA_DIR_MODEL = DATA_DIR.joinpath('models')  # for on prodcution, read from models
DATA_DIR_SLZ = DATA_DIR.joinpath('slz_demo')
DATA_DIR_OTHER = DATA_DIR.joinpath('others')

DB_DIR_LIVE = WORKDIR.joinpath('live_trade').joinpath('dbs') # 实时交易数据库
DB_DIR_FACTOR = WORKDIR.joinpath('datastore').joinpath('dbs') # 因子库 和 因子值数据文件

dirs = [CONFIG_DIR, DATA_DIR, DATA_DIR_CSVS, DATA_DIR_FEATHER, DATA_DIR_FACTOR, 
        DATA_DIR_MODEL, DATA_DIR_SLZ, DATA_DIR_OTHER, DB_DIR_LIVE, DB_DIR_FACTOR]


for dir in dirs:
    dir.mkdir(exist_ok=True, parents=True)


def Singleton(cls):
    _instance = {}

    def _singleton(*args, **kwagrs):
        if cls not in _instance:
            _instance[cls] = cls(*args, **kwagrs)
        return _instance[cls]

    return _singleton

if __name__ == '__main__':
    pass