import subprocess
import sys
import os
import time

# 可以直接修改这
default_url = "https://mp.weixin.qq.com/s/uySG1mER1vHitV-k7mSsSg"

def install_if_missing(package):
    """静默安装缺失的包"""
    try:
        __import__(package.replace('-', '_'))
    except ImportError:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package], 
                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

# 安装依赖
for pkg in ['selenium', 'pillow', 'webdriver-manager']:
    install_if_missing(pkg)

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from PIL import Image
from urllib.parse import urlparse
import hashlib

def url_to_ultra_png(url, output_file=None, page_height=1080):
    """
    直接从URL截取Ultra高清PNG图片，自动分页保存
    
    Args:
        url: 网页链接（如微信公众号文章）
        output_file: 输出文件名前缀（可选）
        page_height: 每页高度像素，默认1080（标准屏幕高度）
    """
    
    if not output_file:
        # 基于URL生成唯一文件名
        url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
        output_file = f"ultra_screenshot_{url_hash}"
    else:
        # 移除扩展名
        output_file = os.path.splitext(output_file)[0]
    
    print("🚀 开始Ultra高清分页截图...")
    
    # Chrome选项配置（保持原有配置）
    options = Options()
    options.add_argument('--headless')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--window-size=1920,1080')
    options.add_argument('--force-device-scale-factor=2')
    options.add_argument('--high-dpi-support=1')
    options.add_argument('--device-scale-factor=2')
    
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
    except Exception as e:
        print(f"⚠️  使用ChromeDriverManager失败，尝试系统Chrome: {e}")
        driver = webdriver.Chrome(options=options)
    
    try:
        print(f"📖 正在加载: {url}")
        driver.get(url)
        time.sleep(3)
        
        # 滚动到底部确保全部内容加载
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(2)
        driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(1)
        
        # 获取完整页面高度
        total_height = driver.execute_script("return Math.max( document.body.scrollHeight, document.body.offsetHeight, document.documentElement.clientHeight, document.documentElement.scrollHeight, document.documentElement.offsetHeight );")
        
        # 计算页数
        pages = (total_height + page_height - 1) // page_height  # 向上取整
        print(f"📄 总高度: {total_height}px, 将分为 {pages} 页截图")
        
        saved_files = []
        
        # 分页截图
        for page in range(pages):
            # 设置窗口尺寸为当前页大小
            current_height = min(page_height, total_height - page * page_height)
            driver.set_window_size(1920, current_height)
            
            # 滚动到当前页位置
            scroll_y = page * page_height
            driver.execute_script(f"window.scrollTo(0, {scroll_y});")
            time.sleep(1)
            
            # 截图文件名
            if pages == 1:
                filename = f"{output_file}.png"
            else:
                filename = f"{output_file}_page{page + 1}.png"
            
            print(f"📸 正在截图第 {page + 1}/{pages} 页...")
            driver.save_screenshot(filename)
            saved_files.append(filename)
            
            # 显示页面信息
            file_size = os.path.getsize(filename) / 1024 / 1024
            with Image.open(filename) as img:
                width, height = img.size
                print(f"   ✅ {filename}: {width}x{height}px, {file_size:.1f}MB")
        
        print(f"🎯 所有截图完成，共保存 {len(saved_files)} 个文件")
        return saved_files
        
    except Exception as e:
        print(f"❌ 截图失败: {e}")
        return None
        
    finally:
        driver.quit()

# 主函数保持不变，只需更新返回值处理
def main():
    """主函数 - 支持命令行参数或直接修改URL"""
    
    if len(sys.argv) > 1:
        url = sys.argv[1]
    else:
        url = default_url
    
    url = url.lstrip('@').strip()
    
    # 执行分页截图
    results = url_to_ultra_png(url)
    
    if results:
        print(f"📋 截图文件已保存到当前目录：")
        for file in results:
            print(f"   • {os.path.abspath(file)}")
        print("🤖 现在可以逐页发送给AI进行识别！")
    else:
        print("❌ 截图失败")

if __name__ == "__main__":
    main()