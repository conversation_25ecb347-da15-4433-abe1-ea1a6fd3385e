"""自动化"""

import time
from pathlib import Path

import cv2
import numpy as np
import pytesseract
import pywinauto
from falcon.feather.config import config
from falcon.feather.logger import logger

# "D:/Program Files/Tesseract-OCR/tesseract.exe"
pytesseract.pytesseract.tesseract_cmd = config["tesseract"]["exe"]


class Automate:
    """自动化"""

    def __init__(self):
        # D:/QMT/bin.x64/XtMiniQmt.exe"
        self.qmt_exe = config["qmt"]["exe"]
        self.user = config["qmt"]["user"]
        self.password = config["qmt"]["password"]

    def start(self, restart):
        """启动"""
        app = self.is_running()
        if app:
            if restart:
                app.kill(True)
                self.clean()
                time.sleep(5)
            else:
                return

        while True:
            try:
                self.login()
                return
            except Exception as e:
                logger.info("异常重试: %s", e)
                time.sleep(1)
                continue

    def login(self):
        """登录"""
        app = pywinauto.Application().start(self.qmt_exe)
        app = pywinauto.Application("uia").connect(path=self.qmt_exe, timeout=10)
        if self.is_logged_in(app):
            logger.info("已登录")
            return

        win = app.top_window()
        win.set_focus()
        win.ComboBox.Edit.click_input()
        win.ComboBox.Edit.type_keys("^a" + self.user)
        win.Edit2.click_input()
        win.Edit2.type_keys(self.password)

        while True:
            captcha = win.Custom3.capture_as_image()
            code = self.ocr(captcha)
            if not code:
                win.Custom3.click_input()
                time.sleep(1)
                continue
            win.Edit3.click_input()
            win.Edit3.type_keys("^a" + code)
            win.Button3.click()
            time.sleep(1)
            if self.is_logged_in(app):
                logger.info("登录成功")
                return
            time.sleep(1)

    def is_running(self):
        """是否运行"""
        try:
            return pywinauto.Application("uia").connect(path=self.qmt_exe, timeout=1)
        except pywinauto.application.ProcessNotFoundError:
            return None

    def is_logged_in(self, app):
        """是否已登录"""
        text = app.top_window().window_text()
        if not text:
            return False
        logger.info(text)
        return self.user in text

    def clean(self):
        """清空无用文件"""
        for f in Path(config["qmt"]["data_dir"]).glob("down_queue_*"):
            if "xtmodel" not in f.name:
                logger.info("删除: %s", f.name)
                try:
                    f.unlink()
                except PermissionError:
                    pass

    def ocr(self, image):
        """验证码识别"""
        image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        _, image = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY_INV | cv2.THRESH_OTSU)
        coords = np.column_stack(np.where(image > 0))
        angle = cv2.minAreaRect(coords)[-1]
        angle = -(angle + 90) if angle < -45 else -angle
        w, h = image.shape[0:2]
        center = (w // 2, h // 2)
        wrapper = cv2.getRotationMatrix2D(center, angle, 1.0)
        image = cv2.warpAffine(image, wrapper, (h, w))
        code = pytesseract.image_to_string(image)
        code = "".join([c for c in code if c in "0123456789+-*/"])
        if not code or not any([c in code for c in "+-*/"]):
            return ""
        logger.info("验证码: %s", code)
        try:
            # pylint: disable-next=eval-used
            result = str(eval(code))
            logger.info("结果: %s", result)
            return result
        except Exception:
            return ""
