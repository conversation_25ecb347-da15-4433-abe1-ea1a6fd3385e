from abc import ABC, abstractmethod
from sklearnex import patch_sklearn # intel加速
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from lightgbm import LGBMRegressor
from xgboost import XGBRegressor
import joblib

patch_sklearn() # intel加速

class ModelStrategy(ABC):
    def __init__(self) -> None:
        self.model = None
        
    @abstractmethod
    def fit_model(self, X_train, y_train):
        pass

    @abstractmethod
    def predict(self, X):
        pass
    
    def save(self, pathname):
        joblib.dump(self.model, pathname)

    def load(self, pathname):
        return joblib.load(pathname) 

class ModelFactory:
    """模型工厂"""
    _models = {
        'linear': lambda **kwargs: LinearRegressionModel(**kwargs),
        'rf': lambda **kwargs: RandomForestModel(**kwargs),
        'lgb': lambda **kwargs: LightGBMModel(**kwargs),
        'xgb': lambda **kwargs: XGBoostModel(**kwargs)
    }
    
    @classmethod
    def create(cls, model_type: str = 'linear', **kwargs):
        """创建模型实例"""
        creator = cls._models.get(model_type.lower())
        if creator is None:
            print(f"警告: 未知模型类型 '{model_type}'，使用默认线性回归模型")
            creator = cls._models['linear']
        return creator(**kwargs)

class LinearRegressionModel(ModelStrategy):
    def __init__(self, fit_intercept=True):
        self.model = LinearRegression(fit_intercept=fit_intercept)
        
    def __str__(self):
        return "LinearRegression"

    def fit_model(self, X_train, y_train):
        self.model.fit(X_train, y_train)

    def predict(self, X):
        return self.model.predict(X)   

class RandomForestModel(ModelStrategy):
    def __init__(self, **kwargs):
        self.model = RandomForestRegressor(**kwargs)
        
    def __str__(self):
        return "RandomForest"
        
    def fit_model(self, X_train, y_train):
        self.model.fit(X_train, y_train)
        
    def predict(self, X):
        return self.model.predict(X)

class LightGBMModel(ModelStrategy):
    def __init__(self, **kwargs):
        self.model = LGBMRegressor(**kwargs)
        
    def __str__(self):
        return "LightGBM"
        
    def fit_model(self, X_train, y_train):
        self.model.fit(X_train, y_train)
        
    def predict(self, X):
        return self.model.predict(X)

class XGBoostModel(ModelStrategy):
    def __init__(self, **kwargs):
        self.model = XGBRegressor(**kwargs)
        
    def __str__(self):
        return "XGBoost"
        
    def fit_model(self, X_train, y_train):
        self.model.fit(X_train, y_train)
        
    def predict(self, X):
        return self.model.predict(X)

# 类似地，可以定义其他模型的策略类，如SVMStrategy, RandomForestStrategy等