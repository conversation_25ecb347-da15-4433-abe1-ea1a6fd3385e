import numpy as np
import random
import pandas as pd
from collections import defaultdict
from deap import base, creator, tools, algorithms

from jzal_pro.utils.perf_utils import calc_sr
from .combo_mgr import ComboGenerator
from jzal_pro.utils import calc_stats_RC, stats_saw_score, calc_sr_RC
from ..modelers.fcts_modeler import FactorsModeler
from enums import METRIC_PARAMS_DEFAULT, MetricParams

class GAComboGenerator(ComboGenerator):
    ''' 遗传算法解决特征选择, 用deap库 '''
    def __init__(self, modeler: FactorsModeler):
        super().__init__()
        self.modeler = modeler
        
    def evaluate(self, individual, big_df: pd.DataFrame, ret_df: pd.DataFrame, params: MetricParams):
        '''
            df: 本次GA全部搜索空间的因子值大宽表, 不含ret或open_ret列
            ret_df: ret或open_ret列的df
            individual: 本次GA的个体
        '''
        day_bars, ann_days, fixed_return, fee_rate, weights, is_ret_open = params
        
        # 获取本次GA组合中的因子
        selected_factors = [factor for factor, selected in zip(big_df.columns, individual) if selected]
        if not selected_factors: # 如果没有选中任何因子，返回极低的适应度
            return -np.inf,
        selected_df = big_df[selected_factors] # 提取相关因子的值
        combined_df = pd.concat([selected_df, ret_df], axis=1)
        # try1: pure装配
        # bar_pct_ret, bar_pct_cost = self.modeler.pure_ensemble(combined_df, 
        #                                                        fee_rate=fee_rate, is_ret_open=is_ret_open)
        # try2: 滚动装配
        bar_pct_ret, bar_pct_cost = self.modeler.wfa_ensemble(combined_df, step_bars=1000, 
                                                               fee_rate=fee_rate, is_ret_open=is_ret_open)
        # try1: saw评分 -- 不够直观 
        # df_ratios = calc_stats_RC(bar_pct_ret, bar_pct_cost, day_bars, ann_days, fixed_return) # 计算组合的绩效指标
        # df_ratios_with_score = stats_saw_score(df_ratios, weights) # 计算组合的评分
        # return df_ratios_with_score['saw_score'].iloc[0], # 只认tuple
        
        # try2: 只看sr -- 暂时与gp统一
        df_sr = calc_sr_RC(bar_pct_ret, bar_pct_cost, day_bars, ann_days, fixed_return)
        return df_sr['sr'].iloc[0], # 只认tuple, 要加','
    
    def generate(self, big_df: pd.DataFrame, ret_df: pd.DataFrame, params: MetricParams) -> list:
        '''
            big_df : 本次GA初始大宽表
        '''
        POP_SIZE = 100
        NGEN = 50 
        CXPB = 0.8
        MUTPB = 0.1
        TOURNSIZE = 3
        SEED = 42
        
        random.seed(SEED)
        np.random.seed(SEED)
        # 创建适应度和个体类
        ouid = self.gen_ordered_uid()
        creator.create("FitnessMax"+ouid, base.Fitness, weights=(1.0,))
        creator.create("Individual"+ouid, list, fitness=getattr(creator, "FitnessMax" + ouid))
        # 初始化工具箱
        toolbox = base.Toolbox()
        # 注册创建个体的方法
        print(len(big_df.columns))
        toolbox.register("attr_bool", random.randint, 0, 1) # 0 1 基因序列
        toolbox.register("individual", tools.initRepeat, getattr(creator, "Individual" + ouid), toolbox.attr_bool, n=len(big_df.columns))
        # 注册种群创建方法
        toolbox.register("population", tools.initRepeat, list, toolbox.individual)
        # 注册适应度评估方法
        toolbox.register("evaluate", self.evaluate, big_df=big_df, ret_df=ret_df, params=params)
        # 注册交叉操作
        toolbox.register("mate", tools.cxTwoPoint)
        # 注册简单变异操作
        toolbox.register("mutate", tools.mutFlipBit, indpb=0.005) # (for mutFlipBit内部): 0.001 至 0.01
        
        # 注册选择操作（繁殖）
        toolbox.register("select", tools.selTournament, tournsize=TOURNSIZE)

        # 创建种群
        pop = toolbox.population(n=POP_SIZE)  # 最后一代也是这个数字

        # 统计信息
        stats = tools.Statistics(lambda ind: ind.fitness.values)
        stats.register("avg", np.mean)
        stats.register("std", np.std)
        stats.register("min", np.min)
        stats.register("max", np.max)

        # 运行遗传算法
        pop, logbook = algorithms.eaSimple(pop, toolbox, cxpb=CXPB, mutpb=MUTPB, 
                                           ngen=NGEN, stats=stats, verbose=True)

        # 输出结果
        pop_sorted = sorted(pop, key=lambda ind: ind.fitness.values, reverse=True)
        expr_list = list(big_df.columns)
        
        # 创建一个字典来存储已见过的特征组合
        seen_combos = defaultdict(int)
        unique_pop = []

        for ind in pop_sorted:
            combo = tuple(self._to_expr_combo(ind, expr_list))
            if seen_combos[combo] == 0:
                seen_combos[combo] += 1
                unique_pop.append(ind)
                
        pop_dict_list = [{'expr_combo': self._to_expr_combo(ind, expr_list), 'fitness': ind.fitness.values[0]} 
                    for ind in unique_pop]
        
        return pop_dict_list
        
    def _to_expr_combo(self, ind, expr_names) -> list:
        expr_combo = [expr for expr, selected in zip(expr_names, ind) if selected]
        return expr_combo
    
    def expr_combo_to_ind(self, expr_combo, full_expr_names) -> list:
        """
        :param expr_combo: List of selected expression names.
        :param expr_names: Full list of all possible expression names.
        :return: Binary indicator list where 1 represents a selected expression and 0 an unselected one.
        """
        ind = [int(expr in expr_combo) for expr in full_expr_names]
        return ind

