
#!/usr/bin/env python
# -*- coding:utf-8 -*-

from hikyuu import *

author = "admin"
version = "20250417"

def part(se: Indicator = CLOSE(), period: int = 25) -> Indicator:
    """
    趋势评分指标：年化收益率 × R平方
    
    趋势评分通过计算价格的线性回归，综合评估趋势的强度和方向。
    指标值越高表示上升趋势越强，值越低表示下降趋势越强，接近0则表示无明显趋势。
    
    计算方法：
    1. 对收盘价取对数，进行线性回归
    2. 计算斜率的年化收益率
    3. 计算线性回归的R平方值
    4. 将年化收益率与R平方相乘得到趋势评分
    
    参数：
        period: 计算窗口长度，默认25天
        
    返回：
        Indicator: 趋势评分指标
    """
    # 获取对数收盘价
    log_c = LN(se)
    
    # 计算线性回归斜率
    slope = SLOPE(log_c, period)
    X_Avg =(period + 1)/2
    Y_Avg = MA(log_c,period)
    # 计算截距
    intercept = Y_Avg - slope * X_Avg
    
    # 创建结果指标
    result = intercept
    result.name = "截距"
    
    return result    

if __name__ == "__main__":
    # 执行 testall 命令时，会多传入一个参数，防止测试时间过长
    # 比如如果在测试代码中执行了绘图操作，可以打开下面的注释代码
    # 此时执行 testall 命令时，将直接返回
    if len(sys.argv) > 1:
        ind = part()
        print(ind)
        exit(0)

    import sys
    if sys.platform == 'win32':
        import os
        os.system('chcp 65001')

    # 仅加载测试需要的数据，请根据需要修改
    options = {
        'stock_list': ['sz000001'],
        'ktype_list': ['day'],
        'preload_num': {'day_max': 100000},
        'load_history_finance': False,
        'load_weight': False,
        'start_spot': False,
        'spot_worker_num': 1,
    }
    load_hikyuu(**options)
    
    stks = tuple([sm[code] for code in options['stock_list']])
    
    # 请在下方编写测试代码
    ind = part()
    print(ind)
    
    # 显示图形
    import matplotlib.pylab as plt
    plt.show()    
