import backtrader as bt
class stampDutyCommInfo(bt.CommInfoBase):
    params = (
        ('stamp_duty',0.005),
        ('percabs', False),  # 百分比值
    )
    def _getcommission(self, size, price, pseudoexec):
        if size > 0:
            return size * price * self.p.commission
        elif size < 0:
            return - size * price * (self.p.stamp_duty + self.p.commission)
        else:
            return 0
        
    def getsize(self, price, cash):
        '''Returns the needed size to meet a cash operation at a given price'''
        if not self._stocklike:
            return int(self.p.leverage * (cash // self.get_margin(price)))

        return int(self.p.leverage * (cash // price) // 100 * 100)