import time
import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
from task_defs.task_fctsmine import TaskFctsMining, Fcts_GP_Mining
from engine.gp_engine import FctsGPEngine
from datafeed.dataloader import Featherloader
from config import CONFIG_DIR, DATA_DIR_FEATHER
from jzal_pro.utils import factor_utils as plt_u

def exprs_checker(task: TaskFctsMining, symbols: list):
    path = DATA_DIR_FEATHER.joinpath(task.data_path).resolve()
    loader = Featherloader(path=path, symbols=symbols, columns=task.columns, 
                            start_date=task.start_date, end_date=task.end_date)
    ''' =================== 采样看算子值本身 ================ '''
    dfs, _ = loader.load_sample_as_norm(fields=task.feature_exprs, 
                                     names=task.feature_names,
                                     n_sample=500)
    df = dfs[0]
    df = df.tail(min(df.shape[0], 500-100)) # consider warmup
    # df[task.feature_names+['close']].to_csv('todo_show_expr.csv')
    plt_u.plot_eq6x4(df[task.feature_names+['close']], 
                       compr_col=['close'], compr_show_cum=False, save_path='0exprs')
    ''' =================== 全量看算子值分布 ================ '''
    dfs, _ = loader.load_as_norm(fields=task.feature_exprs, 
                                 names=task.feature_names, expr_name_excluded=[], # 此处看所有定义的算子, 不用EXPR_NAME_EXCLUDED
                                 to_csv=None, is_plot=False) 
    df = dfs[0]
    plt_u.plot_hist6x4(df[task.feature_names], save_path='0exprs')

def a_expr_checker(task: TaskFctsMining, symbols: list, expr: str):
    path = DATA_DIR_FEATHER.joinpath(task.data_path).resolve()
    loader = Featherloader(path=path, symbols=symbols, columns=task.columns, 
                            start_date=task.start_date, end_date=task.end_date)
    ''' =================== 采样看算子值本身 ================ '''
    dfs, _ = loader.load_sample_as_norm(fields=task.feature_exprs+[expr], 
                                     names=task.feature_names+['a_expr'],
                                     n_sample=500)
    df = dfs[0]
    df = df.tail(min(df.shape[0], 500-100)) # consider warmup
    # df[task.feature_names+['close']].to_csv('todo_show_expr.csv')
    plt_u.plot_eq6x4(df[task.feature_names+['a_expr']+['close']], 
                       compr_col=['close'], compr_show_cum=False, save_path='0Aexpr')
    ''' =================== 全量看算子值分布 ================ '''
    dfs, _ = loader.load_as_norm(fields=task.feature_exprs+[expr], 
                                 names=task.feature_names+['a_expr'], expr_name_excluded=[], # 此处看所有定义的算子, 不用EXPR_NAME_EXCLUDED
                                 to_csv=None, is_plot=False) 
    df = dfs[0]
    plt_u.plot_hist6x4(df[task.feature_names+['a_expr']], save_path='0Aexpr')
    
if __name__ == '__main__':
    start_time = time.time()
    ''' ====================== 读GUI设置的配置文件进行挖掘 =========================== '''
    t = Fcts_GP_Mining()
    config_path = CONFIG_DIR / 'task_mining.toml'
    t.load_config(str(config_path)) # 优先用配置文件的参数设置
    # 算子值检验 -- 单独做 来筛选算子
    # exprs_checker(t,['510050.SH_15']), exit()
    # a_expr_checker(t, ['510050.SH_15'], 'ta_ad(CNTD20, QTLD10, VSUMN_5, JZ004_98)'), exit()
    e = FctsGPEngine(t)
    e.expand_features()  # 加载所有内置特征
    ''' ============= 检查: 特征集/基本配置/NORM参数/GP参数/GP循环/模型类型 =============== '''
    task_uids = e.run()
    ''' ====================== 停下来逐一看看单因子的绩效情况 =========================== '''
    # 半自动
    # task_uids = {'510050.SH_15': '23263b4fac6841b4'}
    # e.L2corr(task_uids, infunc_id='')  # 先corr, 然后停下来看看因子的图形效果(去跳阶的)
    # e.L2corr(task_uids, infunc_id='L2corr')  
    # e.L2Lasso(task_uids, infunc_id='L2corr') # 从L2库中lasso
    # e.L2group(task_uids, infunc_id='L2Lasso')
    # e.L2dive(task_uids, infunc_id='L2Lasso')
    # e.L2dive(task_uids, infunc_id='L2dive')
    ''' ====================== 基于L2因子库进行特征组合的优化 =========================== '''
    # e.opt_combo(task_uids, from_L2 = 'L2dive')
    
    end_time = time.time()
    print('Time cost:=======', end_time - start_time)
    # e.analysis(console=False) 