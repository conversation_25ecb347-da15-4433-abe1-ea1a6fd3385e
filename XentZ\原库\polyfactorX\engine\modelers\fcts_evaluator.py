import numpy as np 
import pandas as pd
import talib as ta
from scipy.stats import pearsonr
import matplotlib.pyplot as plt
import matplotlib.ticker as ticker
import matplotlib.dates as mdates
from sklearnex import patch_sklearn # intel加速
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor

from jzal_pro.cls_common import BaseObj

patch_sklearn() # intel加速

class FactorsEvaluator(BaseObj):
    ''' 支持单品种多因子的同时测评
        因子测评师: (多因子构图时 5个因子开一个图形文件png, 某项指标对应的5个因子的表现叠加显示在各个子图内)
        单品种条件下.. 支持因子 细致评估+必要的图形化
        绩效外其他维度测评
    '''
    def __init__(self, data: pd.DataFrame, prices: pd.DataFrame = None):
        '''
            data: [date], ret/ret_open, fct_col1, fct_col2,...
        '''
        self.data = data
        if prices is not None:
            self.prices = prices # close or open, ic_decay用到
            assert len(self.data) == len(self.prices)
        self._env_init()
        
    def _env_init(self):
        self.X = self.data.iloc[:, 1:].values  # 去掉'ret'列，保留因子值; 用.values因此是独立的numpy数据了, 不影响data
        self.y = self.data.iloc[:, 0].values # 取'ret' 或 'ret_open' 列, 都是第0列, y_raw
        self.fctnames = list(self.data.iloc[:, 1:].columns)  # 存储因子名称
        
    def show_fcteval(self, factors_per_plot=3, save_path=None):
        ''' 测评结果统一图形化: 因子检测报告图 '''
        num_factors = len(self.fctnames)
        num_figures = (num_factors + factors_per_plot - 1) // factors_per_plot
        # 用于存储每个因子的评估结果
        eval_results = {
            'p_value': self.permutation_test(),
            'rolling_ic': self.rolling_ic(window_size=200), # 15min约2周
            'rolling_autocorr': self.rolling_autoic(window_size=200), # 15min约2周
            'factor_response': self.extreme_versus_average_up(),
            'ic_decay': self.ic_decay()
        }
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
        for figure_idx in range(num_figures):
            start_idx = figure_idx * factors_per_plot
            end_idx = min((figure_idx + 1) * factors_per_plot, num_factors)
            fig, axes = plt.subplots(nrows=2, ncols=2, figsize=(16, 16))
            axes = axes.flatten()
            current_factors = self.fctnames[start_idx:end_idx]           
            # 绘制每个评估指标的子图
            for i, metric in enumerate(['p_value', 'rolling_ic', 'rolling_autocorr', 'factor_response', 'ic_decay']):
                if metric == 'rolling_ic':
                    for fctname in current_factors:
                        fct_idx = self.fctnames.index(fctname)
                        rolling_ic = pd.Series(eval_results['rolling_ic'][fctname], index = self.data[fctname].index)
                        axes[0].plot(rolling_ic, label=f'factor{fct_idx+1} IC: {np.mean(rolling_ic.dropna()):.3f}')
                    axes[0].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))  # 显示日期
                    axes[0].tick_params(axis='x', labelsize=8)  # 日期格式和字体大小
                    axes[0].set_yticks(axes[0].get_yticks())  # Set the ticks first
                    axes[0].yaxis.set_major_formatter(ticker.FormatStrFormatter('%.2f'))
                    axes[0].tick_params(axis='y', labelsize=8)
                    axes[0].set_title('滚动IC')
                    axes[0].legend(fontsize=8)
                elif metric == 'rolling_autocorr':
                    for fctname in current_factors:
                        fct_idx = self.fctnames.index(fctname)
                        rolling_autocorr = eval_results['rolling_autocorr'][fctname]
                        axes[1].plot(rolling_autocorr, label=f'factor{fct_idx+1}')
                    axes[1].set_ylim([0.95, 1])
                    axes[1].set_xticks(axes[1].get_xticks())  # Set the ticks first
                    axes[1].tick_params(axis='x', labelsize=8)  
                    axes[1].set_yticks(axes[1].get_yticks())  # Set the ticks first
                    axes[1].yaxis.set_major_formatter(ticker.FormatStrFormatter('%.2f'))
                    axes[1].tick_params(axis='y', labelsize=8)
                    axes[1].set_title('滚动IC自相关')
                    axes[1].legend(fontsize=8)
                elif metric == 'factor_response':
                    bar_width = 0.25
                    x = np.arange(len(current_factors))
                    for j, score_type in enumerate(['upper_score', 'down_score']):
                        scores = [eval_results['factor_response'][fctname][score_type] for fctname in current_factors]
                        axes[2].bar(x + j * bar_width, scores, bar_width, label=score_type)
                    pp = [eval_results['p_value'][fctname] for fctname in current_factors]
                    axes[2].bar(x + 2 * bar_width, pp, bar_width, label='p_value')
                    axes[2].set_xticks(x + bar_width / 2)
                    axes[2].set_xticklabels([f'factor{self.fctnames.index(fctname)+1}' for fctname in current_factors], fontsize=8)
                    axes[2].set_yticks(axes[2].get_yticks())  # Set the ticks first
                    axes[2].yaxis.set_major_formatter(ticker.FormatStrFormatter('%.2f'))
                    axes[2].tick_params(axis='y', labelsize=8)
                    axes[2].set_title('顶层响应 & P-Value', pad=10)
                    axes[2].legend(fontsize=8)  # 调整标签字体大小
                elif metric == 'ic_decay':
                    for fctname in current_factors:
                        fct_idx = self.fctnames.index(fctname) 
                        ic_decay = eval_results['ic_decay'][fctname]
                        axes[3].plot(ic_decay, label=f'factor{fct_idx+1}')
                    axes[3].set_xticks(np.arange(len(ic_decay)))  # 设置正确的x轴刻度
                    axes[3].set_xticklabels([f'{i}' for i in range(len(ic_decay))], fontsize=8)  # 设置x轴标签为数字
                    axes[3].set_yticks(axes[3].get_yticks())  # Set the ticks first
                    axes[3].yaxis.set_major_formatter(ticker.FormatStrFormatter('%.2f'))
                    axes[3].tick_params(axis='y', labelsize=8)
                    axes[3].set_title('IC衰减', pad = 10)
                    axes[3].legend(fontsize=8)
            # 保存图片(按组)
            if save_path is not None:
                plt.savefig(str(save_path) + '_eval({}-{}).png'.format(start_idx + 1, end_idx))
            

        # 添加绘制重要性条形图的大图
        sorted_idx, sorted_importances = self.random_forest()
        fig, ax = plt.subplots(figsize=(10, 8))
        y_pos = np.arange(len(sorted_idx))
        
        ax.barh(y_pos, sorted_importances, align='center')
        ax.set_yticks(y_pos)
        ax.set_yticklabels([f'factor{idx+1}' for idx in sorted_idx])
        ax.invert_yaxis()  # 从上到下按重要性递减
        ax.set_xlabel('Importance')
        ax.set_title('Feature Importance')
        
        # 在每个条形上添加重要性值
        for i in range(len(sorted_idx)):
            ax.text(sorted_importances[i], y_pos[i], f'{sorted_importances[i]:.4f}', 
                    va='center', ha='left', fontsize=8)
        
        plt.tight_layout()
        
        if save_path is None:
            plt.show()
        else:
            plt.savefig(save_path + '_importance.png')
        
    def permutation_test(self, n_permutations=100) -> dict:
        """执行置换检验以评估x和y之间的关系是否显著。
        p_value (float): p_value,the smaller,the better
        -----------------------------------------"""
        p_values = {}
        for fctname in self.fctnames:
            original_corr = pearsonr(self.data[fctname], self.y)[0]  # 计算原始数据的相关系数
            permuted_corrs = np.array([pearsonr(np.random.permutation(self.data[fctname]), self.y)[0] 
                                       for _ in range(n_permutations)])
            p_value = np.mean(np.abs(permuted_corrs) >= np.abs(original_corr)).astype(float)
            p_values[fctname] = p_value
        return p_values # {'ts_sma_21(ta_beta_20(b_rsi_25, ts_sum_17(CNTN30)))': 0.02, 'ts_sqrt1(bb_up_80)': 0.02}

    def rolling_ic(self, window_size=10) -> dict:
        rolling_ics = {}
        for fctname in self.fctnames:
            rolling_ics[fctname] = ta.CORREL(self.data[fctname].values, self.y, window_size)
        return rolling_ics
    
    def rolling_autoic(self, window_size=10) -> dict:  # 性能提升10倍
        rolling_autocorrs = {}
        res = []
        for fctname in self.fctnames:
            arry = self.data[fctname].values
            if len(arry) < window_size:
                window_size = len(arry)
            result = np.full(len(arry), np.nan)
            # 使用 sliding_window_view 创建滑动窗口
            windows = np.lib.stride_tricks.sliding_window_view(arry, window_shape=window_size)
            # 计算每个窗口的自相关系数
            window_means = np.nanmean(windows, axis=1)
            window_centered = windows - window_means[:, np.newaxis]
            autocorr = np.apply_along_axis(lambda x: np.correlate(x, x, mode='full'), 1, window_centered)
            autocorr = autocorr[:, len(autocorr[0]) // 2:]
            autocorrs = autocorr[:, 1] / (autocorr[:, 0] -1)
            # 填充结果
            result[window_size-1:] = autocorrs
            se = pd.Series(result, index=self.data[fctname].index)
            se.name = fctname
            res.append(se)
        rolling_autocorrs = pd.concat(res, axis=1)
        
        return rolling_autocorrs
    
    def extreme_versus_average_up(self, thresh_pct=0.1) -> dict:
        '''
        -------------------------------------核心思想----------------------------------------------------
        顶层响应:反映因子值最高的n%数据点相对其他数据点的平均收益提升，越高越好
        ------------------------------------------------------------------------------------------------
        '''
        res = {}
        for fctname in self.fctnames:
            Xi = self.data[fctname].values
            thresh = int(np.round(thresh_pct * len(Xi)))
            factor_sort = np.argsort(Xi)
            benchmark = Xi[thresh:-thresh].mean()
            res[fctname] = {
                'upper_score': self.y[factor_sort[-thresh:]].mean() - benchmark,
                'down_score': self.y[factor_sort[:thresh]].mean() - benchmark
            }
        return res
    
    def ic_decay(self, space=range(1, 100, 2)) -> dict:
        '''
        -------------------------------------核心思想-------------------------------------------------------
        检验因子的预测能力在时序上的衰减情况
        ---------------------------------------------------------------------------------------------------
        df:DataFrame,行是sample,列是因子值和收盘价close
        factor_name:str,因子名
        '''
        ic_decay_results = {}
        for fctname in self.fctnames:
            ic_list = []
            for i in space:
                future_returns = np.log(self.prices).shift(-i) - np.log(self.prices)
                valid_returns = future_returns.dropna()
                
                common_index = valid_returns.index.intersection(self.data[fctname].index)
                aligned_factor = self.data[fctname].loc[common_index]
                valid_returns_aligned = valid_returns.loc[common_index]
                aligned_factor.replace([np.inf, -np.inf, np.nan], 0, inplace=True)
                valid_returns_aligned.replace([np.inf, -np.inf, np.nan], 0, inplace=True)
                # 检查是否还有非数值类型的元素
                valid_returns_aligned_se = valid_returns_aligned['close']
                assert aligned_factor.apply(lambda x: isinstance(x, (int, float))).all(), "Non-numeric values in aligned_factor"
                assert valid_returns_aligned_se.apply(lambda x: isinstance(x, (int, float))).all(), "Non-numeric values in valid_returns"

                ic = pearsonr(aligned_factor, valid_returns_aligned_se)[0]
                ic_list.append(ic)
            ic_decay_results[fctname] = ic_list
        return ic_decay_results
        
    def random_forest(self):
        '''
        -------------------------------------核心思想-------------------------------------------------------
        随机森林评估特征的重要性
        ---------------------------------------------------------------------------------------------------
        factor_x:ndarray,因子矩阵,行为samples,列为因子名 
        y:ndarray,收益率序列
        '''
        # 检查是否为分类或回归
        if len(np.unique(self.y)) > 10:  # 判断是否为连续变量, 10作为连续和分类的简单分界线
            rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
        else:
            rf = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)

        rf.fit(self.X, self.y)
        feature_importances = rf.feature_importances_
        sorted_idx = feature_importances.argsort()[::-1]
        return sorted_idx, feature_importances[sorted_idx]
    
    def check_stability(self, params:list):
        '''
        -------------------------------------核心思想-------------------------------------------------------
        阈值落点：反映理想的分位数阈值落到参数值的稳定性，平坦区域为佳
        比如一个模型有多个参数，我们想要评估这些参数在不同分位数下的表现是否稳定。
        这里的“稳定性”指的是参数值在不同分位数下的变化是否较小，如果变化小，说明参数值在不同情况下的表现比较一致，即稳定性好。
        ---------------------------------------------------------------------------------------------------
        '''
        ls = np.linspace(0.001, 0.1, 5)
        qs = np.append(ls,1-ls)
        res = {}
        for q in qs:
            res[q] = np.quantile(params.values, q)
        return res
    