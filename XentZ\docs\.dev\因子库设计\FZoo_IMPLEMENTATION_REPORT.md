# XentZ FactorZoo 数据库实施报告

## 📋 实施概要

**实施日期**: 2024-12-26  
**版本**: FactorZoo v2.0.0  
**状态**: ✅ 成功完成  

## 🎯 实施目标

为 XentZ 量化系统创建了一个完整的因子管理系统（FactorZoo），用于存储、管理和评价从 gplearn 等工具生成的因子表达式。

## 📊 实施结果

### 数据库结构
- ✅ **数据库文件**: `D:\myquant\FZoo\database\factorzoo.sqlite` (196KB)
- ✅ **数据表数量**: 9张核心表
- ✅ **视图数量**: 4个查询视图
- ✅ **索引数量**: 24个优化索引
- ✅ **触发器**: 4个自动更新触发器

### 核心表结构

| 表名 | 记录数 | 主要功能 |
|------|--------|----------|
| `factor_batches` | 0 | 因子批次管理 |
| `universes` | 10 | 股票池定义（预置10个池子） |
| `factor_categories` | 37 | 因子分类体系（预置37个分类） |
| `factors` | 0 | 因子表达式主表 |
| `factor_evaluations` | 0 | 因子评价记录 |
| `factor_pools` | 0 | 因子池管理 |
| `factor_pool_members` | 0 | 因子池成员关系 |
| `factor_tags` | 5 | 因子标签系统（含系统信息） |
| `factor_relationships` | 0 | 因子关系网络 |

### 预置数据

#### 因子分类体系（37个分类）

**主分类 (11个)**:
- 量价类 (PRICE_VOLUME)
- 宏观类 (MACRO)  
- 库存类 (INVENTORY)
- 行业类 (INDUSTRY)
- 情绪类 (SENTIMENT)
- 波动率类 (VOLATILITY)
- 截面估值 (CROSS_VALUATION)
- 截面质量 (CROSS_QUALITY)
- 截面成长 (CROSS_GROWTH)
- 截面动量 (CROSS_MOMENTUM)
- 链上数据 (ONCHAIN_DATA)
- ESG因子 (ESG_FACTORS)

**子分类 (26个)**:
- 价格趋势、价格均值回归、成交量形态等

#### 股票池定义（10个池子）

| 股票池ID | 名称 | 类型 | 描述 |
|----------|------|------|------|
| CSI300 | 沪深300 | index_components | 沪深300指数成分股 |
| CSI500 | 中证500 | index_components | 中证500指数成分股 |
| CSI1000 | 中证1000 | index_components | 中证1000指数成分股 |
| COMMODITY_MAIN | 商品期货主力 | dynamic | 主要商品期货主力合约 |
| FINANCIAL_FUTURES | 金融期货 | dynamic | 金融期货合约 |
| SP500 | 标普500 | index_components | 标普500指数成分股 |
| NASDAQ100 | 纳斯达克100 | index_components | 纳斯达克100指数成分股 |
| TOP_CRYPTO | 主流数字货币 | dynamic | 市值前50数字货币 |
| CUSTOM_LIQUID | 高流动性股票池 | dynamic | 高流动性A股 |
| CUSTOM_TECH | 科技股票池 | custom | 自定义科技行业股票池 |

## 🔧 技术特性

### 核心功能支持
- ✅ **GP自动生成+流水线筛选** (L0→L1→L2→L3)
- ✅ **手工创建+直接提交**
- ✅ **多市场支持** (A股/期货/美股/数字货币)
- ✅ **截面因子完整支持**
- ✅ **多次评价支持**
- ✅ **ResMonitor性能监控集成**

### 流水线模式
- `auto_pipeline`: 自动生成+自动流水线筛选
- `manual_pipeline`: 手工创建+手工流水线筛选  
- `direct_submit`: 直接提交(跳过流水线)
- `hybrid`: 混合模式

### 创建方法分类
- `auto_generation`: 自动生成（GP/TSFresh/DEAP等）
- `manual_creation`: 手工创建
- `paper_reference`: 论文参考
- `experience_based`: 经验总结

### 性能监控字段
- `last_computation_time_ms`: 最近计算耗时
- `avg_computation_time_ms`: 平均计算耗时
- `max_memory_usage_mb`: 最大内存使用
- `resource_intensity`: 资源强度等级

## 📁 目录结构

```
D:/myquant/FZoo/
├── database/           # 数据库文件 ✅
│   └── factorzoo.sqlite (196KB)
├── factor_values/      # 因子值数据 ✅
├── evaluations/        # 评价结果和可视化 ✅
├── models/            # 机器学习模型 ✅
├── exports/           # 导出文件和报告 ✅
├── temp/              # 临时文件 ✅
├── logs/              # 日志文件 ✅
└── config/            # 配置文件 ✅
```

## 🔌 核心文件

### 1. 数据库建库脚本
- **文件**: `factor/factor_zoo_schema_fixed.sql` (614行)
- **功能**: 完整的数据库创建脚本
- **特性**: 包含表结构、索引、触发器、视图、预置数据

### 2. 配置接口文件
- **文件**: `factor/factorzoo_config.py`
- **功能**: Python接口，提供便捷的数据库操作
- **类**: `FactorZooConnector`
- **方法**: 
  - `get_categories()`: 获取分类
  - `get_universes()`: 获取股票池
  - `create_batch()`: 创建批次
  - `add_factor()`: 添加因子
  - `search_factors()`: 搜索因子

### 3. 监控系统（分层架构设计）
- **基础层**: `common/cls_base.py` 
  - `ResMonitor`: 通用资源监控器（时间、CPU、内存）
  - `MonitorContext`: 通用监控上下文管理器
  - 适用于所有类型的计算任务

- **专业层**: `factor/factorzoo_monitor.py`
  - `FactorMonitorContext`: 因子专用监控上下文（继承MonitorContext）
  - `FactorZooRunManager`: 因子运行数据管理器
  - 专门处理因子相关的数据存储和统计

- **特性**:
  - 分层设计：通用功能 + 因子专业功能
  - 向后兼容：保留原有接口别名
  - 自动记录性能数据到`factor_performance_logs`表
  - 自动更新FactorZoo主表的性能字段
  - 提供因子级别和批次级别的性能统计

### 4. 监控集成示例
- **文件**: `factor/factorzoo_integration_example.py`
- **功能**: 展示监控系统完整集成流程
- **示例**: 批量因子计算监控、性能统计查询

### 5. 监控使用指南
- **文件**: `factor/README_MONITOR.md`
- **功能**: 详细的监控系统使用文档
- **内容**: 使用说明、最佳实践、典型场景代码示例

### 6. 测试验证脚本
- **文件**: `factor/test_factorzoo_db.py`
- **功能**: 完整的功能测试和验证
- **测试项**: 连接、CRUD操作、数据统计

## ✅ 验证结果

### 数据库连接测试
```bash
✅ 数据库连接成功！
📋 Schema 版本: 2.0.0
📊 数据表数量: 9
🔍 视图数量: 4
🗂️ 索引数量: 24
```

### CRUD操作测试
```bash
✅ 创建测试批次: TEST_20241226_112151
✅ 创建测试因子: F_TEST_20241226_112151
✅ 查询测试成功: F_TEST_20241226_112151 - 测试因子 (量价类)
✅ 测试数据清理完成
```

## 🚀 使用示例

### 基本使用
```python
from factor.factorzoo_config import factorzoo, get_categories, get_universes

# 获取分类
categories = get_categories(level=1)  # 主分类
print(f"主分类数量: {len(categories)}")

# 获取股票池
universes = get_universes(current_only=True)
print(f"当前股票池: {len(universes)}")

# 检查数据库状态
from factor.factorzoo_config import check_database
if check_database():
    print("数据库状态正常")
```

### 添加因子示例
```python
# 创建批次
create_batch(
    batch_id="GP_20241226_001",
    batch_name="第一批GP因子",
    creation_tool="gplearn",
    source_symbols=["510050.SH"],
    source_frequencies=["1d"],
    source_date_ranges={"start": "2020-01-01", "end": "2024-12-01"}
)

# 添加因子
add_factor(
    factor_id="F_GP_20241226_001_001",
    factor_name="MA相对强度",
    factor_expression="close / ts_mean(close, 20) - 1",
    factor_type="time_series",
    data_source_type="single_symbol",
    symbols=["510050.SH"],
    frequencies=["1d"],
    date_ranges={"start": "2020-01-01", "end": "2024-12-01"},
    creation_method="auto_generation",
    primary_category="PRICE_VOLUME",
    batch_id="GP_20241226_001"
)
```

## 📈 后续扩展计划

1. **因子评价系统**
   - 集成 Alphalens 评价框架
   - 多维度性能指标计算
   - 可视化报告生成

2. **因子池管理**
   - 智能因子筛选算法
   - 相关性分析和去重
   - 动态权重分配

3. **实时监控**
   - 因子性能衰减监控
   - 实时计算性能统计
   - 异常因子自动标记

4. **Web界面**
   - 因子管理面板
   - 批次创建向导
   - 评价结果展示

## 📞 技术支持

- **数据库schema版本**: 2.0.0
- **Python接口模块**: `factor.factorzoo_config`
- **测试脚本**: `factor.test_factorzoo_db`
- **数据库位置**: `D:\myquant\FZoo\database\factorzoo.sqlite`

---

**实施完成时间**: 2024-12-26 19:40  
**实施状态**: ✅ 成功  
**监控系统**: ✅ 已集成（参照原库ResMonitor实现）  
**下一步**: 可以开始使用FactorZoo进行因子管理和GP因子生成了！监控系统已就绪，可全程跟踪因子计算性能。 