2025-07-05 00:18 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-05 00:18 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-05 00:18 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-05 00:18 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-05 00:18 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=10
2025-07-05 00:18 | INFO     | QuantStatsReportGenerator.__init__: quantstats_lumi库导入成功
2025-07-05 00:18 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-05 00:18 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: D:\myquant\reports\XentZ
2025-07-05 00:18 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-05 00:18 | INFO     | L3WFAValidator.__init__: L3动态稳健性检验器初始化完成
2025-07-05 00:18 | INFO     | L3WFAValidator.run_validation_pipeline: 开始L3动态稳健性检验流程
2025-07-05 00:18 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤1: 加载配置参数
2025-07-05 00:18 | DEBUG    | L3WFAValidator._load_and_validate_config: 配置参数验证通过
2025-07-05 00:18 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤2: 查询L2阶段通过的因子
2025-07-05 00:18 | INFO     | FactorQueryManager.query_l2_passed_factors: 开始查询L2阶段通过的因子
2025-07-05 00:18 | INFO     | FactorQueryManager.query_l2_passed_factors: 查询完成: 找到1个因子，耗时0.00秒
2025-07-05 00:18 | DEBUG    | FactorQueryManager.query_l2_passed_factors: 品种分布: {'["SH510050"]': 1}
2025-07-05 00:18 | INFO     | L3WFAValidator._query_l2_factors: 查询到1个L2通过的因子
2025-07-05 00:18 | DEBUG    | L3WFAValidator._query_l2_factors: 品种分布: {'["SH510050"]': 1}
2025-07-05 00:18 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤3: 批量执行WFA验证
2025-07-05 00:18 | INFO     | BatchProcessor.process_factors_batch: 开始批量处理: 1个因子
2025-07-05 00:18 | DEBUG    | FactorQueryManager.load_factor_and_price_data: 开始加载因子数据: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-05 00:18 | WARNING  | FactorValueManager._basic_disk_load: 指定因子不存在于批次 L2_LASSO_SH510050_label_1_20250704_718779
2025-07-05 00:18 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: L2_LASSO_SH510050_label_1_20250704_718779/L2 耗时28.5ms
2025-07-05 00:18 | DEBUG    | FactorQueryManager._load_factor_values: 回退到实时计算模式
2025-07-05 00:18 | WARNING  | FactorQueryManager.load_factor_and_price_data: 因子值数据为空: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-05 00:18 | WARNING  | L3WFAValidator.validate_single_factor: 因子数据为空，跳过验证: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-05 00:18 | INFO     | BatchProcessor.progress_callback: 处理进度: 100.0%, 当前因子: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-05 00:18 | DEBUG    | BatchProcessor.process_factors_batch: 块处理完成: 1个因子, 耗时0.03秒
2025-07-05 00:18 | INFO     | BatchProcessor.process_factors_batch: 批量处理完成: 成功0/1, 总耗时0.03秒
2025-07-05 00:18 | INFO     | L3WFAValidator._batch_validate_factors: 批量验证完成: 0/1个因子验证成功
2025-07-05 00:18 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤4: 汇总验证结果
2025-07-05 00:18 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤5: 生成验证报告
2025-07-05 00:18 | INFO     | L3WFAValidator._generate_comprehensive_reports: 开始生成综合验证报告
2025-07-05 00:18 | WARNING  | BatchReportManager.generate_batch_reports: WFA结果为空，无法生成报告
2025-07-05 00:18 | INFO     | L3WFAValidator._generate_validation_report: 验证报告已生成: D:\myquant\reports\XentZ\L3_validation\L3_WFA_validation_report_20250705_001837.txt
2025-07-05 00:18 | WARNING  | L3WFAValidator._generate_comprehensive_reports: 批量报告生成失败: 无数据
2025-07-05 00:18 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤6: 更新因子状态
2025-07-05 00:18 | DEBUG    | L3WFAValidator._update_factor_status: 开始更新因子状态
2025-07-05 00:18 | INFO     | L3WFAValidator._update_factor_status: 因子状态更新完成: 0个因子
2025-07-05 00:18 | INFO     | L3WFAValidator.run_validation_pipeline: L3验证流程完成: 总耗时0.05秒
2025-07-05 19:05 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-05 19:05 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-05 19:05 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-05 19:05 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-05 19:05 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=10
2025-07-05 19:05 | INFO     | QuantStatsReportGenerator.__init__: quantstats_lumi库导入成功
2025-07-05 19:05 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-05 19:05 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: D:\myquant\reports\XentZ
2025-07-05 19:05 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-05 19:05 | INFO     | L3WFAValidator.__init__: L3动态稳健性检验器初始化完成
2025-07-05 19:05 | INFO     | L3WFAValidator.run_validation_pipeline: 开始L3动态稳健性检验流程
2025-07-05 19:05 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤1: 加载配置参数
2025-07-05 19:05 | DEBUG    | L3WFAValidator._load_and_validate_config: 配置参数验证通过
2025-07-05 19:05 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤2: 查询L2阶段通过的因子
2025-07-05 19:05 | INFO     | FactorQueryManager.query_l2_passed_factors: 开始查询L2阶段通过的因子
2025-07-05 19:05 | INFO     | FactorQueryManager.query_l2_passed_factors: 查询完成: 找到1个因子，耗时0.14秒
2025-07-05 19:05 | DEBUG    | FactorQueryManager.query_l2_passed_factors: 品种分布: {'["SH510050"]': 1}
2025-07-05 19:05 | INFO     | L3WFAValidator._query_l2_factors: 查询到1个L2通过的因子
2025-07-05 19:05 | DEBUG    | L3WFAValidator._query_l2_factors: 品种分布: {'["SH510050"]': 1}
2025-07-05 20:09 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-05 20:09 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-05 20:09 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-05 20:09 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-05 20:09 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=10
2025-07-05 20:09 | INFO     | QuantStatsReportGenerator.__init__: quantstats_lumi库导入成功
2025-07-05 20:09 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-05 20:09 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: D:\myquant\reports\XentZ
2025-07-05 20:09 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-05 20:09 | INFO     | L3WFAValidator.__init__: L3动态稳健性检验器初始化完成
2025-07-05 20:09 | INFO     | L3WFAValidator.run_validation_pipeline: 开始L3动态稳健性检验流程
2025-07-05 20:09 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤1: 加载配置参数
2025-07-05 20:09 | DEBUG    | L3WFAValidator._load_and_validate_config: 配置参数验证通过
2025-07-05 20:09 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤2: 查询L2阶段通过的因子
2025-07-05 20:09 | INFO     | FactorQueryManager.query_l2_passed_factors: 开始查询L2阶段通过的因子
2025-07-05 20:09 | INFO     | FactorQueryManager.query_l2_passed_factors: 查询完成: 找到1个因子，耗时0.00秒
2025-07-05 20:09 | DEBUG    | FactorQueryManager.query_l2_passed_factors: 品种分布: {'["SH510050"]': 1}
2025-07-05 20:09 | INFO     | L3WFAValidator._query_l2_factors: 查询到1个L2通过的因子
2025-07-05 20:09 | DEBUG    | L3WFAValidator._query_l2_factors: 品种分布: {'["SH510050"]': 1}
2025-07-05 20:09 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤3: 批量执行WFA验证
2025-07-05 20:09 | INFO     | BatchProcessor.process_factors_batch: 开始批量处理: 1个因子
2025-07-05 20:09 | DEBUG    | FactorQueryManager.load_factor_and_price_data: 开始加载因子数据: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-05 20:09 | WARNING  | FactorValueManager._basic_disk_load: 指定因子不存在于批次 L2_LASSO_SH510050_label_1_20250704_718779
2025-07-05 20:09 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: L2_LASSO_SH510050_label_1_20250704_718779/L2 耗时152.8ms
2025-07-05 20:09 | WARNING  | FactorQueryManager.load_factor_and_price_data: 因子值数据为空: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-05 20:09 | WARNING  | L3WFAValidator.validate_single_factor: 因子数据为空，跳过验证: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-05 20:09 | INFO     | BatchProcessor.progress_callback: 处理进度: 100.0%, 当前因子: F_L2_LASSO_L2_LASSO_SH510050_label_1_20250704_718779_001
2025-07-05 20:09 | DEBUG    | BatchProcessor.process_factors_batch: 块处理完成: 1个因子, 耗时0.15秒
2025-07-05 20:09 | INFO     | BatchProcessor.process_factors_batch: 批量处理完成: 成功0/1, 总耗时0.16秒
2025-07-05 20:09 | INFO     | L3WFAValidator._batch_validate_factors: 批量验证完成: 0/1个因子验证成功
2025-07-05 20:09 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤4: 汇总验证结果
2025-07-05 20:09 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤5: 生成验证报告
2025-07-05 20:09 | INFO     | L3WFAValidator._generate_comprehensive_reports: 开始生成综合验证报告
2025-07-05 20:09 | WARNING  | BatchReportManager.generate_batch_reports: WFA结果为空，无法生成报告
2025-07-05 20:09 | INFO     | L3WFAValidator._generate_validation_report: 验证报告已生成: D:\myquant\reports\XentZ\L3_validation\L3_WFA_validation_report_20250705_200942.txt
2025-07-05 20:09 | WARNING  | L3WFAValidator._generate_comprehensive_reports: 批量报告生成失败: 无数据
2025-07-05 20:09 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤6: 更新因子状态
2025-07-05 20:09 | DEBUG    | L3WFAValidator._update_factor_status: 开始更新因子状态
2025-07-05 20:09 | INFO     | L3WFAValidator._update_factor_status: 因子状态更新完成: 0个因子
2025-07-05 20:09 | INFO     | L3WFAValidator.run_validation_pipeline: L3验证流程完成: 总耗时0.19秒
2025-07-05 21:10 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-05 21:10 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-05 21:10 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-05 21:10 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-05 21:10 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=10
2025-07-05 21:10 | INFO     | QuantStatsReportGenerator.__init__: quantstats_lumi库导入成功
2025-07-05 21:10 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-05 21:10 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: D:\myquant\reports\XentZ
2025-07-05 21:10 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-05 21:10 | INFO     | L3WFAValidator.__init__: L3动态稳健性检验器初始化完成
2025-07-05 21:10 | INFO     | L3WFAValidator.run_validation_pipeline: 开始L3动态稳健性检验流程
2025-07-05 21:10 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤1: 加载配置参数
2025-07-05 21:10 | DEBUG    | L3WFAValidator._load_and_validate_config: 配置参数验证通过
2025-07-05 21:10 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤2: 查询L2阶段通过的因子
