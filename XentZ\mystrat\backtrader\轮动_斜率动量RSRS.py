# 添加项目根目录到 Python 路径
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    
from core.backtrader_extends.engine import BacktraderEngine
from core.backtrader_extends.task_algos import TaskRolling

def Rolling_slope_RSRS():
    task = TaskRolling()
    task.name = '轮动-斜率动量-RSRS'
    task.start_date = '20200101'
    
    task.benchmark = '510300.SH'
    task.symbols = [ "159915.SZ", "159934.SZ", "510880.SH", "513100.SH", "513520.SH",]
    # task.symbols = [ # 小果池
    #         '513100.SH',  # 纳斯达克ETF
    #         '513550.SH',  # 标普500ETF
    #         '510300.SH',  # 沪深300ETF
    #         '159937.SZ',  # 黄金ETF
    #         '510510.SH',  # 中证500ETF
    #         '159659.SZ',  # 纳斯达克ETF
    #         '510050.SH',  # 上证50ETF
    #         '159830.SZ',  # 上海黄金ETF
    #         '511130.SH',  # 30年债券ETF
    #         '159655.SZ',  # 标普ETF
    #         '561300.SH',  # 300指数增强ETF
    #         '513400.SH',  # 道琼斯ETF
    #         '159680.SZ',  # 中证1000ETF
    #         '159501.SZ',  # 纳斯达克ETF
    #         '159941.SZ',  # 纳斯达克ETF
    #         '513850.SH',  # 美国50ETF
    #         '159581.SZ',  # 红利ETF
    #         # '159351',  # A500指数ETF
    #         '513330.SH',  # 纳斯达克ETF
    #         '511090.SH',  # 30年债券ETF
    #         '159915.SZ',  # 创业板ETF
    #         '159985.SZ',  # 豆粕ETF
    #         '159981.SZ',  # 能源化工ETF
    #         '159980.SZ',  # 有色ETF           
    #        ]
    
    task.feature_exprs = ["slope(close,20)", "slope_pair(high,low,18)", "zscore(rsrs_18,600)", ]
    task.feature_names = ["slope_20", "rsrs_18", "rsrs_norm", ]

    task.rules_buy = []
    task.rules_sell = ['rsrs_norm<0']

    task.order_by = 'slope_20'
    return task

if __name__ == '__main__':
    e = BacktraderEngine(Rolling_slope_RSRS())
    e.run_algo_strategy()
    e.analysis(console=False)
    # orders = e.get_orders_df()
    # orders.index = orders.index.strftime('%Y-%m-%d')
    # cols = ['symbol','amount','price','value']
    # orders = orders[cols]
'''
               nav   510300.SH
年化收益          0.23        0.05
复合CAGR        0.22        0.05
最大回撤         -0.28       -0.44
卡玛比率          0.82        0.11
夏普比率           1.1        0.23
累计收益          8.93         0.7
年化波动          0.21        0.22
开始时间    2012-07-19  2012-07-19
结束时间    2024-04-11  2024-04-11
'''