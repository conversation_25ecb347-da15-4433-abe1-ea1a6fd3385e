#!/usr/bin/env python3
# -*- coding: utf-8 -*-

try:
    from box import Box
    print('Box found:', Box)
    
    # Test DynaBox
    try:
        from box import DynaBox
        print('DynaBox found:', DynaBox)
        
        # Create a test DynaBox
        test_box = DynaBox()
        print('DynaBox created:', test_box)
        print('DynaBox attributes:', dir(test_box))
        
        # Check if it has corr method
        if hasattr(test_box, 'corr'):
            print('DynaBox has corr method')
        else:
            print('DynaBox does NOT have corr method')
            
    except ImportError as e:
        print('DynaBox not found:', e)
        
except ImportError as e:
    print('Box not found:', e)
