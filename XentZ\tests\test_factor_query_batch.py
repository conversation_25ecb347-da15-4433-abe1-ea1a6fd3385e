#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
因子查询和批量处理功能测试
验证Task 2.2的实现正确性
"""

import unittest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from factor.factor_query_utils import FactorQueryManager, BatchProcessor


class TestFactorQueryManager(unittest.TestCase):
    """测试因子查询管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.query_manager = FactorQueryManager()
        
        # 模拟因子数据
        self.mock_factors = [
            {
                'factor_id': 'test_factor_1',
                'factor_name': 'GP_L2_factor_1',
                'symbols': ['510050.SH'],
                'pipeline_step': 'L2',
                'batch_id': 'test_batch_1'
            },
            {
                'factor_id': 'test_factor_2', 
                'factor_name': 'GP_L2_factor_2',
                'symbols': ['510300.SH'],
                'pipeline_step': 'L2',
                'batch_id': 'test_batch_2'
            },
            {
                'factor_id': 'test_factor_3',
                'factor_name': 'GP_L2_factor_3', 
                'symbols': ['159915.SZ'],
                'pipeline_step': 'L2',
                'batch_id': 'test_batch_3'
            }
        ]
    
    @patch('factor.factor_query_utils.factorzoo')
    def test_query_l2_passed_factors_success(self, mock_factorzoo):
        """测试成功查询L2通过的因子"""
        # 模拟FactorZoo返回
        mock_factorzoo.search_factors.return_value = self.mock_factors
        
        query_params = {
            'source_pipeline_step': 'L2',
            'factor_limit_per_batch': 100,
            'status_filter': ['L2_PASSED']
        }
        
        # 执行查询
        result = self.query_manager.query_l2_passed_factors(query_params)
        
        # 验证结果
        self.assertEqual(len(result), 3)
        self.assertEqual(result[0]['factor_id'], 'test_factor_1')
        
        # 验证调用参数
        mock_factorzoo.search_factors.assert_called_once()
        call_args = mock_factorzoo.search_factors.call_args
        self.assertEqual(call_args[1]['filters']['pipeline_step'], 'L2')
        self.assertEqual(call_args[1]['limit'], 100)
    
    @patch('factor.factor_query_utils.factorzoo')
    def test_query_l2_passed_factors_with_exclusion(self, mock_factorzoo):
        """测试带排除条件的因子查询"""
        mock_factorzoo.search_factors.return_value = self.mock_factors
        
        query_params = {
            'source_pipeline_step': 'L2',
            'exclude_symbols': ['510300.SH']  # 排除510300.SH
        }
        
        result = self.query_manager.query_l2_passed_factors(query_params)
        
        # 验证排除逻辑
        self.assertEqual(len(result), 2)  # 应该只剩2个因子
        symbols = [f['symbols'][0] for f in result]
        self.assertNotIn('510300.SH', symbols)
    
    @patch('factor.factor_query_utils.factorzoo')
    def test_query_l2_passed_factors_empty_result(self, mock_factorzoo):
        """测试查询返回空结果"""
        mock_factorzoo.search_factors.return_value = []
        
        query_params = {'source_pipeline_step': 'L2'}
        result = self.query_manager.query_l2_passed_factors(query_params)
        
        self.assertEqual(len(result), 0)
    
    @patch('factor.factor_query_utils.factorzoo')
    def test_query_l2_passed_factors_exception(self, mock_factorzoo):
        """测试查询异常处理"""
        mock_factorzoo.search_factors.side_effect = Exception("Database error")
        
        query_params = {'source_pipeline_step': 'L2'}
        result = self.query_manager.query_l2_passed_factors(query_params)
        
        self.assertEqual(len(result), 0)
    
    def test_analyze_factor_distribution(self):
        """测试因子分布分析"""
        distribution = self.query_manager._analyze_factor_distribution(self.mock_factors)
        
        expected = {
            '510050.SH': 1,
            '510300.SH': 1, 
            '159915.SZ': 1
        }
        
        self.assertEqual(distribution, expected)
    
    @patch('factor.factor_query_utils.factor_value_manager')
    def test_load_factor_values_from_cache(self, mock_manager):
        """测试从缓存加载因子值"""
        # 模拟缓存数据
        mock_factors_df = pd.DataFrame({
            'GP_L2_factor_1': [0.1, 0.2, 0.3, 0.4, 0.5]
        }, index=pd.date_range('2024-01-01', periods=5))
        
        mock_manager.load_batch_data.return_value = (pd.DataFrame(), mock_factors_df)
        
        factor_info = {
            'batch_id': 'test_batch_1',
            'factor_name': 'GP_L2_factor_1',
            'pipeline_step': 'L2'
        }
        
        result = self.query_manager._load_factor_values(factor_info)
        
        self.assertFalse(result.empty)
        self.assertEqual(len(result), 5)
        self.assertEqual(result.name, 'GP_L2_factor_1')
    
    def test_align_data(self):
        """测试数据对齐功能"""
        # 创建测试数据
        factor_series = pd.Series([0.1, 0.2, 0.3], 
                                 index=pd.date_range('2024-01-01', periods=3))
        price_series = pd.Series([0.01, 0.02, np.nan], 
                                index=pd.date_range('2024-01-01', periods=3))
        
        factor_aligned, price_aligned = self.query_manager._align_data(factor_series, price_series)
        
        # 验证对齐结果
        self.assertEqual(len(factor_aligned), 2)  # NaN行应该被移除
        self.assertEqual(len(price_aligned), 2)
        self.assertTrue(factor_aligned.index.equals(price_aligned.index))
    
    def test_align_data_empty_input(self):
        """测试空数据对齐"""
        empty_series = pd.Series()
        factor_series = pd.Series([0.1, 0.2])
        
        result1, result2 = self.query_manager._align_data(empty_series, factor_series)
        
        self.assertTrue(result1.empty)
        self.assertTrue(result2.empty)


class TestBatchProcessor(unittest.TestCase):
    """测试批量处理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.batch_processor = BatchProcessor(max_workers=2, chunk_size=3)
        
        # 模拟处理函数
        self.process_count = 0
        
        def mock_process_func(item):
            self.process_count += 1
            if item.get('should_fail'):
                raise ValueError("Mock processing error")
            return {'processed': True, 'item_id': item.get('id')}
        
        self.mock_process_func = mock_process_func
    
    def test_process_factors_batch_success(self):
        """测试成功的批量处理"""
        test_items = [
            {'id': 'item_1'},
            {'id': 'item_2'},
            {'id': 'item_3'},
            {'id': 'item_4'},
            {'id': 'item_5'}
        ]
        
        results = self.batch_processor.process_factors_batch(
            factors=test_items,
            process_func=self.mock_process_func
        )
        
        # 验证结果
        self.assertEqual(len(results), 5)
        self.assertEqual(self.process_count, 5)
        
        # 验证所有结果都成功处理
        for result in results:
            self.assertIsNotNone(result)
            self.assertTrue(result['processed'])
    
    def test_process_factors_batch_with_failures(self):
        """测试包含失败的批量处理"""
        test_items = [
            {'id': 'item_1'},
            {'id': 'item_2', 'should_fail': True},  # 这个会失败
            {'id': 'item_3'},
        ]
        
        results = self.batch_processor.process_factors_batch(
            factors=test_items,
            process_func=self.mock_process_func
        )
        
        # 验证结果
        self.assertEqual(len(results), 3)
        self.assertEqual(self.process_count, 3)  # 所有项都尝试处理了
        
        # 验证失败项返回None
        self.assertIsNotNone(results[0])
        self.assertIsNone(results[1])  # 失败的项
        self.assertIsNotNone(results[2])
    
    def test_process_factors_batch_empty_list(self):
        """测试空列表处理"""
        results = self.batch_processor.process_factors_batch(
            factors=[],
            process_func=self.mock_process_func
        )
        
        self.assertEqual(len(results), 0)
        self.assertEqual(self.process_count, 0)
    
    def test_process_factors_batch_with_progress_callback(self):
        """测试带进度回调的批量处理"""
        test_items = [{'id': f'item_{i}'} for i in range(5)]
        
        progress_calls = []
        
        def progress_callback(progress, current_factor):
            progress_calls.append((progress, current_factor))
        
        results = self.batch_processor.process_factors_batch(
            factors=test_items,
            process_func=self.mock_process_func,
            progress_callback=progress_callback
        )
        
        # 验证进度回调被调用
        self.assertEqual(len(progress_calls), 5)
        
        # 验证进度递增
        for i, (progress, factor_id) in enumerate(progress_calls):
            expected_progress = (i + 1) / 5
            self.assertAlmostEqual(progress, expected_progress, places=2)
    
    def test_create_progress_reporter(self):
        """测试进度报告器创建"""
        reporter = self.batch_processor.create_progress_reporter(log_interval=1)
        
        # 验证返回的是可调用对象
        self.assertTrue(callable(reporter))
        
        # 测试调用（不会抛出异常）
        reporter(0.5, "test_factor")


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    @patch('factor.factor_query_utils.factorzoo')
    @patch('factor.factor_query_utils.factor_value_manager')
    def test_end_to_end_factor_processing(self, mock_manager, mock_factorzoo):
        """测试端到端的因子处理流程"""
        # 模拟查询结果
        mock_factors = [
            {
                'factor_id': 'test_factor_1',
                'factor_name': 'GP_L2_factor_1',
                'symbols': ['510050.SH'],
                'batch_id': 'test_batch_1'
            }
        ]
        mock_factorzoo.search_factors.return_value = mock_factors
        
        # 模拟因子数据
        mock_factors_df = pd.DataFrame({
            'GP_L2_factor_1': [0.1, 0.2, 0.3]
        }, index=pd.date_range('2024-01-01', periods=3))
        mock_manager.load_batch_data.return_value = (pd.DataFrame(), mock_factors_df)
        
        # 创建管理器和处理器
        query_manager = FactorQueryManager()
        batch_processor = BatchProcessor(chunk_size=1)
        
        # 执行查询
        query_params = {'source_pipeline_step': 'L2'}
        factors = query_manager.query_l2_passed_factors(query_params)
        
        # 定义处理函数
        def process_factor(factor_info):
            factor_series, price_series = query_manager.load_factor_and_price_data(factor_info)
            return {
                'factor_id': factor_info['factor_id'],
                'data_loaded': not factor_series.empty,
                'data_length': len(factor_series)
            }
        
        # 批量处理
        results = batch_processor.process_factors_batch(
            factors=factors,
            process_func=process_factor
        )
        
        # 验证结果
        self.assertEqual(len(results), 1)
        self.assertIsNotNone(results[0])
        self.assertEqual(results[0]['factor_id'], 'test_factor_1')


if __name__ == '__main__':
    unittest.main(verbosity=2)
