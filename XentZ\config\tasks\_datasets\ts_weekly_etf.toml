# 周线ETF时序数据集配置
# 用于定义周线数据的ETF研究和回测目标

[dataset]
name = "Weekly_ETF_TimeSeries"
desc = "主要用于周线数据的ETF特征工程、因子挖掘和策略回测"

# ============================== 数据源定义 ============================== #
[data_source]
symbols = ["510050.SH"] # 默认用于快速测试的标的
freq = "W"              # 周线数据

[data_source.hku_loader]
# 数据复权方式: "EQUAL_FORWARD", "EQUAL_BACKWARD", None
recover = "EQUAL_BACKWARD"

# ============================== 数据切分 ============================== #
[time_split]
# 定义不同阶段的数据范围, 若不指定end_date, 则默认取到最新
train_start_date = "2020-01-01"
target_start_date = "2021-01-01"
