"""Metrics to evaluate the fitness of a program.

The :mod:`gplearn.fitness` module contains some metric with which to evaluate
the computer programs created by the :mod:`gplearn.genetic` module.
"""
import numbers
import numpy as np
from joblib import wrap_non_picklable_objects
from scipy.stats import rankdata
import pandas as pd
from sklearn import metrics as me
from sklearn.linear_model import LinearRegression
from datafeed.features.feature_utils import FeatPreprocessing

# __all__ = ['make_fitness']

# 移除全局配置依赖，改为从kwargs获取参数

class _Fitness(object):

    """A metric to measure the fitness of a program.

    This object is able to be called with NumPy vectorized arguments and return
    a resulting floating point score quantifying the quality of the program's
    representation of the true relationship.

    Parameters
    ----------
    function : callable
        A function with signature function(y, y_pred, sample_weight) that
        returns a floating point number. Where `y` is the input target y
        vector, `y_pred` is the predicted values from the genetic program, and
        sample_weight is the sample_weight vector.

    greater_is_better : bool
        Whether a higher value from `function` indicates a better fit. In
        general this would be False for metrics indicating the magnitude of
        the error, and True for metrics indicating the quality of fit.

    """

    def __init__(self, function, greater_is_better):
        self.function = function
        self.greater_is_better = greater_is_better
        self.sign = 1 if greater_is_better else -1

    def __call__(self, *args, **kwargs):
        return self.function(*args, **kwargs)


def make_fitness(*, function, greater_is_better, wrap=True):
    """Make a fitness measure, a metric scoring the quality of a program's fit.

    This factory function creates a fitness measure object which measures the
    quality of a program's fit and thus its likelihood to undergo genetic
    operations into the next generation. The resulting object is able to be
    called with NumPy vectorized arguments and return a resulting floating
    point score quantifying the quality of the program's representation of the
    true relationship.

    Parameters
    ----------
    function : callable
        A function with signature function(y, y_pred, sample_weight) that
        returns a floating point number. Where `y` is the input target y
        vector, `y_pred` is the predicted values from the genetic program, and
        sample_weight is the sample_weight vector.

    greater_is_better : bool
        Whether a higher value from `function` indicates a better fit. In
        general this would be False for metrics indicating the magnitude of
        the error, and True for metrics indicating the quality of fit.

    wrap : bool, optional (default=True)
        When running in parallel, pickling of custom metrics is not supported
        by Python's default pickler. This option will wrap the function using
        cloudpickle allowing you to pickle your solution, but the evolution may
        run slightly more slowly. If you are running single-threaded in an
        interactive Python session or have no need to save the model, set to
        `False` for faster runs.

    """
    if not isinstance(greater_is_better, bool):
        raise ValueError('greater_is_better must be bool, got %s'
                         % type(greater_is_better))
    if not isinstance(wrap, bool):
        raise ValueError('wrap must be an bool, got %s' % type(wrap))
    if function.__code__.co_argcount != 3:
        raise ValueError('function requires 3 arguments (y, y_pred, w),'
                         ' got %d.' % function.__code__.co_argcount)
    if not isinstance(function(np.array([1, 1]),
                      np.array([2, 2]),
                      np.array([1, 1])), numbers.Number):
        raise ValueError('function must return a numeric.')

    if wrap:
        return _Fitness(function=wrap_non_picklable_objects(function),
                        greater_is_better=greater_is_better)
    return _Fitness(function=function,
                    greater_is_better=greater_is_better)


def _weighted_pearson(y, y_pred, w, **kwargs):
    """Calculate the weighted Pearson correlation coefficient."""
    with np.errstate(divide='ignore', invalid='ignore'):
        y_pred_demean = y_pred - np.average(y_pred, weights=w)
        y_demean = y - np.average(y, weights=w)
        corr = ((np.sum(w * y_pred_demean * y_demean) / np.sum(w)) /
                np.sqrt((np.sum(w * y_pred_demean ** 2) *
                         np.sum(w * y_demean ** 2)) /
                        (np.sum(w) ** 2)))
    if np.isfinite(corr):
        return np.abs(corr)
    return 0.


def _weighted_spearman(y, y_pred, w, **kwargs):
    """Calculate the weighted Spearman correlation coefficient."""
    y_pred_ranked = np.apply_along_axis(rankdata, 0, y_pred)
    y_ranked = np.apply_along_axis(rankdata, 0, y)
    return _weighted_pearson(y_pred_ranked, y_ranked, w, **kwargs)


def _mean_absolute_error(y, y_pred, w, **kwargs):
    """Calculate the mean absolute error."""
    return np.average(np.abs(y_pred - y), weights=w)


def _mean_square_error(y, y_pred, w, **kwargs):
    """Calculate the mean square error."""
    return np.average(((y_pred - y) ** 2), weights=w)


def _root_mean_square_error(y, y_pred, w, **kwargs):
    """Calculate the root mean square error."""
    return np.sqrt(np.average(((y_pred - y) ** 2), weights=w))


def _log_loss(y, y_pred, w, **kwargs):
    """Calculate the log loss."""
    eps = 1e-15
    inv_y_pred = np.clip(1 - y_pred, eps, 1 - eps)
    y_pred = np.clip(y_pred, eps, 1 - eps)
    score = y * np.log(y_pred) + (1 - y) * np.log(inv_y_pred)
    return np.average(-score, weights=w)

def _mutual_info(y, y_pred, w, **kwargs):
    #　返回0到1之间，值越高表示两个类别之间的依赖关系越强
    return me.normalized_mutual_info_score(y, y_pred) # 互信息, 衡量两个随机变量之间相关性

# ======================''' from JZAL ''' # ===========================
def _sr_inout_ratio(y, y_pred, w, **kwargs):
    if len(y) < 10:  # TODO: 发现y有时为[1 1]的问题, 暂时没想好如何解决
        return 0
    ''' === y_pred是因子值(算子过程已norm), 与LLM对话确认 === '''
    # y_pred = eu.norm(y_pred, params=NORM_PARAMS_DEFAULT)  # y_hat先滚动norm,clip
    y_pred = y_pred.reshape(-1,1) # 增加维度
    split_index = int(len(y) * 0.75)
    X_train, X_test = y_pred[:split_index], y_pred[split_index:]
    y_train, y_test = y[:split_index], y[split_index:]
    
    model = LinearRegression()
    model.fit(X_train, y_train)
    pred_np_train = model.predict(X_train)
    # 从kwargs提取参数
    pos_params = kwargs.get('norm_pos_params')
    pos_se_train = pd.Series(FeatPreprocessing.norm(pred_np_train, **pos_params))
    y_raw = kwargs['y_raw']

    # 构建sr_params
    M = kwargs.get('day_bars', 1)
    N = kwargs.get('ann_days', 252) 
    free_rate = kwargs.get('fixed_return', 0.03)
    fee_rate = kwargs.get('fee_rate', 0.002)
    nextbar_open = kwargs.get('nextbar_open', True)
    if nextbar_open: # 下一个bar的open买, NOTE: prepare_df时已考虑过y_raw的不同处理
        cost_train = pos_se_train.shift(1).fillna(0).diff().abs() * fee_rate
        ret_train = pos_se_train.shift(1).fillna(0) * y_raw[:split_index] - cost_train.fillna(0)
    else:
        ret_train = pos_se_train * y_raw[:split_index] - pos_se_train.diff(1).abs() * fee_rate
        
    ret_train = ret_train.replace([np.inf, np.nan, -np.inf], 0.0)
    ret_mean_train = np.mean(ret_train)
    ann_ret_train = ret_mean_train * N * M
    ann_vol_train = np.sqrt(N * M) * ret_train.std()
    cond_0_train = np.isclose(ann_vol_train, 0)
    with np.errstate(divide='ignore', invalid='ignore'):
        sr_train = np.where(cond_0_train, 0, (ann_ret_train - free_rate) / ann_vol_train)
    if abs(sr_train) < 1:
        return -99  # 最小极限

    pred_np_test = model.predict(X_test)
    pos_se_test = pd.Series(FeatPreprocessing.norm(pred_np_test, **pos_params))
    if nextbar_open:
        cost_test = pos_se_test.shift(1).fillna(0).diff().abs() * fee_rate
        ret_test = pos_se_test.shift(1).fillna(0) * y_raw[split_index:] - cost_test.fillna(0)
    else:
        ret_test = pos_se_test * y_raw[split_index:] - pos_se_test.diff(1).abs() * fee_rate
    
    ret_test = ret_test.replace([np.inf, np.nan, -np.inf], 0.0)
    ret_mean_test = np.mean(ret_test)
    ann_ret_test = ret_mean_test * N * M
    ann_vol_test = np.sqrt(N * M) * ret_test.std()
    cond_0_test = np.isclose(ann_vol_test, 0)
    with np.errstate(divide='ignore', invalid='ignore'):
        sr_test = np.where(cond_0_test, 0, (ann_ret_test - free_rate) / ann_vol_test)
        
    return sr_test / sr_train if sr_train != 0 else 0

def _sr_intercept_filtered(y, y_pred, w, **kwargs):
    # 每日16个15min bar, 对于期货要用另外的算法!!!!
    if len(y) < 10:  # TODO: 发现y有时为[1 1]的问题, 暂时没想好如何解决
        return 0
    # 因子值转化为仓位, 用滚动norm, clip, NOTE: 然后ema仓位
    ''' === y_pred是因子值(算子过程已norm), 与LLM对话确认 === '''
    # y_pred = eu.norm(y_pred, params=NORM_PARAMS_DEFAULT)  # y_hat先滚动norm,clip
    model = LinearRegression()
    y_pred = y_pred.reshape(-1,1) # 增加维度
    model.fit(X=y_pred, y=y )
    pred_np = model.predict(y_pred)
    
    # filter#1: 截距项过滤
    if model.intercept_ > 0.5 * y.mean():
        return -99.0  # 如果不满足条件，返回-99.0
    
    # 从kwargs提取参数
    pos_params = kwargs.get('norm_pos_params')
    pos_se = pd.Series(FeatPreprocessing.norm(pred_np, **pos_params))
    y_raw = kwargs['y_raw']
    
    # 构建metric_params
    M = kwargs.get('day_bars', 1)
    N = kwargs.get('ann_days', 252) 
    free_rate = kwargs.get('fixed_return', 0.03)
    fee_rate = kwargs.get('fee_rate', 0.002)
    nextbar_open = kwargs.get('nextbar_open', True)
    if nextbar_open: # 下一个bar的open买, NOTE: prepare_df时已考虑过y_raw的不同处理
        cost = pos_se.shift(1).fillna(0).diff().abs() * fee_rate
        ret = pos_se.shift(1).fillna(0) * y_raw - cost.fillna(0)
    else:
        ret = pos_se * y_raw - pos_se.diff(1).abs() * fee_rate
    ret = ret.replace([np.inf, np.nan, -np.inf],0.0)
    # NOTE: 结论: 百分比收益率ret, ret.mean() * N = ann_ret
    ret_mean = np.mean(ret)
    ann_ret = ret_mean * N * M
    ret_std = ret.std() # np.std(ret,ddof=1) 速度接近且结果一致
    ann_vol = np.sqrt(N * M) * ret_std # 每日16个15min bar
    cond_0 = np.isclose(ann_vol,0)
    with np.errstate(divide='ignore', invalid='ignore'):
        sr = np.where(cond_0, 0, (ann_ret - free_rate) / ann_vol)
    return sr
         
def _sr_pure(y, y_pred, w, **kwargs):
    # 每日16个15min bar, 对于期货要用另外的算法!!!!
    if len(y) < 10:  # TODO: 发现y有时为[1 1]的问题, 暂时没想好如何解决
        return 0
    # 因子值转化为仓位, 用滚动norm, clip, NOTE: 然后ema仓位
    # import jzal_pro.utils.expr_utils as eu
    # ''' JZAL test.. '''
    # fct_name = kwargs['fct_name']
    # if fct_name == 'add2(sma10, ts_mean_40(sma5))':
    #     print("y_pred:",y_pred)
    #     pd.Series(y_pred).to_csv('y_pred.csv')
    # ''' end of test'''
    ''' === y_pred是因子值(算子过程已norm), 与LLM对话确认 === '''
    # y_pred = eu.norm(y_pred, params=NORM_PARAMS_DEFAULT)  # y_hat先滚动norm,clip
    model = LinearRegression()
    y_pred = y_pred.reshape(-1,1) # 增加维度
    model.fit(X=y_pred, y=y )
    pred_np = model.predict(y_pred)
    # 从kwargs提取参数
    pos_params = kwargs.get('norm_pos_params')
    pos_se = pd.Series(FeatPreprocessing.norm(pred_np, **pos_params))
    # ret = (pos_se + y) - (np.exp(pos_se.diff(1).abs())-1)*fee_rate # 结果为对数收益率
    y_raw = kwargs['y_raw']
    # x_close = kwargs['x_close']
    symbol = kwargs['symbol']
    
    # 构建metric_params
    M = kwargs.get('day_bars', 1)
    N = kwargs.get('ann_days', 252) 
    free_rate = kwargs.get('fixed_return', 0.03)
    fee_rate = kwargs.get('fee_rate', 0.002)
    nextbar_open = kwargs.get('nextbar_open', True)
    # x_close = kwargs['x_close']
    # if len(x_close) != len(y) or x_close.size == 0:
    #     ret = pos_se * y_raw - pos_se.diff(1).abs() * fee_rate
    # else:
    #     ret = (pos_se * y_raw - pos_se.diff(1).abs() * fee_rate) / (pos_se.shift(1).abs() * x_close) # NOTE: 除以上一bar所需的资本captial
    if nextbar_open: # 下一个bar的open买, NOTE: prepare_df时已考虑过y_raw的不同处理
        cost = pos_se.shift(1).fillna(0).diff().abs() * fee_rate
        ret = pos_se.shift(1).fillna(0) * y_raw - cost.fillna(0)
    else:
        ret = pos_se * y_raw - pos_se.diff(1).abs() * fee_rate
    ret = ret.replace([np.inf, np.nan, -np.inf],0.0)
    # NOTE: 结论: 百分比收益率ret, ret.mean() * N = ann_ret
    ret_mean = np.mean(ret)
    ann_ret = ret_mean * N * M
    ret_std = ret.std() # np.std(ret,ddof=1) 速度接近且结果一致
    ann_vol = np.sqrt(N * M) * ret_std # 每日16个15min bar
    cond_0 = np.isclose(ann_vol,0)
    with np.errstate(divide='ignore', invalid='ignore'):
        sr = np.where(cond_0, 0, (ann_ret - free_rate) / ann_vol)
    return sr

def _sr_rolling_segmented(y, y_pred, w, **kwargs):
    """
    基于滚动分段夏普率的fitness metric
    - 将截距项过滤改为惩罚机制
    - 使用滚动窗口计算夏普率
    - 将滚动夏普率序列分段，取后1/4分位数作为fitness
    """
    if len(y) < 10:  # TODO: 发现y有时为[1 1]的问题, 暂时没想好如何解决
        return 0
    # ====  1. 参数获取
    y_raw = kwargs['y_raw']
    day_bars = kwargs.get('day_bars', 1)
    ann_days = kwargs.get('ann_days', 252)
    free_rate = kwargs.get('fixed_return', 0.03)
    fee_rate = kwargs.get('fee_rate', 0.002)
    nextbar_open = kwargs.get('nextbar_open', True)
    month_days = int(ann_days / 12)
    half_year_bars = int(day_bars * ann_days / 2)  # 半年的bar数
    
    # ====  2. 窗口参数
    window = 6 * month_days  # 1.5月
    min_periods = 3 * month_days  # 1月
    if len(y) < window:
        return -99.0
        
    # ====  3. 因子值映射仓位逻辑
    
    model = LinearRegression()
    y_pred = y_pred.reshape(-1,1)
    model.fit(X=y_pred, y=y)
    pred_np = model.predict(y_pred)
    
    # 新的截距项惩罚逻辑
    intercept_ratio = model.intercept_ / y.mean() if y.mean() != 0 else 0
    intercept_penalty = 1.0
    
    if intercept_ratio > 0.5:
        # 使用sigmoid函数平滑过渡
        penalty_factor = 1 / (1 + np.exp(5 * (intercept_ratio - 0.75)))  # 0.75是中点
        intercept_penalty = max(0.1, penalty_factor)  # 保证最低不小于0.1
    
    # 计算仓位
    pos_params = kwargs.get('norm_pos_params')
    pos_se = pd.Series(FeatPreprocessing.norm(pred_np, **pos_params))

    # 计算收益率(保持原有逻辑)
    if nextbar_open:
        cost = pos_se.shift(1).fillna(0).diff().abs() * fee_rate
        ret = pos_se.shift(1).fillna(0) * y_raw - cost.fillna(0)
    else:
        ret = pos_se * y_raw - pos_se.diff(1).abs() * fee_rate
    ret = ret.replace([np.inf, np.nan, -np.inf], 0.0)
    
    # ====  4. 滚动夏普率   =====
    ret_series = pd.Series(ret)
    with np.errstate(divide='ignore', invalid='ignore'):
        roll_mean = ret_series.rolling(window=int(window), min_periods=int(min_periods)).mean()
        roll_std = ret_series.rolling(window=int(window), min_periods=int(min_periods)).std()
        ann_ret = roll_mean * day_bars * ann_days
        ann_vol = np.sqrt(day_bars * ann_days) * roll_std
        # 双重保护机制
        cond_0 = np.isclose(ann_vol, 0)
        roll_sr = np.where(cond_0, 0, (ann_ret - free_rate) / ann_vol)
    
    # 处理无效值
    roll_sr = np.nan_to_num(roll_sr, posinf=np.nan, neginf=np.nan)
    roll_sr = pd.Series(roll_sr)
    
    # ====  5. 分段计算   =====
    n_segments = max(len(roll_sr) // half_year_bars, 1)  # 至少1段
    segment_srs = []
    
    for i in range(n_segments):
        start_idx = i * half_year_bars
        end_idx = min((i + 1) * half_year_bars, len(roll_sr))
        segment = roll_sr[start_idx:end_idx]
        segment = segment.dropna()
        if len(segment) > 0:
            segment_srs.append(segment.mean())
    
    if not segment_srs:  # 如果所有段都是空的
        return -99.0
        
    if len(segment_srs) >= 4:
        sr_quantile = np.quantile(segment_srs, 0.25)
    else:  # 段数较少时取最差值
        sr_quantile = min(segment_srs)
    
    # 应用截距项惩罚
    return sr_quantile * intercept_penalty

# ==========================================================================

weighted_pearson = _Fitness(function=_weighted_pearson,
                            greater_is_better=True)
weighted_spearman = _Fitness(function=_weighted_spearman,
                             greater_is_better=True)
mean_absolute_error = _Fitness(function=_mean_absolute_error,
                               greater_is_better=False)
mean_square_error = _Fitness(function=_mean_square_error,
                             greater_is_better=False)
root_mean_square_error = _Fitness(function=_root_mean_square_error,
                                  greater_is_better=False)
log_loss = _Fitness(function=_log_loss,
                    greater_is_better=False)
# by JZAL
mutual_info = make_fitness(function=_mutual_info, greater_is_better=True)
sr_pure = make_fitness(function=_sr_pure, greater_is_better=True, wrap=False)
sr_inout_ratio = make_fitness(function=_sr_inout_ratio, greater_is_better=True, wrap=False)
sr_intercept_filtered = make_fitness(function=_sr_intercept_filtered, greater_is_better=True, wrap=False)
sr_rolling_segmented = make_fitness(function=_sr_rolling_segmented, greater_is_better=True, wrap=False)

_fitness_map = {'pearson': weighted_pearson,
                'spearman': weighted_spearman,
                'mean absolute error': mean_absolute_error,
                'mse': mean_square_error,
                'rmse': root_mean_square_error,
                'log loss': log_loss,
                'mutual_info': mutual_info,
                'sr_pure': sr_pure,
                'sr_inout_ratio': sr_inout_ratio,
                'sr_intercept_filtered': sr_intercept_filtered,
                'sr_rolling_segmented': sr_rolling_segmented}
