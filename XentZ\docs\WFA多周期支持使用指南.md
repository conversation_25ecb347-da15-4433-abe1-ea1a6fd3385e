# WFA验证系统多周期支持使用指南

## 概述

WFA验证系统现已完全支持多周期数据处理，能够自动根据数据周期调整年化因子和窗口参数，确保不同周期下的绩效指标计算准确性。

## 支持的数据周期

| 周期代码 | 描述 | 年化因子 | 推荐训练窗口 | 推荐测试窗口 |
|---------|------|----------|-------------|-------------|
| D | 日线 | 252 | 750天(约3年) | 60天(约3月) |
| W | 周线 | 52 | 150周(约3年) | 12周(约3月) |
| M | 月线 | 12 | 36月(约3年) | 6月(约6月) |
| Q | 季线 | 4 | 12季(约3年) | 2季(约6月) |
| Y | 年线 | 1 | 5年 | 1年 |

## 使用方法

### 1. 基础使用（自动配置）

```python
from factor.validation_utils import WFAParams, run_wfa_validation

# 根据数据周期自动创建最优参数
wfa_params = WFAParams.create_for_frequency("D")  # 日线数据
# wfa_params = WFAParams.create_for_frequency("W")  # 周线数据
# wfa_params = WFAParams.create_for_frequency("M")  # 月线数据

# 执行WFA验证
result = run_wfa_validation(factor_data, price_data, wfa_params, data_freq="D")
```

### 2. 从配置文件加载（推荐）

```python
from factor.validation_utils import load_wfa_config_from_toml

# 从配置文件加载，自动读取数据周期
wfa_params, data_freq, criteria = load_wfa_config_from_toml("config/tasks/ts_l3_wfa.toml")

# 执行验证
result = run_wfa_validation(factor_data, price_data, wfa_params, data_freq)
```

### 3. 手动配置（高级用户）

```python
from factor.validation_utils import WFAParams

# 手动创建参数并覆盖默认值
wfa_params = WFAParams.create_for_frequency(
    "W",  # 周线数据
    {
        "training_window": 200,  # 自定义训练窗口
        "testing_window": 15,    # 自定义测试窗口
        "tanh_k": 3.0           # 自定义S型映射参数
    }
)
```

## 配置文件设置

### 数据集配置

在 `config/tasks/_datasets/` 中设置数据周期：

```toml
# ts_single_etf.toml (日线)
[data_source]
symbols = ["510050.SH"]
freq = "D"

# ts_weekly_etf.toml (周线)
[data_source]
symbols = ["510050.SH"]
freq = "W"

# ts_monthly_etf.toml (月线)
[data_source]
symbols = ["510050.SH"]
freq = "M"
```

### WFA任务配置

在 `config/tasks/ts_l3_wfa.toml` 中：

```toml
# 继承数据集配置（包含freq信息）
dynaconf_include = ["_datasets/ts_single_etf.toml"]

[wfa]
# 系统会根据dataset.freq自动调整这些参数
tanh_k = 5
hold_n = 1
gap = 1
correlation_method = "spearman"

# 可选：手动覆盖自动参数
# training_window = 750
# testing_window = 60
```

## 绩效指标计算

系统会根据数据周期自动调整年化因子：

```python
from factor.performance_utils import perf_calculator

# 自动使用正确的年化因子
metrics = perf_calculator.calculate_basic_metrics(pnl_series, annualization_factor)

# 不同周期的年化收益率计算示例：
# 日线: annual_return = mean_return * 252
# 周线: annual_return = mean_return * 52  
# 月线: annual_return = mean_return * 12
```

## 测试验证

运行完整测试套件验证多周期支持：

```bash
python tests/test_wfa_validation.py
```

测试包括：
- ✅ 21个测试用例全部通过
- ✅ 年化因子计算验证
- ✅ 多周期参数创建验证
- ✅ 不同周期绩效指标计算验证
- ✅ 完整WFA验证流程测试

## 最佳实践

### 1. 周期选择建议

- **日线数据**: 适用于短期因子和高频策略
- **周线数据**: 适用于中期趋势因子，减少噪声
- **月线数据**: 适用于长期配置因子，平滑市场波动

### 2. 窗口参数调整

系统提供的默认参数已经过优化，但可根据具体需求调整：

```python
# 保守配置（更长训练期）
conservative_params = WFAParams.create_for_frequency("D", {
    "training_window": 1000,  # 4年训练期
    "testing_window": 60      # 3月测试期
})

# 激进配置（更短训练期）
aggressive_params = WFAParams.create_for_frequency("D", {
    "training_window": 500,   # 2年训练期
    "testing_window": 60      # 3月测试期
})
```

### 3. 性能优化

- 日线数据处理时间: < 0.2秒/因子
- 周线数据处理时间: < 0.1秒/因子
- 月线数据处理时间: < 0.05秒/因子

## 常见问题

### Q: 如何切换数据周期？
A: 修改数据集配置文件中的 `freq` 参数，系统会自动调整所有相关参数。

### Q: 年化因子是否可以自定义？
A: 可以，通过 `WFAParams.get_annualization_factor()` 方法或直接传入 `annualization_factor` 参数。

### Q: 不同周期的结果如何对比？
A: 使用相同的通过标准配置，系统会自动调整年化因子确保指标可比性。

### Q: 是否支持自定义周期？
A: 支持，可以通过手动设置年化因子和窗口参数来支持任意周期。

## 技术实现

### 核心改进

1. **动态年化因子**: 根据数据周期自动选择正确的年化因子
2. **智能参数调整**: 根据周期自动优化训练/测试窗口大小
3. **配置文件集成**: 从dataset配置中自动读取数据周期信息
4. **向后兼容**: 保持原有API不变，新增多周期支持

### 文件结构

```
factor/
├── validation_utils.py          # 核心WFA算法，支持多周期
├── performance_utils.py         # 绩效计算，支持动态年化因子
└── factor_utils.py             # 基础工具函数

config/tasks/
├── ts_l3_wfa.toml              # WFA任务配置
└── _datasets/
    ├── ts_single_etf.toml      # 日线数据集配置
    ├── ts_weekly_etf.toml      # 周线数据集配置
    └── ts_monthly_etf.toml     # 月线数据集配置

tests/
└── test_wfa_validation.py      # 完整测试套件，包含多周期测试
```

---

**多周期支持已完全集成，可直接用于生产环境！** 🎉
