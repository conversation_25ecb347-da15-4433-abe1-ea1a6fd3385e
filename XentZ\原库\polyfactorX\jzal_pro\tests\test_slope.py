import pandas as pd
import numpy as np
import time


def _slope2(se, N=18, min_periods=2):
    slopes = np.full(len(se), np.nan)  # 预先分配一个全是NaN的数组
    if len(se) < N:
        return pd.Series(slopes, index=se.index)
    
    # 使用向量化操作一次计算所有可能的x和y
    x = np.lib.stride_tricks.sliding_window_view(se.values, window_shape=N) # (L - N + 1, N), 对se为10, N为3, x为(8,3)
    
    # min_periods处理
    for i in range(min_periods, N):
        x_min_periods = se.values[:i]
        if np.isnan(x_min_periods).any():
            slopes[i-1] = -1
        else:
            try:
                if x_min_periods[0] == 0:
                    slopes[i-1] = -1
                else:
                    x_min_periods = x_min_periods / x_min_periods[0]  # 归一化
                    slope = np.polyfit(range(len(x_min_periods)), x_min_periods, 1)[0]
                    slopes[i-1] = slope
            except:
                slopes[i-1] = -1
    # 计算斜率
    idx = np.arange(N) # (N, ), eg. (3, )
    x_first = x[:, 0] # 一维数组, shape = (L - N + 1, ) eg. (8, )
    
    with np.errstate(divide='ignore', invalid='ignore'):
        x_normalized = np.where(x_first[:, None] == 0, np.nan, x / x_first[:, None]) # (L - N + 1, N), eg. (8, 3)
        slopes_valid = np.apply_along_axis(lambda arr: np.polyfit(idx, arr, 1)[0], axis=1, arr=x_normalized)
        slopes_valid[np.isnan(slopes_valid)] = -1
    
    slopes[N-1:] = slopes_valid # 将计算结果放到预分配的数组中

    return pd.Series(slopes, index=se.index)

def slope2(se, N=18, min_periods=2):
    return _slope2(se, N, min_periods)


def _slope1(x):
    try:
        if x.iloc[0] == 0:
            return -1
        # 检查窗口中是否有任何NaN值
        if x.isnull().any():
            return -1
        x = x / x.iloc[0]  # 这里做了一个“归一化”
        slope = np.polyfit(range(len(x)), x, 1)[0]
        return slope
    except:
        return -1

def slope1(se, N):
    result = se.rolling(N, min_periods=2).apply(lambda x: _slope1(x))
    return result

# 生成测试数据
np.random.seed(0)
data = pd.Series(np.random.randn(100000))
data[20] = 0
data[223] = -1
data[3332] = np.nan
data[3] = np.inf
data[4] = np.nan
data[0] = 0

# 测试原始函数
start_time = time.time()
original_result = slope1(data, 1000)
print(f"Original function took {time.time() - start_time:.6f} seconds")

# 测试重构后的函数
start_time = time.time()
optimized_result = slope2(data, 1000)
print(f"Optimized function took {time.time() - start_time:.6f} seconds")
# original_result.to_csv('slope.csv')
# optimized_result.to_csv('slope_optimized.csv')
# 比较结果
# assert (original_result == optimized_result).all(), "The results of the two functions do not match."
assert np.allclose(original_result, optimized_result, equal_nan=True), "Results are not consistent"

print("The optimized function produces the same results and is faster.")

# 鲁棒性测试
# 测试含有NaN值的数据
nan_data = data.copy()
nan_data[500] = np.nan

# 测试全零序列
zero_data = pd.Series(np.zeros_like(data))

# 执行鲁棒性测试
for test_data in [nan_data, zero_data]:
    print(f"Testing with {test_data.describe()}")
    assert np.allclose(slope1(test_data, 18), slope2(test_data, 18), equal_nan=True), "The results of the two functions do not match for robustness tests."