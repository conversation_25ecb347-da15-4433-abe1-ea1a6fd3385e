import array
import numpy as np
from icecream import ic

def lagrange(x: np.n<PERSON><PERSON>, y: np.n<PERSON><PERSON>, x_test: float) -> float:
    """
    Parameters:
    - x: numpy array of known x values.
    - y: numpy array of known y values.
    - x_test: the x value at which to interpolate.
    Returns:
    - The interpolated y value at x_test.
    """
    num_points = len(x)
    l = np.ones(num_points)
    
    for k in range(num_points):
        for k_ in range(num_points):
            if k != k_:
                l[k] *= (x_test - x[k_]) / (x[k] - x[k_])
    
    L = np.sum(y * l)
    return L

if __name__ == '__main__':
    x = np.array([0, 1, 2, 3])
    y = np.array([1, 3, 2, 4])
    result = lagrange(x, y, 1.5)
    ic(result)