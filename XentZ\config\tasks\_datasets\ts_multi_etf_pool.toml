# 跨品种ETF池 数据集配置
# 用于定义一个包含多个ETF的资产池，作为跨品种研究（如横截面分析、轮动策略）的数据基础。

[dataset]
name = "Multi_ETF_Pool"
desc = "包含A股、港股、美股等主要ETF，用于跨市场、跨品种的因子和策略研究"

# ============================== 数据源定义 ============================== #
[data_source]
# 核心ETF池, 包含了不同市场和类型的ETF
symbols = [
    # A股宽基
    "510050.SH", # 上证50ETF
    "510300.SH", # 沪深300ETF
    "159915.SZ", # 创业板50ETF
    "510880.SH", # 红利ETF
    # 商品
    "159934.SZ", # 黄金ETF
    # 港股
    "513100.SH", # 恒生ETF
    # 美股/中概股
    "513520.SH"  # 中概互联网ETF
]
freq = "D"

[data_source.hku_loader]
# 数据复权方式: "EQUAL_FORWARD", "EQUAL_BACKWARD", None
recover = "EQUAL_BACKWARD" 