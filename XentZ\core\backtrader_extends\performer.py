from datetime import datetime, timedelta
import pandas as pd

def year_frac(start: datetime, end: datetime):
    """
    start/end: datetime
    year fraction between two dates (i.e. 1.53 years).
    """
    if start > end:
        raise ValueError("start cannot be larger than end")
    return (end - start).total_seconds() / (31557600)


def calc_stats(df_all_nav: pd.DataFrame):
    """
    df_all_nav: 策略净值/价格净值, 可以多资产(多列) df(每日,行数相同)
                    nav1    nav2
    2014-01-03   0.992221   0.989171
    """
    df_all_nav.copy()
    # df_rates = df_all_nav.pct_change()
    df_rates = (df_all_nav / df_all_nav.shift(1)) - 1 # 前一个值 
    df_equity = df_all_nav
    df_equity.dropna(inplace=True)
    df_rates.dropna(inplace=True)
    
    count = len(df_all_nav)
    start = df_all_nav.index[0]
    end = df_all_nav.index[-1]

    accu_return = round(df_equity.iloc[-1] - 1, 2)
    accu_return.name = '累计收益'
    # annu_ret = round((accu_return + 1) ** (252 / count) - 1, 2)
    annu_ret = round(df_rates.mean(axis=0) * 252 , 2)
    annu_ret.name = '年化收益'
    annu_ret2 = round((accu_return+1) ** (1 / year_frac(start, end)) - 1,2)
    annu_ret2.name = '复合CAGR'
    # 标准差
    std = round(df_rates.std() * (252 ** 0.5), 2)
    std.name = '年化波动'
    # 夏普比
    sharpe = round(annu_ret / std, 2)
    sharpe.name = '夏普比率'
    # 最大回撤
    mdd = round((df_equity / df_equity.expanding(min_periods=1).max()).min() - 1, 2)
    mdd.name = '最大回撤'
    ret_2_mdd = round(annu_ret / abs(mdd), 2)
    ret_2_mdd.name = '卡玛比率'
    df_ratios = pd.concat([annu_ret, annu_ret2, mdd, ret_2_mdd, sharpe, accu_return, std], axis=1)
    df_ratios['开始时间'] = start.strftime('%Y-%m-%d')
    df_ratios['结束时间'] = end.strftime('%Y-%m-%d')
    
    ''' 逐年nav表现'''
    df_all_nav['year'] = df_all_nav.index.year
    nav_peryear = df_all_nav['nav'].groupby(df_all_nav['year']).last()/df_all_nav['nav'].groupby(df_all_nav['year']).first() - 1
    if not df_all_nav.iloc[:,1].empty:
        bech_peryear = df_all_nav.iloc[:,1].groupby(df_all_nav['year']).last()/df_all_nav.iloc[:,1].groupby(df_all_nav['year']).first() - 1
    excess_ret = nav_peryear - bech_peryear
    df_nav_peryear = pd.concat([nav_peryear,bech_peryear,excess_ret],axis = 1)
    df_nav_peryear.columns = ['strategy_ret','bench_ret','excess_ret']
    df_nav_peryear = round(df_nav_peryear,3)

    # 净值可视化...
    
    return df_ratios.T, df_nav_peryear.T
