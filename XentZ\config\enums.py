from config.settings import get_mine_run_mode

# ==== GP参数设置
GP_MODE = get_mine_run_mode()

if GP_MODE == 'live':
    GP_PARAMS = {
        # 基础参数
        'population_size': 20000,  # 一次生成因子的数量，初始10w; 测试500
        'hall_of_fame': 1000,     # >=n_components, 测试100
        'n_components': 600,      # 最终输出多少个因子？从相关性角度选了,其他都是metric角度, 测试100
        'tournament_size': 1000,  # 3000, 测试200
        'generations': 2,         # 非常非常非常重要！！！--进化多少轮次？3也就顶天了,一般2
        'init_depth': (1, 3),     # 第二重要的一个部位，控制我们公式的一个深度
        'parsimony_coefficient': 0.0005,  # Xv: 0.005 or None
        'const_range': None,      # (-1, 1), critical
    
        # 变异概率参数
        'p_crossover': 0.8,
        'p_subtree_mutation': 0.02,
        'p_hoist_mutation': 0.02,
        'p_point_mutation': 0.02,
        'p_point_replace': 0.5,   # 不参与概率大于1的检查
        
        # 其他参数
        'verbose': 1,
        'random_state': None
        # 'corrcoef_threshold': 0.8,  # 改进版新增
    }
elif GP_MODE == 'test':
    GP_PARAMS = {
        # 基础参数
        'population_size': 500,  # 一次生成因子的数量，初始10w; 测试500
        'hall_of_fame': 100,     # >=n_components, 测试100
        'n_components': 100,     # 最终输出多少个因子？从相关性角度选了,其他都是metric角度, 测试100
        'tournament_size': 200,  # 3000, 测试200
        'generations': 2,        # 非常非常非常重要！！！--进化多少轮次？3也就顶天了,一般2
        'init_depth': (1, 3),    # 第二重要的一个部位，控制我们公式的一个深度
        'parsimony_coefficient': 0.0005,  # Xv: 0.005 or None
        'const_range': None,     # (-1, 1), critical
    
        # 变异概率参数
        'p_crossover': 0.8,
        'p_subtree_mutation': 0.02,
        'p_hoist_mutation': 0.02,
        'p_point_mutation': 0.02,
        'p_point_replace': 0.5,   # 不参与概率大于1的检查
        
        # 其他参数
        'verbose': 1,
        'random_state': 42
        # 'corrcoef_threshold': 0.8,  # 改进版新增
    }