import pandas as pd
import numpy as np
from typing import Optional
from datetime import datetime

# 示例数据生成
np.random.seed(42)
num_rows = 100
num_columns = 10

data = np.random.randn(num_rows, num_columns)
columns = [f'fct_{i}' for i in range(num_columns)]
df = pd.DataFrame(data, columns=columns)
df['ret'] = np.random.randn(num_rows)

# 模拟选择原方法
class FactorSelectionOriginal:
    @classmethod
    def log(cls, message, level='INFO'):
        print(f"[{level}] {message}")

    @classmethod
    def select_by_corr(cls, a_fcts_df: pd.DataFrame, corr_threshold: float = 0.3, g_uid: Optional[str] = None) -> list:
        cls.log("start compute corr...")
        if a_fcts_df.empty:
            return []
        a_fcts_df = a_fcts_df.copy()
        ret_col = 'ret_open' if 'ret_open' in a_fcts_df.columns else 'ret'
        X, y = a_fcts_df[a_fcts_df.columns.difference([ret_col])], a_fcts_df[ret_col]

        num_features = X.select_dtypes(include=[np.number]).columns
        if len(num_features) != X.shape[1]:
            X = X[num_features]
            cls.log(f"包含非数值列,仅用数值列计算corr...被忽略的列有：{set(X.columns) - set(num_features)}", level='ERROR')

        X_corr_matrix = X.corr().where(np.triu(np.ones(X.corr().shape), k=1).astype(bool))
        factor_list_2 = num_features.tolist()

        for fct_1, fct_2 in zip(*np.triu_indices_from(X_corr_matrix, k=1)):
            corr_value = X_corr_matrix.iloc[fct_1, fct_2]
            if abs(corr_value) > corr_threshold:
                corr_with_y_1 = X[num_features[fct_1]].corr(y)
                corr_with_y_2 = X[num_features[fct_2]].corr(y)
                factor_to_remove = num_features[fct_2] if abs(corr_with_y_1) >= abs(corr_with_y_2) else num_features[fct_1]
                if factor_to_remove in factor_list_2:
                    factor_list_2.remove(factor_to_remove)

        return factor_list_2

# 优化后的方法
class FactorSelectionOptimized(FactorSelectionOriginal):
    @staticmethod
    def _check_correlation(args):
        fct_1, fct_2, X_corr_matrix, X, y, corr_threshold = args
        corr_value = X_corr_matrix.iloc[fct_1, fct_2]
        if corr_value > corr_threshold:
            corr_with_y_1 = X.iloc[:, fct_1].corr(y)
            corr_with_y_2 = X.iloc[:, fct_2].corr(y)
            return fct_2 if abs(corr_with_y_1) >= abs(corr_with_y_2) else fct_1
        return None

    @classmethod
    def select_by_corr(cls, a_fcts_df: pd.DataFrame, corr_threshold: float = 0.3, g_uid: Optional[str] = None) -> list:
        cls.log("start compute corr...")
        if a_fcts_df.empty:
            return []

        a_fcts_df = a_fcts_df.copy()
        ret_col = 'ret_open' if 'ret_open' in a_fcts_df.columns else 'ret'
        X, y = a_fcts_df[a_fcts_df.columns.difference([ret_col])], a_fcts_df[ret_col]

        num_features = X.select_dtypes(include=[np.number]).columns
        if len(num_features) != X.shape[1]:
            X = X[num_features]
            cls.log(f"包含非数值列,仅用数值列计算corr...被忽略的列有：{set(X.columns) - set(num_features)}", level='ERROR')

        X_corr_matrix = X.corr().abs()
        np.fill_diagonal(X_corr_matrix.values, 0)

        factor_list_2 = num_features.tolist()
        pairs_to_check = np.triu_indices_from(X_corr_matrix, k=1)
        
        pool_args = [(fct_1, fct_2, X_corr_matrix, X, y, corr_threshold) 
                     for fct_1, fct_2 in zip(pairs_to_check[0], pairs_to_check[1])]
        
        from multiprocessing import Pool, cpu_count
        with Pool(cpu_count()) as pool:
            results = pool.map(cls._check_correlation, pool_args)
        
        factors_to_remove = [num_features[f] for f in results if f is not None]
        factor_list_2 = [f for f in num_features if f not in factors_to_remove]

        return factor_list_2

# 测试两种方法
corr_threshold = 0.3

# 原方法
original_selected_factors = FactorSelectionOriginal.select_by_corr(df, corr_threshold)
print("Original selected factors:", original_selected_factors)

# 优化方法
optimized_selected_factors = FactorSelectionOptimized.select_by_corr(df, corr_threshold)
print("Optimized selected factors:", optimized_selected_factors)

# 比较结果
print("Results are consistent:", set(original_selected_factors) == set(optimized_selected_factors))