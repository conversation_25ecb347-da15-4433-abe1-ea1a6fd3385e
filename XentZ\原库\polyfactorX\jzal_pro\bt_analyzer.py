import backtrader as bt
import numpy as np
class WeightAnalyzer(bt.Analyzer):
    def __init__(self):
        self.weights = {}  # 用于存储权重信息的字典
    def next(self): # 在策略next执行之后执行
        try:
            bar_weights = self.strategy.temp['weights'].copy()
            bar_dt = self.strategy.datetime.datetime(0) # python的datetime类型
            self.weights[bar_dt] = bar_weights
        except:
            pass

    def get_analysis(self):
        ''' weights>>
        {734506.0: {'510050.SH': 0.5876479085159566, '159915.SZ': 0.4123520914840435}, ...}
        '''
        return self.weights # key是numpy.datetime64类型的时间信息,value是该bar各品种的weight字典