{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [ 
        {
            "name": "Python: Current File",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": false,
            "env": {"PYTHONPATH":"${workspaceRoot}"},
            "envFile": "${workspaceRoot}/.env",
        },        
        {
            "name": "Python Debugger: Current File",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": false,
            "purpose": ["debug-in-terminal"],
            "env": {"PYTHONPATH":"${workspaceRoot}"},
            "envFile": "${workspaceRoot}/.env",
        }
    ]
}