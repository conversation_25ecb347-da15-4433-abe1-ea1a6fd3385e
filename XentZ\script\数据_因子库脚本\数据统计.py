#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键数据统计
快速分析FactorZoo数据统计信息
"""

import sys
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from factorzoo.utils.batch_tools import BatchTools

def analyze_batch_trends():
    """分析批次趋势"""
    print("📈 批次趋势分析")
    print("-" * 40)
    
    tools = BatchTools()
    batches = tools.manager.get_available_batches()
    
    if not batches:
        print("❌ 暂无批次数据")
        return
    
    # 按日期统计
    date_stats = {}
    symbol_stats = {}
    stage_stats = {}
    
    for batch_id in batches:
        try:
            parts = batch_id.split('_')
            if len(parts) >= 4:
                symbol = parts[1]
                date_str = parts[2]
                stage = parts[3]
                
                # 日期统计
                if date_str not in date_stats:
                    date_stats[date_str] = 0
                date_stats[date_str] += 1
                
                # 品种统计
                if symbol not in symbol_stats:
                    symbol_stats[symbol] = 0
                symbol_stats[symbol] += 1
                
                # 阶段统计
                if stage not in stage_stats:
                    stage_stats[stage] = 0
                stage_stats[stage] += 1
        except:
            pass
    
    # 显示日期趋势
    print(f"📅 最近7天批次数量:")
    recent_dates = sorted(date_stats.keys(), reverse=True)[:7]
    for date_str in recent_dates:
        count = date_stats[date_str]
        print(f"  {date_str}: {count:>3} 个批次")
    
    # 显示品种分布
    print(f"\n🏷️  品种分布 (前5个):")
    top_symbols = sorted(symbol_stats.items(), key=lambda x: x[1], reverse=True)[:5]
    for symbol, count in top_symbols:
        print(f"  {symbol:<12}: {count:>3} 个批次")
    
    # 显示阶段分布
    print(f"\n📊 阶段分布:")
    for stage in sorted(stage_stats.keys()):
        count = stage_stats[stage]
        print(f"  {stage}: {count:>3} 个批次")

def analyze_storage_usage():
    """分析存储使用情况"""
    print("\n💾 存储使用分析")
    print("-" * 40)
    
    tools = BatchTools()
    batch_dir = Path(tools.manager.config['by_batch_dir'])
    
    if not batch_dir.exists():
        print("❌ 批次目录不存在")
        return
    
    total_size = 0
    file_type_stats = {}
    batch_sizes = []
    
    batches = tools.manager.get_available_batches()
    
    for batch_id in batches:
        batch_path = batch_dir / batch_id
        if batch_path.exists():
            batch_size = 0
            
            for file_path in batch_path.rglob('*'):
                if file_path.is_file():
                    size = file_path.stat().st_size
                    batch_size += size
                    total_size += size
                    
                    # 文件类型统计
                    ext = file_path.suffix.lower()
                    if ext not in file_type_stats:
                        file_type_stats[ext] = {'count': 0, 'size': 0}
                    file_type_stats[ext]['count'] += 1
                    file_type_stats[ext]['size'] += size
            
            if batch_size > 0:
                batch_sizes.append((batch_id, batch_size))
    
    print(f"总存储大小: {total_size / 1024 / 1024 / 1024:.2f} GB")
    print(f"平均批次大小: {(total_size / len(batch_sizes) / 1024 / 1024):.2f} MB")
    
    # 文件类型分布
    print(f"\n📁 文件类型分布:")
    for ext, stats in sorted(file_type_stats.items(), key=lambda x: x[1]['size'], reverse=True):
        size_mb = stats['size'] / 1024 / 1024
        print(f"  {ext or '无扩展名':<10}: {stats['count']:>4} 个文件, {size_mb:>8.2f} MB")
    
    # 最大的批次
    print(f"\n📈 存储占用最大的批次 (前5个):")
    largest_batches = sorted(batch_sizes, key=lambda x: x[1], reverse=True)[:5]
    for batch_id, size in largest_batches:
        size_mb = size / 1024 / 1024
        print(f"  {batch_id}: {size_mb:.2f} MB")

def analyze_factor_distribution():
    """分析因子分布"""
    print("\n🔢 因子分布分析")
    print("-" * 40)
    
    tools = BatchTools()
    batches = tools.manager.get_available_batches()
    
    if not batches:
        print("❌ 暂无批次数据")
        return
    
    total_factors = 0
    stage_factor_counts = {}
    symbol_factor_counts = {}
    
    for batch_id in batches[:20]:  # 分析前20个批次避免太慢
        try:
            batch_info = tools.manager.get_batch_info(batch_id)
            factor_counts = batch_info.get('factor_counts', {})
            
            if factor_counts:
                batch_total = sum(factor_counts.values())
                total_factors += batch_total
                
                # 按阶段统计
                parts = batch_id.split('_')
                if len(parts) >= 4:
                    stage = parts[3]
                    symbol = parts[1]
                    
                    if stage not in stage_factor_counts:
                        stage_factor_counts[stage] = []
                    stage_factor_counts[stage].append(batch_total)
                    
                    if symbol not in symbol_factor_counts:
                        symbol_factor_counts[symbol] = []
                    symbol_factor_counts[symbol].append(batch_total)
        except:
            pass
    
    print(f"总因子数量: {total_factors:,}")
    
    # 按阶段分析
    print(f"\n📊 各阶段因子数量统计:")
    for stage in sorted(stage_factor_counts.keys()):
        counts = stage_factor_counts[stage]
        if counts:
            avg_count = sum(counts) / len(counts)
            max_count = max(counts)
            min_count = min(counts)
            print(f"  {stage}: 平均 {avg_count:.0f}, 最大 {max_count}, 最小 {min_count}")
    
    # 按品种分析
    print(f"\n🏷️  各品种因子数量统计 (前5个):")
    symbol_avgs = {}
    for symbol, counts in symbol_factor_counts.items():
        if counts:
            symbol_avgs[symbol] = sum(counts) / len(counts)
    
    top_symbols = sorted(symbol_avgs.items(), key=lambda x: x[1], reverse=True)[:5]
    for symbol, avg_count in top_symbols:
        print(f"  {symbol:<12}: 平均 {avg_count:.0f} 个因子")

def analyze_data_quality():
    """分析数据质量"""
    print("\n✅ 数据质量分析")
    print("-" * 40)
    
    tools = BatchTools()
    batches = tools.manager.get_available_batches()
    
    if not batches:
        print("❌ 暂无批次数据")
        return
    
    # 检查最近的5个批次
    recent_batches = sorted(batches, reverse=True)[:5]
    
    quality_issues = []
    healthy_batches = 0
    
    for batch_id in recent_batches:
        print(f"\n🔍 检查批次: {batch_id}")
        
        try:
            batch_info = tools.manager.get_batch_info(batch_id)
            
            # 检查基本信息
            if not batch_info.get('symbol'):
                quality_issues.append(f"{batch_id}: 缺少品种信息")
            
            if not batch_info.get('creation_time'):
                quality_issues.append(f"{batch_id}: 缺少创建时间")
            
            factor_counts = batch_info.get('factor_counts', {})
            if not factor_counts:
                quality_issues.append(f"{batch_id}: 缺少因子数量信息")
            else:
                total_factors = sum(factor_counts.values())
                print(f"  ✅ 因子数量: {total_factors}")
                
                if total_factors == 0:
                    quality_issues.append(f"{batch_id}: 因子数量为0")
            
            # 检查文件完整性
            batch_dir = Path(tools.manager.config['by_batch_dir']) / batch_id
            if batch_dir.exists():
                data_files = list(batch_dir.glob('*.parquet')) + list(batch_dir.glob('*.feather'))
                if not data_files:
                    quality_issues.append(f"{batch_id}: 缺少数据文件")
                else:
                    print(f"  ✅ 数据文件: {len(data_files)} 个")
                
                metadata_file = batch_dir / "metadata.json"
                if metadata_file.exists():
                    print(f"  ✅ 元数据文件存在")
                else:
                    quality_issues.append(f"{batch_id}: 缺少元数据文件")
            else:
                quality_issues.append(f"{batch_id}: 批次目录不存在")
            
            if batch_id not in [issue.split(':')[0] for issue in quality_issues]:
                healthy_batches += 1
                print(f"  ✅ 批次健康")
                
        except Exception as e:
            quality_issues.append(f"{batch_id}: 检查时出错 - {str(e)}")
    
    # 汇总质量报告
    print(f"\n📋 质量汇总:")
    print(f"  ✅ 健康批次: {healthy_batches}/{len(recent_batches)}")
    print(f"  ⚠️  发现问题: {len(quality_issues)} 个")
    
    if quality_issues:
        print(f"\n问题详情:")
        for issue in quality_issues[:10]:  # 只显示前10个问题
            print(f"  ❌ {issue}")
        if len(quality_issues) > 10:
            print(f"  ... 及其他 {len(quality_issues) - 10} 个问题")

def main():
    print("📊 FactorZoo数据统计分析")
    print("=" * 50)
    
    try:
        # 执行各种分析
        analyze_batch_trends()
        analyze_storage_usage()
        analyze_factor_distribution()
        analyze_data_quality()
        
        print("\n" + "="*50)
        print("✅ 统计分析完成!")
        print(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")

if __name__ == "__main__":
    main() 