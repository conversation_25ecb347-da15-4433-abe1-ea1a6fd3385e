from .alpha import AlphaBase
import pandas as pd
from typing import List, Optional
from common.cls_base import BaseObj

from tsfresh.feature_extraction import EfficientFCParameters, ComprehensiveFCParameters, MinimalFCParameters


class AlphaTSFresh(AlphaBase):
    """TSFresh特征集 - 一个类搞定所有TSFresh特征"""
    
    def __init__(self, 
                 feature_set: str = 'efficient',  # 'minimal', 'efficient', 'comprehensive'
                 columns: List[str] = None,       # 要提取特征的列，None表示['close']
                 n_jobs: int = 1,                 # 并行进程数
                 prefix: str = 'tsfsh_'):            # 特征前缀
        """
        Args:
            feature_set: TSFresh特征集类型
                - 'minimal': 最少特征，最快速度
                - 'efficient': 高效特征集，平衡速度和质量 
                - 'comprehensive': 全面特征集，最多特征但最慢
            columns: 要处理的数据列，默认只处理close
            n_jobs: 并行进程数
            prefix: 特征名前缀
        """
        self.feature_set = feature_set
        self.columns = columns or ['close']
        self.n_jobs = n_jobs
        self.prefix = prefix
        
        # TSFresh参数映射
        self.fc_parameters = {
            'minimal': MinimalFCParameters(),
            'efficient': EfficientFCParameters(), 
            'comprehensive': ComprehensiveFCParameters()
        }
    
    def get_exprs_names(self):
        """
        返回TSFresh特征的表达式和名称
        注意：这里返回的是"占位符"，实际计算在calc_expr中通过特殊处理
        """
        exprs = []
        names = []
        
        # 为每个列创建一个TSFresh标记表达式
        for col in self.columns:
            # 特殊标记，让calc_expr知道这需要TSFresh处理
            expr = f"TSFRESH_{self.feature_set.upper()}({col})"
            exprs.append(expr)
            
            # 特征名占位符，实际名称由TSFresh自动生成
            name = f"{self.prefix}{col}_TSFRESH_PLACEHOLDER"
            names.append(name)
        
        return exprs, names
    
    def extract_features_for_data(self, df_all: pd.DataFrame) -> pd.DataFrame:
        """
        TSFresh特征提取核心方法（独立实现）
        """
        if df_all.empty:
            BaseObj.log("输入数据为空", level="WARNING")
            return pd.DataFrame()
        
        try:
            from tsfresh import extract_features
            from tsfresh.utilities.dataframe_functions import impute
            
            # 格式处理
            df_work = df_all.copy()
            if 'symbol' not in df_work.columns:
                if hasattr(df_work.index, 'nlevels') and df_work.index.nlevels > 1:
                    df_work = df_work.reset_index()
                    df_work = df_work.rename(columns={df_work.columns[1]: 'symbol'})
                    df_work = df_work.set_index(df_work.columns[0])
                else:
                    df_work['symbol'] = 'SINGLE_ASSET'
            
            # 按symbol分组处理
            results = []
            for symbol in df_work['symbol'].unique():
                symbol_data = df_work[df_work['symbol'] == symbol].copy()
                symbol_data = symbol_data.drop(columns=['symbol'])
                
                # 获取可用的数值列
                available_cols = [col for col in self.columns if col in symbol_data.columns]
                if not available_cols:
                    BaseObj.log(f"品种{symbol}: 无可用列", level="WARNING")
                    continue
                
                # 为每个列提取特征
                symbol_features = []
                for col in available_cols:
                    # 准备tsfresh数据格式
                    temp_df = symbol_data[[col]].reset_index()
                    temp_df['id'] = symbol
                    temp_df = temp_df.rename(columns={col: 'value'})
                    # 获取正确的时间列名
                    time_col = temp_df.columns[0]  # 第一列是时间列
                    temp_df = temp_df[['id', time_col, 'value']]
                    # 提取特征
                    features = extract_features(
                        temp_df,
                        column_id='id',
                        column_sort=time_col,
                        default_fc_parameters=self.fc_parameters[self.feature_set],
                        n_jobs=self.n_jobs
                    )
                    
                    # 重命名特征列
                    features.columns = [f"{self.prefix}{col}__{feat}" for feat in features.columns]
                    symbol_features.append(features)
                    
                # 合并该symbol的所有特征
                if symbol_features:
                    symbol_result = pd.concat(symbol_features, axis=1)
                    # 处理缺失值
                    impute(symbol_result)
                    symbol_result['symbol'] = symbol
                    results.append(symbol_result)
                    symbol_result.to_csv('tssdf.csv'),exit(0)
            
            # 合并所有结果
            if results:
                result = pd.concat(results, ignore_index=True)
                BaseObj.log(f"TSFresh特征提取完成: {result.shape}", level="INFO")
                return result
            else:
                BaseObj.log("没有提取到任何特征", level="WARNING")
                return pd.DataFrame()
                
        except Exception as e:
            BaseObj.log(f"TSFresh特征提取失败: {str(e)}", level="ERROR")
            import traceback
            BaseObj.log(f"详细错误: {traceback.format_exc()}", level="DEBUG")
            return pd.DataFrame()