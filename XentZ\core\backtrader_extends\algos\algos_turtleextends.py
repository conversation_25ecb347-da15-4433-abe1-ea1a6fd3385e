from .algo_base import Algo
from pybroker import ExecContext
import math


class AlgoTurtleExtends(Algo):
    # 海龟增强策略七个参数
    # 参数（longbuyrule）：做多买入规则
    # 参数（longoutrule）：做多止盈规则
    # 参数（shortbuyrule）：做空买入规则
    # 参数（shortoutrule）：做空止盈规则
    # 参数（positionrate）：交易头寸仓位占比
    # 参数（stoplossrate）：止损系数，多少个ATR
    # 参数（maxaddtimes）：最大加仓次数，-1代表不准许加仓；0代表无限加仓；其他正数即最大加仓次数
    def __init__(self, longbuyrule='longbuy', longoutrule='longout', shortbuyrule='shortbuy', shortoutrule='shortout', positionrate:float=0.01, stoplossrate:float=2.0, maxaddtimes=-1):
        super(AlgoTurtleExtends, self).__init__()

        self.__longbuyrule__ = longbuyrule
        self.__longoutrule__ = longoutrule
        self.__shortbuyrule__ = shortbuyrule
        self.__shortoutrule__ = shortoutrule
        self.__maxaddtimes__ = maxaddtimes
        self.__positionrate__ = positionrate
        self.__stoplossrate__ = stoplossrate
        self.__addtimes__ = 0

        if (self.__longbuyrule__ != '') and (self.__longoutrule__ != '') and (not (self.__longbuyrule__ is None)) and (not (self.__longoutrule__ is None)):
            self.__longtrade__ = 1
        else:
            self.__longtrade__ = 0

        if (self.__shortbuyrule__ != '') and (self.__shortoutrule__ != '') and (not (self.__shortbuyrule__ is None)) and (not (self.__shortoutrule__ is None)):
            self.__shorttrade__ = 1
        else:
            self.__shorttrade__ = 0

        self.__tradeprice__ = 0.0

    # 做多建仓
    def dolongopenPosition(self, se, ctx: ExecContext):
        policyrule = None
        #print('-------------------'+self.__longbuyrule__)
        if self.__longbuyrule__ == 'TURTLE_STAND':
            policyrule = se['close'] > se['h_N']
        if self.__longbuyrule__ == 'TURTLE_ROC':
            policyrule = se['roc_20'] > 0.08
        if policyrule is None:
            policyrule = se[self.__longbuyrule__]

        market_value = ctx.total_market_value
        print(market_value)
        ATR = se['atr']
        currdate = str(se['date'])

        if not ctx.long_pos():
            if policyrule:
                shares = math.floor(float(market_value) * self.__positionrate__ / ATR)
                ctx.buy_shares = shares
                self.__addtimes__ = 0
                self.__tradeprice__ = ctx.close[-1]
                print('%s 做多建仓, 买入:%d, 价格:%.2f' % (currdate, shares, self.__tradeprice__))

    # 做多加仓动作执行
    def longAddPostionAction(self, atrvalue:float, market_value:float, openprice:float, currdate:str, ctx: ExecContext):
        shares = math.floor(float(market_value) * self.__positionrate__ / atrvalue)
        ctx.buy_shares = shares
        self.__addtimes__ = self.__addtimes__ + 1
        self.__tradeprice__ = ctx.close[-1]
        print('%s 做多加仓(次数:%d), 买入:%d, 价格:%.2f' % (currdate, self.__addtimes__, shares, self.__tradeprice__))

    # 做多加仓
    def dolongaddPosition(self, se, ctx: ExecContext):
        market_value = ctx.total_market_value
        ATR = se['atr']
        atr_half = 0.5 * ATR
        currdate = str(se['date'])
        open = se['open']
        close = se['close']

        if self.__maxaddtimes__ >= 0:
            if close > self.__tradeprice__ + atr_half:
                if (self.__maxaddtimes__ != 0) and (self.__maxaddtimes__ > self.__addtimes__):
                    self.longAddPostionAction(ATR, float(market_value), open, currdate, ctx)
                else:
                    if self.__maxaddtimes__ == 0:
                        self.longAddPostionAction(ATR, float(market_value), open, currdate, ctx)

    #做多清仓动作执行
    def clearlongPosition(self, ctx: ExecContext, currdate:str, clearmsg:str):
        holding = ctx.long_pos().shares
        ctx.sell_all_shares()
        print('%s %s卖出, 卖出:%d, 价格:%.2f' % (currdate, clearmsg, int(holding), ctx.close[-1]))
        self.__addtimes__ = 0
        self.__tradeprice__ = 0

    # 做多止盈
    def dolongstopProfit(self,se, ctx: ExecContext):
        currdate = str(se['date'])
        if se[self.__longoutrule__]:
            self.clearlongPosition(ctx, currdate, '做多止盈')

    # 做多止损
    def dolongstopLoss(self, se, ctx: ExecContext):
        currdate = str(se['date'])
        close = se['close']
        ATR = se['atr']
        if close < self.__tradeprice__ - self.__stoplossrate__ * ATR:
            self.clearlongPosition(ctx, currdate, '做多止损')

    # 做空建仓
    def doshortopenPosition(self, se, ctx: ExecContext):
        policyrule = None
        if self.__shortbuyrule__ == 'TURTLE_STAND':
            policyrule = se['close'] < se['l_N']
        if self.__shortbuyrule__ == 'TURTLE_ROC':
            policyrule = se['roc_20'] < -0.08

        if policyrule is None:
            policyrule = se[self.__shortbuyrule__]

        market_value = ctx.total_market_value
        ATR = se['atr']
        currdate = str(se['date'])

        if not ctx.short_pos():
            if policyrule:
                shares = math.floor(float(market_value) * self.__positionrate__ / ATR)
                ctx.sell_shares = shares
                self.__addtimes__ = 0
                self.__tradeprice__ = ctx.close[-1]
                print('%s 做空建仓, 卖出:%d, 价格:%.2f' % (currdate, shares, self.__tradeprice__))

    # 做空加仓动作执行
    def shortAddPostionAction(self, atrvalue: float, market_value: float, closeprice: float, currdate: str,
                                 ctx: ExecContext):
        shares = math.floor(float(market_value) * self.__positionrate__ / atrvalue)
        ctx.sell_shares = shares
        self.__addtimes__ = self.__addtimes__ + 1
        self.__tradeprice__ = ctx.close[-1]
        print('%s 做空加仓(次数:%d), 卖出:%d, 价格:%.2f' % (currdate, self.__addtimes__, shares, self.__tradeprice__))

    # 做空加仓
    def doshortaddPosition(self,  se, ctx: ExecContext):
        market_value = ctx.total_market_value
        ATR = se['atr']
        atr_half = 0.5 * ATR
        currdate = str(se['date'])
        close = se['close']

        if self.__maxaddtimes__ >= 0:
            if close < self.__tradeprice__ - atr_half:
                if (self.__maxaddtimes__ != 0) and (self.__maxaddtimes__ > self.__addtimes__):
                    self.shortAddPostionAction(ATR, float(market_value), close, currdate, ctx)
                else:
                    if self.__maxaddtimes__ == 0:
                        self.shortAddPostionAction(ATR, float(market_value), close, currdate, ctx)

    # 做空清仓动作执行
    def clearshortPosition(self, ctx: ExecContext, currdate: str, clearmsg: str):
        holding = ctx.short_pos().shares
        ctx.cover_all_shares()
        print('%s %s卖出, 卖出:%d, 价格:%.2f' % (currdate, clearmsg, int(holding), ctx.close[-1]))
        self.__addtimes__ = 0
        self.__tradeprice__ = 0

    # 做空止盈
    def doshortstopProfit(self,se, ctx: ExecContext):
        currdate = str(se['date'])
        if se[self.__shortoutrule__]:
            self.clearshortPosition(ctx, currdate, "做空止盈")

    # 做空止损
    def doshortstopLoss(self, se, ctx: ExecContext):
        currdate = str(se['date'])
        close = se['close']
        ATR = se['atr']
        if close > self.__tradeprice__ + self.__stoplossrate__ * ATR:
            self.clearshortPosition(ctx, currdate, "做空止损")

    def exec(self, se, ctx: ExecContext):
        if self.__longtrade__ == 1:
            # 做多建仓
            if not ctx.long_pos():
                self.dolongopenPosition(se, ctx)

            if ctx.long_pos():
                # 做多加仓
                self.dolongaddPosition(se, ctx)

                # 做多止盈
                self.dolongstopProfit(se, ctx)

                # 做多止损
                self.dolongstopLoss(se, ctx)

        if self.__shorttrade__ == 1:
            # 做空建仓
            if not ctx.short_pos():
                self.doshortopenPosition(se, ctx)

            if ctx.short_pos():
                # 做空加仓
                self.doshortaddPosition(se, ctx)

                # 做空止盈
                self.doshortopenPosition(se, ctx)

                # 做空止损
                self.doshortstopLoss(se, ctx)

    def __call__(self, target):
        df_bar = target.df_bar
        #print(df_bar)
        for symbol, ctx in target.ctxs.items():
            self.exec(df_bar.loc[symbol], ctx)
