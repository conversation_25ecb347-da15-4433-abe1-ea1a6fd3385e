from abc import ABC, abstractmethod
import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent.parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
    
from jzal_pro.cls_common import BaseObj

class ComboGenerator(ABC,BaseObj):
    """
    抽象基类，定义了组合生成算法的接口
    """
    def __init__(self):
        pass
    @abstractmethod
    def generate(self, **kwargs):
        pass
class ComboMgr:
    """
    管理器类，负责选择和应用具体的组合生成策略
    """
    def __init__(self, strategy: ComboGenerator):
        self._strategy = strategy

    def set_strategy(self, strategy: ComboGenerator):
        self._strategy = strategy

    def gen_best_combos(self, *args, **kwargs):
        return self._strategy.generate(*args, **kwargs)