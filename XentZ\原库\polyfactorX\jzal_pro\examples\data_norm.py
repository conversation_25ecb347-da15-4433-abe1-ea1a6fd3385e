import numpy as np
import matplotlib.pyplot as plt
from sklearn.svm import SVC
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.datasets import make_classification
from sklearn.metrics import accuracy_score
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error

''' 归一化: 最小最大法 -- 需要严格边界的一堆数'''
def norm_min_max():  
    # 生成两组不同尺度的数据
    data_small_scale = np.random.randint(0, 100, 100)  # 范围在0到100
    data_large_scale = np.random.randint(1000, 10000, 100)  # 范围在1000到10000

    # 最小-最大归一化
    def min_max_normalize(data: np.ndarray) ->np.ndarray:
        return (data - np.min(data)) / (np.max(data) - np.min(data))

    # 应用最小-最大归一化
    normalized_small_scale = min_max_normalize(data_small_scale)
    normalized_large_scale = min_max_normalize(data_large_scale)

    # 可视化对比
    plt.figure(figsize=(12, 6))

    # 绘制原始数据的直方图
    plt.subplot(221)
    plt.hist(data_small_scale, bins=15, color='blue', alpha=0.7)
    plt.title('Small Scale Data')
    plt.xlabel('Value')
    plt.ylabel('Frequency')

    plt.subplot(222)
    plt.hist(data_large_scale, bins=15, color='red', alpha=0.7)
    plt.title('Large Scale Data')
    plt.xlabel('Value')
    plt.ylabel('Frequency')

    # 绘制归一化数据的直方图
    plt.subplot(223)
    plt.hist(normalized_small_scale, bins=15, color='green', alpha=0.7)
    plt.title('Normalized Small Scale Data')
    plt.xlabel('Normalized Value')
    plt.ylabel('Frequency')

    plt.subplot(224)
    plt.hist(normalized_large_scale, bins=15, color='orange', alpha=0.7)
    plt.title('Normalized Large Scale Data')
    plt.xlabel('Normalized Value')
    plt.ylabel('Frequency')

    plt.tight_layout()
    plt.show()

''' 标准化: zscore -- 更适合没有明确边界的情况, 可以处理具有异常值的特征''' 
def norm_zscore():   
    # 生成虚拟数据集
    ''' 没有指定n_classes, 默认是二分类问题 '''
    X, y = make_classification(n_samples=1000, n_features=2, n_redundant=0, n_clusters_per_class=1, random_state=42)

    # 划分数据集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    # 未标准化的SVM模型
    model = SVC(kernel='linear')
    model.fit(X_train, y_train)
    y_pred = model.predict(X_test)
    accuracy_non_standardized = accuracy_score(y_test, y_pred)

    # 应用标准化
    scaler = StandardScaler()
    X_train_standardized = scaler.fit_transform(X_train)
    X_test_standardized = scaler.transform(X_test)

    # 标准化的SVM模型
    model_standardized = SVC(kernel='linear')
    model_standardized.fit(X_train_standardized, y_train)
    y_pred_standardized = model_standardized.predict(X_test_standardized)
    accuracy_standardized = accuracy_score(y_test, y_pred_standardized)

    # 结果展示
    print(f'Accuracy with non-standardized data: {accuracy_non_standardized}')
    print(f'Accuracy with standardized data: {accuracy_standardized}')

    # 可视化
    plt.figure(figsize=(12, 5))

    # 绘制未标准化数据
    plt.subplot(1, 2, 1)
    plt.scatter(X_train[:, 0], X_train[:, 1], c=y_train)
    plt.title("Distribution of Non-Standardized Data")
    plt.xlabel("Feature 1")
    plt.ylabel("Feature 2")

    # 绘制标准化数据
    plt.subplot(1, 2, 2)
    plt.scatter(X_train_standardized[:, 0], X_train_standardized[:, 1], c=y_train)
    plt.title("Distribution of Standardized Data")
    plt.xlabel("Feature 1")
    plt.ylabel("Feature 2")

    plt.show()
    
''' 归一化: 小数定标法, 绝对值最大数移动小数位置到指定范围内'''
def norm_decimal_scale():
    # 生成虚拟数据集
    np.random.seed(0)
    X = np.random.rand(100, 1) * 100  # 随机生成0-100之间的数
    y = 3 * X.squeeze() + 4 + np.random.randn(100)  # 线性关系加上噪声

    # 定义小数定标归一化函数
    def decimal_scaling_normalization(data):
        k = np.ceil(np.log10(np.max(np.abs(data))))
        return data / (10 ** k)

    # 应用归一化
    X_normalized = decimal_scaling_normalization(X)

    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=0)
    X_train_norm, X_test_norm = train_test_split(X_normalized, test_size=0.2, random_state=0)

    # 训练模型
    model = LinearRegression()
    model.fit(X_train, y_train)
    model_norm = LinearRegression()
    model_norm.fit(X_train_norm, y_train)

    # 预测和评估
    y_pred = model.predict(X_test)
    y_pred_norm = model_norm.predict(X_test_norm)

    mse_original = mean_squared_error(y_test, y_pred)
    mse_normalized = mean_squared_error(y_test, y_pred_norm)

    # 结果可视化
    plt.figure(figsize=(12, 5))
    plt.subplot(1, 2, 1)
    plt.scatter(X_test, y_test, color='blue')
    plt.plot(X_test, y_pred, color='red')
    plt.title(f"Original Data (MSE: {mse_original:.2f})")

    plt.subplot(1, 2, 2)
    plt.scatter(X_test_norm, y_test, color='green')
    plt.plot(X_test_norm, y_pred_norm, color='red')
    plt.title(f"Normalized Data (MSE: {mse_normalized:.2f})")

    plt.show()

''' 归一化: 单位长度归一, 除以欧式距离, 特征值按长度缩放; 一定程度改变了数据分布'''    
def norm_linalg():
    # 创建虚拟数据集
    np.random.seed(0)
    X = np.random.rand(100, 3)  # 100个样本，3个特征
    y = X @ np.array([1.5, -2.0, 1.0]) + np.random.randn(100) * 0.5  # 目标变量

    # 单位长度归一化
    norm_X = X / np.linalg.norm(X, axis=1, keepdims=True)

    # 分割数据集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    norm_X_train, norm_X_test = train_test_split(norm_X, test_size=0.2, random_state=42)

    # 训练模型
    model = LinearRegression()
    model.fit(X_train, y_train)
    norm_model = LinearRegression()
    norm_model.fit(norm_X_train, y_train)

    # 预测和评估
    y_pred = model.predict(X_test)
    norm_y_pred = norm_model.predict(norm_X_test)

    mse = mean_squared_error(y_test, y_pred)
    norm_mse = mean_squared_error(y_test, norm_y_pred)

    # 结果比较
    print("MSE without normalization:", mse)
    print("MSE with unit length normalization:", norm_mse)

    # 绘制结果
    plt.figure(figsize=(18, 6))

    # 预测值与实际值对比
    plt.subplot(1, 3, 1)
    plt.scatter(y_test, y_pred, label='Without Normalization')
    plt.scatter(y_test, norm_y_pred, color='r', label='With Normalization')
    plt.title("Predictions vs True Values")
    plt.xlabel("True Values")
    plt.ylabel("Predictions")
    plt.legend()

    # 预测误差分布
    plt.subplot(1, 3, 2)
    plt.hist(y_test - y_pred, bins=15, alpha=0.7, label='Without Normalization')
    plt.hist(y_test - norm_y_pred, bins=15, alpha=0.7, color='r', label='With Normalization')
    plt.title("Prediction Error Distribution")
    plt.xlabel("Prediction Error")
    plt.ylabel("Frequency")
    plt.legend()

    # 特征分布对比
    plt.subplot(1, 3, 3)
    for i in range(X.shape[1]):
        plt.hist(X[:, i], bins=15, alpha=0.5, label=f'Feature {i+1} Original')
        plt.hist(norm_X[:, i], bins=15, alpha=0.5, color='r', label=f'Feature {i+1} Normalized')
    plt.title("Feature Distributions")
    plt.xlabel("Feature Value")
    plt.ylabel("Frequency")
    plt.legend()

    plt.tight_layout()
    plt.show()