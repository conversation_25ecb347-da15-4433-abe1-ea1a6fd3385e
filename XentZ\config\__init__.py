"""
XentZ 配置管理 - 统一配置入口

🎯 简化的配置体系设计：
- 框架配置 (Framework Config): 项目基础设施配置，全局稳定
- 任务配置 (Task Config): 通过 cfg_* 对象直接访问，简洁高效

使用方式:
    from config import settings, ROOT_DIR    # 导入框架配置和根目录
    from config import cfg_wfa, cfg_mine     # 导入任务配置对象

    # 直接访问任务配置:
    wfa_params = cfg_wfa.wfa
    criteria = cfg_wfa.criteria
"""
from dynaconf import Dynaconf
from pathlib import Path
import os

# ============================== 项目根目录和环境检测 ============================== #
# 获取项目根目录 (XentZ/)
ROOT_DIR = Path(__file__).parent.parent

# 环境检测：支持开发、测试、生产环境
ENV = os.getenv('XENTZ_ENV', 'development')

# ============================== 框架配置加载 ============================== #
# 实例化 Dynaconf，加载框架级应用配置
# 这些是项目基础设施配置：路径、数据库、缓存、计算资源等
settings = Dynaconf(
    envvar_prefix="XENTZ",
    root_path=str(ROOT_DIR),
    settings_files=[
        str(ROOT_DIR / "config/app/settings.toml"),
        str(ROOT_DIR / "config/app/.secrets.toml"),  # 可选的敏感信息
    ],
    load_dotenv=True,
    environments=False,  # 启用环境分层
    env_switcher="XENTZ_ENV",
    default_env="development",
    encoding="utf-8",
    silent=True,
)

# ============================== 路径配置 ============================== #
REPORTS_DIR = Path(settings.paths.reports_root)
DATA_DIR = Path(settings.paths.data_root)

# 确保关键目录存在
for dir_path in [REPORTS_DIR, DATA_DIR]:
    dir_path.mkdir(parents=True, exist_ok=True)


# ============================== 任务配置对象导入 ============================== #
# 导入cfg_utils中的配置对象和工具函数
def _get_task_configs():
    """加载任务配置对象和工具函数"""
    try:
        from .cfg_utils import (
            cfg_feat, cfg_mine, cfg_wfa, get_norm_params, mine_norm_params_X,
            mine_gparams, mine_runnum, fitness_params, sr_params, rankic_params
        )
        return {
            'cfg_feat': cfg_feat,
            'cfg_mine': cfg_mine,
            'cfg_wfa': cfg_wfa,
            'get_norm_params': get_norm_params,
            'mine_norm_params_X': mine_norm_params_X,
            'mine_gparams': mine_gparams,
            'mine_runnum': mine_runnum,
            'fitness_params': fitness_params,
            'sr_params': sr_params,
            'rankic_params': rankic_params
        }
    except ImportError as e:
        print(f"警告: 无法导入任务配置对象: {e}")
        return {}

# 动态添加任务配置对象到模块命名空间
_legacy_configs = _get_task_configs()
globals().update(_legacy_configs)

# ============================== 模块导出 ============================== #
__all__ = [
    # 核心配置
    'settings',           # dynaconf全局设置
    'ROOT_DIR',          # 项目根目录
    'ENV',               # 当前环境
    'REPORTS_DIR',       # 报告输出目录
    'DATA_DIR',          # 数据目录
] + list(_legacy_configs.keys())  # 动态添加任务配置对象
