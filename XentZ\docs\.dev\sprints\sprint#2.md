# 任务
mystar中增加ind部件,名字为'斜率年化乘动量除波动率'
# 参照
1. '斜率年化乘R2'部件的实现方式
2. 算法逻辑参照大qmt中的函数:
def calculate_etf_scores(etf_pool, mkdict, lookback_window=63):
	"""
	优化后的ETF评分函数
	:param etf_pool: ETF列表
	:param mkdict: 包含各ETF历史数据的字典
	:param lookback_window: 观察窗口(默认63个交易日，约3个月)
	:return: 排序后的ETF评分DataFrame
	"""
	score_list = []
	valid_etfs = []
	
	for etf in etf_pool:
		try:
			#df = mkdict[etf].tail(63)  # 使用固定时间窗口
			df = mkdict[etf]
			# 数据质量检查
			if len(df) < lookback_window * 0.8:  # 允许20%数据缺失
				continue
				
			# 使用实际时间序列（处理非交易日问题）
			x = pd.to_numeric(df.index).values.reshape(-1, 1)
			y = np.log(df['close'].values)
			
			# 稳健线性回归（处理异常值）
			slope, intercept, r_value, _, _ = stats.linregress(x.flatten(), y)
			
			# 年化收益率计算优化
			daily_growth = np.exp(slope) - 1
			annualized_returns = (1 + daily_growth) ** 252 - 1  # 使用实际交易日数
			
			# 改进的R2计算
			y_pred = slope * x.flatten() + intercept
			r_squared = r2_score(y, y_pred)
			
			# 动态权重调整（波动率调整）
			volatility = np.std(np.diff(y)) * np.sqrt(252)
			risk_adjusted_return = annualized_returns / (volatility + 1e-6)  # 防止除零
			
			# 综合评分（加入动量因子）
			momentum = df['close'].pct_change(21).iloc[-1]  # 1个月动量
			score = (risk_adjusted_return * r_squared) + (0.3 * momentum)
			
			score_list.append(score)
			valid_etfs.append(etf)
			
		except Exception as e:
			log.info(f"Error processing {etf}: {str(e)}")
			continue

	# 创建评分DataFrame
	df_score = pd.DataFrame(index=valid_etfs, data={'score': score_list})
	
	# 分数标准化
	df_score['score'] = (df_score['score'] - df_score['score'].mean()) / df_score['score'].std()
	df_score = df_score.sort_values(by='score', ascending=False)
	
	return df_score

# 测试
part.py中提供简单测试代码
