# 配置管理

## 1. 核心理念：统一入口，分层管理

新的配置系统旨在提供一个**统一、便捷的配置访问入口**，同时保持**框架配置**和**任务参数**的逻辑分离。

-   **框架应用配置 (Framework/App Config)**:
    -   **内容**: 定义项目的核心基础设施，如数据路径、数据库连接、日志级别、缓存设置等。
    -   **特点**: 全局共享、高度稳定、低频修改。
    -   **文件**: `config/app/settings.toml`。

-   **任务参数配置 (Task Config)**:
    -   **内容**: 定义具体研究任务的参数，如模型超参、特征列表、回测时间范围等。
    -   **特点**: 任务专属、灵活易变、高频修改。
    -   **文件**: `config/tasks/*.toml` (例如 `ts_gplearn.toml`)。

## 2. 目录结构

```
config/
├── __init__.py           # 统一配置入口 (暴露 settings, cfg_*, 工具函数)
├── cfg_utils.py          # 任务配置加载和处理工具
│
├── app/
│   ├── settings.toml     # 框架应用配置主文件 (路径, DB, 缓存)
│   └── .secrets.toml     # (可选) 密钥等敏感信息
│
└── tasks/
    ├── _datasets/
    │   ├── ts_single_etf.toml      # 数据集定义
    │   └── ts_multi_etf_pool.toml
    │
    ├── ts_feat.toml                # 特征工程任务配置
    └── ts_gplearn.toml             # GP因子挖掘任务配置
```

-   **`config/__init__.py`**: 项目中**唯一的配置访问入口**。
-   **`config/cfg_utils.py`**: 加载并处理所有任务级配置，提供便捷的工具函数。
-   **`config/app/`**: 存放**框架级**配置。
-   **`config/tasks/`**: **扁平化**存放所有**任务级**配置文件。

## 3. 统一访问入口：`config` 模块

无论你需要框架配置还是任务参数，都通过 `config` 模块进行访问。

```python
# 访问框架级配置
from config import settings
db_uri = settings.database.uri

# 访问任务级配置对象 (以因子挖掘为例)
from config import cfg_mine
population_size = cfg_mine.gparams.population_size

# 访问处理后的任务参数 (推荐方式)
from config import mine_gparams, rankic_params
gp_params = mine_gparams()      # 获取已根据 test/live 模式合并的GP参数
rankic = rankic_params()      # 获取RankIC筛选的全部参数
```

## 4. 实现策略

### 4.1. 框架配置加载 (`config/__init__.py`)

-   `config/__init__.py` 是配置系统的**唯一初始化入口**。
-   它负责加载 `config/app/settings.toml` 到一个名为 `settings` 的全局 `Dynaconf` 对象中。
-   这个 `settings` 对象在整个项目中都可以通过 `from config import settings` 访问。

### 4.2. 任务配置加载与处理 (`config/cfg_utils.py`)

-   `config/cfg_utils.py` 是所有**任务配置的"加工厂"**。
-   它**预先加载**了核心的任务配置文件（如 `ts_feat.toml`, `ts_gplearn.toml`）到专属的 `Dynaconf` 对象中（如 `cfg_feat`, `cfg_mine`）。
-   它还提供了大量**工具函数**（如 `mine_gparams`, `rankic_params`），用于处理复杂的配置逻辑，例如：
    -   根据 `workflow.mode` 动态合并 `test` 和 `live` 环境的参数。
    -   将配置项转换为特定格式（如列表转元组）。
    -   将 `DynaBox` 对象安全地转换为普通的Python字典，避免下游出错。
    -   将多个相关参数打包成一个字典，方便函数解包 `**kwargs`。

### 4.3. 统一暴露 (`config/__init__.py`)

-   `config/__init__.py` 使用 `__all__` 机制，将 `settings` 对象、任务配置对象（`cfg_feat`, `cfg_mine`）以及所有来自 `cfg_utils.py` 的工具函数**全部暴露出来**。
-   这使得使用者无需关心配置的具体来源文件，只需从顶层的 `config` 模块导入即可。

## 5. 推荐使用模式

**场景：在一个新的研究脚本中获取因子挖掘参数**

```python
# my_new_research.py

# 统一从 config 模块导入所需的一切
from config import (
    settings,           # 框架配置
    cfg_mine,           # 原始的任务配置对象 (用于访问单个值)
    mine_gparams,       # 获取处理好的GP引擎参数
    fitness_params      # 获取适应度函数所需参数
)

def main():
    # 1. 获取框架配置
    factor_zoo_path = settings.get("factor_zoo.db_path")
    print(f"因子库路径: {factor_zoo_path}")

    # 2. 获取原始任务配置
    # 比如获取当前的工作流模式
    current_mode = cfg_mine.workflow.mode
    print(f"当前运行模式: {current_mode}")

    # 3. 使用工具函数获取处理好的、可直接使用的参数包 (推荐)
    # 这会自动处理 test/live 模式切换和数据类型转换
    gp_engine_params = mine_gparams()
    print(f"GP引擎种群数量: {gp_engine_params['population_size']}")
    
    # 4. 获取为特定函数准备的参数字典
    # 可以直接用于函数调用
    fit_params = fitness_params()
    # some_fitness_func(y, y_pred, **fit_params)

if __name__ == "__main__":
    main()
```

## 6. 最佳实践

-   **优先使用工具函数**: 尽量通过 `config` 模块提供的工具函数（如 `mine_gparams`）获取参数，而不是直接访问 `cfg_mine`。这能让你享受到动态切换、类型转换等便利，并隔离底层的配置细节。
-   **配置分离**: 严格区分一个参数应该属于框架 (`app/settings.toml`) 还是任务 (`tasks/*.toml`)。
-   **`include` 优于复制**: 对于共享的配置块（尤其是数据集），始终使用 `dynaconf_include`。
-   **注释清晰**: 为 TOML 文件中的每个配置块和关键参数提供简明扼要的注释。
-   **版本控制**: 将所有 `.toml` 文件（除了存放密钥的 `.secrets.toml`）纳入 Git 版本控制，实现研究的可复现性。 