#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FactorZoo格式转换工具
支持Feather、<PERSON>rquet、CSV之间的相互转换
"""

import pandas as pd
import os
import sys
from pathlib import Path
from typing import Optional, List

class FormatConverter:
    """数据格式转换器"""
    
    SUPPORTED_FORMATS = ['.feather', '.parquet', '.csv']
    
    def __init__(self):
        self.stats = {
            'converted': 0,
            'failed': 0,
            'skipped': 0
        }
    
    def convert_file(self, input_path: str, output_path: str = None, 
                    target_format: str = 'csv', **kwargs) -> bool:
        """转换单个文件"""
        input_path = Path(input_path)
        
        if not input_path.exists():
            print(f"❌ 输入文件不存在: {input_path}")
            return False
        
        # 自动生成输出路径
        if output_path is None:
            output_path = input_path.with_suffix(f'.{target_format.lower()}')
        else:
            output_path = Path(output_path)
        
        # 检查是否需要转换
        if input_path.suffix.lower() == f'.{target_format.lower()}':
            print(f"⚠️  跳过: {input_path.name} (已是目标格式)")
            self.stats['skipped'] += 1
            return True
        
        try:
            print(f"🔄 转换: {input_path.name} → {output_path.name}")
            
            # 读取文件
            df = self._load_file(input_path)
            
            # 保存为目标格式
            self._save_file(df, output_path, target_format, **kwargs)
            
            # 显示转换信息
            input_size = input_path.stat().st_size / 1024 / 1024
            output_size = output_path.stat().st_size / 1024 / 1024
            compression_ratio = (1 - output_size / input_size) * 100 if input_size > 0 else 0
            
            print(f"   ✅ 成功! {input_size:.2f}MB → {output_size:.2f}MB "
                  f"(压缩率: {compression_ratio:+.1f}%)")
            
            self.stats['converted'] += 1
            return True
            
        except Exception as e:
            print(f"   ❌ 失败: {str(e)}")
            self.stats['failed'] += 1
            return False
    
    def _load_file(self, file_path: Path) -> pd.DataFrame:
        """加载文件"""
        ext = file_path.suffix.lower()
        
        if ext == '.feather':
            return pd.read_feather(file_path)
        elif ext == '.parquet':
            return pd.read_parquet(file_path)
        elif ext == '.csv':
            # 尝试不同的CSV读取方式
            try:
                return pd.read_csv(file_path, index_col=0)
            except:
                return pd.read_csv(file_path)
        else:
            raise ValueError(f"不支持的输入格式: {ext}")
    
    def _save_file(self, df: pd.DataFrame, file_path: Path, 
                   target_format: str, **kwargs):
        """保存文件"""
        target_format = target_format.lower()
        
        if target_format == 'feather':
            # Feather需要重置索引
            df_to_save = df.reset_index()
            df_to_save.to_feather(file_path)
            
        elif target_format == 'parquet':
            compression = kwargs.get('compression', 'snappy')
            df.to_parquet(file_path, compression=compression, index=True)
            
        elif target_format == 'csv':
            encoding = kwargs.get('encoding', 'utf-8-sig')
            df.to_csv(file_path, index=True, encoding=encoding)
            
        else:
            raise ValueError(f"不支持的输出格式: {target_format}")
    
    def batch_convert_directory(self, input_dir: str, target_format: str = 'csv',
                               recursive: bool = False, **kwargs):
        """批量转换目录中的文件"""
        input_dir = Path(input_dir)
        
        if not input_dir.exists():
            print(f"❌ 目录不存在: {input_dir}")
            return
        
        print(f"📁 批量转换目录: {input_dir}")
        print(f"🎯 目标格式: {target_format.upper()}")
        print("=" * 60)
        
        # 查找支持的文件
        pattern = "**/*" if recursive else "*"
        all_files = []
        
        for ext in self.SUPPORTED_FORMATS:
            all_files.extend(input_dir.glob(f"{pattern}{ext}"))
        
        if not all_files:
            print("❌ 未找到支持的文件")
            return
        
        print(f"📋 发现 {len(all_files)} 个文件")
        print("-" * 60)
        
        # 逐个转换
        for file_path in sorted(all_files):
            self.convert_file(str(file_path), target_format=target_format, **kwargs)
        
        # 显示统计
        self._show_stats()
    
    def convert_factorzoo_batch(self, batch_id: str, target_format: str = 'csv'):
        """转换FactorZoo批次数据"""
        try:
            from factorzoo.factor_value_manager import get_factor_value_manager
            manager = get_factor_value_manager()
            
            batch_dir = Path(manager.config['by_batch_dir']) / batch_id
            
            if not batch_dir.exists():
                print(f"❌ 批次目录不存在: {batch_id}")
                return
            
            print(f"🏦 转换FactorZoo批次: {batch_id}")
            print(f"🎯 目标格式: {target_format.upper()}")
            print("=" * 60)
            
            # 创建转换目录
            convert_dir = batch_dir / f"converted_{target_format}"
            convert_dir.mkdir(exist_ok=True)
            
            # 转换所有数据文件
            data_files = []
            for ext in self.SUPPORTED_FORMATS:
                data_files.extend(batch_dir.glob(f"*{ext}"))
            
            for file_path in sorted(data_files):
                output_path = convert_dir / f"{file_path.stem}.{target_format}"
                self.convert_file(str(file_path), str(output_path), target_format)
            
            # 复制元数据文件
            metadata_file = batch_dir / "metadata.json"
            if metadata_file.exists():
                import shutil
                shutil.copy2(metadata_file, convert_dir / "metadata.json")
                print(f"📄 复制元数据文件: metadata.json")
            
            self._show_stats()
            print(f"\n📁 转换结果保存在: {convert_dir}")
            
        except ImportError:
            print("❌ 无法导入FactorValueManager，请确保在项目环境中运行")
        except Exception as e:
            print(f"❌ 转换失败: {str(e)}")
    
    def _show_stats(self):
        """显示转换统计"""
        total = sum(self.stats.values())
        if total == 0:
            return
        
        print("\n" + "=" * 60)
        print("📊 转换统计:")
        print(f"   ✅ 成功: {self.stats['converted']} 个")
        print(f"   ❌ 失败: {self.stats['failed']} 个")
        print(f"   ⚠️  跳过: {self.stats['skipped']} 个")
        print(f"   📈 成功率: {self.stats['converted']/total*100:.1f}%")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='FactorZoo格式转换工具')
    parser.add_argument('action', choices=['file', 'dir', 'batch'], 
                       help='转换类型：单文件/目录/FactorZoo批次')
    parser.add_argument('input_path', help='输入路径')
    parser.add_argument('--format', '-f', default='csv', 
                       choices=['csv', 'parquet', 'feather'],
                       help='目标格式 (默认: csv)')
    parser.add_argument('--output', '-o', help='输出路径')
    parser.add_argument('--recursive', '-r', action='store_true', 
                       help='递归处理子目录')
    parser.add_argument('--compression', '-c', default='snappy',
                       choices=['snappy', 'gzip', 'brotli'],
                       help='Parquet压缩算法 (默认: snappy)')
    parser.add_argument('--encoding', '-e', default='utf-8-sig',
                       help='CSV编码格式 (默认: utf-8-sig)')
    
    args = parser.parse_args()
    
    print("🔄 FactorZoo格式转换工具")
    print("=" * 50)
    
    converter = FormatConverter()
    
    try:
        if args.action == 'file':
            converter.convert_file(
                args.input_path, 
                args.output, 
                args.format,
                compression=args.compression,
                encoding=args.encoding
            )
        
        elif args.action == 'dir':
            converter.batch_convert_directory(
                args.input_path,
                args.format,
                args.recursive,
                compression=args.compression,
                encoding=args.encoding
            )
        
        elif args.action == 'batch':
            converter.convert_factorzoo_batch(args.input_path, args.format)
        
        converter._show_stats()
        
    except Exception as e:
        print(f"❌ 执行出错: {str(e)}")

if __name__ == "__main__":
    main() 