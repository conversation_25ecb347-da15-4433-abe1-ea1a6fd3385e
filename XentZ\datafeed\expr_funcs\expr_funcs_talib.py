import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent.parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))

import pandas as pd
import numpy as np
import talib

from datafeed.expr_funcs.expr_unary_rolling import *

def bbands_up(close, timeperiod=20, nbdevup=2, nbdevdn=2):
    upper_band, middle_band, lower_band = talib.BBANDS(close.astype(np.float64), timeperiod=timeperiod, nbdevup=nbdevup, nbdevdn=nbdevdn)
    return upper_band

def bbands_mid(close, timeperiod=20, nbdevup=2, nbdevdn=2):
    upper_band, middle_band, lower_band = talib.BBANDS(close.astype(np.float64), timeperiod=timeperiod, nbdevup=nbdevup, nbdevdn=nbdevdn)
    return middle_band

def bbands_dn(close, timeperiod=20, nbdevup=2, nbdevdn=2):
    upper_band, middle_band, lower_band = talib.BBANDS(close.astype(np.float64), timeperiod=timeperiod, nbdevup=nbdevup, nbdevdn=nbdevdn)
    return lower_band

def sar_index(high, low, acceleration=0.02, maximum=0.2):
    return talib.SAR(high.astype(np.float64), low.astype(np.float64), acceleration=acceleration, maximum=maximum)

def sar_r(sar_index, close):
    return (sar_index - close)/close

def aroon(high, low, timeperiod=14):
    return talib.AROONOSC(high.astype(np.float64), low.astype(np.float64), timeperiod)

def cci(high, low, close, timeperiod=14):
    return talib.CCI(high.astype(np.float64), low.astype(np.float64), close.astype(np.float64), timeperiod)

def cmo(se, timeperiod=14): 
    return talib.CMO(se.astype(np.float64), timeperiod)

def mfi(high, low, close, volume, timeperiod = 14):
    return talib.MFI(high.astype(np.float64), low.astype(np.float64), close.astype(np.float64), volume.astype(np.float64), timeperiod)

def mom(se, timeperiod=14):
    return talib.MOM(se.astype(np.float64), timeperiod)

def ppo(se, fastperiod=12, slowperiod=26, matype=0):
    return talib.PPO(se.astype(np.float64), fastperiod=fastperiod, slowperiod=slowperiod, matype=matype)

def ad_index(high, low, close, volume):
    return talib.AD(high.astype(np.float64), low.astype(np.float64), close.astype(np.float64), volume.astype(np.float64))

def adosc(high, low, close, volume, fastperiod=3, slowperiod=10):
    return talib.ADOSC(high.astype(np.float64), low.astype(np.float64), close.astype(np.float64), volume.astype(np.float64), fastperiod=fastperiod, slowperiod=slowperiod)

def obv(close, volume):
    return talib.OBV(close.astype(np.float64), volume.astype(np.float64))

def atr(high, low, close, timeperiod=14):
    return talib.ATR(high.astype(np.float64), low.astype(np.float64), close.astype(np.float64), timeperiod)

def tr_index(high, low, close):
    return talib.TRANGE(high.astype(np.float64), low.astype(np.float64), close.astype(np.float64))

def tr_ma(se, close, timeperiod=14, matype=0):
    return talib.MA(se.astype(np.float64), timeperiod, matype)/close

def kdj_k(high, low, close, fastk_period=9, slowk_period=5, slowk_matype=1,slowd_period=5, slowd_matype=1):
    k,d =talib.STOCH(high.astype(np.float64), low.astype(np.float64), close.astype(np.float64), fastk_period=fastk_period, slowk_period=slowk_period, slowk_matype=slowk_matype,slowd_period=slowd_period, slowd_matype=slowd_matype)
    return k

def kdj_d(high, low, close, fastk_period=9, slowk_period=5, slowk_matype=1,slowd_period=5, slowd_matype=1):
    k,d =talib.STOCH(high.astype(np.float64), low.astype(np.float64), close.astype(np.float64), fastk_period=fastk_period, slowk_period=slowk_period, slowk_matype=slowk_matype,slowd_period=slowd_period, slowd_matype=slowd_matype)
    return d

def kdj_j(k,d):
    return (k-d)

def macd_dif(close, fastperiod=12, slowperiod=26, signalperiod=9):
    macd_dif, macd_dea, macd_hist = talib.MACD(close.astype(np.float64), fastperiod=fastperiod, slowperiod=slowperiod, signalperiod=signalperiod)
    return macd_dif

def macd_dea(close, fastperiod=12, slowperiod=26, signalperiod=9):
    macd_dif, macd_dea, macd_hist = talib.MACD(close.astype(np.float64), fastperiod=fastperiod, slowperiod=slowperiod, signalperiod=signalperiod)
    return macd_dea

def macd_hist(close, fastperiod=12, slowperiod=26, signalperiod=9):
    macd_dif, macd_dea, macd_hist = talib.MACD(close.astype(np.float64), fastperiod=fastperiod, slowperiod=slowperiod, signalperiod=signalperiod)
    return macd_hist

def macd_wt_dif(close,volume,fastperiod=12, slowperiod=26, signalperiod=9):
    fast_ema = ta_ema(close, fastperiod)
    slow_ema = ta_ema(close, slowperiod)
    dif = fast_ema - slow_ema
    # volume_sma = sma(volume, signalperiod)
    volume_sma = ta_ema(volume, signalperiod)
    # volume_sma = volume.mean()
    volume_factor = volume / volume_sma # 成交量因子
    wt_dif = dif * volume_factor
    return wt_dif
    
def macd_wt_dea(close,volume,fastperiod=12, slowperiod=26, signalperiod=9):
    wt_dif = macd_wt_dif(close, volume, fastperiod=fastperiod, slowperiod=slowperiod, signalperiod=signalperiod)
    return ta_ema(wt_dif, signalperiod)

def macd_wt_hist(close,volume,fastperiod=12, slowperiod=26, signalperiod=9):
    wt_dif = macd_wt_dif(close, volume, fastperiod=fastperiod, slowperiod=slowperiod, signalperiod=signalperiod)
    wt_dea = macd_wt_dea(close,volume, fastperiod=fastperiod, slowperiod=slowperiod, signalperiod=signalperiod)
    return (wt_dif - wt_dea)

def ta_sma(se, N):
    return talib.SMA(se.astype(np.float64), N)

def ta_ema(se, N):
    ema = talib.EMA(se.astype(np.float64), N)
    ema = np.nan_to_num(ema, nan=0.0)
    return ema

def ta_rsi(se, N):
    return talib.RSI(se.astype(np.float64), N)

def massi(high, low, N1, N2):
    x1 = ta_ema(high-low, N1)
    x2 = ta_ema(x1, N1)
    x2_safe = np.where(x2 == 0, 1e-10, x2)
    massi = talib.SUM(x1.astype(np.float64) / x2_safe.astype(np.float64), N2)
    return massi

if __name__ == '__main__':
    se = pd.Series([1,2,3,4,5,6,7,8,9,10])
    se2 = pd.Series([0,2,2,3,4,5,6,7,8,7])
    r1 = sar_index(se,se2)
    print(r1)