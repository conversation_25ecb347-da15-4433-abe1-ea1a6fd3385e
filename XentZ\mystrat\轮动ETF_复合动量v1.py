#!/usr/bin/env python
# -*- coding:utf-8 -*-

from hikyuu import *
import matplotlib.pyplot as plt

def my_func():
    # =============== 1. 策略参数设置 ===============
    # ETF列表
    etf_codes = [
        "sz159915",  # 创业板ETF(易方达)
        "sz159934",  # 主要消费ETF(嘉实)
        "sh510880",  # 上证红利ETF(华泰柏瑞)
        "sh513100",  # 纳斯达克100ETF(国泰)
        "sh513520",  # 日经etf
    ]
    # 
    start_date = Datetime(20200101)
    end_date = None
    initial_cash = 200000
    rebalance_days = 1
    rocp_period = 18
    take_profit = 0.15
    top_n = 1
    cost_func = get_part(f"mystar.other.tc_etf")
    mm = get_part(f"mystar.mm.etf_fixed_percent", p=0.9)
    sp = SP_FixedValue(0.002) # 2跳
    # ==================== 2. 系统参数配置 ===================
    options = {
        "stock_list": etf_codes,
        "ktype_list": ["day"],
        "load_history_finance": False,
        "load_weight": False,
        "start_spot": False,
        "spot_worker_num": 1,
    }
    load_hikyuu(**options)
    # =============== 3. 创建模拟交易账户/指标/交易系统 ===============
    tm = crtTM(date=start_date, init_cash=initial_cash, cost_func=cost_func)
    stks = tuple([sm[code] for code in options["stock_list"]])
    ind0 = get_part('mystar.ind.斜率年化乘R2',period=25)*0.4
    ind1 = (ROCP(CLOSE,5)+ROCP(CLOSE,10))*0.5*0.2
    ind2 = MA(VOL(),5) / MA(VOL(),20)*0.4
    ind = ind0 + ind1 + ind2
    
    sys = SYS_Simple()
    sys.set_param("buy_delay", False)
    sys.set_param("sell_delay", False)
    sys.tm = tm
    sys.sp = sp
    sys.mm = mm  # 使用90%的资金进行购买
    sg0 = get_part('mystar.sg.周期_卖出ROCP超值',rocp_period=rocp_period, 
                              take_profit=take_profit,
                              debug_if=False)
    sys.sg = sg0
    # sys.sg = SG_Cycle()
    # =============== 4. 创建选择器/资产分配器/投资组合 ===============
    se = SE_MultiFactor([ind], topn=top_n, 
                        ref_stk = sm['sz159915'],
                        mode="MF_EqualWeight")
    se.add_stock_list(stks,sys)
    af = AF_EqualWeight()
    pf = PF_Simple(tm=tm, af=af, se=se, adjust_cycle=rebalance_days,
                    adjust_mode="query", delay_to_trading_day=True)
    # pf.set_param("trace", True)
    # =============== 5. 指定测试区间并运行回测 ===============
    query = Query(start_date, end_date, 
                  ktype=Query.DAY, recover_type=Query.BACKWARD)
    query = Query(start_date, end_date)
    pf.run(query)
    # =============== 6. 结果保存与展示 ======================
    pf.tm.tocsv(os.path.dirname(os.path.abspath(__file__)))
    pf.performance()
    import matplotlib.pyplot as plt
    plt.show()
    # # =============== 5. 实盘LIVE ===========================
    # # 获取qmt订单代理
    # QMT_ACCOUNT = "********"  # 字符串
    # QMT_PATH = r'D:\p-installed\gjQMT_sim\userdata_mini'  # 自己的QMT交易终端地址
    # stragegy_name = "实盘的DEMO"
    # qmt_broker = get_part("star.other.broker_qmt",
    #                     account=QMT_ACCOUNT, path=QMT_PATH, stg_name=stragegy_name)
    # qmt_broker = crtOB(qmt_broker)
    

    # hku_info("策略执行************************")
    # my_pf = pf
    # run_in_strategy(pf=my_pf, query=Query(Datetime(********)),
    #                 broker=qmt_broker, cost_func=cost_func, other_brokers=[])

if __name__ == "__main__":
    my_func()
    # etf_codes = [
    #     "sz159915",  # 创业板ETF(易方达)
    #     "sz159934",  # 主要消费ETF(嘉实)
    #     "sh510880",  # 上证红利ETF(华泰柏瑞)
    #     "sh513100",  # 纳斯达克100ETF(国泰)
    #     "sh513520",  # 日经etf
    # ]
    # s = Strategy(etf_codes,  [Query.DAY])
    # # 每交易日 14点52分 执行
    # s.run_daily_at(my_func, TimeDelta(0, 14, 52))
    # s.start()