from .alpha import AlphaBase

class AlphaLite(AlphaBase):
    ''' alphaLite & wq101 不含rank系列 '''
    def get_exprs_names(self):
        exprs = []
        names = []
        # ================= Lite ======================
        windows = [2, 5, 10, 20]
        exprs += [f'close/shift(close,{d}) - 1' for d in windows]
        names += ['roc_%02d' % d for d in windows]

        exprs += ['avg(volume,1)/avg(volume,5)']
        names += ['avg_amount_1_avg_amount_5']

        exprs += ['avg(volume,5)/avg(volume,20)']
        names += ['avg_amount_5_avg_amount_20']

        # exprs += ['rank(avg(volume,1))/rank(avg(volume,5))']
        # names += ['rank_avg_amount_1_avg_amount_5']

        # exprs += ['rank(avg(volume,5))/rank(avg(volume,20))']
        # names += ['rank_avg_amount_5_avg_amount_20']

        # windows = [2, 5, 10]
        # exprs += ['rank(roc_%d)' % d for d in windows]
        # names += ['rank_roc_%d' % d for d in windows]

        # exprs += ['rank(roc_2)/rank(roc_5)']
        # names += ['rank_roc_2_rank_roc_5']

        # exprs += ['rank(roc_5)/rank(roc_10)']
        # names += ['rank_roc_5_rank_roc_10']
        
        # ================= world quant 101 ( exclude rank ) ======================
        names.append('alpha006')
        exprs.append('(-1 * corr_pro(open, volume, 10))')
        
        names.append('alpha012')
        exprs.append('(sign(delta(volume, 1)) * (-1 * delta(close, 1)))')
        
        # names.append('alpha028')
        # exprs.append(
        #    'scale(((corr_pro(sma(volume*open, 20), low, 5) + ((high + low) / 2)) - close))')
        
        # names.append('alpha032')
        # exprs.append('scale(((sum(close, 7) / 7) - close)) + (20 * scale(corr_pro(cm_vwap(high, low, close, volume), delay(close, 5), 230)))')
        
        # names.append('alpha053')
        # exprs.append(
        #     '(-1 * delta((((close - low) - (high - close)) / (close - low)), 9))')
        
        # names.append('alpha101')
        # exprs.append('((close - open) / ((high - low) + .001))')
        
        return exprs, names