import sqlite3
from sqlite3 import Error
from dataclasses import dataclass
from typing import List, Optional
import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
    
from jzal_pro.cls_common import BaseObj
from config import DB_DIR_FACTOR

TABLE_GPRUNS = 'gp_runs'

@dataclass
class FactorData:
    expr: str
    task_uid: str
    run_uid: str
    metric: float # 暂时没用! 

class FactorDataMgr(BaseObj):
    ''' TODO: 批量插入时可能遇到同一个因子同一套数据 先去重再批量插入; bug在于metric相差很大的情况发现过,未解决 '''
    def __init__(self, db_path: str, table_name: str):
        super().__init__()
        self.db_path = db_path
        self.tbl_name = table_name
        self._create_table() # 表不存在 则创建table

    def _create_table(self):
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    CREATE TABLE IF NOT EXISTS [{self.tbl_name}] (
                        expr TEXT NOT NULL, 
                        task_uid TEXT NOT NULL,
                        run_uid TEXT NOT NULL,
                        metric REAL NOT NULL,
                        UNIQUE (expr, task_uid, run_uid), -- 去重约束
                        FOREIGN KEY (task_uid) REFERENCES gp_tasks(task_uid)
                    )
                ''')
                conn.commit()
        except Error as e:
            self.log(f"创建{self.tbl_name}表时发生错误: {e}")
    def _is_close(value1, value2, tolerance=1e-5):
        return abs(value1 - value2) <= tolerance

    def add_factor(self, factor_data: FactorData) -> None:
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                # 检查是否有相同expr和task_uid的记录
                cursor.execute(f'''
                    SELECT metric FROM [{self.tbl_name}]
                    WHERE expr = ? AND task_uid = ?
                ''', (factor_data.expr, factor_data.task_uid))
                
                existing_records = cursor.fetchall()
                if existing_records:
                    for record in existing_records:
                        if self._is_close(factor_data.metric, record[0]): # 如果metric值接近，则不插入
                            return   
                    # 如果metric值不接近，打印警告
                    self.log(f"同一套数据的同一个因子出现不同的metric值, 依旧插入!: {factor_data.expr}, {factor_data.task_uid}")

                cursor.execute(f'''
                    INSERT INTO [{self.tbl_name}] (
                        expr, task_uid, run_uid, metric
                    ) VALUES (?, ?, ?, ?)
                ''', (
                    factor_data.expr, factor_data.task_uid, factor_data.run_uid,
                    factor_data.metric
                ))
        except Error as e:
            self.log(f"插入因子数据时发生错误: {e}")
            
    def query_all_factors(self) -> List[FactorData]:
        """查询表中的所有因子数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f"SELECT * FROM [{self.tbl_name}]")
                rows = cursor.fetchall()
                return [FactorData(*row) for row in rows]
        except Error as e:
            self.log(f"查询所有因子数据时发生错误: {e}")
            return []
        
    def load_all_factors(self) -> List[str]:
        """返回所有expr字段的值的列表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f"SELECT expr FROM [{self.tbl_name}]")
                rows = cursor.fetchall()
                return [row[0] for row in rows]
        except Error as e:
            self.log(f"加载所有expr字段的值时发生错误: {e}")
            return []
                
    def query_by_task_uid(self, task_uid: str) -> List[FactorData]:
        """根据任务ID查询因子数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f"SELECT * FROM [{self.tbl_name}] WHERE task_uid=?",(task_uid,))
                rows = cursor.fetchall()
                return [FactorData(*row) for row in rows]
        except Error as e:
            self.log(f"根据任务ID查询因子数据时发生错误: {e}")
            return []
        
    def update_metric_value(self, expr: str, task_uid: str, run_uid: str, new_metric: float) -> bool:
        """更新指定因子数据的metric值"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    UPDATE [{self.tbl_name}]
                    SET metric = ?
                    WHERE expr = ? AND task_uid = ? AND run_uid = ?
                ''', (new_metric, expr, task_uid, run_uid))
                if cursor.rowcount > 0:
                    conn.commit()
                    return True
                else:
                    self.log("没有找到匹配的记录进行更新")
                    return False
        except Error as e:
            self.log(f"更新因子数据metric值时发生错误: {e}")
            return False
        
    def delete_factor(self, expr: str, task_uid: str, run_uid: str) -> bool:
        """删除特定的因子数据记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    DELETE FROM [{self.tbl_name}]
                    WHERE expr = ? AND task_uid = ? AND run_uid = ?
                ''', (expr, task_uid, run_uid))
                if cursor.rowcount > 0:
                    conn.commit()
                    return True
                else:
                    self.log("没有找到匹配的记录进行删除")
                    return False
        except Error as e:
            self.log(f"删除因子数据记录时发生错误: {e}")
            return False

    def add_factor_list(self, factor_list: List[FactorData]) -> List[str]:
        """
        批量添加因子数据到数据库中。
        :param factor_list: 包含多个FactorData对象的列表
        """
        try:
            # 获取数据库中已存在的记录
            existing_records = set()
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    SELECT expr, task_uid FROM [{self.tbl_name}]
                ''')
                existing_records = {(row[0], row[1]) for row in cursor.fetchall()}
            
            # 从factor_list中移除已存在的记录
            filtered_list = [
                factor for factor in factor_list
                if (factor.expr, factor.task_uid) not in existing_records
            ]
            unique_expr_list = [factor.expr for factor in filtered_list]
            # 准备SQL和参数
            sql = f'''
                INSERT INTO [{self.tbl_name}] (expr, task_uid, run_uid, metric)
                VALUES (?, ?, ?, ?)
            '''
            params = [(factor.expr, factor.task_uid, factor.run_uid, factor.metric) for factor in filtered_list]
            
            # 执行批量插入
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.executemany(sql, params)
                conn.commit()
                
            return unique_expr_list
        
        except Error as e:
            self.log(f"批量插入因子数据时发生错误: {e}")
            return []
                    
    def query_by_factor_list(self, factor_list: List[str]) -> List[FactorData]:
        """
        根据传入的因子表达式列表，查询并返回所有相关记录。
        :param factor_list: 包含多个表达式的列表
        :return: 包含查询结果的 FactorData 对象列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                placeholders = ', '.join('?' for _ in factor_list)
                query = f"SELECT * FROM [{self.tbl_name}] WHERE expr IN ({placeholders})"
                cursor.execute(query, factor_list)
                rows = cursor.fetchall()
                return [FactorData(*row) for row in rows]
        except Error as e:
            self.log(f"根据表达式列表查询因子数据时发生错误: {e}")
            return []
        
def main():
    # 定义数据库路径和表名，这里使用内存数据库以简化测试
    # db_path = ":memory:"
    db_path = str(DB_DIR_FACTOR.joinpath('test.sqlite'))
    tbl_name = "factor_data_test"
    
    # 初始化FactorDataMgr实例
    factor_mgr = FactorDataMgr(db_path, tbl_name)
    
    # 测试创建表
    factor_mgr._create_table()
    print("表已创建或确认存在。")
    
    # 插入测试数据
    test_factor = FactorData(expr="Close > Open", task_uid="TST001", run_uid="RUN001", metric=0.75)
    factor_mgr.add_factor(test_factor)
    print("测试数据插入成功。")
    
    # 查询所有数据并打印
    all_factors = factor_mgr.query_all_factors()
    print("查询所有因子数据:")
    for factor in all_factors:
        print(factor)
    
    # 根据task_uid查询并打印
    factors_by_task = factor_mgr.query_by_task_uid("TST001")
    print(f"根据任务ID 'TST001' 查询到的因子数据:")
    for factor in factors_by_task:
        print(factor)
    
    # 更新数据
    new_metric = 0.80
    factor_mgr.update_metric_value(test_factor.expr, test_factor.task_uid, test_factor.run_uid, new_metric)
    print(f"数据更新成功，新的metric值为: {new_metric}")
    
    # 验证更新
    updated_factor = factor_mgr.query_by_task_uid("TST001")[0]
    print(f"验证更新结果: {updated_factor}")
    
    # 删除数据
    factor_mgr.delete_factor(test_factor.expr, test_factor.task_uid, test_factor.run_uid)
    print("数据删除成功。")
    
    # 再次查询所有数据，验证删除操作
    remaining_factors = factor_mgr.query_all_factors()
    print("删除后查询所有因子数据:")
    if remaining_factors:
        for factor in remaining_factors:
            print(factor)
    else:
        print("无数据，删除验证成功。")

if __name__ == "__main__":
    main()
    