from pathlib import Path
import pytz
from loguru import logger
import psutil
from datetime import datetime
from typing import Optional
import time
import inspect
import random
import sys

WORKDIR = Path(__file__).parent.parent
SETTING_TZ = pytz.timezone('Asia/Shanghai')

class BaseObj:
    # 类属性定义日志配置
    _log_initialized = False
    _log_format = "<cyan>{time:YYYY-MM-DD HH:mm}</cyan> | <level>{level: <8}</level> | {message}"
    
    @classmethod
    def _init_logger(cls):
        """初始化日志配置"""
        if not cls._log_initialized:
            # 创建logs目录
            log_dir = WORKDIR.joinpath("logs")
            log_dir.mkdir(exist_ok=True)
            
            # 生成日志文件路径 - 使用日期作为文件名
            log_file = str(log_dir.joinpath("{time:YYYY-MM-DD}.log"))
            
            # 移除默认的sink
            logger.remove()
            
            # 添加控制台输出
            logger.add(
                sink=sys.stderr,
                format=cls._log_format,
                level="DEBUG",
                colorize=True  # 启用颜色
            )
            
            # 添加文件输出 - 按天切分
            logger.add(
                sink=log_file,
                format=cls._log_format,
                level="DEBUG",    # 文件记录所有级别
                rotation="00:00",  # 每天零点创建新文件
                retention="30 days",  # 保留30天的日志
                compression="zip",   # 压缩旧日志
                encoding="utf-8",
                enqueue=True    # 异步写入，提高性能
            )
            
            cls._log_initialized = True
    
    @classmethod
    def log(cls, txt: str, level: str = "INFO", include_traceback: bool = None):
        """
        记录日志到控制台和文件，支持错误行号和可点击链接

        Args:
            txt: 日志消息
            level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            include_traceback: 是否包含详细的调用栈信息，None时ERROR和CRITICAL级别自动包含
        """
        # 确保logger已初始化
        cls._init_logger()

        # 获取调用者信息
        frame = inspect.currentframe().f_back

        # 尝试获取实际的类名
        if 'self' in frame.f_locals:
            caller_class = frame.f_locals['self'].__class__.__name__
        elif 'cls' in frame.f_locals:
            caller_class = frame.f_locals['cls'].__name__
        else:
            caller_class = cls.__name__ if hasattr(cls, '__name__') else cls.__class__.__name__

        # 获取详细的调用信息
        caller_method = frame.f_code.co_name
        caller_filename = frame.f_code.co_filename
        caller_lineno = frame.f_lineno

        # 生成可点击的文件链接 (VSCode格式)
        file_link = f"file:///{caller_filename.replace('\\', '/')}:{caller_lineno}"

        # 获取相对路径 (更简洁的显示)
        try:
            relative_path = Path(caller_filename).relative_to(WORKDIR)
        except ValueError:
            relative_path = Path(caller_filename).name

        # 根据级别决定是否包含详细信息
        level = level.upper()
        if include_traceback is None:
            include_traceback = level in ["ERROR", "CRITICAL"]

        # 构建基础消息
        if include_traceback:
            # 错误级别：包含文件位置和行号
            # location_info = f"[{relative_path}:{caller_lineno}]"
            message = f"{caller_class}.{caller_method}: {txt}"

            # 添加可点击链接 (在控制台中显示)
            clickable_link = f"\n    📍 {file_link}"
            message += clickable_link

            # 获取更详细的调用栈信息
            stack_info = cls._get_enhanced_stack_info(frame)
            if stack_info:
                message += f"\n    📚 调用栈:\n{stack_info}\n"
        else:
            # 普通级别：简洁格式
            message = f"{caller_class}.{caller_method}: {txt}"

        # 根据级别记录日志
        if level == "DEBUG":
            logger.debug(message)
        elif level == "INFO":
            logger.info(message)
        elif level == "WARNING":
            logger.warning(message)
        elif level == "ERROR":
            logger.error(message)
        elif level == "CRITICAL":
            logger.critical(message)
        else:
            raise ValueError(f"Unsupported log level: {level}")

    @classmethod
    def _get_enhanced_stack_info(cls, start_frame, max_depth: int = 5) -> str:
        """获取增强的调用栈信息"""
        stack_lines = []
        frame = start_frame.f_back  # 跳过log方法本身
        depth = 0

        while frame and depth < max_depth:
            filename = frame.f_code.co_filename
            lineno = frame.f_lineno
            func_name = frame.f_code.co_name

            # 生成可点击链接
            file_link = f"file:///{filename.replace('\\', '/')}:{lineno}"

            # 尝试获取代码行内容
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if 0 <= lineno - 1 < len(lines):
                        code_line = lines[lineno - 1].strip()
                    else:
                        code_line = "<无法读取>"
            except Exception:
                code_line = "<无法读取>"

            stack_lines.append(
                f"      {depth + 1}. {func_name}() at {file_link}"
            )

            frame = frame.f_back
            depth += 1

        return "\n".join(stack_lines) if stack_lines else ""
        
    @classmethod
    def gen_ordered_uid(self) -> str:
        # 考虑时区，以UTC为基础， 返回16位uid（字符串）
        now = datetime.now(SETTING_TZ)
        timestamp = int(now.timestamp() * 1000)  # 毫秒级时间戳
        unique_part = str(random.randint(100, 999))  # 简化的3位随机数增加唯一性
        # 组合时间戳和唯一部分，用于哈希
        uid = f"{timestamp}{unique_part}"
        return uid    
        
class ResMonitor(BaseObj):
    def __init__(self):
        super().__init__()
        self.start_times = {}
        self.start_cpu_usage = {}  # 新增：存储开始时的CPU使用情况
        self.start_memory_usage = {}  # 新增：存储开始时的内存使用情况
        self.log("Resource monitor started")

    def _get_memory_usage(self):
        process = psutil.Process()
        mem_info = process.memory_info()
        return mem_info.rss / 10**6  # 返回当前的内存使用量（MB）

    def _get_cpu_usage(self):
        process = psutil.Process()
        cpu_times = process.cpu_times()
        return cpu_times.user, cpu_times.system  # 返回当前的CPU使用量（用户时间和系统时间）

    def start_timer(self, label):
        """开始计时"""
        self.start_times[label] = time.time()

    def end_timer(self, label) -> Optional[str]:
        """结束计时，返回格式化的时间字符串"""
        if label not in self.start_times:
            self.log(f"Timer '{label}' not found", level="WARNING")
            return None
        end_time = time.time()
        elapsed_time = end_time - self.start_times.pop(label)
        formatted_time = self._to_time_format(elapsed_time)
        self.log(f"{label} took {elapsed_time:.2f} seconds ({formatted_time})")
        return formatted_time

    def start_timer_cpu(self, label: str):
        """起始 - 统计一段代码的cpu时间"""
        self.start_cpu_usage[label] = self._get_cpu_usage()

    def end_timer_cpu(self, label: str) -> Optional[str]:
        """结束 - 统计一段代码的cpu时间"""
        if label not in self.start_cpu_usage:
            self.log(f"CPU timer '{label}' not found", level="WARNING")
            return None
        current_cpu_usage = self._get_cpu_usage()
        start_cpu_usage = self.start_cpu_usage.pop(label)
        diff_cpu_usage = (
            current_cpu_usage[0] - start_cpu_usage[0], 
            current_cpu_usage[1] - start_cpu_usage[1]
        )
        result = f"{diff_cpu_usage[0]:.2f}s user, {diff_cpu_usage[1]:.2f}s system"
        self.log(f"{label} CPU usage: {result}")
        return result

    def start_memory_monitor(self, label: str):
        """开始内存监控"""
        self.start_memory_usage[label] = self._get_memory_usage()

    def end_memory_monitor(self, label: str) -> Optional[float]:
        """结束内存监控，返回内存增量(MB)"""
        if label not in self.start_memory_usage:
            self.log(f"Memory monitor '{label}' not found", level="WARNING")
            return None
        current_memory = self._get_memory_usage()
        start_memory = self.start_memory_usage.pop(label)
        memory_diff = current_memory - start_memory
        self.log(f"{label} memory delta: {memory_diff:.2f}MB")
        return memory_diff
    
    def log_memory_usage(self, message: str = '') -> str:
        """记录当前内存使用量"""
        process = psutil.Process()
        mem_info = process.memory_info()
        result = f"{mem_info.rss / 10**6:.2f}MB"
        if message:
            self.log(f"{message} - Memory usage: {result}")
        return result

    def log_cpu_usage(self, message: str = '') -> str:
        """记录当前CPU使用量"""
        process = psutil.Process()
        cpu_times = process.cpu_times()
        result = f"{cpu_times.user:.2f}s user, {cpu_times.system:.2f}s system"
        if message:
            self.log(f"{message} - CPU usage: {result}")
        return result

    def get_system_info(self) -> dict:
        """获取系统信息"""
        try:
            return {
                'cpu_count': psutil.cpu_count(),
                'cpu_percent': psutil.cpu_percent(interval=0.1),
                'memory_total': psutil.virtual_memory().total / 10**9,  # GB
                'memory_available': psutil.virtual_memory().available / 10**9,  # GB
                'memory_percent': psutil.virtual_memory().percent,
            }
        except Exception as e:
            self.log(f"Error getting system info: {e}", level="ERROR")
            return {}

    def _to_time_format(self, value) -> Optional[str]:
        """将秒数转换为xx:xx:xx格式，或CPU使用率转换为时间表示"""
        if value is None:
            return None
        hours, remainder = divmod(int(value), 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

class MonitorContext:
    """通用监控上下文管理器"""
    
    def __init__(self, label: str, monitor: ResMonitor = None):
        self.label = label
        self.monitor = monitor or ResMonitor()
        self.start_time = None
        self.end_time = None
        self.elapsed_time = None
        self.cpu_usage = None
        self.memory_delta = None

    def __enter__(self):
        """进入上下文"""
        self.start_time = datetime.now(SETTING_TZ)
        self.monitor.start_timer(self.label)
        self.monitor.start_timer_cpu(self.label)
        self.monitor.start_memory_monitor(self.label)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        self.end_time = datetime.now(SETTING_TZ)
        self.elapsed_time = self.monitor.end_timer(self.label)
        self.cpu_usage = self.monitor.end_timer_cpu(self.label)
        self.memory_delta = self.monitor.end_memory_monitor(self.label)
        
        if exc_type is not None:
            self.monitor.log(f"{self.label} failed with error: {exc_val}", level="ERROR")
        else:
            self.monitor.log(f"{self.label} completed successfully")
        
        return False  # 不抑制异常

if __name__ == '__main__':
    print(BaseObj().gen_ordered_uid())
    rm = ResMonitor()
    rm.start_timer("test")
    rm.end_timer("test")
    rm.start_timer("test2")
    rm.end_timer("test2")
    rm.start_timer("test3")
    rm.end_timer("test3")
    rm.log_memory_usage("test")