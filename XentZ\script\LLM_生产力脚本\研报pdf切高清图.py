#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
研报PDF切高清图片脚本
目的: 将PDF研报按页转换为高清图片，方便cursor和claude阅读分析
"""

from pathlib import Path
import fitz  # PyMuPDF
from config import WORKDIR

if __name__ == "__main__":
    ''' ====================== 1. 路径和文件配置 ============================= '''
    # 输入路径和文件名配置
    input_dir = Path(r"D:\JG-files\07_量化交易\@@知识库\遗传因子")
    pdf_filename = "华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.pdf"
    pdf_path = input_dir / pdf_filename
    
    # 输出目录配置（使用配置的WORKDIR下的citeds目录）
    output_dir = WORKDIR / "citeds" / pdf_path.stem
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"📄 PDF文件: {pdf_path}")
    print(f"📁 输出目录: {output_dir}")
    
    ''' ====================== 2. 检查文件存在性 ============================= '''
    if not pdf_path.exists():
        print(f"❌ PDF文件不存在: {pdf_path}")
        exit(1)
    
    ''' ====================== 3. 打开PDF文档 ============================= '''
    doc = fitz.open(pdf_path)
    total_pages = len(doc)
    print(f"📊 PDF总页数: {total_pages}")
    
    ''' ====================== 4. 逐页转换为高清图片 ============================= '''
    # 设置高清渲染参数
    zoom_factor = 2.0  # 放大倍数，提高清晰度
    mat = fitz.Matrix(zoom_factor, zoom_factor)
    
    for page_num in range(total_pages):
        print(f"🔄 正在处理第 {page_num + 1}/{total_pages} 页...")
        
        # 获取页面
        page = doc[page_num]
        
        # 渲染为高清图片
        pix = page.get_pixmap(matrix=mat)
        
        # 保存图片（按页顺序命名）
        image_filename = f"page_{page_num + 1:03d}.png"  # 001, 002, 003...
        image_path = output_dir / image_filename
        pix.save(str(image_path))
        
        print(f"✅ 已保存: {image_filename}")
    
    ''' ====================== 5. 清理和完成 ============================= '''
    doc.close()
    print(f"\n🎉 PDF转换完成！")
    print(f"📁 所有图片已保存到: {output_dir}")
    print(f"📊 共转换 {total_pages} 页图片")
    
    # 列出生成的文件
    image_files = sorted(output_dir.glob("*.png"))
    print(f"\n📋 生成的图片文件:")
    for img_file in image_files:
        print(f"   {img_file.name}")








