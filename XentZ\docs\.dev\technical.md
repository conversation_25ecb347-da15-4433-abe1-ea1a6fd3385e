# XentZ 量化交易系统技术文档

## 1. 基础框架

### 1.1 Hikyuu量化框架
基于Hikyuu开源量化框架:
- A股股票或基金行情数据下载/读取/板块管理/除权出息管理/财务数据管理等
- 文档地址: https://hikyuu.readthedocs.io/zh_CN/latest/index.html
- 代码地址: https://github.com/fasiondog/hikyuu
- 项目workspace里放置了hikyuu的源码，框架可以part方式组件化实现复用
- 缺省hub以及作者开设的星球私有的star hub库都是如此目的
- 实盘采用框架提供的QMT broker(含该broker实现的源码)

### 1.2 多框架集成
- 参考市场上其他回测框架以及一些好用的数据处理代码,比如基于pandas或numpy的
- 逐步构建自己的回测+实盘工具集, 鉴于Hikyuu框架对中国A股市场的股票/基金等数据的管理性突出, 数据获取层面可基于此扩展
- 尽管Hikyuu框架有各类组件 比如mm/st/af/sg/ind/pf/ev/cn/sys等等, 这些可以日常进行回测使用, 对于更灵活的回测需求考虑扩展自己的代码比如load框架的历史数据成df, 进行多种处理, 特征工程等等
- 自己的扩展中可以并行引入别的回测引擎来多框架验证策略, 比如backtrader/bt等等
- backtrader文档和代码: https://github.com/mementum/backtrader

## 2. 技术栈

### 2.1 核心依赖
- **Python 3.12+**: 主要开发语言
- **Pandas**: 数据处理和分析
- **NumPy**: 数值计算
- **Hikyuu**: A股数据管理和策略回测
- **BackTrader**: 多框架策略验证
- **Dynaconf**: 配置管理
- **Loguru**: 日志记录
- **PSUtil**: 性能监控

### 2.2 特征工程库
- **TSFresh**: 时间序列特征提取
- **TA-Lib**: 技术指标计算
- **Scikit-learn**: 机器学习和特征选择
- **SciPy**: 科学计算和统计分析

### 2.3 可视化和分析
- **Matplotlib**: 基础图表绘制
- **Seaborn**: 统计图表
- **Plotly**: 交互式图表
- **TQDM**: 进度条显示

### 2.4 开发工具
- **IPython**: 交互式开发
- **Pytest**: 单元测试
- **Black**: 代码格式化
- **setuptools**: 包管理

## 3. 核心功能模块

### 3.1 数据管理 (datafeed)

#### HKUDataloader
```python
# 支持的数据频率
FREQ_MAP = {
    # 基础周期
    'D': Query.DAY,       # 日线
    'W': Query.WEEK,      # 周线
    'M': Query.MONTH,     # 月线
    'Q': Query.QUARTER,   # 季线
    'H': Query.HALFYEAR,  # 半年线
    'Y': Query.YEAR,      # 年线
    
    # 分钟周期
    '1min': Query.MIN,    # 1分钟线
    '5min': Query.MIN5,   # 5分钟线
    '15min': Query.MIN15, # 15分钟线
    '30min': Query.MIN30, # 30分钟线
    '60min': Query.MIN60, # 60分钟线
}

# 复权类型
RECOVER_MAP = {
    'NO_RECOVER': Query.NO_RECOVER,      # 不复权
    'FORWARD': Query.FORWARD,            # 前向复权
    'BACKWARD': Query.BACKWARD,          # 后向复权
    'EQUAL_FORWARD': Query.EQUAL_FORWARD,  # 等比前向复权
    'EQUAL_BACKWARD': Query.EQUAL_BACKWARD  # 等比后向复权
}
```

#### 核心功能
- **多品种数据加载**: `load_df_all()` 批量加载多个品种数据
- **数据格式转换**: 支持单品种、多品种、透视表等多种格式
- **BackTrader适配**: `load_a_backtrader_df()` 生成BackTrader兼容格式
- **表达式计算**: `calc_expr_df_all()` 支持复杂数学表达式计算

### 3.2 特征工程 (features)

#### FeatPreprocessing - 数据预处理
```python
@calc_df_by_symbol  # 装饰器自动按品种分组处理
def fill_missing(df_all: pd.DataFrame, method: str = 'ffill', **kwargs):
    """缺失值填充
    - 'ffill': 前向填充
    - 'bfill': 后向填充  
    - 'mean': 均值填充
    - 'interpolate': 插值填充
    """
    
def mad_clip_df(df_all: pd.DataFrame, k: int = 3, **kwargs):
    """MAD异常值截断"""
    
def norm_df(df: pd.DataFrame, window: int = 2000, 
           algomode: int = 0, **kwargs):
    """数据标准化 - 支持多种算法:
    - algomode=0: Z-Score标准化
    - algomode=1: L2范数归一化
    - algomode=2: MinMax归一化
    - algomode=3: 分位数归一化
    """
```

#### FeatEngineering - 特征构造
```python
def create_lag_features(df: pd.DataFrame, lags: List[int], **kwargs):
    """滞后特征生成"""
    
def create_rolling_features(df: pd.DataFrame, windows: List[int],
                          funcs: List[str] = ['mean', 'std'], **kwargs):
    """滚动窗口特征"""
    
def extract_tsfresh_features(df: pd.DataFrame, 
                           feature_set: str = 'efficient', **kwargs):
    """TSFresh时间序列特征提取"""
```

#### FeatSelection - 特征选择
```python
def select_by_importance(X: pd.DataFrame, y: pd.Series, 
                        method: str = 'random_forest', top_n: int = 10):
    """基于重要性的特征选择"""
    
def select_by_correlation(df: pd.DataFrame, threshold: float = 0.7):
    """相关性过滤"""
    
def select_by_statistical_tests(X: pd.DataFrame, y: pd.Series, **kwargs):
    """统计检验特征选择"""
```

### 3.3 配置管理 (config)

#### 分层配置系统
```python
# 基于Dynaconf的配置管理
settings = Dynaconf(
    environments=False,
    settings_files=[
        f"{config_dir}/{ENV}/research.toml",    # 研究配置
        f"{config_dir}/{ENV}/backtest.toml",    # 回测配置  
        f"{config_dir}/{ENV}/live.toml",        # 实盘配置
    ],
)

# 环境配置
ENV = os.getenv("ENV_FOR_DYNACONF", "default")  # default/prod

# 配置访问接口
def get_norm_params(model_type="linear", data_type="X"):
    """获取标准化参数"""
    
def get_missing_mode():
    """获取缺失值处理模式"""
    
def update_config(value, *keys):
    """动态更新配置"""
```

### 3.4 多因子挖掘 (core/polyfactor)

#### 表达式引擎
```python
# 支持复杂的因子表达式计算
expr = "close / sma(close, 20) - 1"  # 相对强弱
result = calc_expr(df, expr)

# GPLearn遗传编程因子挖掘
class GPLearnTask:
    def run_genetic_programming(self, X, y, **kwargs):
        """遗传编程因子挖掘"""
```

### 3.5 性能监控 (common)

#### BaseObj - 基础对象
```python
class BaseObj:
    @classmethod
    def log(cls, txt: str, level: str = "INFO"):
        """统一日志记录"""
        
    def gen_ordered_uid(self) -> str:
        """生成有序唯一ID"""

class ResMonitor(BaseObj):
    def start_timer(self, label):
        """开始计时"""
        
    def end_timer(self, label):
        """结束计时"""
        
    def log_memory_usage(self, message: str = ''):
        """内存使用监控"""
```

## 4. 核心设计模式

### 4.1 装饰器模式
```python
@calc_df_by_symbol
def your_function(df: pd.DataFrame, **kwargs):
    """自动按品种分组处理的装饰器
    
    处理逻辑：
    1. 多级索引：按第二级(symbol)分组处理
    2. 普通DataFrame有symbol列：按symbol列分组处理  
    3. 既无多级索引也无symbol列：直接处理整个DataFrame
    
    被装饰函数会额外接收current_symbol参数
    """
```

### 4.2 数据格式标准
```python
# 单品种格式
df_single = pd.DataFrame({
    'open': [...], 'high': [...], 'low': [...], 
    'close': [...], 'volume': [...]
}, index=pd.DatetimeIndex([...]))

# 多品种格式  
df_multi = pd.DataFrame({
    'open': [...], 'high': [...], 'low': [...], 
    'close': [...], 'volume': [...], 'symbol': [...]
}, index=pd.DatetimeIndex([...]))

# 透视表格式
df_pivot = pd.DataFrame({
    'AAPL': [...], 'MSFT': [...], 'GOOG': [...]
}, index=pd.DatetimeIndex([...]))
```

### 4.3 配置驱动设计
```toml
# research.toml - 研究配置
[norm]
window = 2000
logmode = 0

[norm.linear.X]
algomode = 0
n_clip = 6
demean = false

[missing]
mode = "ffill"

[outliers]
mode = "mad"

[outliers.mad]
k = 3
axis = 0
```

## 5. 代码风格规范

### 5.1 Python风格
- **开源项目风格惯例**: 遵循PEP 8标准
- **优雅简洁**: 多用推导式，少用for循环，非必要少新增函数
- **类型提示**: 使用Type Hints提高代码可读性
- **文档字符串**: 详细的函数和类文档

### 5.2 命名规范
```python
# 变量和函数: snake_case
def calc_moving_average(data: pd.Series, window: int) -> pd.Series:
    
# 类名: PascalCase  
class FeatPreprocessing:
    
# 常量: UPPER_CASE
FREQ_MAP = {...}

# 私有方法: _private_method
def _calculate_statistics(self, data):
```

### 5.3 错误处理
```python
# 使用日志记录而非print
self.log(f"Processing {symbol}", level="INFO")

# 优雅的异常处理
try:
    result = risky_operation()
except SpecificException as e:
    self.log(f"Error processing {symbol}: {e}", level="ERROR")
    return None
```

## 6. 开发工具和流程

### 6.1 Hikyuu Hub系统
```python
# 常用命令
from hikyuu.interactive import *

# Hub管理
get_hub_name_list()                    # 查看hub列表
add_remote_hub('star', 'https://...')  # 添加远程hub
add_local_hub("mystar", r"D:\path")    # 添加本地hub
update_hub('star')                     # 更新hub

# Part管理  
get_part_name_list('star')             # 查看part列表
get_part_info('star.se.单因子排序')      # 查看part信息
get_part('default.ind.市净率')          # 获取part

# 本地开发
build_hub('mystar', 'create -t ind -n 趋势评分')  # 创建indicator
build_hub('mystar', 'create -t pf -n example')   # 创建portfolio
build_hub('mystar', 'update')                    # 更新hub
build_hub('mystar', 'buildall')                  # 构建所有
```

### 6.2 交互式开发
```python
# IPython环境配置
!git config --global credential.helper store  # 保存凭证

# 常用工具
from hikyuu.interactive import *
from datafeed.hku_dataloader import HKUDataloader  
from datafeed.features.feature_utils import *
from config.settings import *
```

### 6.3 测试和验证
```python
# 单元测试
def test_feature_engineering():
    """测试特征工程功能"""
    
# 性能测试  
def test_performance():
    rm = ResMonitor()
    rm.start_timer("feature_calc")
    # ... 执行代码 ...
    rm.end_timer("feature_calc")
    
# 多框架验证
def test_cross_validation():
    """Hikyuu vs BackTrader结果对比"""
```

## 7. 性能优化策略

### 7.1 数据处理优化
- **向量化计算**: 使用pandas/numpy向量化操作避免循环
- **内存管理**: 及时释放大型DataFrame，使用适当的数据类型
- **并行处理**: 利用装饰器的分组处理实现并行化
- **缓存机制**: 配置参数缓存，计算结果缓存

### 7.2 算法优化
```python
# 高性能标准化算法
def norm_df(df, window=2000, algomode=0, **kwargs):
    """多列时性能比norm_se提升2.77倍!"""
    
# 滑动窗口优化
from numpy.lib.stride_tricks import sliding_window_view
windows = sliding_window_view(data, window_shape=window)
```

### 7.3 监控和调试
```python
# 内置性能监控
rm = ResMonitor()
rm.log_memory_usage("开始处理")
rm.start_timer("processing")
# ... 处理逻辑 ...
rm.end_timer("processing") 
rm.log_memory_usage("处理完成")
```

## 8. 部署和环境

### 8.1 环境配置
```bash
# Python版本要求
Python >= 3.12

# 核心依赖安装
pip install hikyuu pandas numpy loguru dynaconf psutil
pip install tsfresh ta-lib scikit-learn scipy
pip install backtrader matplotlib seaborn plotly
```

### 8.2 配置文件结构
```
config/
├── __init__.py
├── settings.py          # 配置管理核心
├── default/             # 默认环境
│   ├── research.toml    # 研究配置
│   ├── backtest.toml    # 回测配置
│   └── live.toml        # 实盘配置
└── prod/                # 生产环境
    ├── research.toml
    ├── backtest.toml
    └── live.toml
```

### 8.3 日志管理
```python
# 自动日志配置
log_dir = WORKDIR.joinpath("logs")
log_file = "{time:YYYY-MM-DD}.log"

# 日志级别和格式
format = "<cyan>{time:YYYY-MM-DD HH:mm}</cyan> | <level>{level: <8}</level> | {message}"
```

## 9. 扩展开发指南

### 9.1 添加新特征函数
```python
@calc_df_by_symbol
def your_custom_feature(df: pd.DataFrame, param1: int, **kwargs):
    """自定义特征函数
    
    Args:
        df: 输入数据框
        param1: 自定义参数
        **kwargs: 包含current_symbol等装饰器参数
    
    Returns:
        处理后的数据框
    """
    current_symbol = kwargs.get('current_symbol')
    # 实现你的特征逻辑
    return result_df
```

### 9.2 集成新数据源
```python
class CustomDataloader:
    """自定义数据加载器"""
    
    @staticmethod
    def load_data(symbols: List[str], **kwargs) -> pd.DataFrame:
        """加载数据并转换为标准格式"""
        # 实现数据加载逻辑
        return standardized_df
```

### 9.3 添加新策略框架
```python
# 在mystrat目录下添加新的策略实现
class MyCustomStrategy:
    def __init__(self, **params):
        self.params = params
        
    def run_backtest(self, data: pd.DataFrame):
        """运行回测"""
        # 实现策略逻辑
```

AI对话方式尽量用中文, 代码/或惯用词保持英文习惯即可