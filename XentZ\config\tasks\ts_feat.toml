dynaconf_include = ["_datasets/ts_single_etf.toml"]

# ============================== 基础特征定义 ============================== #
[feat.set]
# 定义用于生成gplearn初始特征池的Alpha类
# 在任务脚本中，会根据这个列表加载对应的Alpha模块
enabled_alphas = [
    "AlphaBase",    # 标签
    "AlphaOrigin",  # 基础价量
    "Alpha158",     # WorldQuant 101 Alpha中的一部分
    "AlphaLite",    # 精简版常用因子
    "AlphaJZAL"     # JZAL Alphas
]

# ============================== 命名约定 ============================== #
[label]
mode = 'single' # single, multiple

[label.single]
tdelay = 1
names = ['label_1']

[label.multiple]
tdelay = [1, 5, 10, 20]
names = ['label_1', 'label_5', 'label_10', 'label_20']

# ============================== 数据预处理 ============================== #
[missing]
mode = "ffill" # bfill, mean, median, mode, value, interpolate

[outliers]
mode = 'mad_clip'
mad_clip = {k = 3} #

[norm]
window = 2000 # [1000,2000]
logmode = 0 # [0,1,2]
# —— Norm 参数字典 —— #
[norm.linear] # 线性模型
X     = { algomode = 0, n_clip = 6, smooth = 0, demean = true }
label = { algomode = 0, n_clip = 2, smooth = 0, demean = false }
pos   = { algomode = 3, n_clip = 2, smooth = 1, demean = false }
[norm.robust] # 鲁棒模型, 不进行norm和正态处理, 如树模型
X     = 'skip'
label = 'skip'
pos   = { algomode = 3, n_clip = 2, smooth = 1, demean = false }
[norm.neutral] # 神经网络等算法, 进行norm和强制正态
X     = { algomode = 5, n_clip = 6, smooth = 0, demean = false }
label = { algomode = 5, n_clip = 2, smooth = 0, demean = false }
pos   = { algomode = 3, n_clip = 2, smooth = 1, demean = false }

[norm.cols]
base2keep = ['open','close','high','low']
base2norm = ['volume','amount']

[norm.cols.prefix]
feat2keep = '_ts_cons_'
featlabel = 'label_'

# ============================== 特征配置 ============================== #
[feat.norm]
model = 'robust' # 可选 "linear" 或 "robust" 或 "neutral"; 决定使用哪种模型算法
[feat.drop]
names = ['volume','amount']
[feat.selected]
names = ['JZ007_9', 'b_vwap_512', 'avg_amount_1_avg_amount_5', 'K_UP2', 'JZ009_14', 'JZ002_55', 'ROC5', 'ROC60', 'SUMD30', 'MAX20'] 