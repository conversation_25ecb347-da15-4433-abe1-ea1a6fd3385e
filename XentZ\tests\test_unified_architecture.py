#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一绩效计算架构测试
验证重构后的极简架构的正确性和性能
"""

import unittest
import numpy as np
import pandas as pd
import time

from factor.factor_perf import FactorPerf


class TestUnifiedArchitecture(unittest.TestCase):
    """测试统一的绩效计算架构"""
    
    def setUp(self):
        """设置测试数据"""
        np.random.seed(42)
        self.returns_1d = np.random.randn(252) * 0.02  # 单资产
        self.returns_2d = np.random.randn(252, 3) * 0.02  # 多资产
        self.returns_series = pd.Series(self.returns_1d)
    
    def test_calc_sr_original_functionality(self):
        """测试原有calc_sr方法的功能保持不变"""
        # 测试原有的参数格式
        sr = FactorPerf.calc_sr(self.returns_2d, day_bars=1, trading_days=252, free_rate=0.03)
        
        self.assertEqual(len(sr), 3)
        self.assertTrue(all(np.isfinite(sr)))
        print(f"原有calc_sr功能: 夏普比率 = {sr}")
    
    def test_new_individual_methods(self):
        """测试新增的单个指标计算方法"""
        # 年化收益率
        ann_ret = FactorPerf.calc_annual_return(self.returns_2d)
        self.assertEqual(len(ann_ret), 3)
        
        # 年化波动率
        ann_vol = FactorPerf.calc_annual_volatility(self.returns_2d)
        self.assertEqual(len(ann_vol), 3)
        
        # 最大回撤
        cum_ret = np.cumprod(1 + self.returns_2d, axis=0)
        max_dd = FactorPerf.calc_max_drawdown(cum_ret)
        self.assertEqual(len(max_dd), 3)
        self.assertTrue(all(max_dd >= 0))
        
        # 胜率
        win_rate = FactorPerf.calc_win_rate(self.returns_2d)
        self.assertEqual(len(win_rate), 3)
        self.assertTrue(all((win_rate >= 0) & (win_rate <= 1)))
        
        print(f"年化收益: {ann_ret}")
        print(f"年化波动: {ann_vol}")
        print(f"最大回撤: {max_dd}")
        print(f"胜率: {win_rate}")
    
    def test_batch_calculation_performance(self):
        """测试批量计算的性能和正确性"""
        start_time = time.time()
        metrics = FactorPerf.calc_basic_metrics(self.returns_2d)
        elapsed_time = time.time() - start_time
        
        # 验证返回的指标
        expected_keys = ['annual_return', 'volatility', 'sharpe_ratio', 'max_drawdown', 
                        'calmar_ratio', 'total_return', 'win_rate']
        for key in expected_keys:
            self.assertIn(key, metrics)
            self.assertEqual(len(metrics[key]), 3)
        
        # 性能要求
        self.assertLess(elapsed_time, 0.01, "批量计算应在0.01秒内完成")
        
        print(f"批量计算3资产252天数据耗时: {elapsed_time:.6f}秒")
        print(f"批量计算结果: {metrics}")
    
    def test_single_asset_convenience_method(self):
        """测试单资产便捷方法"""
        # 测试numpy数组输入
        metrics_array = FactorPerf.calc_single_asset_metrics(self.returns_1d)
        
        # 测试pandas Series输入
        metrics_series = FactorPerf.calc_single_asset_metrics(self.returns_series)
        
        # 测试list输入
        metrics_list = FactorPerf.calc_single_asset_metrics(self.returns_1d.tolist())
        
        # 验证结果一致性
        for key in metrics_array.keys():
            self.assertAlmostEqual(metrics_array[key], metrics_series[key], places=10)
            self.assertAlmostEqual(metrics_array[key], metrics_list[key], places=10)
        
        # 验证返回值是标量
        for value in metrics_array.values():
            self.assertIsInstance(value, float)
        
        print(f"单资产便捷方法结果: {metrics_array}")
    
    def test_extreme_cases_robustness(self):
        """测试极端情况的健壮性"""
        # 全零收益
        zero_returns = np.zeros((100, 2))
        metrics_zero = FactorPerf.calc_basic_metrics(zero_returns)
        self.assertEqual(metrics_zero['sharpe_ratio'][0], 0.0)
        self.assertEqual(metrics_zero['volatility'][0], 0.0)
        
        # 包含NaN的数据
        nan_returns = self.returns_2d.copy()
        nan_returns[0:10, 0] = np.nan
        metrics_nan = FactorPerf.calc_basic_metrics(nan_returns)
        self.assertTrue(all(np.isfinite(metrics_nan['sharpe_ratio'])))
        
        # 包含无穷值的数据
        inf_returns = self.returns_2d.copy()
        inf_returns[0, 0] = np.inf
        inf_returns[1, 0] = -np.inf
        metrics_inf = FactorPerf.calc_basic_metrics(inf_returns)
        self.assertTrue(all(np.isfinite(metrics_inf['sharpe_ratio'])))
        
        print("极端情况测试通过")
    
    def test_consistency_with_original_calc_sr(self):
        """测试与原有calc_sr方法的一致性"""
        # 使用相同参数调用两种方法
        original_sr = FactorPerf.calc_sr(self.returns_2d, day_bars=1, trading_days=252, free_rate=0.0)
        
        batch_metrics = FactorPerf.calc_basic_metrics(self.returns_2d, annualization_factor=252, risk_free_rate=0.0)
        new_sr = batch_metrics['sharpe_ratio']
        
        # 验证结果一致性
        np.testing.assert_array_almost_equal(original_sr, new_sr, decimal=10)
        print(f"一致性验证通过: 原方法={original_sr}, 新方法={new_sr}")
    
    def test_performance_comparison(self):
        """测试性能对比"""
        large_returns = np.random.randn(2520, 10) * 0.02  # 10年10资产数据
        
        # 测试批量计算性能
        start_time = time.time()
        batch_metrics = FactorPerf.calc_basic_metrics(large_returns)
        batch_time = time.time() - start_time
        
        # 测试逐个计算性能
        start_time = time.time()
        individual_results = []
        for i in range(large_returns.shape[1]):
            sr = FactorPerf.calc_sr(large_returns[:, i:i+1], day_bars=1, trading_days=252)
            individual_results.append(sr[0])
        individual_time = time.time() - start_time
        
        print(f"批量计算10资产10年数据耗时: {batch_time:.6f}秒")
        print(f"逐个计算10资产10年数据耗时: {individual_time:.6f}秒")
        print(f"性能提升: {individual_time/batch_time:.1f}倍")
        
        # 批量计算应该更快
        self.assertLess(batch_time, individual_time)
    
    def test_api_simplicity(self):
        """测试API的简洁性"""
        # 最简单的调用方式
        metrics = FactorPerf.calc_single_asset_metrics(self.returns_series)
        
        # 验证API简洁性：一行代码获得所有指标
        self.assertGreater(len(metrics), 5)
        self.assertIn('sharpe_ratio', metrics)
        self.assertIn('max_drawdown', metrics)
        
        print(f"API简洁性验证: 一行代码获得{len(metrics)}个指标")


class TestArchitectureDesignPrinciples(unittest.TestCase):
    """测试架构设计原则的实现"""
    
    def test_extreme_simplicity(self):
        """测试极简主义原则"""
        # 验证核心方法的代码行数都很少（通过功能验证间接测试）
        returns = np.random.randn(100, 2) * 0.02
        
        # 所有方法都应该能正常工作且性能良好
        start_time = time.time()
        
        sr = FactorPerf.calc_sr(returns)
        ann_ret = FactorPerf.calc_annual_return(returns)
        ann_vol = FactorPerf.calc_annual_volatility(returns)
        max_dd = FactorPerf.calc_max_drawdown(np.cumprod(1 + returns, axis=0))
        win_rate = FactorPerf.calc_win_rate(returns)
        batch_metrics = FactorPerf.calc_basic_metrics(returns)
        
        elapsed_time = time.time() - start_time
        
        # 所有计算应该快速
        self.assertLess(elapsed_time, 0.1, "极简设计应该带来良好性能")
        
        print(f"极简设计验证: 6个方法调用总耗时 {elapsed_time:.6f}秒")
    
    def test_no_state_no_side_effects(self):
        """测试无状态、无副作用原则"""
        returns = np.random.randn(100, 2) * 0.02
        
        # 多次调用应该得到相同结果
        result1 = FactorPerf.calc_basic_metrics(returns)
        result2 = FactorPerf.calc_basic_metrics(returns)
        
        for key in result1.keys():
            np.testing.assert_array_equal(result1[key], result2[key])
        
        print("无状态、无副作用验证通过")
    
    def test_unified_api_design(self):
        """测试统一API设计"""
        returns = np.random.randn(100, 2) * 0.02
        
        # 所有方法都应该接受相同的基本参数格式
        ann_factor = 252
        
        # 验证参数一致性
        sr = FactorPerf.calc_sr(returns, day_bars=1, trading_days=ann_factor)
        ann_ret = FactorPerf.calc_annual_return(returns, ann_factor)
        ann_vol = FactorPerf.calc_annual_volatility(returns, ann_factor)
        
        # 验证返回格式一致性（都是numpy数组）
        self.assertIsInstance(sr, np.ndarray)
        self.assertIsInstance(ann_ret, np.ndarray)
        self.assertIsInstance(ann_vol, np.ndarray)
        
        print("统一API设计验证通过")


if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)
