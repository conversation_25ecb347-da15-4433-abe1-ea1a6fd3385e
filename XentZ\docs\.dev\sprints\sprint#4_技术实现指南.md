# Sprint #4 技术实现指南 - WFA验证系统

## 技术架构概览

### 核心设计原则
1. **过程式编程**: 遵循项目现有风格，函数职责单一明确
2. **函数复用**: 最大化复用现有绩效计算函数
3. **配置驱动**: 使用统一的配置管理系统
4. **性能优先**: 向量化操作，避免循环计算
5. **可观测性**: 丰富的日志和监控信息

### 技术栈映射
```
算法层: numpy + pandas + scipy (WFA核心计算)
  ├── 复用: factor/factor_utils.py (numba加速函数)
  ├── 复用: 原库/polyfactorX/jzal_pro/utils/perf_utils.py (绩效计算)
  └── 新增: factor/validation_utils.py (WFA特有逻辑)

可视化层: quantstats + matplotlib + seaborn
  ├── quantstats: 专业绩效分析和HTML报告
  ├── matplotlib: 自定义图表生成
  └── seaborn: 统计图表美化

数据层: FactorZoo + 外部报告目录
  ├── FactorZoo.factor_evaluations: 绩效指标存储
  ├── FactorZoo.factors: 因子状态管理
  └── D:/myquant/reports/XentZ/: 报告文件存储

配置层: dynaconf + toml
  ├── config/tasks/ts_l3_wfa.toml: 任务配置
  └── config/__init__.py: 全局配置管理
```

## 核心算法实现模板

### 1. WFA数据结构定义
```python
# factor/validation_utils.py

from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Callable
import pandas as pd
import numpy as np

@dataclass
class WFAParams:
    """WFA验证参数"""
    training_window: int = 750      # 训练窗口大小
    testing_window: int = 60        # 测试窗口大小  
    step_size: int = 60             # 步进大小
    tanh_k: float = 5.0             # S型映射参数
    hold_n: int = 1                 # 持有期
    gap: int = 1                    # 时序验证间隔
    min_periods: int = 50           # 最小有效样本数
    correlation_method: str = "spearman"  # 相关性计算方法

@dataclass
class WFAResult:
    """WFA验证结果"""
    factor_id: str                  # 因子ID
    symbol: str                     # 标的代码
    pnl_series: pd.Series          # PnL序列
    position_series: pd.Series     # 仓位序列
    metrics: Dict[str, float]      # 绩效指标
    test_periods: List[Dict]       # 测试期详情
    pass_status: str               # 通过状态
    fail_reasons: List[str]        # 失败原因
    
    def __post_init__(self):
        """数据验证"""
        if self.pnl_series is None or len(self.pnl_series) == 0:
            raise ValueError("PnL序列不能为空")
        if self.pass_status not in ['L3_PASSED', 'L3_FAILED']:
            raise ValueError("通过状态必须是L3_PASSED或L3_FAILED")
```

### 2. 经验分布函数(ECDF)实现
```python
def calculate_ecdf_mapping(factor_values: pd.Series) -> Callable:
    """
    学习因子值的经验累积分布函数
    
    Args:
        factor_values: 训练期因子值序列
        
    Returns:
        mapping_func: 将因子值映射为百分位排名的函数
    """
    from scipy.stats import rankdata
    
    # 移除缺失值
    clean_values = factor_values.dropna()
    if len(clean_values) < 10:
        raise ValueError("训练数据不足，至少需要10个有效值")
    
    # 计算排名和百分位
    sorted_values = np.sort(clean_values.values)
    n = len(sorted_values)
    
    def mapping_func(new_values: pd.Series) -> pd.Series:
        """将新的因子值映射为百分位排名"""
        # 使用searchsorted进行高效查找
        ranks = np.searchsorted(sorted_values, new_values.values, side='right')
        percentiles = ranks / n
        
        # 边界处理：确保在[0, 1]范围内
        percentiles = np.clip(percentiles, 0.001, 0.999)
        
        return pd.Series(percentiles, index=new_values.index)
    
    return mapping_func

def apply_tanh_position_mapping(percentiles: pd.Series, 
                               tanh_k: float = 5.0,
                               direction: int = 1) -> pd.Series:
    """
    使用tanh函数进行S型仓位映射
    
    Args:
        percentiles: 百分位排名序列 [0, 1]
        tanh_k: S型曲线陡峭度参数
        direction: 因子方向 (1 或 -1)
        
    Returns:
        positions: 最终仓位序列 [-1, 1]
    """
    # 核心公式: tanh(k * (percentile - 0.5)) * direction
    base_positions = np.tanh(tanh_k * (percentiles - 0.5))
    final_positions = base_positions * direction
    
    # 确保仓位在合理范围内
    final_positions = np.clip(final_positions, -1.0, 1.0)
    
    return pd.Series(final_positions, index=percentiles.index)
```

### 3. 相关性计算优化
```python
def calculate_spearman_correlation_optimized(factor_values: pd.Series,
                                           returns: pd.Series) -> float:
    """
    优化的Spearman相关性计算
    复用factor_utils中的numba加速函数
    """
    try:
        from factor.factor_utils import rank_1d
    except ImportError:
        # 降级到scipy实现
        from scipy.stats import spearmanr
        aligned_data = pd.concat([factor_values, returns], axis=1).dropna()
        if len(aligned_data) < 10:
            return 0.0
        correlation, _ = spearmanr(aligned_data.iloc[:, 0], aligned_data.iloc[:, 1])
        return correlation if not np.isnan(correlation) else 0.0
    
    # 数据对齐和清洗
    aligned_data = pd.concat([factor_values, returns], axis=1).dropna()
    if len(aligned_data) < 10:
        return 0.0
    
    # 使用numba加速的排名计算
    factor_ranks = rank_1d(aligned_data.iloc[:, 0].values.astype(np.float32))
    return_ranks = rank_1d(aligned_data.iloc[:, 1].values.astype(np.float32))
    
    # 计算Pearson相关系数（对排名数据等价于Spearman）
    correlation = np.corrcoef(factor_ranks, return_ranks)[0, 1]
    return correlation if not np.isnan(correlation) else 0.0
```

### 4. WFA主流程实现框架
```python
def run_wfa_validation(factor_data: pd.Series,
                      price_data: pd.Series,
                      wfa_params: WFAParams) -> WFAResult:
    """
    WFA验证主流程
    
    实现滚动窗口验证逻辑:
    1. 窗口切分和数据对齐
    2. 滚动训练和测试
    3. PnL序列拼接
    4. 绩效指标计算
    """
    from datetime import datetime
    
    print(f"🔄 开始WFA验证: 训练窗口={wfa_params.training_window}, 测试窗口={wfa_params.testing_window}")
    
    # 1. 数据预处理和对齐
    aligned_data = pd.concat([factor_data, price_data], axis=1).dropna()
    if len(aligned_data) < wfa_params.training_window + wfa_params.testing_window:
        raise ValueError("数据长度不足以进行WFA验证")
    
    factor_aligned = aligned_data.iloc[:, 0]
    price_aligned = aligned_data.iloc[:, 1]
    
    # 计算收益率序列
    returns = price_aligned.pct_change(wfa_params.hold_n).shift(-wfa_params.hold_n - wfa_params.gap + 1)
    
    # 2. 滚动窗口验证
    pnl_segments = []
    test_periods = []
    
    start_idx = 0
    total_windows = 0
    
    while start_idx + wfa_params.training_window + wfa_params.testing_window <= len(factor_aligned):
        total_windows += 1
        
        # 训练期和测试期数据切分
        train_end = start_idx + wfa_params.training_window
        test_end = train_end + wfa_params.testing_window
        
        factor_train = factor_aligned.iloc[start_idx:train_end]
        factor_test = factor_aligned.iloc[train_end:test_end]
        returns_train = returns.iloc[start_idx:train_end]
        returns_test = returns.iloc[train_end:test_end]
        
        try:
            # 训练阶段：学习分布和方向
            ecdf_mapping = calculate_ecdf_mapping(factor_train)
            correlation = calculate_spearman_correlation_optimized(factor_train, returns_train)
            direction = 1 if correlation > 0 else -1
            
            # 测试阶段：仓位映射和PnL计算
            test_percentiles = ecdf_mapping(factor_test)
            test_positions = apply_tanh_position_mapping(test_percentiles, wfa_params.tanh_k, direction)
            
            # 计算测试期PnL
            test_pnl = test_positions * returns_test
            test_pnl = test_pnl.dropna()
            
            if len(test_pnl) > 0:
                pnl_segments.append(test_pnl)
                
                # 记录测试期信息
                test_periods.append({
                    'window_id': total_windows,
                    'train_start': factor_train.index[0],
                    'train_end': factor_train.index[-1],
                    'test_start': factor_test.index[0],
                    'test_end': factor_test.index[-1],
                    'direction': direction,
                    'correlation': correlation,
                    'test_pnl_mean': test_pnl.mean(),
                    'test_pnl_std': test_pnl.std()
                })
            
        except Exception as e:
            print(f"⚠️ 窗口 {total_windows} 处理失败: {str(e)}")
            continue
        
        # 滚动前进
        start_idx += wfa_params.step_size
    
    # 3. 拼接PnL序列
    if not pnl_segments:
        raise ValueError("没有有效的测试期结果")
    
    full_pnl_series = pd.concat(pnl_segments).sort_index()
    
    # 4. 计算绩效指标
    metrics = calculate_wfa_metrics_with_reuse(full_pnl_series)
    
    # 5. 构造结果对象
    result = WFAResult(
        factor_id=factor_data.name or 'unknown',
        symbol='unknown',  # 需要从外部传入
        pnl_series=full_pnl_series,
        position_series=pd.concat([apply_tanh_position_mapping(ecdf_mapping(factor_test), wfa_params.tanh_k, 1) for _ in pnl_segments]),
        metrics=metrics,
        test_periods=test_periods,
        pass_status='PENDING',  # 需要后续判定
        fail_reasons=[]
    )
    
    print(f"✅ WFA验证完成: {total_windows}个窗口, PnL长度={len(full_pnl_series)}")
    return result
```

## 绩效计算复用策略

### 复用现有函数
```python
def calculate_wfa_metrics_with_reuse(pnl_series: pd.Series) -> Dict[str, float]:
    """
    WFA绩效指标计算 (复用现有函数)
    复用项目中已有的成熟绩效计算实现
    """
    try:
        # 复用原库的完整绩效计算
        from 原库.polyfactorX.jzal_pro.utils.perf_utils import calc_stats
        
        # 转换为净值序列 (原库函数需要净值格式)
        nav_series = (1 + pnl_series).cumprod()
        nav_df = pd.DataFrame({'nav': nav_series})
        
        # 调用原库绩效计算
        df_ratios, df_nav_peryear = calc_stats(nav_df)
        
        # 提取WFA需要的核心指标
        wfa_metrics = {
            'sharpe_ratio': df_ratios.loc['夏普比率', 'nav'],
            'max_drawdown': abs(df_ratios.loc['最大回撤', 'nav']),
            'calmar_ratio': df_ratios.loc['卡玛比率', 'nav'],
            'total_return': df_ratios.loc['累计收益', 'nav'],
            'annual_return': df_ratios.loc['年化收益', 'nav'],
            'volatility': df_ratios.loc['年化波动', 'nav'],
        }
        
    except ImportError:
        # 降级到基础计算
        print("⚠️ 原库绩效函数不可用，使用基础计算")
        wfa_metrics = calculate_basic_metrics(pnl_series)
    
    # 补充计算WFA特有指标
    wfa_metrics.update({
        'win_rate': (pnl_series > 0).mean(),
        'avg_return': pnl_series.mean(),
        'skewness': pnl_series.skew(),
        'kurtosis': pnl_series.kurtosis(),
        'var_95': pnl_series.quantile(0.05),
        'cvar_95': pnl_series[pnl_series <= pnl_series.quantile(0.05)].mean(),
    })
    
    return wfa_metrics

def calculate_basic_metrics(pnl_series: pd.Series) -> Dict[str, float]:
    """基础绩效指标计算（降级方案）"""
    cumulative_returns = (1 + pnl_series).cumprod()
    
    # 基础指标计算
    total_return = cumulative_returns.iloc[-1] - 1
    annual_return = (1 + total_return) ** (252 / len(pnl_series)) - 1
    volatility = pnl_series.std() * np.sqrt(252)
    sharpe_ratio = annual_return / volatility if volatility > 0 else 0
    
    # 最大回撤计算
    rolling_max = cumulative_returns.expanding().max()
    drawdowns = (cumulative_returns - rolling_max) / rolling_max
    max_drawdown = abs(drawdowns.min())
    
    # 卡玛比率
    calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else 0
    
    return {
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'calmar_ratio': calmar_ratio,
        'total_return': total_return,
        'annual_return': annual_return,
        'volatility': volatility,
    }
```

## 配置管理最佳实践

### 配置文件模板
```toml
# config/tasks/ts_l3_wfa.toml

# 继承数据集配置
dynaconf_include = ["_datasets/ts_single_etf.toml"]

[task]
name = "L3_WFA_Dynamic_Validation"
desc = "L3阶段因子的Walk-Forward Analysis动态稳健性检验"

# WFA核心参数 (经过优化的最佳配置)
[wfa]
training_window = 750                  # 训练窗口：750个交易日(约3年)
testing_window = 60                    # 测试窗口：60个交易日(约3个月)
step_size = 60                         # 步进大小：等于测试窗口，无重叠
tanh_k = 5                             # S型曲线陡峭度参数
hold_n = 1                             # 持有期：1个交易日
gap = 1                                # 时序验证间隔
min_periods = 50                       # 最小有效样本数
correlation_method = "spearman"        # 相关性计算方法

# 通过标准 (统一标准)
[criteria]
min_sharpe = 0.5                       # 最小夏普比率
max_mdd = 0.30                         # 最大回撤上限
min_win_rate = 0.55                    # 最小胜率
min_calmar = 0.8                       # 最小卡玛比率
max_volatility = 0.25                  # 最大年化波动率
min_skewness = -0.5                    # 最小偏度
max_kurtosis = 5.0                     # 最大峰度

# 因子查询配置
[factor_query]
source_pipeline_step = "L2"            # 从L2阶段筛选通过的因子
batch_limit = 10                       # 最多处理的批次数量
factor_limit_per_batch = 100           # 每批次最多处理的因子数量

# 输出配置
[output]
save_detailed_reports = true           # 是否保存详细报告
save_pnl_series = true                # 是否保存PnL序列
save_position_series = false          # 是否保存仓位序列
plot_format = "png"                   # 图表格式
plot_dpi = 300                        # 图表分辨率
```

### 配置加载代码
```python
def load_and_validate_config() -> Tuple[WFAParams, Dict, Dict]:
    """加载并验证WFA配置"""
    from dynaconf import Dynaconf
    from config import settings
    
    # 加载任务配置
    task_config = Dynaconf(settings_files=["config/tasks/ts_l3_wfa.toml"])
    
    # 转换为数据类
    wfa_params = WFAParams(
        training_window=task_config.wfa.training_window,
        testing_window=task_config.wfa.testing_window,
        step_size=task_config.wfa.step_size,
        tanh_k=task_config.wfa.tanh_k,
        hold_n=task_config.wfa.hold_n,
        gap=task_config.wfa.gap,
        min_periods=task_config.wfa.min_periods,
        correlation_method=task_config.wfa.correlation_method
    )
    
    # 验证参数合理性
    if wfa_params.training_window < 100:
        raise ValueError("训练窗口过小，至少需要100个交易日")
    if wfa_params.testing_window < 20:
        raise ValueError("测试窗口过小，至少需要20个交易日")
    if wfa_params.tanh_k <= 0:
        raise ValueError("tanh_k参数必须大于0")
    
    criteria_params = dict(task_config.criteria)
    query_params = dict(task_config.factor_query)
    
    print(f"✅ 配置加载成功: 训练窗口={wfa_params.training_window}, 测试窗口={wfa_params.testing_window}")
    return wfa_params, criteria_params, query_params
```

---

**下一步**: 继续完善业务流程脚本和可视化报告生成的实现细节。
