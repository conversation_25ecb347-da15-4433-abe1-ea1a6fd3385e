import os
from dataclasses import dataclass, field
from typing import Tuple, Dict, List
from pathlib import Path
from datafeed.dataloader import Featherloader
from config import DATA_DIR_FEATHER
from task_defs.task_base import Task
from datafeed import AlphaOrigin, Alpha158, AlphaLite, AlphaJZAL, AlphaBase
import toml

@dataclass
class TaskFctsMining(Task):
    fee_rate = 0.002 # 千2
    free_rate = 0.03
    job_num: int = -1
    t_delay: int = 1
    freqs: list = field(default_factory=lambda: ['15', '30', '60'])
    func_set: list = field(default_factory=list)
    split_perc:float = 0.75 # 百分比, xx%训练集, (1-xx%)测试集
    _inner_symbols = []
    columns = ['open', 'high', 'low', 'close', 'volume']
    
    # 配置相关
    global_config: Dict = field(default_factory=dict)
    pair_configs: Dict[str, Dict] = field(default_factory=dict)
    
    # 添加新属性
    excluded_features: Dict[str, List[str]] = field(default_factory=dict)  # pair -> excluded features list
    
    def __init__(self):
        super().__init__()
        # 全局默认参数
        self.global_config = {
            'fee_rate': 0.002,
            'free_rate': 0.03,
            'split_perc': 0.75,
            't_delay': 1,
            'job_num': 8,
            'start_date': '20050101',
            'end_date': None  # 默认当前日期
        }    
        # 品种专属配置字典
        self.pair_configs = {}
        
        self.feature_names = []  # 特征名称列表
        self.feature_exprs = []  # 特征表达式列表
    
        
    def load_config(self, config_path: str = None):
        """分层加载配置（兼容GUI和CLI模式）"""
        # 1. 加载默认配置
        self._load_default_config()
        
        # 2. 加载全局配置文件（如果存在）
        if config_path and os.path.exists(config_path):
            self._load_toml_config(config_path)

    def _load_default_config(self):
        """加载类定义中的默认值"""
        self.global_config = {
            'fee_rate': 0.002,
            'free_rate': 0.03,
            'split_perc': 0.75,
            't_delay': 1,
            'job_num': 8,
            'start_date': '20050101',
            'end_date': None
        }

    def _load_toml_config(self, config_path: str):
        """从TOML文件加载配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            configs = toml.load(f)
            
        # 更新全局配置
        if 'global' in configs:
            self.global_config = configs['global']
            # 更新实例属性
            self.fee_rate = self.global_config.get('fee_rate', self.fee_rate)
            self.free_rate = self.global_config.get('free_rate', self.free_rate)
            self.split_perc = self.global_config.get('split_perc', self.split_perc)
            self.t_delay = self.global_config.get('t_delay', self.t_delay)
            self.job_num = self.global_config.get('job_num', self.job_num)
            self.start_date = self.global_config.get('start_date', self.start_date)
            self.end_date = self.global_config.get('end_date', self.end_date)
            
        # 加载品种专属配置    
        if 'pairs' in configs:
            self.pair_configs = configs['pairs']
        
        # 加载已选pairs
        if 'selected_pairs' in configs:
            self.symbols = list(set(symbol for symbol, _ in configs['selected_pairs']))
            self.freqs = list(set(period for _, period in configs['selected_pairs']))
            
        # 加载excluded_features配置(如果有)
        if 'excluded_features' in configs:
            self.excluded_features = configs['excluded_features']
        else:
            # 如果配置文件中没有exclude配置，则初始化为空字典
            self.excluded_features = {}
        
    def get_pair_config(self, symbol: str, period: str) -> dict:
        """获取品种的最终配置(专属配置覆盖全局配置)"""
        symbol_period = f"{symbol}_{period}"
        config = self.global_config.copy()
        
        if symbol_period in self.pair_configs:
            config.update(self.pair_configs[symbol_period])
            
        return config
    
    def get_param_for_pair(self, symbol_period: str, param_name: str):
        """获取指定pair的参数值,如果没有独立配置则返回全局值"""
        if symbol_period in self.pair_configs:
            return self.pair_configs[symbol_period].get(param_name, getattr(self, param_name))
        return getattr(self, param_name)
    
    def _symbol_freq(self) -> list:
        if self.symbols:
            return [f"{symbol}_{freq}" for symbol in self.symbols for freq in self.freqs]
        else:
            self.log('没有指定任何品种!',level='error')
            return []
        
    def load_datas(self) -> Tuple[list,list]:  # 重写父类方法: 用于gplearn任务的全部数据源进行统一处理
        ''' 返回norm后且float32后的df LIST, df包含symbol列, 如510050.SH_15'''
        path = DATA_DIR_FEATHER.joinpath(self.data_path).resolve()
        symbol_freq_pairs = self._symbol_freq()
        self._inner_symbols = symbol_freq_pairs
        # 踢出path中不存在的filename 并更新filenames 列表 同时log踢出的文件名
        existing_pairs = [pair for pair in symbol_freq_pairs if os.path.exists(os.path.join(path, pair + '.feather'))]
        removed_pairs = set(symbol_freq_pairs) - set(existing_pairs)
        for rpair in removed_pairs:
            self.log(f"{rpair}.feather 不存在于路径 {path} , 从拼接列表移除.", level='warning')
            
        loader = Featherloader(path=path, symbols=existing_pairs, columns=self.columns, # 实际的pair(existing_pairs)传到dataloader里去
                            start_date=self.start_date, end_date=self.end_date)
        dfs, base_features = loader.load_as_norm(fields=self.feature_exprs, names=self.feature_names) # 返回df组成的list
        # dfs.dropna(inplace=True)  # 对结果影响非常重要! 会删掉一些新品种的数据
        return dfs, base_features

    def get_init_exprs_names(self) -> Tuple[List[str], List[str]]:
        """获取初始化的特征名称和表达式"""
        return self.feature_names.copy(), self.feature_exprs.copy()

def Fcts_GP_Mining():
    task = TaskFctsMining()
    task.name = 'gp因子挖掘'
    task.start_date = '20050101' # 跟实际数据一致
    # task.end_date = '20050601'
    task.symbols = ['510050.SH']
    task.freqs = ['15', '30']

    a0 = AlphaBase()
    exprs, names = a0.get_a_label()
    task.feature_exprs += exprs
    task.feature_names += names    
    a1 = AlphaOrigin()
    exprs, names = a1.get_exprs_names()
    task.feature_exprs += exprs
    task.feature_names += names
    a2 = Alpha158()
    exprs, names = a2.get_exprs_names()
    task.feature_exprs += exprs
    task.feature_names += names
    a3 = AlphaLite()
    exprs, names = a3.get_exprs_names()
    task.feature_exprs += exprs
    task.feature_names += names
    a4 = AlphaJZAL()
    exprs, names = a4.get_exprs_names()
    task.feature_exprs += exprs
    task.feature_names += names
    
    task.split_perc = 0.70
    task.job_num = 8    # 8, 设置并行核数 设置为-1，将会调用电脑所有进程
    task.t_delay = 1
    from datafeed.mining.gplearn.functions import _function_map
    func_3 = list(_function_map.keys())
    task.func_set = func_3
    
    return task