import sqlite3
from dataclasses import dataclass
from typing import List, Optional
from uuid import uuid4
from sqlite3 import IntegrityError, Error
import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
    
from jzal_pro.cls_common import BaseObj

@dataclass
class TaskData:
    task_uid: str
    symbol: str # 支持510050.SH_15 这种
    bgn_date: str
    end_date: str
    fee_rate: float
    split_perc: float
    
class TaskDataMgr(BaseObj):
    ''' 同一套数据赋予同一个任务号: symbol/end/bgn/fee/split'''
    def __init__(self, db_path: str):
        super().__init__()
        self.db_path = db_path
        self._create_table()  # 表不存在 则创建table

    def _create_table(self):
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS gp_tasks (
                        task_uid TEXT PRIMARY KEY, -- 设定task_uid为主键,确保唯一性, 一套数据对应一个task
                        symbol TEXT NOT NULL,  -- freqs 信息已经包含在symbol里
                        bgn_date TEXT NOT NULL,
                        end_date TEXT, -- end_date可以为None
                        split_perc REAL NOT NULL,
                        fee_rate REAL NOT NULL,
                        UNIQUE (symbol, bgn_date, end_date, split_perc, fee_rate) -- 添加唯一索引以防止指定字段组合重复
                    )
                ''')
        except Error as e:
            self.log(f"创建gp_tasks表时发生错误: {e}")

    def _alter_table(self):
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 创建临时表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS gp_tasks_temp (
                        task_uid TEXT PRIMARY KEY, -- 设定task_uid为主键,确保唯一性, 一套数据对应一个task
                        symbol TEXT NOT NULL,  -- freqs 信息已经包含在symbol里
                        bgn_date TEXT NOT NULL,
                        end_date TEXT,  -- 允许 end_date 为 NULL
                        split_perc REAL NOT NULL,
                        fee_rate REAL NOT NULL,
                        UNIQUE (symbol, bgn_date, end_date, split_perc, fee_rate) -- 添加唯一索引以防止指定字段组合重复
                    )
                ''')

                # 将数据从旧表复制到新表
                cursor.execute('''
                    INSERT INTO gp_tasks_temp (task_uid, symbol, bgn_date, end_date, split_perc, fee_rate)
                    SELECT task_uid, symbol, bgn_date, end_date, split_perc, fee_rate
                    FROM gp_tasks
                ''')
                
                # 删除旧表
                cursor.execute('DROP TABLE gp_tasks')

                # 重命名临时表为原表名
                cursor.execute('ALTER TABLE gp_tasks_temp RENAME TO gp_tasks')

                self.log("表字段定义已成功修改。")
        except Error as e:
            self.log(f"修改gp_tasks表字段定义时发生错误: {e}")
    
    def add_data(self, task_data: TaskData) -> None:
        """向gp_tasks表中插入新的任务数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO gp_tasks (
                        task_uid, symbol, bgn_date, end_date, fee_rate, split_perc
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    task_data.task_uid, task_data.symbol, 
                    task_data.bgn_date, task_data.end_date,
                    task_data.fee_rate, task_data.split_perc
                ))
        except IntegrityError:
            self.log(f"数据插入失败，组合已存在: {task_data}")
        except Error as e:
            self.log(f"数据插入失败，错误信息: {e}")

            
    def get_data_by_uid(self, task_uid: str) -> Optional[TaskData]:
        """通过task_uid获取任务数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM gp_tasks WHERE task_uid = ?', (task_uid,))
                row = cursor.fetchone()
                if row:
                    return TaskData(*row)
                return None
        except Error as e:
            self.log(f"获取任务数据时发生错误: {e}")
        
    def update_data_by_uid(self, task_data: TaskData) -> None:
        """更新表中的任务数据"""
        existing_task  = self.get_data_by_uid(task_data.task_uid)
        if existing_task is None:
            self.log(f"尝试更新不存在的任务数据, task_uid: {task_data.task_uid}")
            return
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE gp_tasks SET
                        symbol = ?, bgn_date = ?, end_date = ?, 
                        fee_rate = ?, split_perc = ?
                    WHERE task_uid = ?
                ''', (
                    task_data.symbol, task_data.bgn_date, task_data.end_date,
                    task_data.fee_rate, task_data.split_perc, task_data.task_uid
                ))
        except Error as e:
            self.log(f"更新任务数据时发生错误: {e}")
            
    def delete_data_by_uid(self, task_uid: str) -> None:
        """通过task_uid删除任务数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM gp_tasks WHERE task_uid = ?', (task_uid,))
        except Error as e:
            self.log(f"删除任务数据时发生错误: {e}")
            
    def get_all_data(self) -> List[TaskData]:
        """获取表中的所有任务数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM gp_tasks')
                rows = cursor.fetchall()
                return [TaskData(*row) for row in rows]
        except Error as e:
            self.log(f"获取所有任务数据时发生错误: {e}")
            return []
        
    def get_available_uuid(self, symbol: str, bgn_date: str, end_date: str, 
                           split_perc: float, fee_rate: float) -> str:
        """
        检查给定的数据组合是否已存在于gp_tasks表中。
        如果存在, 则返回其task_uid; 如果不存在,则创建一个新的task_uid并返回。
        :return: 对应的task_uid
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                # NOTE: sqlite里处理None比较情况
                query_conditions = []
                query_params = [symbol, bgn_date, split_perc, fee_rate]
                if end_date is None:
                    query_conditions.append("end_date IS NULL")
                else:
                    query_conditions.append("end_date = ?")
                    query_params.append(end_date)
                    
                query = f'''
                    SELECT task_uid FROM gp_tasks 
                    WHERE symbol = ? AND bgn_date = ? AND {' AND '.join(query_conditions)}
                    AND split_perc = ? AND fee_rate = ?
                '''
                cursor.execute(query, query_params)
                existing_task_uid = cursor.fetchone()
                
            if existing_task_uid:
                # 如果找到匹配的记录，返回其task_uid
                return existing_task_uid[0]
            else:
                # 如果没有找到匹配记录，生成新的task_uid并返回
                new_task_uid = str(uuid4()).replace('-', '')[:16]
                # 创建一条新的task
                self.add_data(TaskData(new_task_uid, symbol, bgn_date, end_date, fee_rate, split_perc))
                return new_task_uid
        except Error as e:
            self.log(f"获取或生成任务UUID时发生错误: {e}")
            return ""
        
        
if __name__ == "__main__":
    # 数据库文件路径示例
    db_path = "my_database.db"
    db_path = "D:\\myquant\\btq_jzal\\datastore\\dbs\\jzal.sqlite"
    
    # 初始化TaskDataMgr实例
    task_mgr = TaskDataMgr(db_path)
    task_mgr._alter_table()
    exit()
    
    
    # 添加一条任务数据
    new_task = TaskData(task_uid="TST001", symbol="510050.SH_15", 
                        bgn_date="2023-01-01", end_date="2023-06-30", fee_rate=0.001, split_perc=0.9)
    task_mgr.add_data(new_task)
    print(f"任务数据添加: {new_task}")
    
    # 通过uid获取任务数据并打印
    fetched_task = task_mgr.get_data_by_uid("TST001")
    print(f"通过UID获取的任务数据: {fetched_task}")
    
    # 更新任务数据
    updated_task = TaskData(
        task_uid=fetched_task.task_uid,
        symbol="112222",
        bgn_date=fetched_task.bgn_date,
        end_date=fetched_task.end_date,
        fee_rate=fetched_task.fee_rate,
        split_perc=fetched_task.split_perc
    )
    task_mgr.update_data_by_uid(updated_task)
    print(f"任务数据更新后: {updated_task}")
    
    # 再次获取更新后的数据以验证
    updated_fetched_task = task_mgr.get_data_by_uid("TST001")
    print(f"验证更新: {updated_fetched_task}")
    
    # 删除任务数据
    task_mgr.delete_data_by_uid("TST001")
    print("任务数据已删除。")
    
    # 获取所有任务数据，此时应为空
    all_tasks = task_mgr.get_all_data()
    print(f"当前所有任务数据: {all_tasks}")