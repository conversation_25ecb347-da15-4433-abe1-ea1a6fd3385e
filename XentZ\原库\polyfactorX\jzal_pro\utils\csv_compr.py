import pandas as pd
from datetime import datetime

def compr_orders(a,b):
    df_a=pd.read_csv(a)
    df_b=pd.read_csv(b)
    df_a['type'] = df_a['amount'].apply(lambda x: 'buy' if x > 0 else 'sell')

    # 合并两个数据框
    merged_df = pd.merge(df_a, df_b, on=['date', 'symbol'],how='outer')
    return merged_df

def compr_equities(a,b):
    df_a=pd.read_csv(a)
    df_b=pd.read_csv(b)
    # 合并两个数据框
    merged_df = pd.merge(df_a, df_b, on=['date'])
    return merged_df

def compr_prices(a,b):
    df_a=pd.read_csv(a)
    df_b=pd.read_csv(b)
    # 合并两个数据框
    merged_df = pd.merge(df_a, df_b, on='date', suffixes=('_a', '_b'))
    merged_df = merged_df[['date', 'open_a', 'close_a', 'open_b', 'close_b']]

    return merged_df

def check_dfs_from_csv(csv_list,start_dt,end_dt,baseline_num=0):
    '''
    检查多个csv文件的数据是否完成一致, 如:不同时间跑出来的orders保持在各自csv,通过该函数检查同样start_dt,end_dt的区间内的数据是否完成一致,用于检查策略是否有未来
    都和baseline文件比
    csv_list: 以逗号分隔的csv文件名列表
    start_dt: 比对的起始日期
    end_dt: 比对的结束日期
    返回: 指明不一致的地方
    '''
    results = [] # 初始化不一致信息的列表
    base_df = pd.read_csv(csv_list[baseline_num])
    base_df['date'] = pd.to_datetime(base_df['date'])
    base_in_range = base_df[(base_df['date'] >= start_dt) & (base_df['date'] <= end_dt)]
    for csv_file in csv_list:
        if csv_file != csv_list[baseline_num]:
            # 读取CSV文件为DataFrame
            df = pd.read_csv(csv_file)
            df['date'] = pd.to_datetime(df['date'])
            # 筛选出指定日期范围内的数据
            df_in_range = df[(df['date'] >= start_dt) & (df['date'] <= end_dt)]
            merged_df = pd.merge(base_in_range, df_in_range, how='inner', indicator=True, suffixes=('', '_new'))
            not_both = merged_df[merged_df['_merge'] != 'both']
            if not_both.empty:
                results.append({
                    'file1': csv_list[baseline_num],
                    'file2': csv_file,
                    'results': not_both
                })

    return results

# m1=compr_orders('btq-orders.csv','2.4L-orders.csv')
# m1.to_excel('compr-orders.xlsx', index=False)
# m2=compr_equities('btq-equ.csv','2.4L-equ.csv')
# m2.to_excel('compr-equ.xlsx', index=False)
# m3=compr_prices('btq-159915.csv','2.4L-159915.csv')
# m3.to_excel('compr-159915.xlsx', index=False)

start_dt = pd.to_datetime('2014-01-02')
end_dt = pd.to_datetime('2023-02-01')
csv_list = ['ord1.csv','ord2.csv','ord3.csv','ord4.csv']
rs = check_dfs_from_csv(csv_list,start_dt,end_dt)
print(rs)
rs = pd.DataFrame(rs)
rs.to_csv('check.csv')

# df = pd.read_feather('C:\\Users\\<USER>\\Desktop\\DOT_USDT-1d.feather')
# df.to_csv('C:\\Users\\<USER>\\Desktop\\DOT_USDT-1d.csv', index=None)