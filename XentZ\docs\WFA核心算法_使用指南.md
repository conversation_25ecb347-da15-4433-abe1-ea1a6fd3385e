# WFA核心算法使用指南

## 概述

本文档提供WFA (Walk-Forward Analysis) 核心算法库的详细使用指南，包括API参考、最佳实践和常见问题解决方案。

## 快速开始

### 基本导入
```python
from factor.validation_utils import (
    WFAParams, WFAResult,
    run_wfa_validation,
    check_wfa_criteria,
    validate_wfa_inputs
)
import pandas as pd
import numpy as np
```

### 简单示例
```python
# 1. 准备数据
factor_data = pd.Series(np.random.randn(1000), name='my_factor')
price_data = pd.Series(np.random.randn(1000), name='price')

# 2. 配置参数
wfa_params = WFAParams(
    training_window=750,
    testing_window=60,
    step_size=60,
    tanh_k=5.0
)

# 3. 执行WFA验证
result = run_wfa_validation(factor_data, price_data, wfa_params)

# 4. 查看结果
print(f"夏普比率: {result.metrics['sharpe_ratio']:.3f}")
print(f"最大回撤: {result.metrics['max_drawdown']:.2%}")
print(f"PnL序列长度: {len(result.pnl_series)}")
```

## API参考

### WFAParams 类

WFA验证参数配置类。

```python
@dataclass
class WFAParams:
    training_window: int = 750      # 训练窗口大小(约3年)
    testing_window: int = 60        # 测试窗口大小(约3个月)
    step_size: int = 60             # 步进大小
    tanh_k: float = 5.0             # S型映射参数
    hold_n: int = 1                 # 持有期(交易日)
    gap: int = 1                    # 时序验证间隔
    min_periods: int = 50           # 最小有效样本数
    correlation_method: str = "spearman"  # 相关性计算方法
```

**参数说明**:
- `training_window`: 训练窗口大小，建议750个交易日(约3年)
- `testing_window`: 测试窗口大小，建议60个交易日(约3个月)
- `step_size`: 滚动步进大小，建议等于测试窗口避免重叠
- `tanh_k`: S型映射陡峭度，建议5.0平衡噪声过滤与信号响应
- `hold_n`: 持有期，建议1个交易日适合高频因子
- `gap`: 时序间隔，建议1避免信息泄露

### WFAResult 类

WFA验证结果类。

```python
@dataclass
class WFAResult:
    factor_id: str                  # 因子ID
    symbol: str                     # 标的代码
    pnl_series: pd.Series          # PnL序列
    position_series: pd.Series     # 仓位序列
    metrics: Dict[str, float]      # 绩效指标
    test_periods: List[Dict]       # 测试期详情
    pass_status: str               # 通过状态
    fail_reasons: List[str]        # 失败原因
```

**关键属性**:
- `pnl_series`: 策略损益序列，用于后续分析
- `metrics`: 包含夏普比率、最大回撤、胜率等关键指标
- `test_periods`: 每个测试窗口的详细信息
- `pass_status`: 'L3_PASSED', 'L3_FAILED', 或 'PENDING'

### 核心函数

#### run_wfa_validation()

执行完整的WFA验证流程。

```python
def run_wfa_validation(factor_data: pd.Series,
                      price_data: pd.Series,
                      wfa_params: WFAParams) -> WFAResult
```

**参数**:
- `factor_data`: 因子值序列，必须有时间索引
- `price_data`: 价格序列，必须与因子数据时间对齐
- `wfa_params`: WFA参数配置

**返回**: WFAResult对象

**示例**:
```python
# 使用自定义参数
custom_params = WFAParams(
    training_window=500,
    testing_window=50,
    tanh_k=3.0
)

result = run_wfa_validation(factor_data, price_data, custom_params)
```

#### check_wfa_criteria()

检查WFA结果是否通过预设标准。

```python
def check_wfa_criteria(metrics: Dict[str, float], 
                      criteria_params: Dict[str, float]) -> Tuple[str, List[str]]
```

**参数**:
- `metrics`: WFA绩效指标字典
- `criteria_params`: 通过标准参数

**返回**: (通过状态, 失败原因列表)

**示例**:
```python
criteria = {
    'min_sharpe': 0.5,
    'max_mdd': 0.30,
    'min_win_rate': 0.55,
    'min_calmar': 0.8
}

status, reasons = check_wfa_criteria(result.metrics, criteria)
if status == 'L3_PASSED':
    print("✅ 通过验证")
else:
    print("❌ 未通过验证:")
    for reason in reasons:
        print(f"  - {reason}")
```

## 最佳实践

### 1. 数据准备

```python
# 确保数据质量
def prepare_data(factor_df, price_df):
    # 时间对齐
    common_dates = factor_df.index.intersection(price_df.index)
    factor_aligned = factor_df.loc[common_dates]
    price_aligned = price_df.loc[common_dates]
    
    # 数据验证
    assert len(factor_aligned) >= 810, "数据长度不足"
    assert factor_aligned.notna().mean() > 0.8, "因子数据缺失过多"
    assert price_aligned.notna().mean() > 0.8, "价格数据缺失过多"
    
    return factor_aligned, price_aligned
```

### 2. 参数优化

```python
# 根据数据频率调整参数
def get_optimal_params(data_frequency='D'):
    if data_frequency == 'D':  # 日频
        return WFAParams(
            training_window=750,
            testing_window=60,
            step_size=60
        )
    elif data_frequency == 'W':  # 周频
        return WFAParams(
            training_window=150,
            testing_window=12,
            step_size=12
        )
    elif data_frequency == 'M':  # 月频
        return WFAParams(
            training_window=36,
            testing_window=6,
            step_size=6
        )
```

### 3. 批量处理

```python
def batch_wfa_validation(factor_dict, price_dict, wfa_params):
    """批量处理多个因子"""
    results = {}
    
    for factor_id, factor_data in factor_dict.items():
        try:
            # 获取对应的价格数据
            symbol = factor_data.name.split('_')[0]  # 假设命名规则
            price_data = price_dict.get(symbol)
            
            if price_data is None:
                print(f"⚠️ 因子 {factor_id} 缺少价格数据")
                continue
            
            # 执行WFA验证
            result = run_wfa_validation(factor_data, price_data, wfa_params)
            results[factor_id] = result
            
            print(f"✅ 因子 {factor_id} 验证完成")
            
        except Exception as e:
            print(f"❌ 因子 {factor_id} 验证失败: {str(e)}")
            continue
    
    return results
```

### 4. 结果分析

```python
def analyze_wfa_results(results):
    """分析WFA结果"""
    summary = {
        'total_factors': len(results),
        'passed_factors': 0,
        'avg_sharpe': 0,
        'avg_drawdown': 0
    }
    
    sharpe_list = []
    drawdown_list = []
    
    for factor_id, result in results.items():
        metrics = result.metrics
        sharpe_list.append(metrics['sharpe_ratio'])
        drawdown_list.append(metrics['max_drawdown'])
        
        if result.pass_status == 'L3_PASSED':
            summary['passed_factors'] += 1
    
    summary['avg_sharpe'] = np.mean(sharpe_list)
    summary['avg_drawdown'] = np.mean(drawdown_list)
    summary['pass_rate'] = summary['passed_factors'] / summary['total_factors']
    
    return summary
```

## 性能优化

### 1. 数据预处理优化

```python
# 预先对齐数据，避免重复计算
def preprocess_data(factor_data, price_data):
    # 一次性对齐和清洗
    aligned_data = pd.concat([factor_data, price_data], axis=1).dropna()
    return aligned_data.iloc[:, 0], aligned_data.iloc[:, 1]
```

### 2. 内存管理

```python
# 大批量处理时的内存管理
def memory_efficient_batch_processing(factor_list, price_list, wfa_params):
    for i, (factor_data, price_data) in enumerate(zip(factor_list, price_list)):
        result = run_wfa_validation(factor_data, price_data, wfa_params)
        
        # 处理结果后立即释放内存
        yield result
        del result, factor_data, price_data
        
        if i % 10 == 0:
            import gc
            gc.collect()  # 定期垃圾回收
```

### 3. 并行处理

```python
from concurrent.futures import ThreadPoolExecutor

def parallel_wfa_validation(factor_dict, price_dict, wfa_params, max_workers=4):
    """并行WFA验证"""
    def process_single_factor(factor_id):
        factor_data = factor_dict[factor_id]
        symbol = factor_data.name.split('_')[0]
        price_data = price_dict.get(symbol)
        
        if price_data is None:
            return factor_id, None
        
        try:
            result = run_wfa_validation(factor_data, price_data, wfa_params)
            return factor_id, result
        except Exception as e:
            return factor_id, str(e)
    
    results = {}
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {executor.submit(process_single_factor, fid): fid 
                  for fid in factor_dict.keys()}
        
        for future in futures:
            factor_id, result = future.result()
            results[factor_id] = result
    
    return results
```

## 常见问题

### Q1: 数据长度不足错误
```
ValueError: 数据长度不足以进行WFA验证
```

**解决方案**:
```python
# 检查数据长度
min_required = wfa_params.training_window + wfa_params.testing_window
if len(factor_data) < min_required:
    # 调整参数或获取更多数据
    wfa_params.training_window = len(factor_data) // 2
    wfa_params.testing_window = len(factor_data) // 10
```

### Q2: 绩效指标异常值
```
指标 sharpe_ratio 值异常: inf，设为0
```

**解决方案**:
```python
# 检查数据质量
def check_data_quality(pnl_series):
    if pnl_series.std() == 0:
        print("⚠️ PnL序列方差为0，可能因子无效")
    if np.any(np.isinf(pnl_series)):
        print("⚠️ PnL序列包含无穷值")
    if pnl_series.isna().mean() > 0.1:
        print("⚠️ PnL序列缺失值过多")
```

### Q3: 内存使用过高
**解决方案**:
- 使用生成器进行批量处理
- 定期执行垃圾回收
- 减少同时处理的因子数量
- 使用更小的数据类型 (float32 vs float64)

### Q4: 处理速度慢
**解决方案**:
- 确保numba函数正常工作
- 使用并行处理
- 优化数据预处理步骤
- 减少不必要的数据复制

## 扩展功能

### 自定义绩效指标
```python
def custom_metrics_calculator(pnl_series):
    """自定义绩效指标计算"""
    custom_metrics = {}
    
    # 添加自定义指标
    custom_metrics['profit_factor'] = (
        pnl_series[pnl_series > 0].sum() / 
        abs(pnl_series[pnl_series < 0].sum())
    )
    
    custom_metrics['max_consecutive_losses'] = (
        (pnl_series < 0).astype(int)
        .groupby((pnl_series >= 0).cumsum())
        .sum().max()
    )
    
    return custom_metrics
```

### 结果可视化
```python
import matplotlib.pyplot as plt

def plot_wfa_results(result):
    """绘制WFA结果"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # PnL曲线
    cumulative_pnl = result.pnl_series.cumsum()
    axes[0, 0].plot(cumulative_pnl.index, cumulative_pnl.values)
    axes[0, 0].set_title('累积PnL曲线')
    
    # 回撤曲线
    rolling_max = cumulative_pnl.expanding().max()
    drawdown = (cumulative_pnl - rolling_max) / rolling_max
    axes[0, 1].fill_between(drawdown.index, 0, drawdown.values, alpha=0.3)
    axes[0, 1].set_title('回撤曲线')
    
    # PnL分布
    axes[1, 0].hist(result.pnl_series, bins=50, alpha=0.7)
    axes[1, 0].set_title('PnL分布')
    
    # 滚动夏普比率
    rolling_sharpe = (
        result.pnl_series.rolling(60)
        .apply(lambda x: x.mean() / x.std() * np.sqrt(252))
    )
    axes[1, 1].plot(rolling_sharpe.index, rolling_sharpe.values)
    axes[1, 1].set_title('滚动夏普比率')
    
    plt.tight_layout()
    plt.show()
```

---

**文档版本**: 1.0  
**最后更新**: 2024年12月  
**维护者**: Augment Agent
