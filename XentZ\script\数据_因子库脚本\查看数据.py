#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键查看FactorZoo数据
快速浏览最新的因子数据文件
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from factorzoo.utils.data_viewer import DataViewer
from factorzoo.utils.batch_tools import BatchTools

def main():
    print("🔍 FactorZoo数据快速查看")
    print("=" * 50)
    
    # 初始化工具
    batch_tools = BatchTools()
    viewer = DataViewer()
    
    # 1. 显示最新批次
    print("📅 最新批次数据:")
    print("-" * 30)
    batches = batch_tools.manager.get_available_batches()
    if batches:
        # 按时间排序，显示最新5个
        latest_batches = sorted(batches, reverse=True)[:5]
        for i, batch_id in enumerate(latest_batches, 1):
            try:
                batch_info = batch_tools.manager.get_batch_info(batch_id)
                symbol = batch_info.get('symbol', 'Unknown')
                creation_time = batch_info.get('creation_time', '')[:10]
                factor_counts = batch_info.get('factor_counts', {})
                total_factors = sum(factor_counts.values()) if factor_counts else 0
                
                print(f"{i}. {batch_id}")
                print(f"   品种: {symbol} | 时间: {creation_time} | 因子: {total_factors}个")
                print()
            except:
                print(f"{i}. {batch_id} (信息读取失败)")
    else:
        print("❌ 暂无批次数据")
        return
    
    # 2. 交互选择
    print("💡 操作选项:")
    print("1. 查看指定批次的详细信息")
    print("2. 查看指定品种的所有批次")
    print("3. 浏览具体数据文件")
    print("4. 退出")
    
    try:
        choice = input("\n请选择操作 (1-4): ").strip()
        
        if choice == '1':
            batch_id = input("请输入批次ID: ").strip()
            if batch_id:
                batch_tools.show_batch_files(batch_id)
                
                # 询问是否查看具体文件
                data_choice = input("\n是否查看具体数据文件? (y/n): ").strip().lower()
                if data_choice in ['y', 'yes', '是']:
                    # 自动选择第一个parquet文件
                    batch_dir = Path(batch_tools.manager.config['by_batch_dir']) / batch_id
                    parquet_files = list(batch_dir.glob('*.parquet'))
                    if parquet_files:
                        file_path = parquet_files[0]
                        print(f"\n🔍 查看文件: {file_path.name}")
                        if viewer.load_file(str(file_path)):
                            viewer.show_info()
                            viewer.show_head(5)
                    else:
                        print("❌ 未找到parquet文件")
        
        elif choice == '2':
            symbol = input("请输入品种代码 (如: 510050.SH): ").strip()
            if symbol:
                batch_tools.list_by_symbol(symbol)
        
        elif choice == '3':
            file_path = input("请输入文件路径: ").strip()
            if file_path and viewer.load_file(file_path):
                viewer.show_info()
                viewer.show_head(5)
                
                # 询问是否进入交互模式
                interactive_choice = input("\n是否进入交互模式? (y/n): ").strip().lower()
                if interactive_choice in ['y', 'yes', '是']:
                    viewer.interactive_mode()
        
        elif choice == '4':
            print("👋 退出")
            return
        
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 退出")
    except Exception as e:
        print(f"❌ 操作失败: {str(e)}")

if __name__ == "__main__":
    main() 