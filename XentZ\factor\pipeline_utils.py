''' 服务上层脚本, 脚本工作流过程中的可复用逻辑的封装 '''

from pathlib import Path
from typing import Dict, List, Any

import pandas as pd
from common.cls_base import BaseObj

def pl_results_to_csv(discovered_factors: Dict[str, Dict[str, List[Any]]], file_path: str):
    """
    统计一个嵌套的因子结果字典，并将其扁平化后保存到CSV文件。
    Args:
        discovered_factors (Dict[str, Dict[str, List[Any]]]):
            嵌套的因子字典，期望的结构是：
            {
                'symbol_1': {
                    'label_A': [factor_expr_1, factor_expr_2],
                    'label_A_pj': [pj_factor_1],
                    'label_B': [...]
                },
                'symbol_2': {...}
            }
            其中，`factor_expr` 是可以被 `str()` 转换的任何对象。
        file_path (str): 结果CSV文件的完整保存路径。
    """
    if not discovered_factors:
        BaseObj.log("结果字典为空，无需保存。", level="WARNING")
        return

    symbols = list(discovered_factors.keys())
    all_label_keys = set()
    for symbol_data in discovered_factors.values():
        all_label_keys.update(symbol_data.keys())

    # 提取基础的label名称（不含_pj后缀）
    label_cols = sorted(list(set(key.replace('_pj', '') for key in all_label_keys)))

    # 统计结果
    total_factors = 0
    total_pj_factors = 0
    for symbol in symbols:
        symbol_total = 0
        symbol_pj_total = 0
        if symbol not in discovered_factors:
            continue

        for label_col in label_cols:
            # 统计f类因子
            if label_col in discovered_factors[symbol]:
                factor_count = len(discovered_factors[symbol][label_col])
                symbol_total += factor_count
                total_factors += factor_count

            # 统计pj类因子
            pj_key = f"{label_col}_pj"
            if pj_key in discovered_factors[symbol]:
                pj_count = len(discovered_factors[symbol][pj_key])
                symbol_pj_total += pj_count
                total_pj_factors += pj_count

        if symbol_total > 0 or symbol_pj_total > 0:
            BaseObj.log(f"{symbol}: f类{symbol_total}个 + pj类{symbol_pj_total}个", level="DEBUG")

    grand_total = total_factors + total_pj_factors
    if grand_total == 0:
        BaseObj.log("全局未发现任何因子。", level="WARNING")
        return

    BaseObj.log(f"全局总计: {grand_total} 个独特因子（f类{total_factors} + pj类{total_pj_factors}）", level="DEBUG")

    # 保存结果到CSV
    all_results = []
    for symbol in symbols:
        if symbol not in discovered_factors:
            continue
        for label_col in label_cols:
            # 保存f类因子
            if label_col in discovered_factors[symbol] and discovered_factors[symbol][label_col]:
                for i, factor in enumerate(discovered_factors[symbol][label_col], 1):
                    all_results.append({
                        'symbol': symbol,
                        'label': label_col,
                        'factor_type': 'f',
                        'factor_id': i,
                        'expression': str(factor)
                    })

            # 保存pj类因子
            pj_key = f"{label_col}_pj"
            if pj_key in discovered_factors[symbol] and discovered_factors[symbol][pj_key]:
                for i, factor in enumerate(discovered_factors[symbol][pj_key], 1):
                    all_results.append({
                        'symbol': symbol,
                        'label': label_col,
                        'factor_type': 'pj',
                        'factor_id': i,
                        'expression': str(factor)
                    })

    if all_results:
        result_df = pd.DataFrame(all_results)
        output_path_obj = Path(file_path)
        output_path_obj.parent.mkdir(parents=True, exist_ok=True)
        result_df.to_csv(output_path_obj, index=False)
        BaseObj.log(f"综合结果已保存至: {output_path_obj}", level="DEBUG")