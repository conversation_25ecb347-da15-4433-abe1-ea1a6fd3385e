#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WFA验证报告生成器
集成quantstats_lumi和matplotlib生成专业的绩效分析报告

核心功能：
1. quantstats_lumi专业HTML报告生成
2. 自定义matplotlib图表生成
3. 批量报告管理和索引
4. 报告模板和样式定制

技术特点：
- 采用过程式编程风格，流程清晰
- 使用推导式优化性能
- 严格的日志分级记录
- 完整的异常处理机制
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import warnings
import time

from common.cls_base import BaseObj
from config import REPORTS_DIR

# 设置matplotlib中文支持和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
warnings.filterwarnings('ignore')


class QuantStatsReportGenerator(BaseObj):
    """quantstats_lumi专业报告生成器"""

    def __init__(self):
        """初始化quantstats_lumi报告生成器"""
        super().__init__()

        # 尝试导入quantstats_lumi
        try:
            import quantstats_lumi as qs
            self.qs = qs
            self.qs_available = True
            self.log("quantstats_lumi库导入成功", "INFO")
        except ImportError:
            self.qs = None
            self.qs_available = False
            self.log("quantstats_lumi库未安装，将跳过HTML报告生成", "WARNING")
    
    def generate_html_report(self, returns_series: pd.Series,
                           benchmark_series: Optional[pd.Series] = None,
                           output_path: str = None,
                           title: str = "WFA验证报告") -> str:
        """
        生成quantstats_lumi HTML报告

        流程：
        1. 验证数据有效性
        2. 配置quantstats_lumi参数
        3. 生成HTML报告
        4. 保存到指定路径
        
        Args:
            returns_series: 收益率序列
            benchmark_series: 基准收益率序列（可选）
            output_path: 输出路径
            title: 报告标题
            
        Returns:
            str: 报告文件路径
        """
        if not self.qs_available:
            self.log("quantstats_lumi不可用，跳过HTML报告生成", "WARNING")
            return ""

        self.log(f"开始生成quantstats_lumi HTML报告: {title}", "INFO")
        start_time = time.time()
        
        try:
            # 步骤1: 验证数据有效性
            if returns_series.empty:
                self.log("收益率序列为空", "ERROR")
                return ""
            
            # 移除NaN值
            clean_returns = returns_series.dropna()
            if len(clean_returns) < 10:
                self.log("有效数据点过少，无法生成报告", "WARNING")
                return ""
            
            # 步骤2: 配置输出路径
            if output_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_path = REPORTS_DIR / f"quantstats_lumi_report_{timestamp}.html"
            else:
                output_path = Path(output_path)

            output_path.parent.mkdir(parents=True, exist_ok=True)

            # 步骤3: 配置quantstats_lumi参数
            self.qs.extend_pandas()  # 扩展pandas功能
            
            # 步骤4: 生成HTML报告
            if benchmark_series is not None and not benchmark_series.empty:
                # 有基准的报告
                clean_benchmark = benchmark_series.dropna()
                # 对齐数据
                common_index = clean_returns.index.intersection(clean_benchmark.index)
                if len(common_index) > 10:
                    aligned_returns = clean_returns.loc[common_index]
                    aligned_benchmark = clean_benchmark.loc[common_index]
                    
                    self.qs.reports.html(
                        aligned_returns,
                        benchmark=aligned_benchmark,
                        output=str(output_path),
                        title=title
                    )
                else:
                    # 基准数据不足，生成无基准报告
                    self.qs.reports.html(
                        clean_returns,
                        output=str(output_path),
                        title=title
                    )
            else:
                # 无基准的报告
                self.qs.reports.html(
                    clean_returns,
                    output=str(output_path),
                    title=title
                )
            
            elapsed_time = time.time() - start_time
            self.log(f"HTML报告生成完成: {output_path}, 耗时{elapsed_time:.2f}秒", "INFO")
            
            return str(output_path)
            
        except Exception as e:
            self.log(f"HTML报告生成失败: {str(e)}", "ERROR")
            return ""
    
    def generate_metrics_summary(self, returns_series: pd.Series,
                               benchmark_series: Optional[pd.Series] = None) -> Dict[str, float]:
        """
        生成绩效指标摘要
        
        Args:
            returns_series: 收益率序列
            benchmark_series: 基准收益率序列（可选）
            
        Returns:
            Dict[str, float]: 绩效指标字典
        """
        if not self.qs_available:
            return self._calculate_basic_metrics(returns_series)
        
        try:
            clean_returns = returns_series.dropna()
            if len(clean_returns) < 10:
                return {}
            
            # 使用quantstats_lumi计算详细指标
            metrics = {
                'total_return': self.qs.stats.comp(clean_returns),
                'annual_return': self.qs.stats.cagr(clean_returns),
                'volatility': self.qs.stats.volatility(clean_returns),
                'sharpe_ratio': self.qs.stats.sharpe(clean_returns),
                'max_drawdown': self.qs.stats.max_drawdown(clean_returns),
                'calmar_ratio': self.qs.stats.calmar(clean_returns),
                'win_rate': self.qs.stats.win_rate(clean_returns),
                'avg_win': self.qs.stats.avg_win(clean_returns),
                'avg_loss': self.qs.stats.avg_loss(clean_returns),
                'skewness': self.qs.stats.skew(clean_returns),
                'kurtosis': self.qs.stats.kurtosis(clean_returns)
            }
            
            # 如果有基准，计算相对指标
            if benchmark_series is not None and not benchmark_series.empty:
                clean_benchmark = benchmark_series.dropna()
                common_index = clean_returns.index.intersection(clean_benchmark.index)
                if len(common_index) > 10:
                    aligned_returns = clean_returns.loc[common_index]
                    aligned_benchmark = clean_benchmark.loc[common_index]
                    
                    metrics.update({
                        'alpha': self.qs.stats.alpha(aligned_returns, aligned_benchmark),
                        'beta': self.qs.stats.beta(aligned_returns, aligned_benchmark),
                        'correlation': aligned_returns.corr(aligned_benchmark),
                        'information_ratio': self.qs.stats.information_ratio(aligned_returns, aligned_benchmark)
                    })
            
            return metrics
            
        except Exception as e:
            self.log(f"指标计算失败: {str(e)}", "ERROR")
            return self._calculate_basic_metrics(returns_series)
    
    def _calculate_basic_metrics(self, returns_series: pd.Series) -> Dict[str, float]:
        """计算基础绩效指标（不依赖quantstats_lumi）"""
        try:
            clean_returns = returns_series.dropna()
            if len(clean_returns) < 2:
                return {}
            
            # 基础统计指标
            total_return = (1 + clean_returns).prod() - 1
            annual_return = (1 + clean_returns.mean()) ** 252 - 1
            volatility = clean_returns.std() * np.sqrt(252)
            sharpe_ratio = annual_return / volatility if volatility > 0 else 0
            
            # 最大回撤计算
            cumulative = (1 + clean_returns).cumprod()
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max
            max_drawdown = drawdown.min()
            
            # 胜率计算
            win_rate = (clean_returns > 0).mean()
            
            return {
                'total_return': total_return,
                'annual_return': annual_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'skewness': clean_returns.skew(),
                'kurtosis': clean_returns.kurtosis()
            }
            
        except Exception as e:
            self.log(f"基础指标计算失败: {str(e)}", "ERROR")
            return {}


class CustomChartGenerator(BaseObj):
    """自定义matplotlib图表生成器"""
    
    def __init__(self, figsize: Tuple[int, int] = (12, 8), dpi: int = 300):
        """
        初始化图表生成器
        
        Args:
            figsize: 图表尺寸
            dpi: 图表分辨率
        """
        super().__init__()
        self.figsize = figsize
        self.dpi = dpi
        self.log(f"自定义图表生成器初始化完成: figsize={figsize}, dpi={dpi}", "INFO")
    
    def generate_wfa_analysis_chart(self, wfa_results: List[Dict],
                                  output_path: str = None) -> str:
        """
        生成WFA分析图表
        
        流程：
        1. 数据预处理和验证
        2. 创建多子图布局
        3. 绘制各类分析图表
        4. 保存图表文件
        
        Args:
            wfa_results: WFA验证结果列表
            output_path: 输出路径
            
        Returns:
            str: 图表文件路径
        """
        if not wfa_results:
            self.log("WFA结果为空，无法生成图表", "WARNING")
            return ""
        
        self.log("开始生成WFA分析图表", "INFO")
        start_time = time.time()
        
        try:
            # 步骤1: 数据预处理
            valid_results = [r for r in wfa_results if r.get('performance_metrics')]
            if not valid_results:
                self.log("无有效的绩效数据", "WARNING")
                return ""
            
            # 提取绩效指标
            metrics_data = []
            for result in valid_results:
                metrics = result['performance_metrics']
                factor_info = result.get('factor_info', {})
                metrics_data.append({
                    'factor_id': factor_info.get('factor_id', 'unknown'),
                    'sharpe_ratio': metrics.get('sharpe_ratio', 0),
                    'max_drawdown': metrics.get('max_drawdown', 0),
                    'annual_return': metrics.get('annual_return', 0),
                    'win_rate': metrics.get('win_rate', 0.5),
                    'volatility': metrics.get('volatility', 0)
                })
            
            df_metrics = pd.DataFrame(metrics_data)
            
            # 步骤2: 创建图表布局
            fig, axes = plt.subplots(2, 3, figsize=self.figsize, dpi=self.dpi)
            fig.suptitle('WFA验证结果分析', fontsize=16, fontweight='bold')
            
            # 步骤3: 绘制各类图表
            # 3.1 夏普比率分布
            axes[0, 0].hist(df_metrics['sharpe_ratio'], bins=20, alpha=0.7, color='skyblue')
            axes[0, 0].set_title('夏普比率分布')
            axes[0, 0].set_xlabel('夏普比率')
            axes[0, 0].set_ylabel('频数')
            axes[0, 0].axvline(df_metrics['sharpe_ratio'].mean(), color='red', linestyle='--', label='均值')
            axes[0, 0].legend()
            
            # 3.2 最大回撤分布
            axes[0, 1].hist(df_metrics['max_drawdown'], bins=20, alpha=0.7, color='lightcoral')
            axes[0, 1].set_title('最大回撤分布')
            axes[0, 1].set_xlabel('最大回撤')
            axes[0, 1].set_ylabel('频数')
            axes[0, 1].axvline(df_metrics['max_drawdown'].mean(), color='red', linestyle='--', label='均值')
            axes[0, 1].legend()
            
            # 3.3 收益率vs风险散点图
            axes[0, 2].scatter(df_metrics['volatility'], df_metrics['annual_return'], 
                             alpha=0.6, c=df_metrics['sharpe_ratio'], cmap='viridis')
            axes[0, 2].set_title('收益率 vs 风险')
            axes[0, 2].set_xlabel('年化波动率')
            axes[0, 2].set_ylabel('年化收益率')
            
            # 3.4 胜率分布
            axes[1, 0].hist(df_metrics['win_rate'], bins=20, alpha=0.7, color='lightgreen')
            axes[1, 0].set_title('胜率分布')
            axes[1, 0].set_xlabel('胜率')
            axes[1, 0].set_ylabel('频数')
            axes[1, 0].axvline(0.5, color='red', linestyle='--', label='50%基准线')
            axes[1, 0].legend()
            
            # 3.5 绩效指标相关性热力图
            corr_matrix = df_metrics[['sharpe_ratio', 'max_drawdown', 'annual_return', 'win_rate', 'volatility']].corr()
            sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, ax=axes[1, 1])
            axes[1, 1].set_title('绩效指标相关性')
            
            # 3.6 综合评分散点图
            # 计算综合评分：夏普比率权重0.4，回撤权重0.3，胜率权重0.3
            df_metrics['composite_score'] = (
                df_metrics['sharpe_ratio'] * 0.4 + 
                (-df_metrics['max_drawdown']) * 0.3 + 
                df_metrics['win_rate'] * 0.3
            )
            
            axes[1, 2].scatter(range(len(df_metrics)), df_metrics['composite_score'], 
                             alpha=0.6, c=df_metrics['composite_score'], cmap='RdYlGn')
            axes[1, 2].set_title('因子综合评分')
            axes[1, 2].set_xlabel('因子序号')
            axes[1, 2].set_ylabel('综合评分')
            
            plt.tight_layout()
            
            # 步骤4: 保存图表
            if output_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_path = REPORTS_DIR / f"wfa_analysis_{timestamp}.png"
            else:
                output_path = Path(output_path)
            
            output_path.parent.mkdir(parents=True, exist_ok=True)
            plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight')
            plt.close()
            
            elapsed_time = time.time() - start_time
            self.log(f"WFA分析图表生成完成: {output_path}, 耗时{elapsed_time:.2f}秒", "INFO")
            
            return str(output_path)
            
        except Exception as e:
            self.log(f"WFA分析图表生成失败: {str(e)}", "ERROR")
            return ""


    def generate_time_window_analysis(self, wfa_results: List[Dict],
                                     output_path: str = None) -> str:
        """
        生成时间窗口分析图表

        显示不同时间窗口下的因子表现稳定性

        Args:
            wfa_results: WFA验证结果列表
            output_path: 输出路径

        Returns:
            str: 图表文件路径
        """
        if not wfa_results:
            return ""

        self.log("开始生成时间窗口分析图表", "INFO")

        try:
            # 提取时间序列数据
            time_series_data = []
            for result in wfa_results:
                if 'test_periods' in result and result['test_periods']:
                    for period in result['test_periods']:
                        time_series_data.append({
                            'factor_id': result.get('factor_info', {}).get('factor_id', 'unknown'),
                            'period_start': period.get('start_date'),
                            'period_end': period.get('end_date'),
                            'period_return': period.get('period_return', 0),
                            'period_sharpe': period.get('period_sharpe', 0)
                        })

            if not time_series_data:
                self.log("无时间序列数据", "WARNING")
                return ""

            df_time = pd.DataFrame(time_series_data)

            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=self.figsize, dpi=self.dpi)
            fig.suptitle('WFA时间窗口分析', fontsize=16, fontweight='bold')

            # 时间序列收益率
            for factor_id in df_time['factor_id'].unique()[:5]:  # 只显示前5个因子
                factor_data = df_time[df_time['factor_id'] == factor_id]
                axes[0, 0].plot(factor_data['period_start'], factor_data['period_return'],
                              label=factor_id, alpha=0.7)

            axes[0, 0].set_title('各时间窗口收益率')
            axes[0, 0].set_xlabel('时间')
            axes[0, 0].set_ylabel('收益率')
            axes[0, 0].legend()
            axes[0, 0].tick_params(axis='x', rotation=45)

            # 收益率稳定性分析
            factor_stability = df_time.groupby('factor_id')['period_return'].agg(['mean', 'std']).reset_index()
            axes[0, 1].scatter(factor_stability['std'], factor_stability['mean'], alpha=0.6)
            axes[0, 1].set_title('收益率稳定性分析')
            axes[0, 1].set_xlabel('收益率标准差')
            axes[0, 1].set_ylabel('平均收益率')

            # 夏普比率时间序列
            sharpe_by_period = df_time.groupby('period_start')['period_sharpe'].mean()
            axes[1, 0].plot(sharpe_by_period.index, sharpe_by_period.values, marker='o')
            axes[1, 0].set_title('平均夏普比率时间序列')
            axes[1, 0].set_xlabel('时间')
            axes[1, 0].set_ylabel('平均夏普比率')
            axes[1, 0].tick_params(axis='x', rotation=45)

            # 因子表现热力图
            pivot_data = df_time.pivot_table(values='period_return',
                                           index='factor_id',
                                           columns='period_start',
                                           aggfunc='mean')
            sns.heatmap(pivot_data.iloc[:10], cmap='RdYlGn', center=0, ax=axes[1, 1])
            axes[1, 1].set_title('因子表现热力图')

            plt.tight_layout()

            # 保存图表
            if output_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_path = REPORTS_DIR / f"wfa_time_analysis_{timestamp}.png"
            else:
                output_path = Path(output_path)

            output_path.parent.mkdir(parents=True, exist_ok=True)
            plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight')
            plt.close()

            self.log(f"时间窗口分析图表生成完成: {output_path}", "INFO")
            return str(output_path)

        except Exception as e:
            self.log(f"时间窗口分析图表生成失败: {str(e)}", "ERROR")
            return ""

    def generate_robustness_heatmap(self, wfa_results: List[Dict],
                                  output_path: str = None) -> str:
        """
        生成稳健性热力图

        显示因子在不同市场条件下的表现稳健性

        Args:
            wfa_results: WFA验证结果列表
            output_path: 输出路径

        Returns:
            str: 图表文件路径
        """
        if not wfa_results:
            return ""

        self.log("开始生成稳健性热力图", "INFO")

        try:
            # 构建稳健性矩阵
            robustness_data = []
            for result in wfa_results:
                factor_info = result.get('factor_info', {})
                metrics = result.get('performance_metrics', {})

                robustness_data.append({
                    'factor_id': factor_info.get('factor_id', 'unknown'),
                    'sharpe_ratio': metrics.get('sharpe_ratio', 0),
                    'max_drawdown': -metrics.get('max_drawdown', 0),  # 转为正值
                    'win_rate': metrics.get('win_rate', 0.5),
                    'calmar_ratio': metrics.get('calmar_ratio', 0),
                    'volatility': metrics.get('volatility', 0)
                })

            df_robust = pd.DataFrame(robustness_data)

            if df_robust.empty:
                self.log("无稳健性数据", "WARNING")
                return ""

            # 标准化数据（Z-score）
            numeric_cols = ['sharpe_ratio', 'max_drawdown', 'win_rate', 'calmar_ratio', 'volatility']
            df_normalized = df_robust.copy()
            for col in numeric_cols:
                df_normalized[col] = (df_robust[col] - df_robust[col].mean()) / df_robust[col].std()

            # 创建热力图
            fig, ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)

            # 准备热力图数据
            heatmap_data = df_normalized.set_index('factor_id')[numeric_cols]

            # 绘制热力图
            sns.heatmap(heatmap_data,
                       annot=True,
                       fmt='.2f',
                       cmap='RdYlGn',
                       center=0,
                       ax=ax,
                       cbar_kws={'label': '标准化得分'})

            ax.set_title('因子稳健性热力图\n(绿色=优秀, 红色=较差)', fontsize=14, fontweight='bold')
            ax.set_xlabel('绩效指标')
            ax.set_ylabel('因子ID')

            plt.tight_layout()

            # 保存图表
            if output_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_path = REPORTS_DIR / f"wfa_robustness_heatmap_{timestamp}.png"
            else:
                output_path = Path(output_path)

            output_path.parent.mkdir(parents=True, exist_ok=True)
            plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight')
            plt.close()

            self.log(f"稳健性热力图生成完成: {output_path}", "INFO")
            return str(output_path)

        except Exception as e:
            self.log(f"稳健性热力图生成失败: {str(e)}", "ERROR")
            return ""


class BatchReportManager(BaseObj):
    """批量报告管理器"""

    def __init__(self, base_dir: str = None):
        """
        初始化批量报告管理器

        Args:
            base_dir: 报告基础目录
        """
        super().__init__()
        self.base_dir = Path(base_dir) if base_dir else REPORTS_DIR
        self.index_file = self.base_dir / "report_index.json"
        self.log(f"批量报告管理器初始化完成: {self.base_dir}", "INFO")

    def generate_batch_reports(self, wfa_results: List[Dict],
                             config: Dict = None) -> Dict[str, Any]:
        """
        批量生成WFA验证报告

        流程：
        1. 创建报告目录结构
        2. 生成quantstats_lumi HTML报告
        3. 生成自定义图表
        4. 创建汇总报告
        5. 更新报告索引

        Args:
            wfa_results: WFA验证结果列表
            config: 报告配置参数

        Returns:
            Dict[str, Any]: 报告生成结果
        """
        if not wfa_results:
            self.log("WFA结果为空，无法生成报告", "WARNING")
            return {"status": "failed", "error": "无数据"}

        self.log(f"开始批量生成报告: {len(wfa_results)}个因子", "INFO")
        start_time = time.time()

        try:
            # 步骤1: 创建报告目录结构
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_session_dir = self.base_dir / f"L3_WFA_session_{timestamp}"
            report_session_dir.mkdir(parents=True, exist_ok=True)

            # 子目录结构
            html_dir = report_session_dir / "html_reports"
            charts_dir = report_session_dir / "charts"
            data_dir = report_session_dir / "data"

            for dir_path in [html_dir, charts_dir, data_dir]:
                dir_path.mkdir(exist_ok=True)

            # 步骤2: 生成报告
            report_results = {
                "session_id": timestamp,
                "session_dir": str(report_session_dir),
                "total_factors": len(wfa_results),
                "generated_reports": [],
                "generated_charts": [],
                "summary_metrics": {},
                "generation_time": 0
            }

            # 2.1 生成个别因子的HTML报告
            html_reports = []
            for i, result in enumerate(wfa_results):
                if 'pnl_series' in result and not result['pnl_series'].empty:
                    factor_id = result.get('factor_info', {}).get('factor_id', f'factor_{i}')
                    html_path = quantstats_generator.generate_html_report(
                        returns_series=result['pnl_series'],
                        output_path=html_dir / f"{factor_id}_report.html",
                        title=f"因子 {factor_id} WFA验证报告"
                    )
                    if html_path:
                        html_reports.append(html_path)

            report_results["generated_reports"] = html_reports

            # 2.2 生成汇总图表
            charts = []

            # WFA分析图表
            wfa_chart = chart_generator.generate_wfa_analysis_chart(
                wfa_results,
                output_path=charts_dir / "wfa_analysis.png"
            )
            if wfa_chart:
                charts.append(wfa_chart)

            # 时间窗口分析图表
            time_chart = chart_generator.generate_time_window_analysis(
                wfa_results,
                output_path=charts_dir / "time_window_analysis.png"
            )
            if time_chart:
                charts.append(time_chart)

            # 稳健性热力图
            robust_chart = chart_generator.generate_robustness_heatmap(
                wfa_results,
                output_path=charts_dir / "robustness_heatmap.png"
            )
            if robust_chart:
                charts.append(robust_chart)

            report_results["generated_charts"] = charts

            # 步骤3: 生成汇总指标
            summary_metrics = self._calculate_batch_summary(wfa_results)
            report_results["summary_metrics"] = summary_metrics

            # 步骤4: 保存原始数据
            self._save_batch_data(wfa_results, data_dir)

            # 步骤5: 生成汇总HTML报告
            summary_html = self._generate_summary_html(report_results, report_session_dir)
            if summary_html:
                report_results["summary_report"] = summary_html

            # 步骤6: 更新报告索引
            self._update_report_index(report_results)

            elapsed_time = time.time() - start_time
            report_results["generation_time"] = elapsed_time

            self.log(f"批量报告生成完成: {len(html_reports)}个HTML报告, {len(charts)}个图表, 耗时{elapsed_time:.2f}秒", "INFO")

            return {"status": "success", "results": report_results}

        except Exception as e:
            self.log(f"批量报告生成失败: {str(e)}", "ERROR")
            return {"status": "failed", "error": str(e)}

    def _calculate_batch_summary(self, wfa_results: List[Dict]) -> Dict[str, Any]:
        """计算批量验证的汇总指标"""
        try:
            # 提取所有绩效指标
            all_metrics = []
            for result in wfa_results:
                if 'performance_metrics' in result:
                    all_metrics.append(result['performance_metrics'])

            if not all_metrics:
                return {}

            # 计算汇总统计
            metrics_df = pd.DataFrame(all_metrics)

            summary = {
                'total_factors': len(wfa_results),
                'valid_factors': len(all_metrics),
                'avg_sharpe_ratio': metrics_df['sharpe_ratio'].mean(),
                'median_sharpe_ratio': metrics_df['sharpe_ratio'].median(),
                'avg_max_drawdown': metrics_df['max_drawdown'].mean(),
                'avg_win_rate': metrics_df['win_rate'].mean(),
                'top_10_percent_sharpe': metrics_df['sharpe_ratio'].quantile(0.9),
                'bottom_10_percent_sharpe': metrics_df['sharpe_ratio'].quantile(0.1),
                'sharpe_ratio_std': metrics_df['sharpe_ratio'].std(),
                'factors_above_1_sharpe': (metrics_df['sharpe_ratio'] > 1.0).sum(),
                'factors_above_0_5_sharpe': (metrics_df['sharpe_ratio'] > 0.5).sum()
            }

            return summary

        except Exception as e:
            self.log(f"汇总指标计算失败: {str(e)}", "ERROR")
            return {}

    def _save_batch_data(self, wfa_results: List[Dict], data_dir: Path) -> None:
        """保存批量验证的原始数据"""
        try:
            # 保存结果摘要
            summary_data = []
            for result in wfa_results:
                factor_info = result.get('factor_info', {})
                metrics = result.get('performance_metrics', {})

                summary_data.append({
                    'factor_id': factor_info.get('factor_id'),
                    'factor_name': factor_info.get('factor_name'),
                    'symbols': factor_info.get('symbols'),
                    'validation_status': result.get('status'),
                    **metrics
                })

            summary_df = pd.DataFrame(summary_data)
            summary_df.to_csv(data_dir / "validation_summary.csv", index=False)

            # 保存详细的PnL序列（如果需要）
            pnl_data = {}
            for result in wfa_results:
                if 'pnl_series' in result and not result['pnl_series'].empty:
                    factor_id = result.get('factor_info', {}).get('factor_id', 'unknown')
                    pnl_data[factor_id] = result['pnl_series']

            if pnl_data:
                pnl_df = pd.DataFrame(pnl_data)
                pnl_df.to_csv(data_dir / "pnl_series.csv")

            self.log(f"批量数据保存完成: {data_dir}", "DEBUG")

        except Exception as e:
            self.log(f"批量数据保存失败: {str(e)}", "ERROR")

    def _generate_summary_html(self, report_results: Dict, output_dir: Path) -> str:
        """生成汇总HTML报告"""
        try:
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>WFA验证汇总报告</title>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                    .metrics {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }}
                    .metric-card {{ background-color: #f9f9f9; padding: 15px; border-radius: 5px; text-align: center; }}
                    .charts {{ margin: 20px 0; }}
                    .chart-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }}
                    .chart-item {{ text-align: center; }}
                    .chart-item img {{ max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 5px; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>WFA验证汇总报告</h1>
                    <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <p>会话ID: {report_results['session_id']}</p>
                </div>

                <div class="metrics">
                    <div class="metric-card">
                        <h3>总因子数</h3>
                        <p style="font-size: 24px; color: #333;">{report_results['total_factors']}</p>
                    </div>
                    <div class="metric-card">
                        <h3>生成报告数</h3>
                        <p style="font-size: 24px; color: #333;">{len(report_results['generated_reports'])}</p>
                    </div>
                    <div class="metric-card">
                        <h3>生成图表数</h3>
                        <p style="font-size: 24px; color: #333;">{len(report_results['generated_charts'])}</p>
                    </div>
                    <div class="metric-card">
                        <h3>处理时间</h3>
                        <p style="font-size: 24px; color: #333;">{report_results['generation_time']:.1f}秒</p>
                    </div>
                </div>

                <div class="charts">
                    <h2>分析图表</h2>
                    <div class="chart-grid">
            """

            # 添加图表
            for chart_path in report_results['generated_charts']:
                chart_name = Path(chart_path).stem
                relative_path = Path(chart_path).relative_to(output_dir)
                html_content += f"""
                        <div class="chart-item">
                            <h3>{chart_name}</h3>
                            <img src="{relative_path}" alt="{chart_name}">
                        </div>
                """

            html_content += """
                    </div>
                </div>

                <div class="reports">
                    <h2>个别因子报告</h2>
                    <ul>
            """

            # 添加个别报告链接
            for report_path in report_results['generated_reports']:
                report_name = Path(report_path).stem
                relative_path = Path(report_path).relative_to(output_dir)
                html_content += f'<li><a href="{relative_path}">{report_name}</a></li>'

            html_content += """
                    </ul>
                </div>
            </body>
            </html>
            """

            # 保存HTML文件
            summary_path = output_dir / "summary_report.html"
            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            self.log(f"汇总HTML报告生成完成: {summary_path}", "INFO")
            return str(summary_path)

        except Exception as e:
            self.log(f"汇总HTML报告生成失败: {str(e)}", "ERROR")
            return ""

    def _update_report_index(self, report_results: Dict) -> None:
        """更新报告索引"""
        try:
            # 读取现有索引
            if self.index_file.exists():
                import json
                with open(self.index_file, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)
            else:
                index_data = {"sessions": []}

            # 添加新会话
            session_info = {
                "session_id": report_results["session_id"],
                "timestamp": datetime.now().isoformat(),
                "total_factors": report_results["total_factors"],
                "session_dir": report_results["session_dir"],
                "summary_metrics": report_results.get("summary_metrics", {}),
                "generation_time": report_results["generation_time"]
            }

            index_data["sessions"].append(session_info)

            # 保持最近50个会话
            index_data["sessions"] = index_data["sessions"][-50:]

            # 保存索引
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(index_data, f, ensure_ascii=False, indent=2)

            self.log(f"报告索引更新完成: {self.index_file}", "DEBUG")

        except Exception as e:
            self.log(f"报告索引更新失败: {str(e)}", "ERROR")


# 全局实例
quantstats_generator = QuantStatsReportGenerator()
chart_generator = CustomChartGenerator()
batch_report_manager = BatchReportManager()
