# 因子库管理系统设计文档

> 本文档记录因子库管理系统的设计思路和架构方案  
> 状态：设计阶段 | 更新时间：2024-12-25

## 📋 设计目标

### 核心需求
1. **表达式为王** - 因子表达式是核心资产，可根据表达式重新计算
2. **分层管理** - 支持GP初筛 → 流水线精筛的多步骤处理
3. **交易导向** - 面向实际交易应用，支持因子池管理
4. **性能优良** - 智能缓存避免重复计算，支持大规模因子库
5. **易于维护** - 清晰架构，便于追溯和比较

### 设计原则
- **易维护**：清晰的分层架构，各层职责明确
- **好追溯**：完整的批次信息和流水线步骤记录
- **好比较**：统一的分类体系和检索接口
- **性能好**：智能缓存机制，避免重复计算

## 🏗️ 系统架构

### 三层核心架构
```
┌─────────────────┐
│   因子池管理层   │  交易用的因子组合、因子池配置
├─────────────────┤  
│   因子表达式层   │  表达式+分类+流水线步骤信息
├─────────────────┤
│   因子值持久化层 │  parquet缓存，避免重复计算
└─────────────────┘
```

### 数据流转
```
GP挖掘 → L1初筛 → L2精筛 → L3再筛选 → 因子池 → 交易策略
   ↓        ↓        ↓         ↓        ↓
 表达式库  缓存值   更新缓存   组合配置  最终使用
```

## 🏷️ 因子分类体系

### 1. 流水线步骤维度
```python
pipeline_info = {
    'pipeline_step': 'L1',           # L1:GP初筛, L2:夏普筛选, L3:相关性筛选, L4:稳健性测试...
    'parent_step': 'L1',             # 上级步骤（L2的parent是L1）
    'step_method': 'gp_mining',      # 步骤方法：gp_mining/sharpe_filter/corr_filter/wfa_test
    'step_params': {...},            # 步骤参数（JSON格式）
    'selection_reason': '初筛通过',   # 筛选理由
}
```

### 2. 交易应用维度
```python
trading_application = {
    'signal_type': 'trend_following',     # 信号类型：trend_following/mean_reversion/breakout
    'holding_period': 'short',            # 持仓周期：intraday/short/medium/long
    'market_condition': 'trending',       # 适用市场：trending/sideways/volatile/all
    'risk_level': 'medium',               # 风险等级：low/medium/high
    'capacity': 'large',                  # 容量：small/medium/large（适用资金规模）
    'asset_universe': 'equity_etf',       # 适用资产：equity_etf/commodity_etf/bond_etf/stock
}
```

### 3. 因子特征维度
```python
factor_features = {
    'feature_family': 'momentum',         # 特征族：momentum/reversal/volatility/volume/technical
    'mathematical_form': 'ratio',        # 数学形式：ratio/difference/rank/z_score
    'time_scale': 'short_term',          # 时间尺度：intraday/short_term/medium_term/long_term
    'data_dependency': ['close', 'volume'], # 数据依赖
    'computation_complexity': 'medium',   # 计算复杂度：low/medium/high
    'memory_footprint': 'small',         # 内存占用：small/medium/large
}
```

### 4. 交易池配置维度
```python
pool_config = {
    'pool_name': 'momentum_pool_v1',      # 因子池名称
    'pool_type': 'single_factor',         # 池类型：single_factor/factor_combo/ensemble
    'selection_criteria': {...},          # 选择标准
    'weight_method': 'equal',             # 权重方法：equal/performance/adaptive
    'rebalance_freq': 'monthly',          # 再平衡频率
    'max_factors': 10,                    # 最大因子数
}
```

## 🗃️ 数据库设计

### 1. 因子表达式主表
```sql
CREATE TABLE factor_expressions (
    factor_id TEXT PRIMARY KEY,              -- 唯一ID: batch_id + "_" + sequential_num
    expression TEXT NOT NULL,                -- 因子表达式（核心）
    batch_id TEXT NOT NULL,                  -- 批次ID
    symbol TEXT NOT NULL,                    -- 标的
    frequency TEXT NOT NULL,                 -- 频率
    
    -- 流水线信息
    pipeline_step TEXT NOT NULL,             -- L1/L2/L3/L4...
    parent_step TEXT,                        -- 上级步骤
    step_method TEXT,                        -- 筛选方法
    step_params TEXT,                        -- 步骤参数(JSON)
    selection_reason TEXT,                   -- 筛选理由
    step_metric_value REAL,                  -- 该步骤的metric值
    
    -- 交易应用属性
    signal_type TEXT,                        -- 信号类型
    holding_period TEXT,                     -- 持仓周期
    market_condition TEXT,                   -- 适用市场条件
    risk_level TEXT,                         -- 风险等级
    capacity TEXT,                           -- 容量等级
    asset_universe TEXT,                     -- 适用资产
    
    -- 因子特征
    feature_family TEXT,                     -- 特征族
    mathematical_form TEXT,                  -- 数学形式
    time_scale TEXT,                         -- 时间尺度
    data_dependency TEXT,                    -- 数据依赖(JSON)
    computation_complexity TEXT,             -- 计算复杂度
    memory_footprint TEXT,                   -- 内存占用
    
    -- 元数据
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_computed DATETIME,                  -- 最后计算时间
    compute_count INTEGER DEFAULT 0,         -- 计算次数
    status TEXT DEFAULT 'active',            -- active/deprecated/archived
    tags TEXT,                               -- 自定义标签(JSON)
    notes TEXT,                              -- 备注
    
    UNIQUE(expression, batch_id),            -- 同批次内表达式唯一
    FOREIGN KEY (batch_id) REFERENCES batches(batch_id)
);
```

### 2. 批次信息表
```sql
CREATE TABLE batches (
    batch_id TEXT PRIMARY KEY,              -- 批次ID
    gp_task_uid TEXT,                       -- GP任务ID
    symbol TEXT NOT NULL,                   -- 标的
    frequency TEXT NOT NULL,                -- 频率
    data_start_date DATE,                   -- 数据开始日期
    data_end_date DATE,                     -- 数据结束日期
    strategy_type TEXT,                     -- 策略类型
    market_type TEXT,                       -- 市场类型
    
    -- GP配置信息
    gp_config TEXT,                         -- GP配置(JSON)
    base_features TEXT,                     -- 基础特征列表(JSON)
    
    -- 统计信息
    total_factors INTEGER DEFAULT 0,        -- 总因子数
    l1_factors INTEGER DEFAULT 0,           -- L1步骤因子数
    l2_factors INTEGER DEFAULT 0,           -- L2步骤因子数
    l3_factors INTEGER DEFAULT 0,           -- L3步骤因子数
    
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'active'
);
```

### 3. 因子池配置表
```sql
CREATE TABLE factor_pools (
    pool_id TEXT PRIMARY KEY,               -- 因子池ID
    pool_name TEXT NOT NULL,                -- 因子池名称
    pool_type TEXT,                         -- 池类型
    selection_criteria TEXT,                -- 选择标准(JSON)
    factor_ids TEXT,                        -- 包含的因子ID列表(JSON)
    
    -- 配置信息
    weight_method TEXT,                     -- 权重方法
    rebalance_freq TEXT,                    -- 再平衡频率
    max_factors INTEGER,                    -- 最大因子数
    
    -- 元数据
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'active'
);
```

## 💾 因子值持久化策略

### 文件组织结构
```
factor_library/
├── expressions.db                    # SQLite数据库
├── cache/                           # 因子值缓存
│   ├── {batch_id}/                 # 按批次组织
│   │   ├── base_data.parquet       # 基础数据（OHLCV等）
│   │   ├── factors_L1_001.parquet  # L1步骤因子值（分片1）
│   │   ├── factors_L1_002.parquet  # L1步骤因子值（分片2）
│   │   ├── factors_L2_001.parquet  # L2步骤因子值
│   │   └── metadata.json           # 缓存元数据
│   └── index/                      # 索引文件
│       ├── expression_hash.json    # 表达式哈希索引
│       └── dependency_graph.json   # 依赖关系图
└── pools/                          # 因子池数据
    ├── {pool_id}/
    │   ├── pool_config.json        # 池配置
    │   └── factor_values.parquet   # 池内因子值
    └── active_pools.json           # 活跃池列表
```

### 缓存策略
1. **分步骤缓存**：L1/L2/L3各步骤分别缓存
2. **分片存储**：超过列数限制时自动分片
3. **智能更新**：基于表达式哈希判断是否需要重新计算
4. **依赖追踪**：记录因子间依赖关系，支持增量计算

## 🔧 核心管理类框架

### FactorLibraryManager
```python
class FactorLibraryManager(BaseObj):
    """因子库统一管理器"""
    
    def __init__(self, library_path: str):
        self.library_path = Path(library_path)
        self.db_path = self.library_path / "expressions.db"
        self.cache_path = self.library_path / "cache"
        self.pools_path = self.library_path / "pools"
        
        # 初始化各个组件
        self._init_database()
        self._init_directories()
    
    # 批次管理
    def create_batch(self, batch_info: dict) -> str:
        """创建新批次"""
        
    def add_factors_to_batch(self, batch_id: str, factors: List[dict], 
                           pipeline_step: str = 'L1') -> List[str]:
        """向批次添加因子"""
        
    # 因子值管理
    def get_factor_values(self, factor_ids: List[str], 
                         use_cache: bool = True,
                         force_recompute: bool = False) -> pd.DataFrame:
        """获取因子值"""
        
    def cache_factor_values(self, batch_id: str, factor_values: pd.DataFrame,
                          pipeline_step: str = 'L1') -> bool:
        """缓存因子值"""
        
    # 搜索和筛选
    def search_factors(self, **filters) -> List[dict]:
        """多条件搜索因子"""
        
    def get_factors_by_step(self, batch_id: str, pipeline_step: str) -> List[dict]:
        """获取指定步骤的因子"""
        
    # 因子池管理
    def create_factor_pool(self, pool_config: dict) -> str:
        """创建因子池"""
        
    def get_pool_factors(self, pool_id: str) -> pd.DataFrame:
        """获取池内因子值"""
```

## 🔄 与现有代码整合

### 整合点
1. **与FactorLoader整合**：`get_factor_values`内部调用`FactorLoader.get_fct_df`
2. **与GP挖掘整合**：GP结果直接导入到L1步骤
3. **与流水线整合**：每个筛选步骤的结果自动进入下一个L级别

### 数据流
```
GP挖掘结果 → FactorLibraryManager.add_factors_to_batch(step='L1')
         ↓
L1因子表达式 → 流水线筛选脚本 → L2因子列表
         ↓
L2因子表达式 → 进一步筛选 → L3因子列表
         ↓
最终因子池 → 交易策略使用
```

## 📝 待完善事项

### 高优先级
1. **具体实现代码编写**
2. **缓存失效和更新机制细化**
3. **因子池管理的具体逻辑**
4. **与现有FactorLoader的集成方式**

### 中优先级
1. **WFA分析集成**
2. **可视化接口设计**
3. **性能优化策略**
4. **错误处理和日志机制**

### 低优先级
1. **分布式计算支持**
2. **云端存储适配**
3. **API接口设计**
4. **监控和运维工具**

## 🗓️ 开发计划

### Phase 1: 核心框架
- [ ] 数据库表结构实现
- [ ] 基础的FactorLibraryManager类
- [ ] 批次管理功能
- [ ] 基础缓存机制

### Phase 2: 完整功能
- [ ] 因子值计算和缓存
- [ ] 搜索和筛选功能
- [ ] 因子池管理
- [ ] 与现有代码集成

### Phase 3: 优化增强
- [ ] 性能优化
- [ ] WFA分析集成
- [ ] 可视化支持
- [ ] 完整测试

## 📄 SQL建库脚本设计

### 关键设计决策
基于最终讨论确认的需求：

1. **唯一性约束**：同一数据源（品种+周期+时段或universe+周期+时段）下因子表达式唯一
2. **ID生成规则**：批量 `F_{batch_id}_{序号}`，人工 `F_MANUAL_{日期}_{序号}`
3. **流水线管理**：记录步骤信息，父子关系代码控制
4. **文件路径**：自动推导，不在数据库存储
5. **股票池版本化**：严格记录历史变化
6. **多次评价**：支持同一因子多次、多方法评价
7. **启用控制**：因子状态管理
8. **多市场扩展**：支持A股/期货/美股/数字货币

### 待实现的完整SQL脚本
包含所有核心表结构、索引、约束和预置数据。

---

> **说明**：本文档将随着开发进展持续更新完善 