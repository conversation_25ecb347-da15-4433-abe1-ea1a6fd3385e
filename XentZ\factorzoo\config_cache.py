#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FactorZoo 性能优化配置
"""

from dataclasses import dataclass
from typing import Dict, List, Optional
from pathlib import Path
import threading


@dataclass
class CacheConfig:
    """缓存配置"""
    # 内存缓存设置
    enable_memory_cache: bool = True
    max_memory_cache_size_mb: int = 1024  # 1GB内存缓存
    cache_ttl_seconds: int = 3600  # 1小时缓存生命周期
    
    # 热点因子缓存
    enable_hot_factor_cache: bool = True
    hot_factor_threshold: int = 5  # 访问5次以上认为是热点
    max_hot_factors: int = 100  # 最多缓存100个热点因子
    
    # 表达式哈希缓存
    enable_expression_hash: bool = True
    max_expression_cache: int = 10000  # 最多缓存1万个表达式哈希
    
    # LRU缓存清理
    enable_lru_cleanup: bool = True
    cleanup_interval_seconds: int = 300  # 5分钟清理一次


@dataclass
class CompressionConfig:
    """压缩配置"""
    # 压缩算法设置
    compression_algorithm: str = 'zstd'  # zstd, gzip, lz4
    compression_level: int = 3  # 平衡压缩率和速度
    
    # 分片设置
    enable_auto_sharding: bool = True
    max_columns_per_shard: int = 150  # 超过150列自动分片
    
    # 压缩阈值
    compression_threshold_mb: float = 10.0  # 超过10MB才压缩


@dataclass
class ParallelConfig:
    """并行处理配置"""
    # 多线程设置
    enable_parallel_loading: bool = True
    max_worker_threads: int = 4  # 最多4个工作线程
    
    # 异步IO设置
    enable_async_io: bool = True
    io_buffer_size: int = 8192  # 8KB IO缓冲区
    
    # 批量处理
    enable_batch_processing: bool = True
    batch_size: int = 10  # 批量处理10个文件


@dataclass
class IndexConfig:
    """索引优化配置"""
    # 索引缓存
    enable_index_cache: bool = True
    index_cache_size: int = 1000  # 缓存1000个索引条目
    
    # 预加载
    enable_index_preload: bool = True
    preload_recent_batches: int = 10  # 预加载最近10个批次的索引
    
    # 索引更新
    enable_incremental_index: bool = True
    index_update_interval: int = 60  # 60秒更新一次索引


@dataclass
class MonitoringConfig:
    """性能监控配置"""
    # 性能指标收集
    enable_performance_metrics: bool = True
    metrics_collection_interval: int = 30  # 30秒收集一次指标
    
    # 缓存命中率统计
    enable_cache_stats: bool = True
    stats_window_size: int = 1000  # 统计最近1000次操作
    
    # 性能告警
    enable_performance_alerts: bool = True
    slow_operation_threshold_ms: int = 1000  # 超过1秒的操作告警
    low_cache_hit_threshold: float = 0.5  # 缓存命中率低于50%告警


class PerformanceConfig:
    """性能优化总配置"""
    
    def __init__(self):
        self.cache = CacheConfig()
        self.compression = CompressionConfig()
        self.parallel = ParallelConfig()
        self.index = IndexConfig()
        self.monitoring = MonitoringConfig()
        
        # 全局锁
        self._config_lock = threading.RLock()
        
        # 动态配置
        self._dynamic_config = {
            'auto_tune_cache_size': True,
            'adaptive_compression': True,
            'smart_prefetch': True
        }
    
    def update_config(self, section: str, **kwargs):
        """动态更新配置"""
        with self._config_lock:
            if hasattr(self, section):
                config_obj = getattr(self, section)
                for key, value in kwargs.items():
                    if hasattr(config_obj, key):
                        setattr(config_obj, key, value)
                        print(f"✅ 更新配置 {section}.{key} = {value}")
                    else:
                        print(f"⚠️  配置项 {section}.{key} 不存在")
            else:
                print(f"⚠️  配置节 {section} 不存在")
    
    def get_config_summary(self) -> Dict:
        """获取配置摘要"""
        return {
            'cache': {
                'memory_cache_enabled': self.cache.enable_memory_cache,
                'max_memory_mb': self.cache.max_memory_cache_size_mb,
                'hot_factor_enabled': self.cache.enable_hot_factor_cache,
                'expression_hash_enabled': self.cache.enable_expression_hash
            },
            'compression': {
                'algorithm': self.compression.compression_algorithm,
                'level': self.compression.compression_level,
                'auto_sharding': self.compression.enable_auto_sharding
            },
            'parallel': {
                'parallel_loading': self.parallel.enable_parallel_loading,
                'max_threads': self.parallel.max_worker_threads,
                'async_io': self.parallel.enable_async_io
            },
            'monitoring': {
                'metrics_enabled': self.monitoring.enable_performance_metrics,
                'cache_stats_enabled': self.monitoring.enable_cache_stats,
                'alerts_enabled': self.monitoring.enable_performance_alerts
            }
        }
    
    def optimize_for_workload(self, workload_type: str):
        """根据工作负载优化配置"""
        with self._config_lock:
            if workload_type == 'research':
                # 研究场景：重缓存，多样化访问
                self.cache.max_memory_cache_size_mb = 2048
                self.cache.hot_factor_threshold = 3
                self.cache.cache_ttl_seconds = 7200
                self.parallel.max_worker_threads = 6
                print("🔬 优化配置用于研究场景")
                
            elif workload_type == 'production':
                # 生产场景：稳定性优先，适中缓存
                self.cache.max_memory_cache_size_mb = 512
                self.cache.hot_factor_threshold = 10
                self.cache.cache_ttl_seconds = 1800
                self.parallel.max_worker_threads = 2
                self.monitoring.enable_performance_alerts = True
                print("🏭 优化配置用于生产场景")
                
            elif workload_type == 'batch':
                # 批处理场景：高吞吐，低延迟要求
                self.cache.max_memory_cache_size_mb = 4096
                self.parallel.max_worker_threads = 8
                self.parallel.batch_size = 20
                self.compression.compression_level = 1  # 快速压缩
                print("📦 优化配置用于批处理场景")
                
            elif workload_type == 'memory_constrained':
                # 内存受限场景：最小化内存使用
                self.cache.max_memory_cache_size_mb = 128
                self.cache.enable_hot_factor_cache = False
                self.compression.compression_level = 6  # 高压缩率
                self.parallel.max_worker_threads = 2
                print("💾 优化配置用于内存受限场景")
                
            else:
                print(f"⚠️  未知工作负载类型: {workload_type}")


# 全局性能配置实例
perf_config = PerformanceConfig()


def get_performance_config() -> PerformanceConfig:
    """获取全局性能配置"""
    return perf_config


if __name__ == "__main__":
    # 配置演示
    config = get_performance_config()
    
    print("📊 当前性能配置摘要:")
    summary = config.get_config_summary()
    for section, settings in summary.items():
        print(f"\n{section.upper()}:")
        for key, value in settings.items():
            print(f"  {key}: {value}")
    
    print("\n🔧 工作负载优化演示:")
    print("=" * 40)
    
    # 演示不同场景的优化
    scenarios = ['research', 'production', 'batch', 'memory_constrained']
    for scenario in scenarios:
        print(f"\n📋 {scenario} 场景:")
        config.optimize_for_workload(scenario) 