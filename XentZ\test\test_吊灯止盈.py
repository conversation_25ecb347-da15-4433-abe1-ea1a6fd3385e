#!/usr/bin/env python
# -*- coding: utf-8 -*-

from hikyuu import *
from hikyuu.interactive import *
import pandas as pd
import matplotlib.pyplot as plt

class TP_LampTrailingStop(StoplossBase):
    """跟踪止盈策略，使用吊灯方式实现
    # 多次加仓情况下以最后一次开仓为准
    # 当盈利达到指定比例后启动跟踪止盈，记录持仓过程中的最高价，
    # 当价格从最高点回撤指定比例时触发止盈离场。
    # 延迟计算由系统的tp_delay_n参数控制，这里不再使用delay_periods
    # 吊灯止盈一定属于单调递增, tp_monotonic参数不用暴露
    参数：
        activate_percent: 激活跟踪止盈的盈利百分比，如0.1表示盈利10%时激活
        trailing_percent: 从最高价回撤百分比时触发止盈，如0.03表示从最高点回撤3%
        use_high_price: 是否使用K线最高价而不是收盘价来更新最高价记录，默认为True
    """
    
    def __init__(self, activate_percent=0.1, trailing_percent=0.03, use_high_price=True):
        super(TP_LampTrailingStop, self).__init__("TP_LampTrailingStop")
        
        # 记录参数
        self.set_param("activate_percent", activate_percent)
        self.set_param("trailing_percent", trailing_percent)
        self.set_param("use_high_price", use_high_price)
        
        # 用于记录每个股票的状态
        self._stock_info = {}
        # 添加时序数据结构，用于记录止盈价格
        self._tp_price_series = {}  # 格式: {code: {datetime: price, ...}}
        self._entry_price_series = {}  # 格式: {code: {datetime: price, ...}}
        self._highest_price_series = {}  # 格式: {code: {datetime: price, ...}}
        
    def _reset(self):
        """清空内部私有变量"""
        self._stock_info = {}
        self._tp_price_series = {}
        self._entry_price_series = {}
        self._highest_price_series = {}
        
    def _clone(self):
        """克隆"""
        tp = TP_LampTrailingStop(
            activate_percent=self.get_param("activate_percent"),
            trailing_percent=self.get_param("trailing_percent"),
            use_high_price=self.get_param("use_high_price")
        )
        return tp
    
    def _calculate(self):  
        """预计算方法，在本策略中不需要预先计算，保持为空实现"""  
        pass 
            
    def get_price(self, datetime, price):
        """获取本次计算的止损价格，向系统报告
        
        参数:
            datetime: 当前日期时间
            price: 当前价格，在持仓期间是当前bar的收盘价
            
        返回:
            非零值: 触发止盈，返回止盈价格（通常是当前close）
                  注意：实际成交价格由系统决定：
                  - 如果系统未设置延迟交易，则使用当前bar的收盘价
                  - 如果系统设置了延迟交易，则使用下一个bar的开盘价
            0: 不触发止盈
        """
        if self.tm is None:
            return 0.0
            
        stock = self.to.get_stock()
        if stock.is_null():
            return 0.0
        
        code = stock.code    
        if code not in self._stock_info:
            self._stock_info[code] = {
                'highest_price': 0.0,
                'activated': False,
                'entry_price': 0.0,
                'entry_datetime': None,
                'bars_since_entry': 0
            }
            self._tp_price_series[code] = {}
            self._entry_price_series[code] = {}
            self._highest_price_series[code] = {}
                        
        stock_info = self._stock_info[code]
        
        # 获取当前持仓记录
        position = self.tm.get_position(datetime, stock)
        
        # 如果没有持仓，重置状态并返回0
        if position.number == 0:
            stock_info['highest_price'] = 0.0
            stock_info['activated'] = False  # 确保无持仓时重置激活状态
            stock_info['entry_price'] = 0.0
            stock_info['entry_datetime'] = None
            stock_info['bars_since_entry'] = 0
            # 记录空仓时的止盈价格为0
            self._tp_price_series[code][datetime] = 0.0
            self._highest_price_series[code][datetime] = 0.0
            self._entry_price_series[code][datetime] = 0.0
            
            return 0.0
            
        # 以下是持仓期间的逻辑
        
        # 获取当前K线数据，用于获取最高价
        current_high = price  # 默认使用收盘价
        
        if self.get_param("use_high_price"):
            # 尝试获取当前K线的最高价
            try:
                current_krecord = stock.get_krecord(datetime)
                if current_krecord is not None:
                    current_high = current_krecord.high
            except Exception:
                # 如果获取失败，使用收盘价
                pass
         
        # 检查是否是新建仓位或者持仓信息需要更新
        if stock_info['entry_datetime'] != position.take_datetime:
            # 新建仓位，初始化数据
            stock_info['entry_datetime'] = position.take_datetime
            stock_info['entry_price'] = price
            stock_info['bars_since_entry'] = 0
            stock_info['highest_price'] = current_high  # 使用当前价格初始化最高价
            stock_info['activated'] = False  # 关键：确保新建仓位时重置激活状态
            print(f"DEBUG: {datetime} - 新建仓位，入场价: {stock_info['entry_price']:.4f}, 当前价: {price:.4f}, 当前最高价: {current_high:.4f}")
        else:
            # 已有仓位，增加持仓天数计数
            stock_info['bars_since_entry'] += 1
            
            # 更新最高价记录
            if current_high > stock_info['highest_price']:
                old_highest = stock_info['highest_price']
                stock_info['highest_price'] = current_high
                print(f"DEBUG: {datetime} - 更新最高价: {old_highest:.4f} -> {stock_info['highest_price']:.4f}")
        
        # 记录入场价格和最高价
        self._entry_price_series[code][datetime] = stock_info['entry_price']
        self._highest_price_series[code][datetime] = stock_info['highest_price']
    
        # 计算当前盈利比例
        profit_percent = (stock_info['highest_price'] / stock_info['entry_price']) - 1.0
    
        # 检查是否激活跟踪止盈
        activate_percent = self.get_param("activate_percent")
        
        # 关键修复：检查是否应该激活止盈
        if not stock_info['activated'] and profit_percent >= activate_percent:
            stock_info['activated'] = True
            print(f"DEBUG: {datetime} - 激活跟踪止盈，最高价: {stock_info['highest_price']:.4f}, 入场价: {stock_info['entry_price']:.4f}, 盈利比例: {profit_percent:.2%}")
        
        # 打印当前状态，帮助调试
        print(f"DEBUG: {datetime} - 状态检查: 激活={stock_info['activated']}, 盈利比例={profit_percent:.2%}, 激活阈值={activate_percent:.2%}")
    
        # 计算止盈价格
        trailing_percent = self.get_param("trailing_percent")
        stop_price = 0.0
        
        # 如果已激活跟踪止盈，计算止盈价格
        if stock_info['activated']:
            stop_price = stock_info['highest_price'] * (1 - trailing_percent)
            print(f"DEBUG: {datetime} - 止盈价: {stop_price:.4f}, 当前价: {price:.4f}, 最高价: {stock_info['highest_price']:.4f}")
        
        # 记录止盈价格（即使未激活也记录，只是值为0）
        self._tp_price_series[code][datetime] = stop_price
        
        # 如果已激活跟踪止盈且当前价格低于止盈价，触发止盈
        if stock_info['activated'] and price <= stop_price:
            print(f"DEBUG: {datetime} - 触发止盈! 当前价: {price:.4f}, 止盈价: {stop_price:.4f}")
            return price  # 返回当前价格作为止盈价
                
        return 0.0

    def tp_price_to_ind(self, code: str = None):
        """将止盈价格时序数据转换为指标
        # _tp_price_series保存的时序数据可以不连续(只有持仓才会有数据, 0.0为没启动止盈时的值)
        # 本函数在sys组件run()执行后的数据分析或绘图时用到
        # run()执行前则没数据可返回
        # 返回的ind对象通过ALIGN是与K线对齐的且0.0的值被替换为nan
        """
        if code is not None:
            if code in self._tp_price_series:
                # 返回指定股票的数据
                se = pd.Series(self._tp_price_series[code])
                se.name = "TP_Price"
                se = se.replace(0.0, np.nan)
                values = se.values.tolist()
                dates = se.index.tolist()
                hku_dates = DatetimeList([Datetime(d) for d in dates])
                ind = PRICELIST(values, align_dates=hku_dates)
                aligned_ind = ALIGN(ind, self.to.get_datetime_list())
                return aligned_ind
            else:
                return None
        else:
            return None
        
    def get_highest_price_series(self, code=None):
        """获取最高价时序数据"""
        if code is not None:
            if code in self._highest_price_series:
                return pd.Series(self._highest_price_series[code])
            return None
        
        result = {}
        for code in self._highest_price_series:
            result[code] = pd.Series(self._highest_price_series[code])
        return result
        
    def get_entry_price_series(self, code=None):
        """获取入场价格时序数据"""
        if code is not None:
            if code in self._entry_price_series:
                return pd.Series(self._entry_price_series[code])
            return None
        
        result = {}
        for code in self._entry_price_series:
            result[code] = pd.Series(self._entry_price_series[code])
        return result
    

if __name__ == "__main__":    
    # 选择一只股票和时间范围
    stock_code = 'sz159915'
    start_date = Datetime(2024, 1, 1)
    end_date = Datetime(2025, 1, 1)
    
    # 获取K线数据
    stock = sm[stock_code]
    kdata = stock.get_kdata(Query(start_date, end_date))
    
    # 创建交易管理器
    tm = crtTM(init_cash=100000, cost_func=get_part('mystar.other.tc_etf'))
    
    # 创建信号指示器（双均线交叉）
    fast_ma = MA(CLOSE(), 10)
    slow_ma = MA(CLOSE(), 30)
    sg = SG_Cross(fast_ma, slow_ma)
    
    # 创建资金管理策略（固定数量）
    mm = MM_FixedPercent(0.9)
    
    # 创建止盈策略
    tp = TP_LampTrailingStop(activate_percent=0.02, trailing_percent=0.01)
    
    # 创建交易系统
    sys = SYS_Simple(tm=tm, sg=sg, mm=mm, tp=tp)
    sys.set_param("tp_delay_n", 0)  # 设置止盈延迟天数
    sys.set_param("buy_delay", False)  
    sys.set_param("sell_delay", False)  
    
    # 运行系统
    sys.run(stock, Query(start_date, end_date))
    
    # 获取交易记录
    trade_records = tm.get_trade_list()
    
    # 创建图表并绘制K线
    ax1, ax2 = create_figure(2)
    kdata.plot(axes=ax1)
    
    # 绘制均线
    fast_ma(kdata).plot(axes=ax1, color='b', legend_on=True)
    slow_ma(kdata).plot(axes=ax1, color='purple', legend_on=True)

    # 获取止盈价格时序数据
    tp_ind = tp.tp_price_to_ind(stock.code)
    tp_ind.plot(new=False,axes=ax1,color='orange', linestyle='--')
    
    VOL(kdata).plot(axes=ax2)
    
    plt.show()
    # ======================  测试数据检验 ========================
    tp_dates = tp_ind.get_datetime_list()  
    tp_df = concat_to_df(tp_dates, [tp_ind])
    tp_df.set_index('date', inplace=True)
    
    # 获取更多时序数据进行比对验证
    highest_price_series = tp.get_highest_price_series(stock.code)
    entry_price_series = tp.get_entry_price_series(stock.code)
    
    # 创建一个DataFrame来比对所有相关数据
    validation_df = pd.DataFrame(index=kdata.get_datetime_list())
    validation_df['close'] = [k.close for k in kdata]
    validation_df['high'] = [k.high for k in kdata]
    
    # 添加买卖信号和持仓状态列
    validation_df['signal'] = ''  # 空字符串表示无信号
    validation_df['position'] = 'NONE'  # 初始状态为无持仓    
    
    # 标记买卖信号
    for record in trade_records:
        if record.datetime in validation_df.index:
            if record.business == BUSINESS.BUY:
                validation_df.loc[record.datetime, 'signal'] = 'BUY'
            elif record.business == BUSINESS.SELL:
                validation_df.loc[record.datetime, 'signal'] = 'SELL'
    
    # 标记持仓状态
    current_position = 'NONE'
    for idx in validation_df.index:
        if validation_df.loc[idx, 'signal'] == 'BUY':
            current_position = 'LONG'
        elif validation_df.loc[idx, 'signal'] == 'SELL':
            current_position = 'NONE'
        validation_df.loc[idx, 'position'] = current_position
    
    # 添加止盈相关数据
    if tp_df is not None and not tp_df.empty:
        validation_df['tp_price'] = tp_df
    
    if highest_price_series is not None and not highest_price_series.empty:
        validation_df['highest_price'] = highest_price_series
    
    if entry_price_series is not None and not entry_price_series.empty:
        validation_df['entry_price'] = entry_price_series
    
    # 计算理论上的止盈价格（用于验证）
    validation_df['activated'] = False
    validation_df['calc_tp_price'] = 0.0
    
    # 只处理有持仓的部分（entry_price > 0）
    mask = validation_df['entry_price'] > 0
    if mask.any():
        # 计算盈利百分比
        validation_df.loc[mask, 'profit_percent'] = (
            validation_df.loc[mask, 'highest_price'] / 
            validation_df.loc[mask, 'entry_price'] - 1.0
        )
        
    # 计算理论上的止盈价格（用于验证）
    validation_df['activated'] = False
    validation_df['calc_tp_price'] = 0.0
    
    # 只处理有持仓的部分（entry_price > 0）
    mask = validation_df['entry_price'] > 0
    if mask.any():
        # 计算盈利百分比
        validation_df.loc[mask, 'profit_percent'] = (
            validation_df.loc[mask, 'highest_price'] / 
            validation_df.loc[mask, 'entry_price'] - 1.0
        )
        
        # 标记激活状态
        activate_percent = tp.get_param("activate_percent")
        trailing_percent = tp.get_param("trailing_percent")
        
        # 修改：按交易周期分组计算激活状态
        # 使用signal列识别新的交易周期
        trade_cycles = []
        current_cycle = []
        in_position = False
        
        for idx in validation_df.index:
            if validation_df.loc[idx, 'signal'] == 'BUY':
                # 新周期开始
                if in_position and current_cycle:
                    trade_cycles.append(current_cycle)
                current_cycle = [idx]
                in_position = True
            elif validation_df.loc[idx, 'signal'] == 'SELL':
                # 周期结束
                if in_position and current_cycle:
                    current_cycle.append(idx)
                    trade_cycles.append(current_cycle)
                    current_cycle = []
                in_position = False
            elif in_position:
                # 持仓中
                current_cycle.append(idx)
        
        # 添加最后一个未完成的周期
        if in_position and current_cycle:
            trade_cycles.append(current_cycle)
        
        # 对每个交易周期单独计算激活状态
        for cycle in trade_cycles:
            activated = False
            for idx in cycle:
                if idx in validation_df.index and mask[idx]:
                    profit_percent = validation_df.loc[idx, 'profit_percent']
                    if not activated and profit_percent >= activate_percent:
                        activated = True
                    validation_df.loc[idx, 'activated'] = activated
                    
                    # 计算理论止盈价
                    if activated:
                        validation_df.loc[idx, 'calc_tp_price'] = (
                            validation_df.loc[idx, 'highest_price'] * (1 - trailing_percent)
                        )
    
    # 比较计算的止盈价与实际止盈价
    validation_df['tp_diff'] = validation_df['tp_price'] - validation_df['calc_tp_price']
    
    # 输出验证结果
    print("\n=== 止盈计算验证 ===")
    print("止盈参数: 激活比例={}%, 回撤比例={}%".format(
        tp.get_param("activate_percent")*100, 
        tp.get_param("trailing_percent")*100
    ))
    
    # 只显示有持仓的行
    valid_rows = validation_df[validation_df['entry_price'] > 0]
    if not valid_rows.empty:
        print("\n持仓期间数据验证:")
        print(valid_rows[['signal', 'position', 'close', 'high', 'highest_price', 'entry_price', 
                          'profit_percent', 'activated', 'tp_price', 
                          'calc_tp_price', 'tp_diff']].dropna(how='all'))
    validation_df.to_csv('validation_df.csv')
    
    # 检查是否有差异
    has_diff = (abs(valid_rows['tp_diff']) > 0.0001).any()
    if has_diff:
        print("\n警告: 发现止盈价格计算差异!")
        diff_rows = valid_rows[abs(valid_rows['tp_diff']) > 0.0001]
        print(diff_rows[['signal', 'position', 'close', 'high', 'highest_price', 'entry_price', 
                         'profit_percent', 'activated', 'tp_price', 'calc_tp_price', 'tp_diff']])
        
        # 检查交易记录与持仓状态的一致性
        print("\n检查交易记录与持仓状态:")
        for record in trade_records:
            record_date = record.datetime.date()
            print(f"{record.datetime}: {record.business} {record.stock.code} {record.number}股 价格:{record.real_price}")
            
            # 检查交易记录是否在验证数据中有对应的信号
            if record.business == BUSINESS.BUY:
                # 查找最接近的日期
                closest_date = None
                min_diff = float('inf')
                for date in valid_rows.index:
                    date_diff = abs((date.date() - record_date).days)
                    if date_diff < min_diff:
                        min_diff = date_diff
                        closest_date = date
                
                if closest_date is not None:
                    signal = valid_rows.loc[closest_date, 'signal'] if 'signal' in valid_rows.columns else 'UNKNOWN'
                    print(f"  最接近的验证数据日期: {closest_date}, 信号: {signal}, 相差天数: {min_diff}")
                    if signal != 'BUY' and min_diff <= 3:  # 允许3天的误差
                        print(f"  警告: 交易记录显示买入，但验证数据中没有对应的BUY信号!")
            
            elif record.business == BUSINESS.SELL:
                # 查找最接近的日期
                closest_date = None
                min_diff = float('inf')
                for date in valid_rows.index:
                    date_diff = abs((date.date() - record_date).days)
                    if date_diff < min_diff:
                        min_diff = date_diff
                        closest_date = date
                
                if closest_date is not None:
                    signal = valid_rows.loc[closest_date, 'signal'] if 'signal' in valid_rows.columns else 'UNKNOWN'
                    print(f"  最接近的验证数据日期: {closest_date}, 信号: {signal}, 相差天数: {min_diff}")
                    if signal != 'SELL' and min_diff <= 3:  # 允许3天的误差
                        print(f"  警告: 交易记录显示卖出，但验证数据中没有对应的SELL信号!")
                        # 检查是否应该触发止盈
                        if closest_date in valid_rows.index:
                            row = valid_rows.loc[closest_date]
                            if row['activated'] and row['calc_tp_price'] > 0:
                                if row['close'] <= row['calc_tp_price']:
                                    print(f"  {closest_date}: 价格({row['close']:.4f})低于理论止盈价({row['calc_tp_price']:.4f})，应触发止盈")
        
        # 根据交易记录重建实际持仓周期
        print("\n根据交易记录重建实际持仓周期:")
        buy_records = [r for r in trade_records if r.business == BUSINESS.BUY]
        sell_records = [r for r in trade_records if r.business == BUSINESS.SELL]
        
        for i, buy_record in enumerate(buy_records):
            if i < len(sell_records):
                sell_record = sell_records[i]
                buy_date = buy_record.datetime
                sell_date = sell_record.datetime
                
                print(f"\n实际持仓周期 {i+1}: {buy_date} 到 {sell_date}")
                print(f"  入场价: {buy_record.real_price:.4f}")
                print(f"  出场价: {sell_record.real_price:.4f}")
                
                # 查找这个周期内的验证数据
                cycle_start = None
                cycle_end = None
                for date in valid_rows.index:
                    if date.date() >= buy_date.date() and (cycle_start is None or date < cycle_start):
                        cycle_start = date
                    if date.date() <= sell_date.date() and (cycle_end is None or date > cycle_end):
                        cycle_end = date
                
                if cycle_start is not None and cycle_end is not None:
                    cycle_mask = (valid_rows.index >= cycle_start) & (valid_rows.index <= cycle_end)
                    cycle_data = valid_rows[cycle_mask]
                    
                    if not cycle_data.empty:
                        print(f"  周期内最高价: {cycle_data['highest_price'].max():.4f}")
                        print(f"  激活状态: {cycle_data.iloc[-1]['activated']}")
                        
                        # 检查是否有止盈价格
                        has_tp = (cycle_data['tp_price'] > 0).any()
                        print(f"  是否有止盈价格: {has_tp}")
                        
                        # 检查是否有差异
                        has_cycle_diff = (abs(cycle_data['tp_diff']) > 0.0001).any()
                        print(f"  是否有计算差异: {has_cycle_diff}")
                        
                        if has_cycle_diff:
                            # 检查是否有应该触发止盈但未触发的情况
                            should_tp = False
                            for idx, row in cycle_data.iterrows():
                                if row['activated'] and row['tp_price'] == 0 and row['calc_tp_price'] > 0:
                                    if row['close'] <= row['calc_tp_price']:
                                        should_tp = True
                                        print(f"  {idx}: 价格({row['close']:.4f})低于理论止盈价({row['calc_tp_price']:.4f})，应触发止盈但未触发")
                            
                            if should_tp:
                                print("  警告: 在此周期内有应该触发止盈但未触发的情况!")         
