# XentZ 量化交易系统架构设计

## 1. 整体架构

```mermaid
graph TB
    subgraph "核心层 (Core)"
        A[数据管理<br/>datafeed] --> B[特征工程<br/>feature_utils]
        B --> C[多因子挖掘<br/>polyfactor]
        C --> D[投资组合<br/>portfolio]
        A --> E[配置管理<br/>config]
        E --> F[基础组件<br/>common]
    end
    
    subgraph "策略层 (Strategy)"
        G[Hikyuu策略<br/>sys/ind/sg/mm] --> H[BackTrader策略<br/>mystrat/backtrader]
        H --> I[自定义策略<br/>mystrat]
        I --> J[实盘交易<br/>QMT Broker]
    end
    
    subgraph "工具层 (Tools)"
        K[数据分析<br/>analyze_stats] --> L[特征选择<br/>feature_selection]
        L --> M[性能监控<br/>ResMonitor]
        M --> N[日志管理<br/>BaseObj]
    end

    核心层 --> 策略层
    核心层 --> 工具层
    策略层 --> 工具层
```

## 2. 核心模块架构

```mermaid
graph LR
    subgraph "数据流水线"
        A[HKU数据源<br/>hikyuu.interactive] --> B[数据加载器<br/>HKUDataloader]
        B --> C[表达式计算<br/>expr_funcs]
        C --> D[特征预处理<br/>FeatPreprocessing]
        D --> E[特征工程<br/>FeatEngineering]
        E --> F[特征选择<br/>FeatSelection]
    end

    subgraph "配置管理"
        G[Dynaconf配置<br/>settings.py] --> H[环境配置<br/>default/prod]
        H --> I[分层配置<br/>research/backtest/live]
    end
    
    subgraph "策略组件"
        J[Hikyuu组件<br/>sys/ind/sg/mm] --> K[BackTrader扩展<br/>backtrader_extends]
        K --> L[自定义策略<br/>mystrat]
    end
```

## 3. 数据架构设计

```mermaid
graph TD
    A[Hikyuu数据源] --> B[HKUDataloader]
    B --> C[DataFrame标准化]
    C --> D[多品种数据处理]
    
    subgraph "数据格式标准"
        E[单品种格式<br/>index: datetime<br/>columns: ohlcv]
        F[多品种格式<br/>index: datetime<br/>columns: ohlcv + symbol]
        G[透视表格式<br/>index: datetime<br/>columns: symbols]
    end
    
    subgraph "特征数据流"
        H[原始OHLCV] --> I[技术指标]
        I --> J[滞后特征]
        J --> K[滚动窗口特征]
        K --> L[TSFresh特征]
        L --> M[标准化处理]
    end
    
    D --> E
    D --> F
    D --> G
    E --> H
    F --> H
    G --> H
```

## 4. 架构说明

### 4.1 分层设计
- **核心层**: 提供数据管理、特征工程、配置管理等基础功能
  - `datafeed`: 数据加载和预处理，支持Hikyuu数据源
  - `config`: 基于Dynaconf的分层配置管理
  - `common`: 基础类和工具，包含日志、性能监控等
  - `core/polyfactor`: 多因子挖掘和表达式计算
  - `factor`: 因子库和因子管理
  - `portfolio`: 投资组合管理和资金分配

- **策略层**: 支持多种策略开发框架
  - Hikyuu原生策略组件 (sys/ind/sg/mm/ev/cn/pf等)
  - BackTrader策略框架扩展
  - 自定义策略实现
  - QMT实盘交易接口

- **工具层**: 提供分析和监控功能
  - 统计分析和可视化
  - 特征分布检验
  - 性能监控和资源管理
  - 日志记录和错误追踪

### 4.2 核心组件设计

#### 4.2.1 数据管理组件
- **HKUDataloader**: 
  - 支持多种频率数据加载 (1min, 5min, 日线, 周线等)
  - 支持多种复权方式 (前复权, 后复权, 不复权等)
  - 统一的数据格式输出
  - 多品种数据批量处理

#### 4.2.2 特征工程组件
- **FeatPreprocessing**: 
  - 缺失值处理 (前向填充, 后向填充, 插值等)
  - 异常值检测和处理 (MAD截断, Z-Score等)
  - 数据标准化和归一化
  - 数据分布转换

- **FeatEngineering**:
  - 滞后特征生成
  - 滚动窗口特征
  - TSFresh时间序列特征
  - 技术指标计算
  - 特征交叉和组合

- **FeatSelection**:
  - 基于重要性的特征选择
  - 相关性过滤
  - 统计检验特征选择
  - 特征相关性分析

#### 4.2.3 配置管理组件
- **设置系统**: 基于Dynaconf + TOML
  - 分环境配置 (default/prod)
  - 分模块配置 (research/backtest/live)
  - 动态配置更新
  - 参数缓存机制

#### 4.2.4 多因子组件
- **表达式引擎**: 
  - 基于字符串的因子表达式计算
  - 支持复杂数学运算和技术指标
  - GPLearn遗传编程因子挖掘
  - 因子有效性验证

### 4.3 数据架构特点

#### 4.3.1 统一数据格式
- **标准DataFrame格式**: 
  - 时间索引统一为datetime
  - 多品种数据包含symbol列
  - 支持多级索引结构

#### 4.3.2 多品种处理机制
- **装饰器模式**: `@calc_df_by_symbol`
  - 自动按品种分组处理
  - 支持多级索引和普通DataFrame
  - 保持数据完整性和索引一致性

#### 4.3.3 特征数据流
- **流水线处理**: 
  - 原始数据 → 特征工程 → 特征选择 → 标准化
  - 支持批量处理和增量更新
  - 内存优化和性能监控

### 4.4 策略框架集成

#### 4.4.1 多框架支持
- **Hikyuu框架**: 
  - 原生C++性能优势
  - 完整的A股数据支持
  - 丰富的技术指标库

- **BackTrader框架**:
  - 灵活的策略开发
  - 完整的回测功能
  - 多种数据源支持

#### 4.4.2 实盘交易
- **QMT Broker**: 
  - 支持A股实盘交易
  - 完整的订单管理
  - 风险控制机制

## 5. 扩展性设计

### 5.1 模块化架构
- 组件独立，接口标准化
- 支持插件式扩展
- 配置驱动的功能开关

### 5.2 多数据源支持
- 数据源适配器模式
- 统一的数据接口
- 可扩展的数据格式支持

### 5.3 特征工程扩展
- 自定义特征函数
- 外部库集成 (TSFresh, TA-Lib等)
- GPU加速支持预留

## 6. 性能考虑

### 6.1 计算性能
- 向量化计算优化
- 多进程并行处理
- 内存管理和缓存机制

### 6.2 数据处理性能
- 装饰器模式避免重复计算
- 按品种分组的并行处理
- 增量更新机制

### 6.3 监控和调试
- 内置性能监控 (ResMonitor)
- 详细的日志记录
- 资源使用情况跟踪

## 7. 开发工具支持

### 7.1 Hikyuu Hub系统
- 组件库管理 (default hub, star hub, mystar hub)
- 本地组件开发支持
- 版本管理和更新机制

### 7.2 交互式开发
- IPython集成
- 实时策略调试
- 可视化分析工具

### 7.3 测试和验证
- 单元测试框架
- 回测验证工具
- 多框架对比验证
