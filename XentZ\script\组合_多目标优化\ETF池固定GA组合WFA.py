import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import numpy as np
import pandas as pd
from datetime import datetime
from functools import partial
from multiprocessing import Pool
from deap import base, creator, tools, algorithms
import numba as nb
import matplotlib.pyplot as plt

from config import REPORTS_DIR

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

#  DEAP Base
if "FitnessMulti" not in dir(creator):
    creator.create("FitnessMulti", base.Fitness, weights=(1.0, -1.0))
    creator.create("Individual", list, fitness=creator.FitnessMulti)

''' ==================== 4.脚本核心参数集中配置 ==================== '''
ETF_PIVOT_PATH = "etf_select_20250507_1610" 
ETF_PIVOT_FILE = "etf_returns.csv"  # 相对路径
ETF_NAME_FILE = "selected_etfs.txt"  # 对应etf名称码表

#  参数
TRADING_DAYS = 252 # 一年多少天
POP_SIZE, NGEN = 800, 120 # GA
CX_PB, MUT_PB = 0.7, 0.2 # GA
MAX_DD_LIMIT = -0.05
GLOBAL_CAP = 0.35 # 避免某个品种超过40% weight
GRID_MIN_W = [i / 100 for i in range(0, 11)]
# EXCLUDE_TICKERS = {"SH", "IEF"}
ASSET_BOUNDS = {"SH518850": (0.10, 0.30),
                "VOO": (0.05, 0.25),
                "2823.HK": (0.05, 0.20)}

TRAIN_DAYS = 252  # 2 年
STEP_DAYS = 21  # 3 周（约1个月）
OPT_FREQ = 21  # 每3个月优化一次
STOP_LOSS = -0.10  # 固定止损点为-10%

TC_RATE = 0.002  # 0.02% 交易成本
MIN_TRADE = 0.01  # 调仓阈值；设 0.01 降换手

#  NUMBA JIT 函数
@nb.njit(cache=True, fastmath=True)
def metrics_jit(w, ret_mat):
    # 确保数组是连续的
    w = np.ascontiguousarray(w)
    ret_mat = np.ascontiguousarray(ret_mat)
    
    port = ret_mat @ w
    ar = port.mean() * TRADING_DAYS
    av = port.std() * np.sqrt(TRADING_DAYS)

    cum = np.cumprod(1.0 + port)

    peak = np.empty_like(cum)
    running = 0.0
    for i in range(cum.size):
        if cum[i] > running:
            running = cum[i]
        peak[i] = running

    mdd = ((cum - peak) / peak).min()
    shp = 0.0 if av == 0 else ar / av
    return ar, av, mdd, shp


@nb.njit(cache=True)
def enforce_cap_floor_jit(w, floors, caps):
    w = np.clip(w, floors, caps)
    
    # 设置最大迭代次数，避免无限循环
    max_iter = 50
    iter_count = 0
    
    while True:
        iter_count += 1
        if iter_count > max_iter:
            # 达到最大迭代次数，强制退出前再次应用上限约束
            w = w / np.sum(w)  # 先归一化
            w = np.clip(w, floors, caps)  # 再应用约束
            
            # 如果应用约束后总和不为1，进行一次最终调整
            if abs(w.sum() - 1.0) > 1e-8:
                # 找出可调整的权重
                if w.sum() > 1.0:
                    # 需要减少权重
                    room = w - floors
                    idx = room > 1e-8
                    if idx.any():
                        excess = w.sum() - 1.0
                        w[idx] -= excess * room[idx] / room[idx].sum()
                else:
                    # 需要增加权重
                    room = caps - w
                    idx = room > 1e-8
                    if idx.any():
                        deficit = 1.0 - w.sum()
                        w[idx] += deficit * room[idx] / room[idx].sum()
                
                # 最终再次应用约束并归一化
                w = np.clip(w, floors, caps)
            
            # 确保总和接近1
            w = w / np.sum(w)
            return w
        
        diff = w.sum() - 1.0
        if abs(diff) < 1e-8:
            break
        
        if diff > 0:
            room = w - floors
            idx = room > 1e-8
            if not idx.any():
                # 如果没有可用空间，尝试减小最大权重
                max_idx = np.argmax(w)
                w[max_idx] -= diff
            else:
                w[idx] -= diff * room[idx] / room[idx].sum()
        else:
            room = caps - w
            idx = room > 1e-8
            if not idx.any():
                # 如果没有可用空间，尝试增大最小权重
                min_idx = np.argmin(w)
                w[min_idx] += (-diff)
            else:
                w[idx] += (-diff) * room[idx] / room[idx].sum()
        w = np.clip(w, floors, caps)
    return w


# 约束包装
def enforce_cap_floor(w, tickers, floor):
    caps = np.array([ASSET_BOUNDS.get(t, (floor, GLOBAL_CAP))[1] for t in tickers])
    floors = np.array([ASSET_BOUNDS.get(t, (floor, GLOBAL_CAP))[0] for t in tickers])
    return enforce_cap_floor_jit(w.astype(np.float64), floors, caps)


def make_repair(floor, tickers):
    def repair(ind):
        w = np.asarray(ind, float)
        w = np.random.dirichlet(np.ones_like(w)) if w.sum() <= 0 else w / w.sum()
        ind[:] = enforce_cap_floor(w, tickers, floor).tolist()
        return ind

    return repair


#  GA 工具
def safe_dirichlet(n):
    w = np.random.dirichlet(np.ones(n))
    return w / w.sum()


def mutate(ind, indpb, repair):
    if np.random.random() < indpb:
        i, j = np.random.choice(len(ind), 2, False)
        d = np.random.uniform(-0.1, 0.1)
        ind[i] += d
        ind[j] -= d
    repair(ind)
    return ind,


def mate(ind1, ind2, alpha, repair):
    tools.cxBlend(ind1, ind2, alpha)
    repair(ind1)
    repair(ind2)
    return ind1, ind2


def evaluate_nsga(ind, ret_mat):
    ar, _, mdd, _ = metrics_jit(np.asarray(ind), ret_mat)
    return ar, abs(mdd)


#  优化
def optimize_weights(ret_train_df, tickers, pref):
    ret_mat = ret_train_df.values
    # 检查数据 --- DEBUG
    if np.isnan(ret_mat).any() or np.isinf(ret_mat).any():
        print("警告: 收益率数据包含NaN或无穷大值!")
        # 替换NaN和无穷大值
        ret_mat = np.nan_to_num(ret_mat, nan=0.0, posinf=0.0, neginf=0.0)
    # 检查数据 --- DEBUG END
    best = None
    
    # 预先计算最大可能的floor值
    n_assets = len(tickers)
    max_possible_floor = 1.0 / n_assets
    print(f"资产数量: {n_assets}, 理论最大floor值: {max_possible_floor:.4f}")
    # 调整GRID_MIN_W，只测试有解的floor值
    valid_floors = [fl for fl in GRID_MIN_W if fl * n_assets <= 1.0]
    if not valid_floors:
        print("警告: 所有floor值都导致无解问题，使用最大可能的floor值")
        valid_floors = [max_possible_floor * 0.99]  # 稍微小于理论最大值
    
    print(f"将测试以下floor值: {[f'{fl:.2f}' for fl in valid_floors]}")
    
    # for fl in GRID_MIN_W:
    for fl in valid_floors:
        print(f"\n开始处理 floor={fl:.2f}")
        repair = make_repair(fl, tickers)
        n = len(tickers)
        tb = base.Toolbox()
        tb.register("attr_float", lambda: safe_dirichlet(n).tolist())
        tb.register("individual", tools.initIterate, creator.Individual, tb.attr_float)
        tb.register("population", tools.initRepeat, list, tb.individual)
        tb.register("mate", mate, alpha=0.5, repair=repair)
        tb.register("mutate", mutate, indpb=0.9, repair=repair)
        tb.register("select", tools.selNSGA2)
        tb.register("evaluate", partial(evaluate_nsga, ret_mat=ret_mat))

        with Pool(processes=os.cpu_count()-1) as pool:
            tb.register("map", pool.map)
            pop = tb.population(POP_SIZE)
            for ind in pop: # 关键修改: 让父代从开始就满足个体约束!
                repair(ind)
            pop, _ = algorithms.eaMuPlusLambda(pop, tb, POP_SIZE, POP_SIZE,
                                               cxpb=CX_PB, mutpb=MUT_PB,
                                               ngen=NGEN, verbose=False)
            
        front = tools.sortNondominated(pop, len(pop), True)[0]
        feasible = [ind for ind in front if metrics_jit(np.asarray(ind), ret_mat)[2] >= MAX_DD_LIMIT]
        if not feasible:
            feasible = [min(front, key=lambda ind: metrics_jit(np.asarray(ind), ret_mat)[2])]
        key = (lambda ind: metrics_jit(np.asarray(ind), ret_mat)[0]) if pref == 1 \
            else (lambda ind: metrics_jit(np.asarray(ind), ret_mat)[3])
        cand = max(feasible, key=key)
        if best is None or key(cand) > key(best):
            best = cand
        # # 添加调试信息
        # repaired_weights = repair(list(best))
        # if any(w > GLOBAL_CAP for w in repaired_weights):
        #     print(f"Warning: Weights exceed global cap after repair: {repaired_weights}")
        
        # 添加调试信息
        if any(w > GLOBAL_CAP for w in list(best)):
            print(f"Warning: Weights exceed global cap after repair: {list(best)}")

        print(f"   • floor={fl:.0%} … OK (Pareto {len(front)})")
    return np.asarray(best)

def main():
    ''' ==================== 5.WFA回测: GA求解每步最优weights ==================== '''
    # 构建完整路径
    full_path = os.path.join(REPORTS_DIR, ETF_PIVOT_PATH, ETF_PIVOT_FILE)
    base_info = os.path.join(REPORTS_DIR, ETF_PIVOT_PATH, ETF_NAME_FILE) # etf名称码表
    
    # 尝试读取指定的ETF数据文件
    if os.path.exists(full_path):
        print(f"\n正在加载ETF数据: {ETF_PIVOT_FILE}")
        ret_all = pd.read_csv(full_path, index_col=0, parse_dates=True)
        print(f"加载了 {len(ret_all.columns)} 只ETF的收益率数据")
        print(f"数据时间范围: {ret_all.index[0]} 至 {ret_all.index[-1]}")
    ticks = ret_all.columns.tolist()
    dates = ret_all.index
    print(ret_all.head(3))

    while True:
        sel = input("选择目标：1=收益优先  2=夏普优先 > ").strip()
        if sel in ("1", "2"):
            pref = int(sel)
            break
        
    # 设置生成报告存放位置
    ts = datetime.now().strftime("%Y%m%d_%H%M")
    script_name = os.path.basename(__file__).split('.')[0]
    outdir = REPORTS_DIR.joinpath(f"{script_name}_{ts}")
    os.makedirs(outdir, exist_ok=True)

    start = TRAIN_DAYS
    prev_w = np.ones(len(ticks)) / len(ticks)
    nav = [1.0]
    records = []
    steps_since_last_opt = 0  # 跟踪自上次优化以来的步数

    first_w = None
    first_port = None
    nav_in = None

    print("\n🔄 Walk‑forward 开始 ...")
    while start + STEP_DAYS < len(dates):
        train = ret_all.iloc[start - TRAIN_DAYS:start]
        test = ret_all.iloc[start:start + STEP_DAYS]
        # 计算自上次优化以来的天数
        days_since_last_opt = steps_since_last_opt * STEP_DAYS

        # 只在指定频率进行优化
        if steps_since_last_opt == 0 or days_since_last_opt >= OPT_FREQ:
            print(f"\n[{dates[start].date()}] 执行优化 (距上次优化: {days_since_last_opt}天) ...")
            w = optimize_weights(train, ticks, pref)
            
            if first_w is None:
                first_w = w.copy()
                first_train = ret_all.iloc[:TRAIN_DAYS]
                first_port = (first_train * first_w).sum(axis=1)
                nav_in = (1 + first_port).cumprod()
                
            steps_since_last_opt = 0  # 重置计数器

            # 保存本次优化的权重
            opt_date = dates[start].strftime('%Y%m%d')
            opt_weights_df = pd.DataFrame({"权重": w, "百分比": (w * 100).round(2)}, index=ticks)
            opt_weights_df.to_csv(os.path.join(outdir, f"weights_{opt_date}.csv"), encoding="utf-8-sig")
            # 读取ETF名称码表
            etf_names = {}
            try:
                if os.path.exists(base_info):
                    with open(base_info, 'r', encoding='utf-8') as f:
                        for line in f:
                            line = line.strip()
                            if line and not line.startswith('#'):
                                parts = line.split(',', 1)
                                if len(parts) == 1 and '#' in line:  # 处理格式: SH512010, # 医药ETF
                                    parts = line.split('#', 1)
                                    code = parts[0].strip().rstrip(',')
                                    name = parts[1].strip()
                                elif len(parts) >= 2:  # 处理其他可能的格式
                                    code = parts[0].strip()
                                    name = parts[1].strip()
                                    if name.startswith('#'):
                                        name = name[1:].strip()
                                else:
                                    continue
                                etf_names[code] = name
            except Exception as e:
                print(f"读取ETF名称码表出错: {e}")
                etf_names = {}            

            # 绘制本次优化的权重图
            plt.figure(figsize=(12, 6))

            # 创建带名称的标签
            labels = []
            for tick in ticks:
                if tick in etf_names:
                    labels.append(f"{tick}\n{etf_names[tick]}")
                else:
                    labels.append(tick)

            # 绘制条形图
            plt.bar(range(len(ticks)), w)

            # 设置x轴标签
            plt.xticks(range(len(ticks)), labels, rotation=45, ha='right')

            # 在条形上方添加百分比标签
            for i, v in enumerate(w):
                plt.text(i, v + 0.01, f"{v:.1%}", ha='center')

            plt.title(f"优化权重 ({opt_date})")
            plt.grid(axis='y', linestyle='--', alpha=.5)
            plt.tight_layout()
            plt.savefig(os.path.join(outdir, f"weights_bar_{opt_date}.png"), dpi=300)
            plt.close()
            
        else:
            print(f"\n[{dates[start].date()}] 使用前一步权重 (距上次优化: {days_since_last_opt}天) ...")
            w = prev_w
            
        steps_since_last_opt += 1  # 增加步数计数
        
        # if (start - TRAIN_DAYS) % OPT_FREQ == 0:
        #     w = optimize_weights(train, ticks, pref)
        # else:
        #     w = prev_w

        delta = np.abs(w - prev_w)
        mask = delta < MIN_TRADE
        delta[mask] = 0
        w_adj = prev_w.copy()
        w_adj[~mask] = w[~mask]
        turnover = delta.sum()
        cost = turnover * TC_RATE
        port = (test * w_adj).sum(axis=1)
        port.iloc[0] -= cost
        nav_seg = (1 + port).cumprod() * nav[-1]
        nav.extend(nav_seg.values[1:])
        ar_seg = port.mean() * TRADING_DAYS
        mdd_seg = ((nav_seg - nav_seg.cummax()) / nav_seg.cummax()).min()
        print(f"   → turnover {turnover:.1%} | OOS年化 {ar_seg:.2%} | MDD {mdd_seg:.2%}")
        records.append((dates[start].date(), ar_seg, mdd_seg))
        prev_w = w_adj
        start += STEP_DAYS

        # 检查止损条件
        current_nav = nav[-1]
        peak_nav = max(nav)
        drawdown = (current_nav - peak_nav) / peak_nav
        if drawdown <= STOP_LOSS:
            print(f"触发止损条件，当前净值 {current_nav:.4f}, 最大回撤 {drawdown:.2%}. 停止交易.")
            break

    # 回测实际开始于 TRAIN_DAYS 位置
    start_idx = TRAIN_DAYS
    end_idx = start_idx + len(nav) - 1  # -1 是因为nav初始值为[1.0]
    nav_dates = dates[start_idx:end_idx]

    # 确保日期和净值长度匹配
    if len(nav_dates) != len(nav) - 1:
        print(f"警告：日期长度({len(nav_dates)})与净值长度({len(nav)-1})不匹配")
        # 取较短的长度
        min_len = min(len(nav_dates), len(nav) - 1)
        nav_dates = nav_dates[:min_len]
        nav = nav[:min_len+1]  # +1 是因为nav包含初始值1.0

    # 创建正确的净值序列，不包括初始值1.0
    nav_s = pd.Series(nav[1:], index=nav_dates)
    ret_oos = nav_s.pct_change().dropna()
    ann_ret = ret_oos.mean() * TRADING_DAYS
    ann_vol = ret_oos.std() * np.sqrt(TRADING_DAYS)
    cum = (1 + ret_oos).cumprod()
    mdd = ((cum - cum.cummax()) / cum.cummax()).min()
    shp = ann_ret / ann_vol if ann_vol > 0 else 0

    print("\n============== 总结(样本外) ==============")
    print(f"年化 {ann_ret:.2%} | 波动 {ann_vol:.2%} | 最大回撤 {mdd:.2%} | Sharpe {shp:.2f}")

    # 保存完整净值数据
    nav_s.to_csv(os.path.join(outdir, "nav_series_out.csv"), encoding="utf-8-sig")
    nav_s.plot(figsize=(12, 6), title="净值曲线(OUT)")
    plt.grid()
    plt.tight_layout()
    plt.savefig(os.path.join(outdir, "net_curve_out.png"), dpi=300);
    plt.close()

    if nav_in is not None:
        # 保存样本内净值数据
        nav_in.to_csv(os.path.join(outdir, "nav_series_in.csv"), encoding="utf-8-sig")
        
        # 单独绘制样本内净值曲线
        plt.figure(figsize=(12, 6))
        nav_in.plot(title="净值曲线(IN)")
        plt.grid()
        plt.tight_layout()
        plt.savefig(os.path.join(outdir, "net_curve_in.png"), dpi=300)
        plt.close()
        
        # 绘制样本内和样本外的组合图
        plt.figure(figsize=(14, 7))
        # 调整样本外净值起点与样本内终点对齐
        scale_factor = nav_in.iloc[-1] / nav_s.iloc[0]
        nav_s_aligned = nav_s * scale_factor
        
        # 创建完整的净值序列
        full_dates = nav_in.index.tolist() + nav_s.index.tolist()
        full_values = nav_in.values.tolist() + (nav_s_aligned).values.tolist()
        nav_full = pd.Series(full_values, index=full_dates)
        
        # 绘制完整净值曲线
        nav_full.plot(label="完整净值曲线")
        plt.axvline(x=nav_in.index[-1], color='r', linestyle='--', label="样本内/样本外分界")
        plt.legend()
        plt.grid()
        plt.title("完整回测净值曲线")
        plt.tight_layout()
        plt.savefig(os.path.join(outdir, "net_curve_full.png"), dpi=300)
        plt.close()

    df_alloc = pd.DataFrame({"权重": prev_w, "百分比": (prev_w * 100).round(2)}, index=ticks)
    df_alloc.to_csv(os.path.join(outdir, "weights_last.csv"), encoding="utf-8-sig")
    plt.figure(figsize=(10, 4))
    plt.bar(ticks, prev_w)
    plt.xticks(rotation=45, ha='right')
    plt.title("最终训练窗口权重")
    plt.grid(axis='y', linestyle='--', alpha=.5)
    plt.tight_layout()
    plt.savefig(os.path.join(outdir, "weights_bar_last.png"), dpi=300)
    plt.close()

    with open(os.path.join(outdir, "walk_forward_metrics.txt"), "w", encoding="utf-8") as f:
        f.write(f"AnnualReturn {ann_ret:.2%}\nVol {ann_vol:.2%}\nMDD {mdd:.2%}\nSharpe {shp:.2f}\n")
        for d, a, m in records:
            f.write(f"{d}  AnnRet {a:.2%}  MDD {m:.2%}\n")

    print(f"\n📂 结果保存在 ./{outdir}/   DONE")
    
if __name__ == "__main__":
    main()