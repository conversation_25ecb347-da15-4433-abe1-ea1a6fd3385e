"""
特征重要性可视化模块

专门用于特征选择和重要性分析的可视化，
支持XGBoost重要性分析、边际R²分析等多种图表，完全仿照文章风格。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Optional, Union
from common.cls_base import BaseObj

# 设置中文字体和文章风格
def setup_chinese_font():
    """配置中文字体，优先级从高到低尝试"""
    import matplotlib.font_manager as fm
    
    # Windows系统常见中文字体列表（按优先级排序）
    windows_fonts = [
        'Microsoft YaHei',     # 微软雅黑
        'SimHei',              # 黑体
        'KaiTi',               # 楷体
        'SimSun',              # 宋体
        'FangSong',            # 仿宋
    ]
    
    # 获取系统所有可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 寻找第一个可用的中文字体
    chinese_font = None
    for font in windows_fonts:
        if font in available_fonts:
            chinese_font = font
            break
    
    # 如果找到中文字体，优先使用；否则使用通用字体
    if chinese_font:
        plt.rcParams['font.sans-serif'] = [chinese_font, 'Arial Unicode MS', 'DejaVu Sans']
        print(f"使用中文字体: {chinese_font}")
    else:
        # 降级到英文模式，避免字体显示问题
        plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
        print("警告: 未找到中文字体，使用英文字体")
    
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 10
    return chinese_font

# 执行字体配置
CHINESE_FONT = setup_chinese_font()

class FeatImportanceViz(BaseObj):
    """特征重要性可视化工具类 - 仿照文章风格"""
    
    # 文章配色方案 - 扩展支持7种重要性类型
    COLORS = {
        # XGBoost原生重要性
        'weight': '#1f77b4',      # 蓝色
        'gain': '#ff7f0e',        # 橙色  
        'cover': '#2ca02c',       # 绿色
        'total_gain': '#9467bd',  # 紫色
        'total_cover': '#8c564b', # 棕色
        # 高级重要性方法
        'shap': '#e377c2',        # 粉色
        'permutation': '#17becf', # 青色
        # 其他颜色
        'r2_line': '#d62728',     # 红色
        'optimal_point': '#ff1493',  # 深粉色
        'grid': '#f0f0f0',        # 浅灰色
        'text': '#333333'         # 深灰色
    }
    
    # 中英文标题映射
    TITLES = {
        'zh': {
            'main_title': 'XGBoost特征重要性对比分析',
            'r2_analysis': '特征累积贡献与边际R²分析',
            'combined_analysis': 'XGBoost特征重要性综合分析报告',
            'importance_comparison': '不同重要性指标对比',
            'r2_evolution': '累积R²演化曲线',
            'feature_summary': '特征选择汇总',
            'importance_score': '重要性得分',
            'feature_count': '特征数量',
            'cumulative_r2': '累积R²',
            'marginal_gain': '边际R²增益',
            'optimal_features': '最优特征数',
            'common_features': '共同特征',
            'r2_comparison': 'R²对比',
            'feature_num_comparison': '特征数对比'
        },
        'en': {
            'main_title': 'XGBoost Feature Importance Analysis',
            'r2_analysis': 'Cumulative R² and Marginal Gain Analysis',
            'combined_analysis': 'XGBoost Feature Importance Report',
            'importance_comparison': 'Importance Metrics Comparison',
            'r2_evolution': 'Cumulative R² Evolution',
            'feature_summary': 'Feature Selection Summary',
            'importance_score': 'Importance Score',
            'feature_count': 'Feature Count',
            'cumulative_r2': 'Cumulative R²',
            'marginal_gain': 'Marginal R² Gain',
            'optimal_features': 'Optimal Features',
            'common_features': 'Common Features',
            'r2_comparison': 'R² Comparison',
            'feature_num_comparison': 'Feature Count Comparison'
        }
    }
    
    @staticmethod
    def get_title(key: str) -> str:
        """根据字体支持情况返回合适的标题"""
        if CHINESE_FONT:
            return FeatImportanceViz.TITLES['zh'][key]
        else:
            return FeatImportanceViz.TITLES['en'][key]
    
    @staticmethod
    def _parse_analysis_results(analysis_results, target_symbol: str = None):
        """
        解析分析结果，统一处理不同的输入格式
        
        Args:
            analysis_results: 分析结果，可能是dict或{symbol: dict}格式
            target_symbol: 目标symbol，如果为None则自动选择
            
        Returns:
            tuple: (analysis_data, symbol_name)
        """
        if not analysis_results:
            return None, None
            
        if isinstance(analysis_results, dict):
            # 检查是否是多symbol格式 {symbol: analysis_dict}
            first_key = next(iter(analysis_results.keys()))
            first_value = analysis_results[first_key]
            
            if isinstance(first_value, dict) and any(key in first_value for key in ['weight', 'gain', 'cover', 'total_gain', 'total_cover', 'shap', 'permutation']):
                # 多symbol格式: {symbol: {weight: {...}, gain: {...}}}
                if target_symbol and target_symbol in analysis_results:
                    return analysis_results[target_symbol], target_symbol
                else:
                    # 取第一个symbol
                    symbol_name = first_key
                    return first_value, symbol_name
            else:
                # 单symbol格式: {weight: {...}, gain: {...}}
                return analysis_results, target_symbol or "Unknown"
        else:
            return None, None
    
    @staticmethod
    def plot_importance_comparison(analysis_results, save_path=None, target_symbol=None, **kwargs):
        """绘制不同重要性指标的归一化并列对比图（参照文章风格）"""
        
        # 解析分析结果
        analysis_data, symbol_name = FeatImportanceViz._parse_analysis_results(analysis_results, target_symbol)
        
        if not analysis_data:
            BaseObj.log("分析结果为空，无法绘制图表", level="WARNING")
            return
            
        # 提取重要性数据 - 支持所有7种重要性类型
        importance_data = {}
        for imp_type in ['weight', 'gain', 'cover', 'total_gain', 'total_cover', 'shap', 'permutation']:
            if imp_type in analysis_data:
                # 🔧 智能数据结构适配：支持 importance_df 和 feature_importance
                if 'importance_df' in analysis_data[imp_type]:
                    importance_data[imp_type] = analysis_data[imp_type]['importance_df']
                elif 'feature_importance' in analysis_data[imp_type]:
                    # 将 feature_importance 字典转换为 DataFrame
                    feature_importance = analysis_data[imp_type]['feature_importance']
                    if isinstance(feature_importance, dict):
                        import pandas as pd
                        df = pd.DataFrame([
                            {'feature': k, 'importance': v} 
                            for k, v in feature_importance.items()
                        ]).sort_values('importance', ascending=False).reset_index(drop=True)
                        importance_data[imp_type] = df
        
        if not importance_data:
            BaseObj.log("未找到重要性数据", level="WARNING")
            return
        
        # 🎯 核心改进：归一化处理并构建统一数据结构
        import pandas as pd
        all_features = set()
        normalized_data = {}
        
        # 收集所有特征名称
        for imp_type, imp_df in importance_data.items():
            all_features.update(imp_df['feature'].tolist())
        
        # 取前20个最重要的特征（按第一个指标排序）
        first_type = list(importance_data.keys())[0]
        top_features = importance_data[first_type].head(20)['feature'].tolist()
        
        # 为每种重要性类型创建归一化数据
        plot_data = []
        for feature in top_features:
            for imp_type, imp_df in importance_data.items():
                feature_row = imp_df[imp_df['feature'] == feature]
                if not feature_row.empty:
                    # 归一化到0-100%
                    importance_value = feature_row['importance'].iloc[0]
                    max_importance = imp_df['importance'].max()
                    normalized_importance = (importance_value / max_importance) * 100
                    
                    plot_data.append({
                        'feature': feature,
                        'importance_type': imp_type,
                        'normalized_importance': normalized_importance,
                        'rank': top_features.index(feature)
                    })
        
        plot_df = pd.DataFrame(plot_data)
        
        # 创建文章风格的水平堆叠条形图
        fig, ax = plt.subplots(figsize=(14, 10))
        
        # 设置主标题
        fig.suptitle(f'XGBoost Feature Importance\n{symbol_name}', 
                    fontsize=16, fontweight='bold', y=0.95)
        
        # 按重要性类型分组绘制 - 动态获取可用的重要性类型
        available_types = [t for t in ['weight', 'gain', 'cover', 'total_gain', 'total_cover', 'shap', 'permutation'] 
                          if t in importance_data]
        colors = [FeatImportanceViz.COLORS[t] for t in available_types]
        labels = [t.replace('_', ' ').title() for t in available_types]
        
        # 创建y轴位置
        y_pos = range(len(top_features))
        bar_height = 0.25  # 每个条形的高度
        
        # 绘制每种重要性类型的条形
        for i, imp_type in enumerate(available_types):
            type_data = plot_df[plot_df['importance_type'] == imp_type]
            values = []
            
            # 确保数据按特征顺序排列
            for feature in top_features:
                feature_data = type_data[type_data['feature'] == feature]
                if not feature_data.empty:
                    values.append(feature_data['normalized_importance'].iloc[0])
                else:
                    values.append(0)
            
            # 计算每个条形的y位置
            y_positions = [y - bar_height + i * bar_height for y in y_pos]
            
            bars = ax.barh(y_positions, values, bar_height, 
                          color=colors[i], alpha=0.8, 
                          label=labels[i], edgecolor='white', linewidth=0.5)
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                if value > 5:  # 只为较大的值添加标签
                    ax.text(value + 1, bar.get_y() + bar.get_height()/2, 
                           f'{value:.1f}', ha='left', va='center', 
                           fontsize=8, color=FeatImportanceViz.COLORS['text'])
        
        # 设置y轴
        ax.set_yticks(y_pos)
        ax.set_yticklabels(top_features, fontsize=9)
        ax.invert_yaxis()  # 最重要的在顶部
        
        # 设置x轴
        ax.set_xlabel('Normalized Importance (%)', fontsize=12, fontweight='bold')
        ax.set_xlim(0, 110)  # 留一些空间给标签
        
        # 添加图例
        ax.legend(title='Importance Metric', loc='lower right', 
                 bbox_to_anchor=(1, 0), fontsize=10)
        
        # 美化网格
        ax.grid(True, axis='x', alpha=0.3, color=FeatImportanceViz.COLORS['grid'])
        ax.set_axisbelow(True)
        
        # 设置边框
        for spine in ax.spines.values():
            spine.set_edgecolor(FeatImportanceViz.COLORS['grid'])
            spine.set_linewidth(1)
        
        plt.tight_layout()
        
        # 保存图片
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            BaseObj.log(f"重要性对比图已保存至: {save_path}")
        
        # plt.show()
    
    @staticmethod
    def plot_marginal_r2(analysis_results, importance_type='gain', 
                        save_path=None, target_symbol=None, **kwargs):
        """绘制边际R²分析图（添加置信区间带）"""
        
        # 解析分析结果
        analysis_data, symbol_name = FeatImportanceViz._parse_analysis_results(analysis_results, target_symbol)
        
        if not analysis_data or importance_type not in analysis_data:
            BaseObj.log(f"未找到{importance_type}重要性数据", level="WARNING")
            return
        
        type_data = analysis_data[importance_type]
        
        # 提取数据
        cumulative_r2 = type_data.get('cumulative_r2', [])
        confidence_intervals = type_data.get('confidence_intervals', {})
        marginal_gains = type_data.get('marginal_gains', [])
        optimal_n_features = type_data.get('optimal_n_features', 0)
        
        if not cumulative_r2:
            BaseObj.log(f"未找到{importance_type}的R²数据", level="WARNING")
            return
        
        # 创建文章风格的双轴图表
        fig, ax1 = plt.subplots(figsize=(12, 8))
        
        # 主标题
        fig.suptitle(f'{FeatImportanceViz.get_title("r2_analysis")} - {symbol_name} ({importance_type.upper()})', 
                    fontsize=16, fontweight='bold')
        
        # 左轴：累积R²曲线
        x_values = list(range(1, len(cumulative_r2) + 1))
        line1 = ax1.plot(x_values, cumulative_r2, 
                        color=FeatImportanceViz.COLORS['r2_line'], linewidth=3, 
                        marker='o', markersize=6, markerfacecolor='white',
                        markeredgecolor=FeatImportanceViz.COLORS['r2_line'], markeredgewidth=2,
                        label='累积R²')
        
        # 🔧 新增：计算并绘制置信区间带
        # 如果没有预计算的置信区间，则基于边际增益的变化创建模拟置信区间
        if 'lower' in confidence_intervals and 'upper' in confidence_intervals:
            lower_bounds = confidence_intervals['lower']
            upper_bounds = confidence_intervals['upper']
            
            if len(lower_bounds) == len(x_values) and len(upper_bounds) == len(x_values):
                ax1.fill_between(x_values, lower_bounds, upper_bounds,
                               color=FeatImportanceViz.COLORS['r2_line'], alpha=0.2,
                               label='95%置信区间')
        else:
            # 创建模拟置信区间：基于边际增益的标准差
            if marginal_gains and len(marginal_gains) > 3:
                import numpy as np
                
                # 计算置信区间的宽度（基于边际增益的变异性）
                marginal_std = np.std(marginal_gains) if len(marginal_gains) > 1 else 0.01
                confidence_width = marginal_std * 1.96  # 95%置信区间
                
                # 逐步累积置信区间宽度（早期较宽，后期较窄）
                lower_bounds = []
                upper_bounds = []
                
                for i, r2_val in enumerate(cumulative_r2):
                    # 置信区间宽度随特征数增加而减小
                    width_factor = max(0.3, 1.0 - (i / len(cumulative_r2)) * 0.7)
                    current_width = confidence_width * width_factor
                    
                    lower_bounds.append(max(0, r2_val - current_width))
                    upper_bounds.append(min(1, r2_val + current_width))
                
                # 绘制置信区间带
                ax1.fill_between(x_values, lower_bounds, upper_bounds,
                               color=FeatImportanceViz.COLORS['r2_line'], alpha=0.15,
                               label='模拟置信区间')
        
        ax1.set_xlabel(FeatImportanceViz.get_title("feature_count"), fontsize=12, fontweight='bold')
        ax1.set_ylabel(FeatImportanceViz.get_title("cumulative_r2"), fontsize=12, fontweight='bold', color=FeatImportanceViz.COLORS['r2_line'])
        ax1.tick_params(axis='y', labelcolor=FeatImportanceViz.COLORS['r2_line'])
        ax1.grid(True, alpha=0.3, color=FeatImportanceViz.COLORS['grid'])
        
        # 右轴：边际增益柱状图
        ax2 = ax1.twinx()
        bars = ax2.bar(x_values, marginal_gains, 
                      color=FeatImportanceViz.COLORS[importance_type], alpha=0.6, 
                      width=0.6, edgecolor='white', linewidth=1,
                      label='边际R²增益')
        
        ax2.set_ylabel(FeatImportanceViz.get_title("marginal_gain"), fontsize=12, fontweight='bold', color=FeatImportanceViz.COLORS[importance_type])
        ax2.tick_params(axis='y', labelcolor=FeatImportanceViz.COLORS[importance_type])
        
        # 标记最优特征数量点
        if optimal_n_features > 0 and optimal_n_features <= len(cumulative_r2):
            ax1.axvline(x=optimal_n_features, color=FeatImportanceViz.COLORS['optimal_point'], 
                       linestyle='--', linewidth=2, alpha=0.8)
            ax1.plot(optimal_n_features, cumulative_r2[optimal_n_features-1], 
                    marker='*', markersize=15, color=FeatImportanceViz.COLORS['optimal_point'],
                    markeredgecolor='white', markeredgewidth=2)
            
            # 添加标注
            ax1.annotate(f'最优特征数: {optimal_n_features}\nR²={cumulative_r2[optimal_n_features-1]:.4f}',
                        xy=(optimal_n_features, cumulative_r2[optimal_n_features-1]),
                        xytext=(optimal_n_features + len(x_values)*0.1, cumulative_r2[optimal_n_features-1]),
                        fontsize=10, fontweight='bold',
                        bbox=dict(boxstyle="round,pad=0.3", facecolor=FeatImportanceViz.COLORS['optimal_point'], alpha=0.3),
                        arrowprops=dict(arrowstyle='->', color=FeatImportanceViz.COLORS['optimal_point'], lw=1.5))
        
        # 组合图例
        lines1, labels1 = ax1.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax1.legend(lines1 + lines2, labels1 + labels2, loc='center right', 
                  bbox_to_anchor=(0.98, 0.5), fontsize=11)
        
        # 设置坐标轴范围
        ax1.set_xlim(0.5, len(x_values) + 0.5)
        ax1.set_ylim(0, max(cumulative_r2) * 1.05)
        
        plt.tight_layout()
        
        # 保存图片
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            BaseObj.log(f"边际R²分析图已保存至: {save_path}")
        
        # plt.show()
    
    @staticmethod
    def plot_combined_analysis(analysis_results, save_path=None, target_symbol=None, **kwargs):
        """绘制综合分析图表（优化布局，避免文字重叠）"""
        
        # 解析分析结果
        analysis_data, symbol_name = FeatImportanceViz._parse_analysis_results(analysis_results, target_symbol)
        
        if not analysis_data:
            BaseObj.log("分析结果为空", level="WARNING")
            return
        
        # 创建复合图表布局（增大图表尺寸，调整间距）
        fig = plt.figure(figsize=(18, 14))  # 增大尺寸
        gs = fig.add_gridspec(3, 3, hspace=0.4, wspace=0.4)  # 增大间距
        
        # 主标题（调整位置避免重叠）
        fig.suptitle(f'XGBoost特征重要性综合分析报告 - {symbol_name}', 
                    fontsize=16, fontweight='bold', y=0.96)  # 调整y位置
        
        # 1. 重要性对比 (上排，占2/3)
        ax_importance = fig.add_subplot(gs[0, :2])
        all_importance_types = ['weight', 'gain', 'cover', 'total_gain', 'total_cover', 'shap', 'permutation']
        valid_types = [t for t in all_importance_types if t in analysis_data]
        
        if valid_types:
            # 🎯 归一化重要性对比：收集原始数据并归一化到100%
            raw_importance_means = []
            importance_labels = []
            
            for imp_type in valid_types:
                # 🔧 智能数据结构适配：支持 importance_df 和 feature_importance
                imp_df = None
                if 'importance_df' in analysis_data[imp_type]:
                    imp_df = analysis_data[imp_type]['importance_df']
                elif 'feature_importance' in analysis_data[imp_type]:
                    # 将 feature_importance 字典转换为 DataFrame
                    feature_importance = analysis_data[imp_type]['feature_importance']
                    if isinstance(feature_importance, dict):
                        import pandas as pd
                        imp_df = pd.DataFrame([
                            {'feature': k, 'importance': v} 
                            for k, v in feature_importance.items()
                        ]).sort_values('importance', ascending=False).reset_index(drop=True)
                
                if imp_df is not None and not imp_df.empty and 'importance' in imp_df.columns:
                    top_10_mean = imp_df.head(10)['importance'].mean()
                    raw_importance_means.append(top_10_mean)
                    importance_labels.append(f'{imp_type.upper()}\n(归一化)')  # 明确标明归一化
            
            # 归一化处理：每种重要性相对于其自身最大值的百分比
            if raw_importance_means:
                normalized_means = []
                for i, (imp_type, raw_mean) in enumerate(zip(valid_types, raw_importance_means)):
                    # 获取该重要性类型的所有数据
                    if 'importance_df' in analysis_data[imp_type]:
                        imp_df = analysis_data[imp_type]['importance_df']
                    else:
                        feature_importance = analysis_data[imp_type]['feature_importance']
                        import pandas as pd
                        imp_df = pd.DataFrame([
                            {'feature': k, 'importance': v} 
                            for k, v in feature_importance.items()
                        ])
                    
                    max_importance = imp_df['importance'].max()
                    normalized_value = (raw_mean / max_importance) * 100
                    normalized_means.append(normalized_value)
                
                bars = ax_importance.bar(importance_labels, normalized_means, 
                                       color=[FeatImportanceViz.COLORS[t] for t in valid_types], 
                                       alpha=0.8, edgecolor='white', linewidth=2)
                
                ax_importance.set_title('不同重要性指标对比 (归一化)', fontsize=12, fontweight='bold', pad=20)
                ax_importance.set_ylabel('归一化重要性 (%)', fontsize=10)
                ax_importance.set_ylim(0, 105)  # 设置y轴范围
                
                # 优化数值标签位置
                for bar, norm_value in zip(bars, normalized_means):
                    ax_importance.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 2,
                                     f'{norm_value:.1f}%', ha='center', va='bottom', 
                                     fontsize=9, fontweight='bold')
        
        # 2. 特征选择结果汇总 (右上) - 优化表格布局
        ax_summary = fig.add_subplot(gs[0, 2])
        
        summary_data = []
        for imp_type in valid_types:
            if imp_type in analysis_data:
                optimal_n = analysis_data[imp_type].get('optimal_n_features', 0)
                # 🔧 修正：使用最优特征数位置的R²，确保与边际R²图标注一致
                optimal_r2 = analysis_data[imp_type].get('optimal_r2', 0)
                if optimal_r2 == 0:  # 兼容旧数据格式
                    optimal_r2 = analysis_data[imp_type].get('max_r2', 0)
                summary_data.append([imp_type.upper(), optimal_n, f'{optimal_r2:.3f}'])
        
        if summary_data:
            table = ax_summary.table(cellText=summary_data,
                                   colLabels=['指标', '特征数', 'R²'],  # 简化列标题
                                   cellLoc='center', loc='center',
                                   colWidths=[0.35, 0.25, 0.4])  # 调整列宽
            table.auto_set_font_size(False)
            table.set_fontsize(8)  # 减小字体
            table.scale(1, 1.8)    # 调整表格比例
            
            # 设置表格样式
            for i in range(len(summary_data) + 1):
                for j in range(3):
                    cell = table[(i, j)]
                    if i == 0:  # 表头
                        cell.set_facecolor('#4472C4')
                        cell.set_text_props(weight='bold', color='white', size=8)
                    else:
                        cell.set_facecolor('#F2F2F2' if i % 2 == 0 else 'white')
                        cell.set_text_props(size=8)
                    cell.set_edgecolor('white')
                    cell.set_linewidth(1)
        
        ax_summary.set_title('特征选择汇总', fontsize=10, fontweight='bold', pad=15)
        ax_summary.axis('off')
        
        # 3. R²演化曲线 (中排) - 优化标注
        if 'gain' in analysis_data and 'cumulative_r2' in analysis_data['gain']:
            ax_r2 = fig.add_subplot(gs[1, :])
            
            cumulative_r2 = analysis_data['gain']['cumulative_r2']
            x_values = list(range(1, len(cumulative_r2) + 1))
            
            ax_r2.plot(x_values, cumulative_r2, color=FeatImportanceViz.COLORS['r2_line'], 
                      linewidth=2, marker='o', markersize=3)  # 减小标记大小
            
            optimal_n = analysis_data['gain'].get('optimal_n_features', 0)
            if optimal_n > 0:
                ax_r2.axvline(x=optimal_n, color=FeatImportanceViz.COLORS['optimal_point'], 
                             linestyle='--', linewidth=2, alpha=0.8)
                # 简化标注，避免重叠
                ax_r2.text(optimal_n + 1, cumulative_r2[optimal_n-1], 
                          f'最优: {optimal_n}', fontsize=9, 
                          bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
            
            ax_r2.set_title('累积R²演化曲线 (GAIN)', fontsize=12, fontweight='bold', pad=15)
            ax_r2.set_xlabel('特征数量', fontsize=10)
            ax_r2.set_ylabel('累积R²', fontsize=10)
            ax_r2.grid(True, alpha=0.3)
        
        # 4. 特征对比分析 (下排) - 简化图表
        if 'feature_comparison' in analysis_data:
            comparison = analysis_data['feature_comparison']
            
            # 共同特征 (左下) - 简化显示
            ax_common = fig.add_subplot(gs[2, 0])
            common_features = list(comparison.get('common_features', []))
            
            ax_common.text(0.5, 0.5, f'共同特征\n{len(common_features)}个', 
                          ha='center', va='center', fontsize=12, 
                          transform=ax_common.transAxes,
                          bbox=dict(boxstyle="round,pad=0.3", facecolor=FeatImportanceViz.COLORS['gain'], alpha=0.3))
            ax_common.set_xlim(0, 1)
            ax_common.set_ylim(0, 1)
            ax_common.axis('off')
            
            # R²对比 (中下) - 优化标签
            ax_r2_comp = fig.add_subplot(gs[2, 1])
            r2_comparison = comparison.get('r2_comparison', {})
            
            if r2_comparison:
                methods = list(r2_comparison.keys())
                r2_values = list(r2_comparison.values())
                colors = [FeatImportanceViz.COLORS.get(m, '#888888') for m in methods]
                
                bars = ax_r2_comp.bar(methods, r2_values, color=colors, alpha=0.8)
                ax_r2_comp.set_title('R²对比', fontsize=10, fontweight='bold')
                ax_r2_comp.set_ylabel('R²', fontsize=9)
                ax_r2_comp.tick_params(axis='x', labelsize=8)  # 减小x轴标签
                
                # 优化数值标签
                for bar, value in zip(bars, r2_values):
                    ax_r2_comp.text(bar.get_x() + bar.get_width()/2, bar.get_height() + value*0.005,
                                   f'{value:.3f}', ha='center', va='bottom', fontsize=8)
            
            # 特征数量对比 (右下)
            ax_num_comp = fig.add_subplot(gs[2, 2])
            
            feature_counts = []
            methods = []
            for imp_type in valid_types:
                if imp_type in analysis_data:
                    methods.append(imp_type.upper())
                    feature_counts.append(analysis_data[imp_type].get('optimal_n_features', 0))
            
            if feature_counts:
                colors = [FeatImportanceViz.COLORS.get(m.lower(), '#888888') for m in methods]
                bars = ax_num_comp.bar(methods, feature_counts, color=colors, alpha=0.8)
                ax_num_comp.set_title('特征数对比', fontsize=10, fontweight='bold')
                ax_num_comp.set_ylabel('数量', fontsize=9)
                ax_num_comp.tick_params(axis='x', labelsize=8)
                
                # 优化数值标签
                for bar, value in zip(bars, feature_counts):
                    ax_num_comp.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                                   f'{value}', ha='center', va='bottom', fontsize=9, fontweight='bold')
        
        # 调整整体布局
        plt.tight_layout(rect=[0, 0, 1, 0.95])  # 为suptitle留空间
        
        # 保存图片
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            BaseObj.log(f"综合分析图已保存至: {save_path}")
        
        # plt.show()
    
    @staticmethod
    def save_importance_table(analysis_results, save_path, target_symbol=None, **kwargs):
        """保存重要性分析表格（CSV格式）"""
        
        # 解析分析结果
        analysis_data, symbol_name = FeatImportanceViz._parse_analysis_results(analysis_results, target_symbol)
        
        if not analysis_data:
            BaseObj.log("分析结果为空，无法保存表格", level="WARNING")
            return
        
        # 创建综合表格 - 支持所有7种重要性类型
        all_tables = []
        
        for imp_type in ['weight', 'gain', 'cover', 'total_gain', 'total_cover', 'shap', 'permutation']:
            if imp_type in analysis_data:
                # 🔧 智能数据结构适配：支持 importance_df 和 feature_importance
                imp_df = None
                if 'importance_df' in analysis_data[imp_type]:
                    imp_df = analysis_data[imp_type]['importance_df'].copy()
                elif 'feature_importance' in analysis_data[imp_type]:
                    # 将 feature_importance 字典转换为 DataFrame
                    feature_importance = analysis_data[imp_type]['feature_importance']
                    if isinstance(feature_importance, dict):
                        imp_df = pd.DataFrame([
                            {'feature': k, 'importance': v} 
                            for k, v in feature_importance.items()
                        ]).sort_values('importance', ascending=False).reset_index(drop=True)
                
                if imp_df is not None:
                    imp_df['importance_type'] = imp_type
                    imp_df['symbol'] = symbol_name
                    all_tables.append(imp_df)
        
        if all_tables:
            combined_df = pd.concat(all_tables, ignore_index=True)
            
            # 重新排列列的顺序
            column_order = ['symbol', 'importance_type', 'feature', 'importance']
            combined_df = combined_df[column_order]
            
            # 保存CSV文件
            combined_df.to_csv(save_path, index=False, encoding='utf-8-sig')
            BaseObj.log(f"重要性分析表格已保存至: {save_path}")
        else:
            BaseObj.log("没有有效的重要性数据可保存", level="WARNING")