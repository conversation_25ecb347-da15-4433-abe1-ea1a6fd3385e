2025-06-30 14:51 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-30 14:51 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-30 15:41 | INFO     | DatabaseReset.__init__: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-30 15:41 | INFO     | DatabaseReset.__init__: Schema文件路径: d:\myquant\XentZ\script\schema.sql
2025-06-30 15:41 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-30 15:41 | INFO     | DatabaseReset.run_reset: 开始执行 XentZ 因子动物园数据库重置
2025-06-30 15:41 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-30 15:41 | INFO     | DatabaseReset.validate_files: 步骤1: 验证文件存在性...
2025-06-30 15:41 | ERROR    | DatabaseReset.run_reset: ============================================================
2025-06-30 15:41 | ERROR    | DatabaseReset.run_reset: 数据库重置失败: Schema文件不存在: d:\myquant\XentZ\script\schema.sql
2025-06-30 15:41 | ERROR    | DatabaseReset.run_reset: ============================================================
2025-06-30 15:43 | INFO     | DatabaseReset.__init__: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-30 15:43 | INFO     | DatabaseReset.__init__: Schema文件路径: D:\myquant\FZoo\schema.sql
2025-06-30 15:43 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-30 15:43 | INFO     | DatabaseReset.run_reset: 开始执行 XentZ 因子动物园数据库重置
2025-06-30 15:43 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-30 15:43 | INFO     | DatabaseReset.validate_files: 步骤1: 验证文件存在性...
2025-06-30 15:43 | ERROR    | DatabaseReset.run_reset: ============================================================
2025-06-30 15:43 | ERROR    | DatabaseReset.run_reset: 数据库重置失败: 'str' object has no attribute 'exists'
2025-06-30 15:43 | ERROR    | DatabaseReset.run_reset: ============================================================
2025-06-30 15:45 | INFO     | DatabaseReset.__init__: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-30 15:45 | INFO     | DatabaseReset.__init__: Schema文件路径: D:\myquant\FZoo\schema.sql
2025-06-30 15:45 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-30 15:45 | INFO     | DatabaseReset.run_reset: 开始执行 XentZ 因子动物园数据库重置
2025-06-30 15:45 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-30 15:45 | INFO     | DatabaseReset.validate_files: 步骤1: 验证文件存在性...
2025-06-30 15:45 | ERROR    | DatabaseReset.run_reset: ============================================================
2025-06-30 15:45 | ERROR    | DatabaseReset.run_reset: 数据库重置失败: Schema文件不存在: D:\myquant\FZoo\schema.sql
2025-06-30 15:45 | ERROR    | DatabaseReset.run_reset: ============================================================
2025-06-30 15:46 | INFO     | DatabaseReset.__init__: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-30 15:46 | INFO     | DatabaseReset.__init__: Schema文件路径: schema.sql
2025-06-30 15:46 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-30 15:46 | INFO     | DatabaseReset.run_reset: 开始执行 XentZ 因子动物园数据库重置
2025-06-30 15:46 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-30 15:46 | INFO     | DatabaseReset.validate_files: 步骤1: 验证文件存在性...
2025-06-30 15:46 | ERROR    | DatabaseReset.run_reset: ============================================================
2025-06-30 15:46 | ERROR    | DatabaseReset.run_reset: 数据库重置失败: 'str' object has no attribute 'exists'
2025-06-30 15:46 | ERROR    | DatabaseReset.run_reset: ============================================================
2025-06-30 15:47 | INFO     | DatabaseReset.__init__: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-30 15:47 | INFO     | DatabaseReset.__init__: Schema文件路径: d:\myquant\XentZ\script\因子库脚本\schema.sql
2025-06-30 15:47 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-30 15:47 | INFO     | DatabaseReset.run_reset: 开始执行 XentZ 因子动物园数据库重置
2025-06-30 15:47 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-30 15:47 | INFO     | DatabaseReset.validate_files: 步骤1: 验证文件存在性...
2025-06-30 15:47 | INFO     | DatabaseReset.validate_files: Schema文件存在: d:\myquant\XentZ\script\因子库脚本\schema.sql
2025-06-30 15:47 | INFO     | DatabaseReset.backup_database: 步骤2: 备份现有数据库...
2025-06-30 15:47 | INFO     | DatabaseReset.backup_database: 数据库已备份到: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250630_154721
2025-06-30 15:47 | INFO     | DatabaseReset.get_existing_tables: 步骤3: 获取现有表列表...
2025-06-30 15:47 | INFO     | DatabaseReset.get_existing_tables: 发现现有表 (10个): factor_batches, factor_categories, factor_evaluations, factor_performance_logs, factor_pool_members, factor_pools, factor_relationships, factor_tags, factors, universes
2025-06-30 15:47 | INFO     | DatabaseReset.drop_all_tables: 步骤4: 删除现有表 (10个)...
2025-06-30 15:47 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_batches
2025-06-30 15:47 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_categories
2025-06-30 15:47 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_evaluations
2025-06-30 15:47 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_performance_logs
2025-06-30 15:47 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_pool_members
2025-06-30 15:47 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_pools
2025-06-30 15:47 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_relationships
2025-06-30 15:47 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_tags
2025-06-30 15:47 | INFO     | DatabaseReset.drop_all_tables:   删除表: factors
2025-06-30 15:47 | INFO     | DatabaseReset.drop_all_tables:   删除表: universes
2025-06-30 15:47 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_active_factors
2025-06-30 15:47 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_batch_statistics
2025-06-30 15:47 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_factor_performance_summary
2025-06-30 15:47 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_performance_stats
2025-06-30 15:47 | INFO     | DatabaseReset.drop_all_tables: 成功删除 10 个表, 4 个视图, 0 个索引
2025-06-30 15:47 | INFO     | DatabaseReset.execute_schema: 步骤5: 执行schema.sql重建表结构...
2025-06-30 15:47 | INFO     | DatabaseReset.execute_schema: 读取schema文件，长度: 26452 字符
2025-06-30 15:47 | INFO     | DatabaseReset.execute_schema: Schema执行完成
2025-06-30 15:47 | INFO     | DatabaseReset.verify_tables: 步骤6: 验证表创建结果...
2025-06-30 15:47 | INFO     | DatabaseReset.verify_tables: 创建完成统计:
2025-06-30 15:47 | INFO     | DatabaseReset.verify_tables:   - 表 (11个): factor_batches, factor_categories, factor_evaluations, factor_performance_logs, factor_pool_members, factor_pools, factor_relationships, factor_tags, factor_values, factors, universes
2025-06-30 15:47 | INFO     | DatabaseReset.verify_tables:   - 视图 (4个): v_active_factors, v_batch_statistics, v_factor_performance_summary, v_performance_stats
2025-06-30 15:47 | INFO     | DatabaseReset.verify_tables:   - 索引 (32个): idx_evaluations_factor_id, idx_evaluations_method, idx_evaluations_period, idx_evaluations_sharpe, idx_factor_performance_logs_batch_id, idx_factor_performance_logs_memory, idx_factor_performance_logs_operation_type, idx_factor_performance_logs_start_time, idx_factor_performance_logs_total_time, idx_factor_tags_tag_name, idx_factor_tags_tag_type, idx_factor_values_date, idx_factor_values_factor_id, idx_factors_batch_id, idx_factors_computation_time, idx_factors_creation_date, idx_factors_creation_method, idx_factors_factor_type, idx_factors_generation_tool, idx_factors_market_type, idx_factors_pipeline_mode, idx_factors_pipeline_step, idx_factors_primary_category, idx_factors_resource_intensity, idx_factors_status, idx_factors_target_label, idx_factors_unique_expression, idx_pool_members_factor_id, idx_pool_members_pool_id, idx_pool_members_status, idx_universes_current, idx_universes_effective_date
2025-06-30 15:47 | INFO     | DatabaseReset.verify_tables: 所有核心表创建成功
2025-06-30 15:47 | INFO     | DatabaseReset.insert_default_data: 步骤7: 插入默认数据...
2025-06-30 15:47 | INFO     | DatabaseReset.insert_default_data: 默认数据插入完成
2025-06-30 15:47 | INFO     | DatabaseReset.insert_default_data:   - 因子分类: 10 个
2025-06-30 15:47 | INFO     | DatabaseReset.insert_default_data:   - 股票池: 2 个
2025-06-30 15:47 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-30 15:47 | INFO     | DatabaseReset.run_reset: 数据库重置完成！
2025-06-30 15:47 | INFO     | DatabaseReset.run_reset: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-30 15:47 | INFO     | DatabaseReset.run_reset: 备份路径: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250630_154721
2025-06-30 15:47 | INFO     | DatabaseReset.run_reset: 创建统计: 11表 + 4视图 + 32索引
2025-06-30 15:47 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-30 15:48 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:48 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-30 15:48 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-30 15:48 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-30 15:48 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-30 15:48 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-30 15:48 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-30 15:48 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-30 15:48 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-30 15:48 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-30 15:48 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-30 15:48 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-30 15:48 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-30 15:48 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-30 15:48 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-30 15:48 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-30 15:48 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-30 15:48 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:48 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-30 15:51 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 123.84 seconds (00:02:03)
2025-06-30 15:51 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 16.31s user, 22.28s system
2025-06-30 15:51 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 1049.73MB
2025-06-30 15:51 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-30 15:51 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:51 | INFO     | FactorLoader.get_fct_df: 开始计算597个因子表达式
2025-06-30 15:51 | INFO     | FactorLoader.get_fct_df: 返回结果: 597个指定因子
2025-06-30 15:51 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 0.98 seconds (00:00:00)
2025-06-30 15:51 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 0.97s user, 0.00s system
2025-06-30 15:51 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 3.58MB
2025-06-30 15:51 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-30 15:51 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:51 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.47 seconds (00:00:00)
2025-06-30 15:51 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.45s user, 0.00s system
2025-06-30 15:51 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 0.09MB
2025-06-30 15:51 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-30 15:51 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:51 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.27 seconds (00:00:00)
2025-06-30 15:51 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.30s user, 0.00s system
2025-06-30 15:51 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: 0.02MB
2025-06-30 15:51 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-30 15:51 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:51 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 27.66 seconds (00:00:27)
2025-06-30 15:51 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 26.58s user, 1.47s system
2025-06-30 15:51 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: -36.53MB
2025-06-30 15:51 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-30 15:51 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:51 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-30 15:51 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-30 15:51 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 1.52 seconds (00:00:01)
2025-06-30 15:51 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 1.50s user, 0.02s system
2025-06-30 15:51 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.18MB
2025-06-30 15:51 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-30 15:51 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:51 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-30 15:53 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 96.10 seconds (00:01:36)
2025-06-30 15:53 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 4.45s user, 1.16s system
2025-06-30 15:53 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 61.53MB
2025-06-30 15:53 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-30 15:53 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:53 | INFO     | FactorLoader.get_fct_df: 开始计算599个因子表达式
2025-06-30 15:53 | INFO     | FactorLoader.get_fct_df: 返回结果: 599个指定因子
2025-06-30 15:53 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 0.97 seconds (00:00:00)
2025-06-30 15:53 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 0.94s user, 0.00s system
2025-06-30 15:53 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: 0.09MB
2025-06-30 15:53 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-30 15:53 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:53 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.46 seconds (00:00:00)
2025-06-30 15:53 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.45s user, 0.00s system
2025-06-30 15:53 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-30 15:53 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-30 15:53 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:53 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.28 seconds (00:00:00)
2025-06-30 15:53 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.28s user, 0.00s system
2025-06-30 15:53 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-30 15:53 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-30 15:53 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:53 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 3.00 seconds (00:00:02)
2025-06-30 15:53 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 5.58s user, 0.00s system
2025-06-30 15:53 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: 0.07MB
2025-06-30 15:53 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-30 15:53 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:53 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-30 15:53 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-30 15:53 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 1.32 seconds (00:00:01)
2025-06-30 15:53 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 1.31s user, 0.00s system
2025-06-30 15:53 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.01MB
2025-06-30 15:53 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-30 15:53 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:53 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-30 15:54 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 97.04 seconds (00:01:37)
2025-06-30 15:54 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 4.14s user, 1.14s system
2025-06-30 15:54 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: 9.08MB
2025-06-30 15:54 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-30 15:54 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:54 | INFO     | FactorLoader.get_fct_df: 开始计算599个因子表达式
2025-06-30 15:54 | INFO     | FactorLoader.get_fct_df: 返回结果: 599个指定因子
2025-06-30 15:54 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 1.09 seconds (00:00:01)
2025-06-30 15:54 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 1.09s user, 0.00s system
2025-06-30 15:54 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: 0.10MB
2025-06-30 15:54 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-30 15:54 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:54 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.44 seconds (00:00:00)
2025-06-30 15:54 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.44s user, 0.00s system
2025-06-30 15:54 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: -1.05MB
2025-06-30 15:54 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-30 15:54 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:54 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.28 seconds (00:00:00)
2025-06-30 15:54 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.27s user, 0.00s system
2025-06-30 15:54 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-30 15:54 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-30 15:54 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:54 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 2.94 seconds (00:00:02)
2025-06-30 15:54 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 5.61s user, 0.00s system
2025-06-30 15:54 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: -0.61MB
2025-06-30 15:54 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-30 15:54 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:54 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-30 15:54 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-30 15:54 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 1.48 seconds (00:00:01)
2025-06-30 15:54 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 1.47s user, 0.00s system
2025-06-30 15:54 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 0.03MB
2025-06-30 15:54 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-30 15:54 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-30 15:54 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-30 15:54 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:54 | INFO     | FactorLoader.get_fct_df: 开始计算3个因子表达式
2025-06-30 15:54 | INFO     | FactorLoader.get_fct_df: 返回结果: 3个指定因子
2025-06-30 15:54 | INFO     | ResMonitor.end_timer: CALC_F_VALUES_SH510050 took 0.01 seconds (00:00:00)
2025-06-30 15:54 | INFO     | ResMonitor.end_timer_cpu: CALC_F_VALUES_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-30 15:54 | INFO     | ResMonitor.end_memory_monitor: CALC_F_VALUES_SH510050 memory delta: 0.00MB
2025-06-30 15:54 | INFO     | FactorMonitorContext.__exit__: CALC_F_VALUES_SH510050 completed successfully
2025-06-30 15:54 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:54 | INFO     | FactorLoader.get_fct_df: 开始计算4个因子表达式
2025-06-30 15:54 | INFO     | FactorLoader.get_fct_df: 返回结果: 4个指定因子
2025-06-30 15:54 | INFO     | ResMonitor.end_timer: CALC_PJ_VALUES_SH510050 took 0.02 seconds (00:00:00)
2025-06-30 15:54 | INFO     | ResMonitor.end_timer_cpu: CALC_PJ_VALUES_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-30 15:54 | INFO     | ResMonitor.end_memory_monitor: CALC_PJ_VALUES_SH510050 memory delta: 0.00MB
2025-06-30 15:54 | INFO     | FactorMonitorContext.__exit__: CALC_PJ_VALUES_SH510050 completed successfully
2025-06-30 15:54 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:54 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_SH510050_20250630_L0_727617
2025-06-30 15:54 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_SH510050_20250630_L0_727617 耗时147.4ms
2025-06-30 15:54 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.15 seconds (00:00:00)
2025-06-30 15:54 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.05s user, 0.00s system
2025-06-30 15:54 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 7.29MB
2025-06-30 15:54 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-06-30 15:54 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:54 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250630_L0_727617_label_1_001 took 0.01 seconds (00:00:00)
2025-06-30 15:54 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250630_L0_727617_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-06-30 15:54 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250630_L0_727617_label_1_001 memory delta: 0.01MB
2025-06-30 15:54 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250630_L0_727617_label_1_001 completed successfully
2025-06-30 15:54 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:54 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250630_L0_727617_label_1_002 took 0.01 seconds (00:00:00)
2025-06-30 15:54 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250630_L0_727617_label_1_002 CPU usage: 0.00s user, 0.00s system
2025-06-30 15:54 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250630_L0_727617_label_1_002 memory delta: 0.00MB
2025-06-30 15:54 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250630_L0_727617_label_1_002 completed successfully
2025-06-30 15:54 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:54 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250630_L0_727617_label_1_003 took 0.01 seconds (00:00:00)
2025-06-30 15:54 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250630_L0_727617_label_1_003 CPU usage: 0.00s user, 0.02s system
2025-06-30 15:54 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250630_L0_727617_label_1_003 memory delta: 0.00MB
2025-06-30 15:54 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250630_L0_727617_label_1_003 completed successfully
2025-06-30 15:54 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:54 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250630_L0_PJ_727617_label_1_001 took 0.01 seconds (00:00:00)
2025-06-30 15:54 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250630_L0_PJ_727617_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-06-30 15:54 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250630_L0_PJ_727617_label_1_001 memory delta: 0.00MB
2025-06-30 15:54 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250630_L0_PJ_727617_label_1_001 completed successfully
2025-06-30 15:54 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:54 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250630_L0_PJ_727617_label_1_002 took 0.01 seconds (00:00:00)
2025-06-30 15:54 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250630_L0_PJ_727617_label_1_002 CPU usage: 0.02s user, 0.02s system
2025-06-30 15:54 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250630_L0_PJ_727617_label_1_002 memory delta: 0.00MB
2025-06-30 15:54 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250630_L0_PJ_727617_label_1_002 completed successfully
2025-06-30 15:54 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:54 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250630_L0_PJ_727617_label_1_003 took 0.01 seconds (00:00:00)
2025-06-30 15:54 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250630_L0_PJ_727617_label_1_003 CPU usage: 0.00s user, 0.02s system
2025-06-30 15:54 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250630_L0_PJ_727617_label_1_003 memory delta: 0.01MB
2025-06-30 15:54 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250630_L0_PJ_727617_label_1_003 completed successfully
2025-06-30 15:54 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 15:54 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250630_L0_PJ_727617_label_1_004 took 0.02 seconds (00:00:00)
2025-06-30 15:54 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250630_L0_PJ_727617_label_1_004 CPU usage: 0.02s user, 0.00s system
2025-06-30 15:54 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250630_L0_PJ_727617_label_1_004 memory delta: 0.17MB
2025-06-30 15:54 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250630_L0_PJ_727617_label_1_004 completed successfully
2025-06-30 15:55 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250630_L0_727617 took 361.31 seconds (00:06:01)
2025-06-30 15:55 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250630_L0_727617 CPU usage: 72.61s user, 26.31s system
2025-06-30 15:55 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250630_L0_727617 memory delta: 1104.93MB
2025-06-30 15:55 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250630_L0_727617 completed successfully
2025-06-30 16:25 | INFO     | DatabaseReset.__init__: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-30 16:25 | INFO     | DatabaseReset.__init__: Schema文件路径: d:\myquant\XentZ\script\因子库脚本\schema.sql
2025-06-30 16:25 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-30 16:25 | INFO     | DatabaseReset.run_reset: 开始执行 XentZ 因子动物园数据库重置
2025-06-30 16:25 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-30 16:25 | INFO     | DatabaseReset.validate_files: 步骤1: 验证文件存在性...
2025-06-30 16:25 | INFO     | DatabaseReset.validate_files: Schema文件存在: d:\myquant\XentZ\script\因子库脚本\schema.sql
2025-06-30 16:25 | INFO     | DatabaseReset.backup_database: 步骤2: 备份现有数据库...
2025-06-30 16:25 | INFO     | DatabaseReset.backup_database: 数据库已备份到: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250630_162516
2025-06-30 16:25 | INFO     | DatabaseReset.get_existing_tables: 步骤3: 获取现有表列表...
2025-06-30 16:25 | INFO     | DatabaseReset.get_existing_tables: 发现现有表 (11个): factor_batches, factor_categories, factor_evaluations, factor_performance_logs, factor_pool_members, factor_pools, factor_relationships, factor_tags, factor_values, factors, universes
2025-06-30 16:25 | INFO     | DatabaseReset.drop_all_tables: 步骤4: 删除现有表 (11个)...
2025-06-30 16:25 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_batches
2025-06-30 16:25 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_categories
2025-06-30 16:25 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_evaluations
2025-06-30 16:25 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_performance_logs
2025-06-30 16:25 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_pool_members
2025-06-30 16:25 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_pools
2025-06-30 16:25 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_relationships
2025-06-30 16:25 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_tags
2025-06-30 16:25 | INFO     | DatabaseReset.drop_all_tables:   删除表: factor_values
2025-06-30 16:25 | INFO     | DatabaseReset.drop_all_tables:   删除表: factors
2025-06-30 16:25 | INFO     | DatabaseReset.drop_all_tables:   删除表: universes
2025-06-30 16:25 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_active_factors
2025-06-30 16:25 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_batch_statistics
2025-06-30 16:25 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_factor_performance_summary
2025-06-30 16:25 | INFO     | DatabaseReset.drop_all_tables:   删除视图: v_performance_stats
2025-06-30 16:25 | INFO     | DatabaseReset.drop_all_tables: 成功删除 11 个表, 4 个视图, 0 个索引
2025-06-30 16:25 | INFO     | DatabaseReset.execute_schema: 步骤5: 执行schema.sql重建表结构...
2025-06-30 16:25 | INFO     | DatabaseReset.execute_schema: 读取schema文件，长度: 26452 字符
2025-06-30 16:25 | INFO     | DatabaseReset.execute_schema: Schema执行完成
2025-06-30 16:25 | INFO     | DatabaseReset.verify_tables: 步骤6: 验证表创建结果...
2025-06-30 16:25 | INFO     | DatabaseReset.verify_tables: 创建完成统计:
2025-06-30 16:25 | INFO     | DatabaseReset.verify_tables:   - 表 (11个): factor_batches, factor_categories, factor_evaluations, factor_performance_logs, factor_pool_members, factor_pools, factor_relationships, factor_tags, factor_values, factors, universes
2025-06-30 16:25 | INFO     | DatabaseReset.verify_tables:   - 视图 (4个): v_active_factors, v_batch_statistics, v_factor_performance_summary, v_performance_stats
2025-06-30 16:25 | INFO     | DatabaseReset.verify_tables:   - 索引 (32个): idx_evaluations_factor_id, idx_evaluations_method, idx_evaluations_period, idx_evaluations_sharpe, idx_factor_performance_logs_batch_id, idx_factor_performance_logs_memory, idx_factor_performance_logs_operation_type, idx_factor_performance_logs_start_time, idx_factor_performance_logs_total_time, idx_factor_tags_tag_name, idx_factor_tags_tag_type, idx_factor_values_date, idx_factor_values_factor_id, idx_factors_batch_id, idx_factors_computation_time, idx_factors_creation_date, idx_factors_creation_method, idx_factors_factor_type, idx_factors_generation_tool, idx_factors_market_type, idx_factors_pipeline_mode, idx_factors_pipeline_step, idx_factors_primary_category, idx_factors_resource_intensity, idx_factors_status, idx_factors_target_label, idx_factors_unique_expression, idx_pool_members_factor_id, idx_pool_members_pool_id, idx_pool_members_status, idx_universes_current, idx_universes_effective_date
2025-06-30 16:25 | INFO     | DatabaseReset.verify_tables: 所有核心表创建成功
2025-06-30 16:25 | INFO     | DatabaseReset.insert_default_data: 步骤7: 插入默认数据...
2025-06-30 16:25 | INFO     | DatabaseReset.insert_default_data: 默认数据插入完成
2025-06-30 16:25 | INFO     | DatabaseReset.insert_default_data:   - 因子分类: 10 个
2025-06-30 16:25 | INFO     | DatabaseReset.insert_default_data:   - 股票池: 2 个
2025-06-30 16:25 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-30 16:25 | INFO     | DatabaseReset.run_reset: 数据库重置完成！
2025-06-30 16:25 | INFO     | DatabaseReset.run_reset: 数据库路径: D:\myquant\FZoo\database\factorzoo.sqlite
2025-06-30 16:25 | INFO     | DatabaseReset.run_reset: 备份路径: D:\myquant\FZoo\database\factorzoo.sqlite.backup_20250630_162516
2025-06-30 16:25 | INFO     | DatabaseReset.run_reset: 创建统计: 11表 + 4视图 + 32索引
2025-06-30 16:25 | INFO     | DatabaseReset.run_reset: ============================================================
2025-06-30 16:25 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:25 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:25 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-30 16:25 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-30 16:25 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-30 16:25 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-30 16:25 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-30 16:25 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-30 16:25 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-30 16:25 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-30 16:25 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-30 16:25 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-30 16:25 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-30 16:25 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-30 16:25 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-30 16:25 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-30 16:25 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-30 16:25 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-30 16:25 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:25 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-30 16:27 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R01 took 112.04 seconds (00:01:52)
2025-06-30 16:27 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R01 CPU usage: 15.75s user, 20.72s system
2025-06-30 16:27 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R01 memory delta: 1555.38MB
2025-06-30 16:27 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R01 completed successfully
2025-06-30 16:27 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:27 | INFO     | FactorLoader.get_fct_df: 开始计算599个因子表达式
2025-06-30 16:27 | INFO     | FactorLoader.get_fct_df: 返回结果: 599个指定因子
2025-06-30 16:27 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R01 took 1.03 seconds (00:00:01)
2025-06-30 16:27 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R01 CPU usage: 1.00s user, 0.02s system
2025-06-30 16:27 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R01 memory delta: 3.19MB
2025-06-30 16:27 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R01 completed successfully
2025-06-30 16:27 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:27 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R01 took 0.47 seconds (00:00:00)
2025-06-30 16:27 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R01 CPU usage: 0.45s user, 0.00s system
2025-06-30 16:27 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R01 memory delta: 0.03MB
2025-06-30 16:27 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R01 completed successfully
2025-06-30 16:27 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:27 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R01 took 0.28 seconds (00:00:00)
2025-06-30 16:27 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R01 CPU usage: 0.28s user, 0.00s system
2025-06-30 16:27 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R01 memory delta: 0.00MB
2025-06-30 16:27 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R01 completed successfully
2025-06-30 16:27 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:28 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R01 took 28.40 seconds (00:00:28)
2025-06-30 16:28 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R01 CPU usage: 27.25s user, 1.42s system
2025-06-30 16:28 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R01 memory delta: 35.03MB
2025-06-30 16:28 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R01 completed successfully
2025-06-30 16:28 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:28 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-30 16:28 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-30 16:28 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R01 took 1.55 seconds (00:00:01)
2025-06-30 16:28 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R01 CPU usage: 1.55s user, 0.00s system
2025-06-30 16:28 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R01 memory delta: 0.09MB
2025-06-30 16:28 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R01 completed successfully
2025-06-30 16:28 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:28 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-30 16:29 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R02 took 99.77 seconds (00:01:39)
2025-06-30 16:29 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R02 CPU usage: 4.14s user, 1.25s system
2025-06-30 16:29 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R02 memory delta: 14.43MB
2025-06-30 16:29 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R02 completed successfully
2025-06-30 16:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:29 | INFO     | FactorLoader.get_fct_df: 开始计算599个因子表达式
2025-06-30 16:29 | INFO     | FactorLoader.get_fct_df: 返回结果: 599个指定因子
2025-06-30 16:29 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R02 took 1.20 seconds (00:00:01)
2025-06-30 16:29 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R02 CPU usage: 1.08s user, 0.00s system
2025-06-30 16:29 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R02 memory delta: 2.57MB
2025-06-30 16:29 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R02 completed successfully
2025-06-30 16:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:29 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R02 took 0.45 seconds (00:00:00)
2025-06-30 16:29 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R02 CPU usage: 0.45s user, 0.00s system
2025-06-30 16:29 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-30 16:29 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R02 completed successfully
2025-06-30 16:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:29 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R02 took 0.26 seconds (00:00:00)
2025-06-30 16:29 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R02 CPU usage: 0.27s user, 0.00s system
2025-06-30 16:29 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-30 16:29 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R02 completed successfully
2025-06-30 16:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:29 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R02 took 2.86 seconds (00:00:02)
2025-06-30 16:29 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R02 CPU usage: 5.11s user, 0.03s system
2025-06-30 16:29 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R02 memory delta: 7.82MB
2025-06-30 16:29 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R02 completed successfully
2025-06-30 16:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:29 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-30 16:29 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-30 16:29 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R02 took 1.24 seconds (00:00:01)
2025-06-30 16:29 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R02 CPU usage: 1.22s user, 0.00s system
2025-06-30 16:29 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R02 memory delta: 0.00MB
2025-06-30 16:29 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R02 completed successfully
2025-06-30 16:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:29 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-30 16:31 | INFO     | ResMonitor.end_timer: GP_MINE_SH510050_label_1_R03 took 96.11 seconds (00:01:36)
2025-06-30 16:31 | INFO     | ResMonitor.end_timer_cpu: GP_MINE_SH510050_label_1_R03 CPU usage: 4.31s user, 1.11s system
2025-06-30 16:31 | INFO     | ResMonitor.end_memory_monitor: GP_MINE_SH510050_label_1_R03 memory delta: -334.58MB
2025-06-30 16:31 | INFO     | FactorMonitorContext.__exit__: GP_MINE_SH510050_label_1_R03 completed successfully
2025-06-30 16:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:31 | INFO     | FactorLoader.get_fct_df: 开始计算600个因子表达式
2025-06-30 16:31 | INFO     | FactorLoader.get_fct_df: 返回结果: 600个指定因子
2025-06-30 16:31 | INFO     | ResMonitor.end_timer: CALC_SH510050_label_1_R03 took 0.99 seconds (00:00:00)
2025-06-30 16:31 | INFO     | ResMonitor.end_timer_cpu: CALC_SH510050_label_1_R03 CPU usage: 0.95s user, 0.02s system
2025-06-30 16:31 | INFO     | ResMonitor.end_memory_monitor: CALC_SH510050_label_1_R03 memory delta: 0.02MB
2025-06-30 16:31 | INFO     | FactorMonitorContext.__exit__: CALC_SH510050_label_1_R03 completed successfully
2025-06-30 16:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:31 | INFO     | ResMonitor.end_timer: SKEW_SH510050_label_1_R03 took 0.46 seconds (00:00:00)
2025-06-30 16:31 | INFO     | ResMonitor.end_timer_cpu: SKEW_SH510050_label_1_R03 CPU usage: 0.42s user, 0.00s system
2025-06-30 16:31 | INFO     | ResMonitor.end_memory_monitor: SKEW_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-30 16:31 | INFO     | FactorMonitorContext.__exit__: SKEW_SH510050_label_1_R03 completed successfully
2025-06-30 16:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:31 | INFO     | ResMonitor.end_timer: KURT_SH510050_label_1_R03 took 0.26 seconds (00:00:00)
2025-06-30 16:31 | INFO     | ResMonitor.end_timer_cpu: KURT_SH510050_label_1_R03 CPU usage: 0.27s user, 0.00s system
2025-06-30 16:31 | INFO     | ResMonitor.end_memory_monitor: KURT_SH510050_label_1_R03 memory delta: 0.00MB
2025-06-30 16:31 | INFO     | FactorMonitorContext.__exit__: KURT_SH510050_label_1_R03 completed successfully
2025-06-30 16:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:31 | INFO     | ResMonitor.end_timer: METRIC_SH510050_label_1_R03 took 2.78 seconds (00:00:02)
2025-06-30 16:31 | INFO     | ResMonitor.end_timer_cpu: METRIC_SH510050_label_1_R03 CPU usage: 5.30s user, 0.03s system
2025-06-30 16:31 | INFO     | ResMonitor.end_memory_monitor: METRIC_SH510050_label_1_R03 memory delta: 0.01MB
2025-06-30 16:31 | INFO     | FactorMonitorContext.__exit__: METRIC_SH510050_label_1_R03 completed successfully
2025-06-30 16:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:31 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-30 16:31 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-30 16:31 | INFO     | ResMonitor.end_timer: CORR_SH510050_label_1_R03 took 1.31 seconds (00:00:01)
2025-06-30 16:31 | INFO     | ResMonitor.end_timer_cpu: CORR_SH510050_label_1_R03 CPU usage: 1.30s user, 0.00s system
2025-06-30 16:31 | INFO     | ResMonitor.end_memory_monitor: CORR_SH510050_label_1_R03 memory delta: 0.01MB
2025-06-30 16:31 | INFO     | FactorMonitorContext.__exit__: CORR_SH510050_label_1_R03 completed successfully
2025-06-30 16:31 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-30 16:31 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-30 16:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:31 | INFO     | FactorLoader.get_fct_df: 开始计算2个因子表达式
2025-06-30 16:31 | INFO     | FactorLoader.get_fct_df: 返回结果: 2个指定因子
2025-06-30 16:31 | INFO     | ResMonitor.end_timer: CALC_F_VALUES_SH510050 took 0.02 seconds (00:00:00)
2025-06-30 16:31 | INFO     | ResMonitor.end_timer_cpu: CALC_F_VALUES_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-30 16:31 | INFO     | ResMonitor.end_memory_monitor: CALC_F_VALUES_SH510050 memory delta: 0.00MB
2025-06-30 16:31 | INFO     | FactorMonitorContext.__exit__: CALC_F_VALUES_SH510050 completed successfully
2025-06-30 16:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:31 | INFO     | FactorLoader.get_fct_df: 开始计算5个因子表达式
2025-06-30 16:31 | INFO     | FactorLoader.get_fct_df: 返回结果: 5个指定因子
2025-06-30 16:31 | INFO     | ResMonitor.end_timer: CALC_PJ_VALUES_SH510050 took 0.02 seconds (00:00:00)
2025-06-30 16:31 | INFO     | ResMonitor.end_timer_cpu: CALC_PJ_VALUES_SH510050 CPU usage: 0.03s user, 0.00s system
2025-06-30 16:31 | INFO     | ResMonitor.end_memory_monitor: CALC_PJ_VALUES_SH510050 memory delta: 0.00MB
2025-06-30 16:31 | INFO     | FactorMonitorContext.__exit__: CALC_PJ_VALUES_SH510050 completed successfully
2025-06-30 16:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:31 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: GP_SH510050_20250630_L0_085450
2025-06-30 16:31 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: GP_SH510050_20250630_L0_085450 耗时163.0ms
2025-06-30 16:31 | INFO     | ResMonitor.end_timer: PERSIST_SH510050 took 0.16 seconds (00:00:00)
2025-06-30 16:31 | INFO     | ResMonitor.end_timer_cpu: PERSIST_SH510050 CPU usage: 0.05s user, 0.08s system
2025-06-30 16:31 | INFO     | ResMonitor.end_memory_monitor: PERSIST_SH510050 memory delta: 5.74MB
2025-06-30 16:31 | INFO     | FactorMonitorContext.__exit__: PERSIST_SH510050 completed successfully
2025-06-30 16:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:31 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250630_L0_085450_label_1_001 took 0.01 seconds (00:00:00)
2025-06-30 16:31 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250630_L0_085450_label_1_001 CPU usage: 0.00s user, 0.00s system
2025-06-30 16:31 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250630_L0_085450_label_1_001 memory delta: 0.01MB
2025-06-30 16:31 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250630_L0_085450_label_1_001 completed successfully
2025-06-30 16:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:31 | INFO     | ResMonitor.end_timer: F_GP_GP_SH510050_20250630_L0_085450_label_1_002 took 0.01 seconds (00:00:00)
2025-06-30 16:31 | INFO     | ResMonitor.end_timer_cpu: F_GP_GP_SH510050_20250630_L0_085450_label_1_002 CPU usage: 0.02s user, 0.00s system
2025-06-30 16:31 | INFO     | ResMonitor.end_memory_monitor: F_GP_GP_SH510050_20250630_L0_085450_label_1_002 memory delta: 0.00MB
2025-06-30 16:31 | INFO     | FactorMonitorContext.__exit__: F_GP_GP_SH510050_20250630_L0_085450_label_1_002 completed successfully
2025-06-30 16:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:31 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_001 took 0.01 seconds (00:00:00)
2025-06-30 16:31 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_001 CPU usage: 0.02s user, 0.00s system
2025-06-30 16:31 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_001 memory delta: 0.00MB
2025-06-30 16:31 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_001 completed successfully
2025-06-30 16:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:31 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_002 took 0.03 seconds (00:00:00)
2025-06-30 16:31 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_002 CPU usage: 0.00s user, 0.02s system
2025-06-30 16:31 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_002 memory delta: 0.00MB
2025-06-30 16:31 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_002 completed successfully
2025-06-30 16:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:31 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_003 took 0.01 seconds (00:00:00)
2025-06-30 16:31 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_003 CPU usage: 0.00s user, 0.00s system
2025-06-30 16:31 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_003 memory delta: 0.00MB
2025-06-30 16:31 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_003 completed successfully
2025-06-30 16:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:31 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_004 took 0.01 seconds (00:00:00)
2025-06-30 16:31 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_004 CPU usage: 0.00s user, 0.00s system
2025-06-30 16:31 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_004 memory delta: 0.00MB
2025-06-30 16:31 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_004 completed successfully
2025-06-30 16:31 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:31 | INFO     | ResMonitor.end_timer: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_005 took 0.01 seconds (00:00:00)
2025-06-30 16:31 | INFO     | ResMonitor.end_timer_cpu: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_005 CPU usage: 0.02s user, 0.00s system
2025-06-30 16:31 | INFO     | ResMonitor.end_memory_monitor: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_005 memory delta: 0.00MB
2025-06-30 16:31 | INFO     | FactorMonitorContext.__exit__: F_GP_PJ_GP_SH510050_20250630_L0_PJ_085450_label_1_005 completed successfully
2025-06-30 16:31 | INFO     | ResMonitor.end_timer: GP_MINING_GP_MULTI_20250630_L0_085450 took 352.70 seconds (00:05:52)
2025-06-30 16:31 | INFO     | ResMonitor.end_timer_cpu: GP_MINING_GP_MULTI_20250630_L0_085450 CPU usage: 71.55s user, 25.00s system
2025-06-30 16:31 | INFO     | ResMonitor.end_memory_monitor: GP_MINING_GP_MULTI_20250630_L0_085450 memory delta: 1298.09MB
2025-06-30 16:31 | INFO     | MonitorContext.__exit__: GP_MINING_GP_MULTI_20250630_L0_085450 completed successfully
2025-06-30 16:31 | INFO     | ResMonitor.end_timer: global_process took 352.71 seconds (00:05:52)
2025-06-30 16:38 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-30 16:38 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-30 16:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:38 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250630_L0_085450/L0
2025-06-30 16:38 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250630_L0_085450/L0 耗时253.7ms
2025-06-30 16:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:38 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-30 16:38 | INFO     | ResMonitor.end_timer: L2_CORR_SH510050 took 0.01 seconds (00:00:00)
2025-06-30 16:38 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-30 16:38 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_SH510050 memory delta: 0.38MB
2025-06-30 16:38 | INFO     | FactorMonitorContext.__exit__: L2_CORR_SH510050 completed successfully
2025-06-30 16:38 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 16:38 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_CORR_SH510050_20250630_268837
2025-06-30 16:38 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_CORR_SH510050_20250630_268837 耗时33.0ms
2025-06-30 16:38 | INFO     | ResMonitor.end_timer: PERSIST_L2_SH510050 took 0.03 seconds (00:00:00)
2025-06-30 16:38 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-30 16:38 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_SH510050 memory delta: 2.15MB
2025-06-30 16:38 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_SH510050 completed successfully
2025-06-30 16:38 | INFO     | ResMonitor.end_timer: L2_CORR_FILTER_1751272702268837 took 0.40 seconds (00:00:00)
2025-06-30 16:38 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_FILTER_1751272702268837 CPU usage: 0.14s user, 0.11s system
2025-06-30 16:38 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_FILTER_1751272702268837 memory delta: 16.44MB
2025-06-30 16:38 | INFO     | MonitorContext.__exit__: L2_CORR_FILTER_1751272702268837 completed successfully
2025-06-30 17:13 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-30 17:13 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-30 17:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 17:13 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250630_L0_085450/L0
2025-06-30 17:13 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250630_L0_085450/L0 耗时145.7ms
2025-06-30 17:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 17:13 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-30 17:13 | INFO     | ResMonitor.end_timer: L2_CORR_SH510050 took 0.01 seconds (00:00:00)
2025-06-30 17:13 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_SH510050 CPU usage: 0.02s user, 0.02s system
2025-06-30 17:13 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_SH510050 memory delta: 0.37MB
2025-06-30 17:13 | INFO     | FactorMonitorContext.__exit__: L2_CORR_SH510050 completed successfully
2025-06-30 17:13 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 17:13 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_CORR_SH510050_20250630_749263
2025-06-30 17:13 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_CORR_SH510050_20250630_749263 耗时31.0ms
2025-06-30 17:13 | INFO     | ResMonitor.end_timer: PERSIST_L2_SH510050 took 0.03 seconds (00:00:00)
2025-06-30 17:13 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-30 17:13 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_SH510050 memory delta: 2.12MB
2025-06-30 17:13 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_SH510050 completed successfully
2025-06-30 17:13 | INFO     | ResMonitor.end_timer: L2_CORR_FILTER_1751274790749263 took 0.27 seconds (00:00:00)
2025-06-30 17:13 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_FILTER_1751274790749263 CPU usage: 0.17s user, 0.03s system
2025-06-30 17:13 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_FILTER_1751274790749263 memory delta: 17.16MB
2025-06-30 17:13 | INFO     | MonitorContext.__exit__: L2_CORR_FILTER_1751274790749263 completed successfully
2025-06-30 17:16 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-30 17:16 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-30 17:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 17:16 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250630_L0_085450/L0
2025-06-30 17:16 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250630_L0_085450/L0 耗时137.6ms
2025-06-30 17:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 17:16 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-30 17:16 | INFO     | ResMonitor.end_timer: L2_CORR_SH510050 took 0.01 seconds (00:00:00)
2025-06-30 17:16 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_SH510050 CPU usage: 0.00s user, 0.00s system
2025-06-30 17:16 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_SH510050 memory delta: 0.43MB
2025-06-30 17:16 | INFO     | FactorMonitorContext.__exit__: L2_CORR_SH510050 completed successfully
2025-06-30 17:16 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 17:16 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_CORR_SH510050_20250630_445491
2025-06-30 17:16 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_CORR_SH510050_20250630_445491 耗时31.9ms
2025-06-30 17:16 | INFO     | ResMonitor.end_timer: PERSIST_L2_SH510050 took 0.03 seconds (00:00:00)
2025-06-30 17:16 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_SH510050 CPU usage: 0.05s user, 0.00s system
2025-06-30 17:16 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_SH510050 memory delta: 2.17MB
2025-06-30 17:16 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_SH510050 completed successfully
2025-06-30 17:16 | INFO     | ResMonitor.end_timer: L2_CORR_FILTER_1751274993445491 took 0.26 seconds (00:00:00)
2025-06-30 17:16 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_FILTER_1751274993445491 CPU usage: 0.17s user, 0.05s system
2025-06-30 17:16 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_FILTER_1751274993445491 memory delta: 17.74MB
2025-06-30 17:16 | INFO     | MonitorContext.__exit__: L2_CORR_FILTER_1751274993445491 completed successfully
2025-06-30 17:28 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-30 17:28 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-30 17:28 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 17:28 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250630_L0_085450/L0
2025-06-30 17:28 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250630_L0_085450/L0 耗时199.2ms
2025-06-30 17:28 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 17:28 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-30 17:28 | INFO     | ResMonitor.end_timer: L2_CORR_SH510050 took 0.02 seconds (00:00:00)
2025-06-30 17:28 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_SH510050 CPU usage: 0.03s user, 0.02s system
2025-06-30 17:28 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_SH510050 memory delta: 0.38MB
2025-06-30 17:28 | INFO     | FactorMonitorContext.__exit__: L2_CORR_SH510050 completed successfully
2025-06-30 17:28 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 17:28 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_CORR_SH510050_20250630_678566
2025-06-30 17:28 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_CORR_SH510050_20250630_678566 耗时34.9ms
2025-06-30 17:28 | INFO     | ResMonitor.end_timer: PERSIST_L2_SH510050 took 0.04 seconds (00:00:00)
2025-06-30 17:28 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_SH510050 CPU usage: 0.03s user, 0.00s system
2025-06-30 17:28 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_SH510050 memory delta: 2.10MB
2025-06-30 17:28 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_SH510050 completed successfully
2025-06-30 17:28 | INFO     | ResMonitor.end_timer: L2_CORR_FILTER_1751275738678566 took 0.35 seconds (00:00:00)
2025-06-30 17:28 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_FILTER_1751275738678566 CPU usage: 0.22s user, 0.09s system
2025-06-30 17:28 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_FILTER_1751275738678566 memory delta: 16.54MB
2025-06-30 17:28 | INFO     | MonitorContext.__exit__: L2_CORR_FILTER_1751275738678566 completed successfully
2025-06-30 17:29 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-06-30 17:29 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-06-30 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 17:29 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250630_L0_085450/L0
2025-06-30 17:29 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250630_L0_085450/L0 耗时213.7ms
2025-06-30 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 17:29 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-06-30 17:29 | INFO     | ResMonitor.end_timer: L2_CORR_SH510050 took 0.02 seconds (00:00:00)
2025-06-30 17:29 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_SH510050 CPU usage: 0.02s user, 0.00s system
2025-06-30 17:29 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_SH510050 memory delta: 0.38MB
2025-06-30 17:29 | INFO     | FactorMonitorContext.__exit__: L2_CORR_SH510050 completed successfully
2025-06-30 17:29 | INFO     | ResMonitor.__init__: Resource monitor started
2025-06-30 17:29 | DEBUG    | FactorValueManager._preheat_cache: 缓存预热完成: L2_CORR_SH510050_20250630_965995
2025-06-30 17:29 | INFO     | FactorValueManager.save_batch_data: 批次数据保存成功: L2_CORR_SH510050_20250630_965995 耗时39.9ms
2025-06-30 17:29 | INFO     | ResMonitor.end_timer: PERSIST_L2_SH510050 took 0.04 seconds (00:00:00)
2025-06-30 17:29 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L2_SH510050 CPU usage: 0.03s user, 0.00s system
2025-06-30 17:29 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L2_SH510050 memory delta: 2.22MB
2025-06-30 17:29 | INFO     | FactorMonitorContext.__exit__: PERSIST_L2_SH510050 completed successfully
2025-06-30 17:29 | INFO     | ResMonitor.end_timer: L2_CORR_FILTER_1751275752965995 took 0.35 seconds (00:00:00)
2025-06-30 17:29 | INFO     | ResMonitor.end_timer_cpu: L2_CORR_FILTER_1751275752965995 CPU usage: 0.19s user, 0.09s system
2025-06-30 17:29 | INFO     | ResMonitor.end_memory_monitor: L2_CORR_FILTER_1751275752965995 memory delta: 17.21MB
2025-06-30 17:29 | INFO     | MonitorContext.__exit__: L2_CORR_FILTER_1751275752965995 completed successfully
