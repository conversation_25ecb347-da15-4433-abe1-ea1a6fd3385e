import pandas as pd
import numpy as np
import talib
from datafeed.expr_funcs.expr_utils import calc_by_symbol, calc_by_date

@calc_by_symbol
def Abs(se: pd.Series):
    return se.abs()

@calc_by_symbol
def Constant(se: pd.Series, v: float):
    """
    返回一个与给定Series长度相同的常数Series, 其中每个元素都为v
    """
    x = np.full_like(se, fill_value=v)
    return x

@calc_by_symbol
def shift(se: pd.Series, N):
    return se.shift(N)

@calc_by_symbol
def sign(se: pd.Series):
    return np.sign(se)

@calc_by_symbol
def roc(se: pd.Series, N):
    return se / shift(se, N) - 1

@calc_by_symbol
def label(se, N):
    res = pd.Series(np.where(se > N, 1, 0),dtype=np.int32)
    res.index = se.index
    return res

@calc_by_symbol
def log(x1: pd.Series):
    """Closure of log for zero and negative arguments."""
    with np.errstate(divide='ignore', invalid='ignore'):
        res = np.where(np.abs(x1) > 0.001, np.log(np.abs(x1)), 0.)
    return pd.Series(res)

@calc_by_symbol
def log1p(x1: pd.Series):
    """Closure of log1p for zero and negative arguments."""
    with np.errstate(divide='ignore', invalid='ignore'):  # 仅忽略无效操作警告
        adjusted_x = 1 + x1 # 首先将输入x1加1
        res = np.where(np.abs(adjusted_x) > 0.001, np.log1p(np.abs(x1)), 0.)
    return pd.Series(res)

@calc_by_symbol
def delta(se: pd.Series, periods=20):
    se_result = se - se.shift(periods=periods)
    return se_result

@calc_by_symbol
def sign(se: pd.Series):
    return np.sign(se)

@calc_by_symbol
def scale(se: pd.Series, a=1):
    """
    NOTE: 是累计求和做分母, 不是滚动 也不是全局
    """
    epsilon = np.nextafter(0, 1)  # 获取距离0最近的正浮点数
    sum_abs_x = np.cumsum(np.abs(se)).replace(0, epsilon)
    # 计算缩放因子，现在安全，因为sum_abs_x中没有0了
    scale_factor = a / sum_abs_x
    # 将累积和原本为0位置的缩放因子设为0
    scale_factor = scale_factor.where(sum_abs_x != epsilon, other=0)
    return se * scale_factor

@calc_by_symbol
def delay(se: pd.Series, periods=5):
    return se.shift(periods=periods)

@calc_by_symbol
def ts_delay(se: pd.Series, periods=5):
    return se.shift(periods=periods)

@calc_by_symbol
def rvi(se:pd.Series, period=14):
    x = se.values.astype(np.float64)
    std = talib.STDDEV(x, timeperiod=period)
    # 计算价格的变化
    x_diff = np.diff(x, prepend=x[0])
    # 正的和负的标准差
    pos_std = np.where(x_diff > 0, std, 0)
    neg_std = np.where(x_diff <= 0, std, 0)
    
    # 使用 EMA 计算平均正标准差和负标准差
    pos_std = np.nan_to_num(pos_std, nan=0.0)
    neg_std = np.nan_to_num(neg_std, nan=0.0)
    avg_pos_std = talib.EMA(pos_std, timeperiod=period)
    avg_neg_std = talib.EMA(neg_std, timeperiod=period)
    # 计算 RVI
    avg_pos_std = np.nan_to_num(avg_pos_std, nan=0.0)
    avg_neg_std = np.nan_to_num(avg_neg_std, nan=0.0)
    epsilon = 1e-10
    denominator = avg_pos_std + avg_neg_std
    denominator[denominator == 0] = epsilon
    rvi = 100 * avg_pos_std / denominator
    return rvi

@calc_by_symbol
def slope(se, N=18, min_periods=2):
    ''' 支持最小期逻辑
        窗口内遇nan 则该窗口数据全部置为-1
        窗口首值为0 则仅该数据置为-1
    '''
    slopes = np.full(len(se), np.nan)  # 预先分配一个全是NaN的数组
    if len(se) < N:
        return pd.Series(slopes, index=se.index)
    
    # 使用向量化操作一次计算所有可能的x和y
    x = np.lib.stride_tricks.sliding_window_view(se.values, window_shape=N) # (L - N + 1, N), 对se为10, N为3, x为(8,3)
    
    for i in range(min_periods, N): # min_periods处理
        x_min_periods = se.values[:i]
        if np.isnan(x_min_periods).any():
            slopes[i-1] = -1
        else:
            try:
                if x_min_periods[0] == 0:
                    slopes[i-1] = -1
                else:
                    x_min_periods = x_min_periods / x_min_periods[0]  # 归一化
                    slope = np.polyfit(range(len(x_min_periods)), x_min_periods, 1)[0]
                    slopes[i-1] = slope
            except:
                slopes[i-1] = -1
    # 计算斜率
    idx = np.arange(N) # (N, ), eg. (3, )
    x_first = x[:, 0] # 一维数组, shape = (L - N + 1, ) eg. (8, )
    
    with np.errstate(divide='ignore', invalid='ignore'):
        x_normalized = np.where(x_first[:, None] == 0, np.nan, x / x_first[:, None]) # (L - N + 1, N), eg. (8, 3)
        slopes_valid = np.apply_along_axis(lambda arr: np.polyfit(idx, arr, 1)[0], axis=1, arr=x_normalized)
        slopes_valid[np.isnan(slopes_valid)] = -1
    
    slopes[N-1:] = slopes_valid # 将计算结果放到预分配的数组中

    return pd.Series(slopes, index=se.index)

@calc_by_symbol
def _high_pass_3(data, length):
    '''
        高通滤波 - John Ehlers
        data: pd.Series
    '''
    if len(data) < 3:
        return pd.Series(np.nan, index=data.index)
    
    f = 1.414 * np.pi / length
    a1 = np.exp(-f)
    c2 = 2 * a1 * np.cos(f)
    c3 = -a1 * a1
    c1 = (1 + c2 - c3) / 4
    # 确保数据连续性
    data = data.ffill()
    
    hp = np.zeros_like(data.values)
    hp[2:] = c1 * (data.values[2:] - 2 * data.values[1:-1] + data.values[:-2])
    hp[2:] += c2 * hp[1:-1] + c3 * hp[:-2]
    
    # for i in range(2, len(data)):
    #     hp[i] += c2 * hp[i-1] + c3 * hp[i-2]

    return pd.Series(hp, index=data.index)

@calc_by_symbol
def hpass_trends(data, length1, length2):
    ''' data: pd.Series '''
    hp1 = _high_pass_3(data, length1)
    hp2 = _high_pass_3(data, length2)
    trends = hp1 - hp2
    return trends

@calc_by_symbol
def hpass_troc(data, length1, length2):
    ''' data: pd.Series '''
    hp1 = _high_pass_3(data, length1)
    hp2 = _high_pass_3(data, length2)
    trends = hp1 - hp2
    # Calculate TROC
    troc = (length2 / (2 * np.pi)) * (trends.iloc[1:] - trends.iloc[:-1])
    return troc

@calc_by_symbol
def inv(x: pd.Series):
    ''' 计算倒数 '''
    with np.errstate(divide="ignore", invalid="ignore"):
        se = np.where(np.abs(x) > 0.001, 1.0 / x, 0.0)
        return pd.Series(se, index=x.index)
    
@calc_by_symbol
def decay_linear(series, window):
    """
    对输入的时间序列进行线性衰减。
    :return: 衰减后的序列。
    """
    weights = np.arange(1, window + 1)
    decay = np.convolve(series, weights, 'valid') / np.sum(weights)
    return decay

if __name__ == '__main__':
    # 生成更仿真的市场数据
    np.random.seed(42)  # 确保结果可复现
    num_points = 1000
    initial_price = 1000
    price_changes = np.random.normal(loc=0, scale=1, size=num_points)  # 正态分布的价格变化
    close_prices = initial_price + np.cumsum(price_changes)
    
    rvi_values = rvi(pd.Series(close_prices), period=20)
    print("RVI values:")
    print(rvi_values)
