import numpy as np
import pandas as pd

def custom_factor_binning(data, n_bins=20, zero_bins=4):
    # 计算分位数
    quantiles = [i/n_bins for i in range(1, n_bins)]
    bin_edges = np.quantile(data, quantiles)
    
    # 创建分箱
    labels = list(range(1, n_bins+1))
    binned_data = pd.cut(data, bins=[-np.inf] + list(bin_edges) + [np.inf], labels=labels)
    
    # 计算中间的零信号区
    mid = n_bins // 2
    zero_start = mid - zero_bins // 2
    zero_end = zero_start + zero_bins
    
    # 创建信号映射
    signal_map = {i: i - mid for i in range(1, n_bins+1)}
    for i in range(zero_start, zero_end):
        signal_map[i] = 0
    
    # 应用信号映射
    signals = binned_data.map(signal_map)
    
    return signals

# 示例使用
np.random.seed(0)
factor_data = np.random.randn(1000)
signals = custom_factor_binning(factor_data)

print(signals.value_counts().sort_index())