# 基因规划(GPlearn)因子挖掘任务配置
# 本任务依赖于 ts_feat.toml 的输出结果（特征子集）
dynaconf_include = ["ts_feat.toml"]

# ============================== 工作流总控 (Workflow Control) ============================== #
[workflow]
# 总控开关: 'test' 或 'live'。修改此处可切换下面所有参数集。
mode = 'test'

# ============================== 按模式存放的参数仓库 ============================== #
[mode]
  [mode.test]
    [mode.test.gparams]
      population_size = 500
      hall_of_fame = 100
      n_components = 100
      tournament_size = 200
      random_state = 42
    [mode.test.mine]
      runnum = 1

  [mode.live]
    [mode.live.gparams]
      population_size = 20000
      hall_of_fame = 1000
      n_components = 600
      tournament_size = 1000
      random_state = "@jinja {{ None }}"
    [mode.live.mine]
      runnum = 50

# ========================= GPlearn 最终生效参数 ========================= #
[gparams]
# 在此定义所有模式共享的默认/基础参数
p_crossover = 0.8
p_subtree_mutation = 0.02
p_hoist_mutation = 0.02
p_point_mutation = 0.02
p_point_replace = 0.5
generations = 2
init_depth = [1, 3]
parsimony_coefficient = 0.0005
verbose = 1

# ========================= 挖掘流程最终生效参数 ========================= #
[mine]
dynaconf_merge = true # 允许合并
# 在此定义所有模式共享的默认/基础参数
# ============================== 挖掘配置 ============================== #
[mine.norm]
model = 'linear' # 挖掘metric用线性模型
[mine.run]
nextbar = true
jobnum = -1
fee = 0.002
free = 0.03
[mine.run.perf]
daybars = 1 # 表示用的日线
anndays = 252

[mine.filter]
skewthresh = 0.5
kurtthresh = 5
corrthresh = 0.3
metric2use = 'sic' # 'sic', 'sr'

[mine.filter.sr]
srthresh = 0.8
pjsrthresh = 0.2
[mine.filter.rankic]
tdelay = 1
window = 240
minperiod = 120
toppct = 0.2
pjpct = 0.2
# 动态拉取当前工作流模式下的专属mine参数并合并
"@merge" = "@jinja {{ this.mode[this.workflow.mode].mine }}"