from .alpha import AlphaBase

class Alpha158(AlphaBase):

    def get_exprs_names(self):
        # ['CORD30', 'STD30', 'CORR5', 'RESI10', 'CORD60', 'STD5', 'LOW0',
        # 'WVMA30', 'RESI5', 'ROC5', 'KSFT', 'STD20', 'RSV5', 'STD60', 'KLEN']
        fields = []
        names = []

        # kbar
        fields += [
            "(close-open)/open",
            "(high-low)/open",
            "(close-open)/(high-low+1e-12)",
            "(high-greater(open, close))/open",
            "(high-greater(open, close))/(high-low+1e-12)",
            "(less(open, close)-low)/open",
            "(less(open, close)-low)/(high-low+1e-12)",
            "(2*close-high-low)/open",
            "(2*close-high-low)/(high-low+1e-12)",
        ]
        names += [
            "KMID",
            "<PERSON>LEN",
            "K_MID2",
            "K<PERSON>",
            "K_UP2",
            "KLOW",
            "KL_OW2",
            "KSFT",
            "K_SFT2",
        ]

        # =========== price ==========
        feature = ["OPEN", "HIGH", "LOW", "CLOSE"]
        windows = range(5)
        fields_temp, names_temp = [], []
        for field in feature:
            field = field.lower()
            fields_temp += ["shift(%s, %d)/close" % (field, d) if d != 0 else "%s/close" % field for d in windows]
            names_temp += [field.upper() + str(d) for d in windows]
        fields += [f for f in fields_temp if f != 'close/close']
        names += [n for n in names_temp if n != 'CLOSE0']

        # ================ volume ===========
        fields += ["shift(volume, %d)/(volume+1e-12)" % d for d in windows if d!=0]
        names += ["VOLUME" + str(d) for d in windows if d!=0]

        # ================= rolling ====================
        windows = [5, 10, 20, 30, 60]
        fields += ["shift(close, %d)/close" % d for d in windows]
        names += ["ROC%d" % d for d in windows]

        fields += ["mean(close, %d)/close" % d for d in windows]
        names += ["MA%d" % d for d in windows]

        fields += ["std(close, %d)/close" % d for d in windows]
        names += ["STD%d" % d for d in windows]

        fields += ["slope(close, %d)/close" % d for d in windows]
        names += ["BETA%d" % d for d in windows]

        fields += ["max(high, %d)/close" % d for d in windows]
        names += ["MAX%d" % d for d in windows]

        fields += ["min(low, %d)/close" % d for d in windows]
        names += ["MIN%d" % d for d in windows]

        fields += ["quantile(close, %d, 0.8)/close" % d for d in windows]
        names += ["QTLU%d" % d for d in windows]

        fields += ["quantile(close, %d, 0.2)/close" % d for d in windows]
        names += ["QTLD%d" % d for d in windows]

        #fields += ["ts_rank(close, %d)" % d for d in windows]
        #names += ["RANK%d" % d for d in windows]

        # fields += ["(close-min(low, %d))/(max(high, %d)-min(low, %d)+1e-12)" % (d, d, d) for d in windows]
        # names += ["RSV%d" % d for d in windows]

        # fields += ["idxmax(high, %d)/%d" % (d, d) for d in windows]
        # names += ["IMAX_%d" % d for d in windows]

        # fields += ["idxmin(low, %d)/%d" % (d, d) for d in windows]
        # names += ["IMIN_%d" % d for d in windows]

        # fields += ["(idxmax(high, %d)-idxmin(low, %d))/%d" % (d, d, d) for d in windows]
        # names += ["IMXD_%d" % d for d in windows]

        # fields += ["corr_pro(close, log(volume+1), %d)" % d for d in windows]
        # names += ["CORR%d" % d for d in windows]

        # fields += ["corr_pro(close/shift(close,1), log(volume/shift(volume, 1)+1), %d)" % d for d in windows]
        # names += ["CORD%d" % d for d in windows]

        fields += ["mean(close>shift(close, 1), %d)" % d for d in windows]
        names += ["CNTP%d" % d for d in windows]

        fields += ["mean(close<shift(close, 1), %d)" % d for d in windows]
        names += ["CNTN%d" % d for d in windows]

        fields += ["mean(close>shift(close, 1), %d)-mean(close<shift(close, 1), %d)" % (d, d) for d in windows]
        names += ["CNTD%d" % d for d in windows]

        fields += [
            "sum(greater(close-shift(close, 1), 0), %d)/(sum(Abs(close-shift(close, 1)), %d)+1e-12)" % (d, d)
            for d in windows
        ]
        names += ["SUMP%d" % d for d in windows]

        fields += [
            "sum(greater(shift(close, 1)-close, 0), %d)/(sum(Abs(close-shift(close, 1)), %d)+1e-12)" % (d, d)
            for d in windows
        ]
        names += ["SUMN%d" % d for d in windows]

        fields += [
            "(sum(greater(close-shift(close, 1), 0), %d)-sum(greater(shift(close, 1)-close, 0), %d))"
            "/(sum(Abs(close-shift(close, 1)), %d)+1e-12)" % (d, d, d)
            for d in windows
        ]
        names += ["SUMD%d" % d for d in windows]

        # fields += ["mean(volume, %d)/(volume+1e-12)" % d for d in windows]
        # names += ["VMA_%d" % d for d in windows]

        # fields += ["std(volume, %d)/(volume+1e-12)" % d for d in windows]
        # names += ["VSTD_%d" % d for d in windows]

        fields += [
            "std(Abs(close/shift(close, 1)-1)*volume, %d)/(mean(Abs(close/shift(close, 1)-1)*volume, %d)+1e-12)"
            % (d, d)
            for d in windows
        ]
        names += ["WV_MA_%d" % d for d in windows]

        fields += [
            "sum(greater(volume-shift(volume, 1), 0), %d)/(sum(Abs(volume-shift(volume, 1)), %d)+1e-12)"
            % (d, d)
            for d in windows
        ]
        names += ["VSUMP_%d" % d for d in windows]

        fields += [
            "sum(greater(shift(volume, 1)-volume, 0), %d)/(sum(Abs(volume-shift(volume, 1)), %d)+1e-12)"
            % (d, d)
            for d in windows
        ]
        names += ["VSUMN_%d" % d for d in windows]

        fields += [
            "(sum(greater(volume-shift(volume, 1), 0), %d)-sum(greater(shift(volume, 1)-volume, 0), %d))"
            "/(sum(Abs(volume-shift(volume, 1)), %d)+1e-12)" % (d, d, d)
            for d in windows
        ]
        names += ["VSUMD_%d" % d for d in windows]

        return fields, names

