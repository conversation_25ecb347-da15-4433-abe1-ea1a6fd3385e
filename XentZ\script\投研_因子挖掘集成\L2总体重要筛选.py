''' L2Lasso筛选脚本 - 对L1相关筛选结果进行LassoCV筛选
基于原库L2Lasso方法，采用过程式编程风格，注重执行效率
使用sklearn.linear_model.LassoCV进行特征选择
'''
import pandas as pd
import numpy as np
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from sklearn.linear_model import LassoCV
from common.cls_base import BaseObj, MonitorContext
from config import REPORTS_DIR, cfg_mine
from factor.factorloader import FactorLoader
from factorzoo import factorzoo, FactorMonitorContext, FactorZooRunManager, factorstore


def load_multi_batch_factors(batch_ids: List[str], pipeline_step: str = 'L1') -> Dict[str, pd.DataFrame]:
    """从多个批次加载因子数据 - 按品种+标签组织
    
    Args:
        batch_ids: 批次ID列表
        pipeline_step: 管道步骤，默认L1
        
    Returns:
        Dict[context_key, data_dict]: 按上下文（品种+标签）组织的数据，data_dict包含symbol, target_label, base_data, factor_data, batch_ids
    """
    print(f"开始加载 {len(batch_ids)} 个批次的因子数据...")
    
    # 从FactorZoo获取target_label信息
    all_target_labels = set()
    batch_symbol_mapping = {}
    for batch_id in batch_ids:
        try:
            batch_factors = factorzoo.search_factors({'batch_id': batch_id}, limit=1000)
            if not batch_factors:
                print(f"批次 {batch_id} 在FactorZoo中无因子记录，跳过")
                continue

            # 从第一个因子记录中提取通用信息
            first_factor = batch_factors[0]
            
            # --- 关键修复：健壮地处理symbols字段 ---
            raw_symbols = first_factor.get('symbols', 'unknown')
            symbol = 'unknown'
            if isinstance(raw_symbols, list) and raw_symbols:
                symbol = raw_symbols[0]
            elif isinstance(raw_symbols, str):
                try:
                    # 尝试从JSON或eval格式的字符串恢复列表
                    import ast
                    parsed_list = ast.literal_eval(raw_symbols)
                    if isinstance(parsed_list, list) and parsed_list:
                        symbol = parsed_list[0]
                except (ValueError, SyntaxError):
                     # 如果解析失败，直接使用字符串（虽然不太可能）
                    symbol = raw_symbols
            # --- 修复结束 ---

            target_label = first_factor.get('target_label', 'unknown')
            
            all_target_labels.add(target_label)
            batch_symbol_mapping[batch_id] = {'symbol': symbol, 'target_label': target_label}

        except Exception as e:
            print(f"获取批次 {batch_id} 的元信息失败: {str(e)}")
            batch_symbol_mapping[batch_id] = {'symbol': 'unknown', 'target_label': 'unknown'}
    
    print(f"发现的target_label集合: {sorted(all_target_labels)}")
    
    context_factor_data = {}
    total_factors_loaded = 0
    
    # 遍历批次加载数据
    for batch_id in batch_ids:
        try:
            # 获取批次信息
            batch_info = factorstore.get_batch_info(batch_id)
            if not batch_info:
                print(f"批次 {batch_id} 信息不存在，跳过")
                continue
            
            # 获取symbol和target_label
            symbol = batch_symbol_mapping.get(batch_id, {}).get('symbol', 'unknown')
            target_label = batch_symbol_mapping.get(batch_id, {}).get('target_label', 'unknown')
            
            if symbol == 'unknown' or target_label == 'unknown':
                print(f"批次 {batch_id} 品种或标签信息缺失，跳过")
                continue
            
            # 加载批次数据
            base_data, factor_data = factorstore.load_batch_data(
                batch_id=batch_id, pipeline_step=pipeline_step
            )
            
            if factor_data.empty:
                print(f"批次 {batch_id} 因子数据为空，跳过")
                continue
            
            # 按上下文（品种+标签）合并数据
            context_key = f"{symbol}_{target_label}"
            if context_key not in context_factor_data:
                context_factor_data[context_key] = {
                    'symbol': symbol,
                    'target_label': target_label,
                    'base_data': base_data,
                    'factor_data': factor_data,
                    'batch_ids': [batch_id]
                }
            else:
                # 合并因子数据（相同时间索引）
                existing_factor_data = context_factor_data[context_key]['factor_data']
                merged_factor_data = pd.concat([existing_factor_data, factor_data], axis=1)
                # 去重列名
                merged_factor_data = merged_factor_data.loc[:, ~merged_factor_data.columns.duplicated()]
                context_factor_data[context_key]['factor_data'] = merged_factor_data
                context_factor_data[context_key]['batch_ids'].append(batch_id)
            
            total_factors_loaded += factor_data.shape[1]
            print(f"批次 {batch_id} 加载完成: {symbol}_{target_label}, {factor_data.shape[1]} 个因子")
            
        except Exception as e:
            print(f"加载批次 {batch_id} 失败: {str(e)}")
            continue
    
    print(f"多批次加载完成: {len(context_factor_data)} 个上下文, 总计 {total_factors_loaded} 个因子")
    return context_factor_data


def apply_lasso_cv_filter_for_context(context_key: str, data_dict: Dict,
                                     split_perc: float = 0.7,
                                     cv_folds: int = 5,
                                     max_iter: int = 100000,
                                     tol: float = 1e-4) -> List[str]:
    """对单个上下文应用LassoCV筛选 - 时序验证设计

    Args:
        context_key: 上下文键（品种_标签）
        data_dict: 包含symbol, target_label, base_data和factor_data的字典
        split_perc: 训练集比例，默认0.7
        cv_folds: 时序交叉验证折数，默认5
        max_iter: 最大迭代次数，默认100000
        tol: 收敛容忍度，默认1e-4

    Returns:
        List[str]: 筛选后的因子列表
    """
    from sklearn.model_selection import TimeSeriesSplit

    symbol = data_dict['symbol']
    target_label = data_dict['target_label']
    base_data = data_dict['base_data']
    factor_data = data_dict['factor_data']

    if factor_data.empty:
        return []

    print(f"开始对 {context_key} 的 {factor_data.shape[1]} 个因子进行时序LassoCV筛选...")

    # 构建完整数据集 - 包含因子值和收益率
    full_data = factor_data.copy()

    # 添加收益率列 - 根据配置决定使用哪种收益率
    if 'ret' in base_data.columns:
        full_data['ret'] = base_data['ret']
    elif 'close' in base_data.columns:
        full_data['ret'] = base_data['close'].pct_change().fillna(0)
    else:
        print(f"上下文 {context_key} 缺少收益率数据，无法进行筛选")
        return []

    # 添加开盘收益率（如果需要）
    if 'ret_open' not in full_data.columns and 'open' in base_data.columns:
        full_data['ret_open'] = base_data['open'].pct_change().fillna(0)

    # 确定使用的收益率列
    ret_col = 'ret_open' if cfg_mine.mine.run.nextbar and 'ret_open' in full_data.columns else 'ret'

    # 分离特征和标签
    X_full = full_data.drop(columns=[ret_col, 'ret', 'ret_open'], errors='ignore')
    y_full = full_data[ret_col]

    # 确保所有特征均为数值类型
    num_features = X_full.select_dtypes(include=['number']).columns
    if len(num_features) != X_full.shape[1]:
        X_full = X_full[num_features]
        print(f"包含非数值列，仅用数值列进行LassoCV，忽略列: {set(X_full.columns) - set(num_features)}")

    if X_full.empty or len(num_features) < 2:
        print(f"上下文 {context_key} 有效因子数量不足，跳过筛选")
        return []

    # 时序分割 - 保持时间顺序，避免未来信息泄露
    split_idx = int(len(X_full) * split_perc)
    X_train = X_full.iloc[:split_idx].copy()
    y_train = y_full.iloc[:split_idx].copy()

    print(f"训练集样本数: {len(X_train)}, 特征数: {X_train.shape[1]}")

    # 处理缺失值 - 将空值替换为0
    X_train_values = np.nan_to_num(X_train.values)
    y_train_values = np.nan_to_num(y_train.values)

    # 时序交叉验证设计 - 关键参数调整
    # gap=1: 避免相邻时间点的信息泄露
    # test_size设为训练集的1/cv_folds，确保每折有足够样本
    test_size = max(20, len(X_train) // cv_folds)  # 测试集不少于20个样本

    tscv = TimeSeriesSplit(
        n_splits=cv_folds,
        test_size=test_size,
        gap=1  # 训练集和测试集之间留1个时间点间隔，避免信息泄露
    )

    # 应用时序LassoCV筛选
    try:
        lasso = LassoCV(
            cv=tscv,                  # 使用时序交叉验证
            random_state=0,           # 随机状态，确保结果可复现
            max_iter=max_iter,        # 最大迭代次数
            tol=tol,                  # 收敛容忍度
            n_jobs=-1,                # 并行计算
            selection='cyclic',       # 坐标轴下降法
            fit_intercept=True,       # 拟合截距项
            precompute=False          # 不预计算Gram矩阵，节省内存
        )

        lasso.fit(X_train_values, y_train_values)

        # 筛选非零系数对应的因子
        selected_mask = lasso.coef_ != 0
        selected_features = X_train.columns[selected_mask].tolist()

        # 输出时序LassoCV结果信息
        print(f"最优正则化参数 alpha: {lasso.alpha_:.6f}")
        print(f"筛选出因子数量: {len(selected_features)}")
        print(f"截距项: {lasso.intercept_:.6f}")
        print(f"迭代次数: {lasso.n_iter_}")
        print(f"时序CV折数: {cv_folds}, 测试集大小: {test_size}")

        print(f"上下文 {context_key} 时序LassoCV筛选完成: {len(selected_features)}/{factor_data.shape[1]} 个因子保留")
        return sorted(selected_features)

    except Exception as e:
        print(f"上下文 {context_key} 时序LassoCV筛选失败: {str(e)}")
        return []


def save_filtering_results(results: Dict[str, List[str]], current_date: str, global_uid: str):
    """保存筛选结果到文件"""
    try:
        # 转换为DataFrame格式保存
        all_results = []
        for context_key, factors in results.items():
            symbol_label = context_key.split('_', 1)
            symbol = symbol_label[0] if len(symbol_label) > 0 else 'unknown'
            target_label = symbol_label[1] if len(symbol_label) > 1 else 'unknown'
            
            for i, factor in enumerate(factors, 1):
                all_results.append({
                    'symbol': symbol,
                    'target_label': target_label,
                    'context_key': context_key,
                    'factor_id': i,
                    'expression': factor,
                    'filter_stage': 'L2_lasso',
                    'filter_date': current_date
                })
        
        if all_results:
            result_df = pd.DataFrame(all_results)
            output_path = REPORTS_DIR / 'gplearn' / f'l2_lasso_filtered_factors_{current_date}_{global_uid[-6:]}.csv'
            output_path.parent.mkdir(parents=True, exist_ok=True)
            result_df.to_csv(output_path, index=False)
            print(f"💾 筛选结果已保存: {output_path}")
            return True
            
    except Exception as e:
        print(f"保存筛选结果失败: {str(e)}")
        return False


def persist_and_register_factors(filtered_results: Dict[str, List[str]], 
                                context_factor_data: Dict[str, pd.DataFrame],
                                current_date: str, global_uid: str, 
                                lasso_params: Dict) -> bool:
    """持久化筛选后的因子值和入库到FactorZoo"""
    print("🏦 开始因子值持久化和入库...")
    
    total_success = 0
    total_failed = 0
    
    # 遍历每个上下文处理
    for context_key, selected_factors in filtered_results.items():
        if not selected_factors:
            continue
            
        try:
            # 获取该上下文的原始数据
            data_dict = context_factor_data[context_key]
            symbol = data_dict['symbol']
            target_label = data_dict['target_label']
            base_data = data_dict['base_data']
            full_factor_data = data_dict['factor_data']
            
            # 提取筛选后的因子数据
            filtered_factor_data = full_factor_data[selected_factors].copy()
            
            BaseObj.log(f"处理上下文 {context_key}: {len(selected_factors)} 个筛选因子", "INFO")
            
            # 1. 利用数据库唯一性约束进行去重检查
            # 数据库已有唯一性约束，相同的因子表达式+上下文在同一pipeline_step下只能存在一条记录
            # 循环检查每个因子表达式，如果有任何一个已存在，就复用其批次ID
            
            # 从toml配置中读取数据源配置信息
            data_freq = getattr(cfg_mine.data_source, 'freq', 'D') if hasattr(cfg_mine, 'data_source') else 'D'
            
            # 从toml配置中读取日期范围信息
            date_ranges_info = {}
            if hasattr(cfg_mine, 'time_split'):
                date_ranges_info = {
                    "start": getattr(cfg_mine.time_split, 'train_start_date', None),
                    "end": getattr(cfg_mine.time_split, 'target_end_date', None) or getattr(cfg_mine.time_split, 'target_start_date', None)
                }
            
            # 循环检查每个因子表达式是否已经存在
            existing_batch_id = None
            for factor_expr in selected_factors:
                factor_expr_str = str(factor_expr)
                existing_factors = factorzoo.search_factors({
                    'factor_expression': factor_expr_str,
                    'symbols': [symbol],
                    'frequencies': [data_freq],
                    'date_ranges': date_ranges_info,
                    'factor_type': 'time_series',
                    'target_label': target_label,
                    'creation_method': 'auto_generation',
                    'pipeline_step': 'L2',
                    'pipeline_mode': 'auto_pipeline',
                    'generation_tool': 'l2_lasso_filter'
                }, limit=1)
                
                if existing_factors:
                    existing_batch_id = existing_factors[0]['batch_id']
                    BaseObj.log(f"    ℹ️  发现已存在的因子表达式: {factor_expr_str[:50]}{'...' if len(factor_expr_str) > 50 else ''}", "WARNING")
                    break
            
            if existing_batch_id:
                # 复用已有的批次ID，数据库唯一性约束会阻止重复插入
                BaseObj.log(f"    ℹ️  复用批次ID: {existing_batch_id}", "WARNING")
                l2_batch_id = existing_batch_id
                skip_persistence = True
            else:
                # 创建新的L2批次ID
                l2_batch_id = f"L2_LASSO_{symbol}_{target_label}_{current_date}_{global_uid[-6:]}"
                skip_persistence = False
            
            # 2. 持久化因子值（如果需要）
            if not skip_persistence:
                with FactorMonitorContext(f"PERSIST_L2_LASSO_{context_key}", 
                                        operation_type='l2_lasso_persistence', 
                                        batch_id=l2_batch_id,
                                        symbol=symbol,
                                        data_size=filtered_factor_data.shape[0] * filtered_factor_data.shape[1]):
                    
                    # 准备持久化元数据
                    persistence_metadata = {
                        'symbol': symbol,
                        'target_label': target_label,
                        'date': current_date,
                        'stage': 'L2',
                        'filter_method': 'lasso_cv',
                        'total_factors': len(selected_factors),
                        'source_batches': data_dict.get('batch_ids', []),
                        'filter_params': lasso_params,
                        'creation_time': datetime.now().isoformat(),
                        'global_batch_id': f"L2_LASSO_{global_uid}"
                    }
                    
                    # 执行因子值持久化
                    persistence_success = factorstore.save_batch_data(
                        batch_id=l2_batch_id,
                        base_data=base_data,
                        factor_data_dict={'L2': filtered_factor_data},
                        metadata=persistence_metadata
                    )
                    
                    if persistence_success:
                        BaseObj.log(f"    ✅ 因子值持久化成功: {filtered_factor_data.shape}", "INFO")
                    else:
                        BaseObj.log("    ❌ 因子值持久化失败", "ERROR")
                        total_failed += len(selected_factors)
                        continue
            else:
                print(f"    ⏭️  跳过因子值持久化（数据已存在）")
            
            # 3. 创建FactorZoo批次（如果需要）
            if not skip_persistence:
                factorzoo.create_batch(
                    batch_id=l2_batch_id,
                    batch_name=f"L2Lasso筛选_{symbol}_{target_label}_{current_date}",
                    creation_tool="l2_lasso_filter",
                    source_symbols=[symbol],
                    source_frequencies=[data_freq],
                    source_date_ranges=date_ranges_info,
                    generation_params={
                        "symbol": symbol,
                        "target_label": target_label,
                        "date": current_date,
                        "stage": "L2",
                        "filter_method": "lasso_cv",
                        "lasso_params": lasso_params,
                        "source_batches": data_dict.get('batch_ids', []),
                        "factor_count": len(selected_factors)
                    }
                )
            else:
                BaseObj.log(f"    ⏭️  跳过批次表新记录创建（批次已存在）", "WARNING")
            
            # 4. 入库筛选后的因子
            symbol_success = 0
            symbol_failed = 0
            
            for i, factor_expr in enumerate(selected_factors):
                factor_id = f"F_L2_LASSO_{l2_batch_id}_{i+1:03d}"
                
                try:
                    # 计算因子复杂度评分 - 基于表达式长度和操作符数量
                    expr_str = str(factor_expr)
                    complexity_score = min(100, len(expr_str) + expr_str.count('(') * 2 + expr_str.count(','))
                    
                    # 评估资源强度 - 基于复杂度（L2筛选后的因子通常复杂度较高）
                    if complexity_score < 40:  # L2阈值稍高
                        resource_intensity = 'low'
                    elif complexity_score < 70:
                        resource_intensity = 'medium'
                    else:
                        resource_intensity = 'high'
                    
                    # 因子分类
                    expr_lower = expr_str.lower()
                    if any(word in expr_lower for word in ['volume', 'vol', 'amount']):
                        factor_category = 'PRICE_VOLUME'
                    elif any(word in expr_lower for word in ['ts_mean', 'ts_sum', 'ts_max', 'ts_min', 'rolling']):
                        factor_category = 'PRICE_TREND'
                    else:
                        factor_category = 'PRICE_VOLUME'
                    
                    # 如果跳过了持久化（数据已存在），则直接标记为成功
                    if skip_persistence:
                        BaseObj.log(f"    ℹ️  因子 {factor_id} 已存在，跳过重复入库", "WARNING")
                        symbol_success += 1  # 已存在的因子算作成功
                    else:
                        # 添加因子到FactorZoo - 包含性能数据
                        add_success = factorzoo.add_factor(
                            factor_id=factor_id,
                            factor_name=f"L2_LASSO_{symbol}_{target_label}_{i+1}",
                            factor_expression=expr_str,
                            factor_type="time_series",
                            data_source_type="single_symbol",
                            symbols=[symbol],
                            frequencies=[data_freq],
                            date_ranges=date_ranges_info,
                            creation_method="auto_generation",
                            generation_tool="l2_lasso_filter",
                            pipeline_step="L2",
                            pipeline_mode="auto_pipeline", 
                            primary_category=factor_category,
                            batch_id=l2_batch_id,
                            status="active",
                            target_label=target_label,
                            # 性能相关字段 - 顺带填充
                            complexity_score=complexity_score,
                            resource_intensity=resource_intensity
                        )
                        
                        if add_success:
                            symbol_success += 1
                        else:
                            symbol_failed += 1
                        
                except Exception as e:
                    symbol_failed += 1
                    BaseObj.log(f"    ⚠️  因子 {factor_id} 入库失败: {str(e)}", "ERROR")
            
            total_success += symbol_success
            total_failed += symbol_failed
            
            BaseObj.log(f"    📊 {context_key} 处理完成: 成功{symbol_success}个，失败{symbol_failed}个", "INFO")
            
        except Exception as e:
            BaseObj.log(f"❌ 处理上下文 {context_key} 时出错: {str(e)}", "ERROR")
            total_failed += len(selected_factors)
            continue
    
    # 全局统计
    print("📊 因子持久化和入库完成:")
    print(f"    ✅ 成功处理: {total_success} 个因子")
    if total_failed > 0:
        print(f"    ❌ 处理失败: {total_failed} 个因子")
    if total_success + total_failed > 0:
        success_rate = total_success / (total_success + total_failed) * 100
        print(f"    📈 成功率: {success_rate:.1f}%")
    
    return total_success > 0


def run_l2_lasso_filter_pipeline(batch_ids: List[str] = None,
                                pipeline_step: str = 'L1',
                                split_perc: float = 0.7,
                                cv_folds: int = 5,
                                max_iter: int = 100000,
                                tol: float = 1e-4) -> Dict[str, List[str]]:
    """运行L2Lasso筛选管道 - 主流程函数

    Args:
        batch_ids: 指定批次ID列表，None则自动获取
        pipeline_step: 输入管道步骤
        split_perc: 训练集比例，默认0.7
        cv_folds: 交叉验证折数，默认5
        max_iter: 最大迭代次数，默认100000
        tol: 收敛容忍度，默认1e-4

    Returns:
        筛选结果字典
    """
    print("🚀 开始L2Lasso筛选流程...")
    start_time = datetime.now()
    global_uid = BaseObj.gen_ordered_uid()
    current_date = start_time.strftime('%Y%m%d')

    with MonitorContext(f"L2_LASSO_FILTER_{global_uid}"):

        # 1. 获取批次列表
        if batch_ids is None:
            available_batches = factorstore.get_available_batches(stage=pipeline_step)

            if not available_batches:
                print(f"❌ 未找到可用的{pipeline_step}批次，请先运行L1相关筛选生成因子")
                return {}

            # 选择最近的批次进行处理
            target_batches = available_batches[-10:] if len(available_batches) >= 10 else available_batches
        else:
            target_batches = batch_ids

        print(f"📋 将处理以下批次: {len(target_batches)} 个")
        for i, batch_id in enumerate(target_batches[:5], 1):  # 只显示前5个
            print(f"    {i}. {batch_id}")
        if len(target_batches) > 5:
            print(f"    ... 及其他 {len(target_batches) - 5} 个批次")

        # 2. 显示LassoCV参数
        lasso_params = {
            'split_perc': split_perc,
            'cv_folds': cv_folds,
            'max_iter': max_iter,
            'tol': tol,
            'selection': 'cyclic',
            'random_state': 0
        }

        print("🎯 时序LassoCV参数:")
        print(f"    训练集比例: {split_perc}")
        print(f"    时序交叉验证折数: {cv_folds}")
        print(f"    最大迭代次数: {max_iter}")
        print(f"    收敛容忍度: {tol}")
        print("    时序验证: TimeSeriesSplit with gap=1")

        # 3. 加载多批次因子数据
        context_factor_data = load_multi_batch_factors(target_batches, pipeline_step)

        if not context_factor_data:
            print("未加载到有效的因子数据")
            return {}

        # 4. 按上下文进行LassoCV筛选
        filtered_results = {}
        total_input_factors = 0
        total_output_factors = 0

        for context_key, data_dict in context_factor_data.items():
            print(f"\n{'='*50}")
            print(f"🎯 处理上下文: {context_key}")
            print(f"{'='*50}")

            try:
                symbol = data_dict['symbol']
                target_label = data_dict['target_label']
                
                with FactorMonitorContext(f"L2_LASSO_{context_key}",
                                        operation_type='l2_lasso_filtering',
                                        batch_id=f"L2_LASSO_{context_key}_{current_date}",
                                        symbol=symbol,
                                        data_size=data_dict['factor_data'].shape[1]):

                    input_count = data_dict['factor_data'].shape[1]

                    # 应用LassoCV筛选
                    selected_factors = apply_lasso_cv_filter_for_context(
                        context_key, data_dict, split_perc, cv_folds, max_iter, tol
                    )
                    output_count = len(selected_factors)

                    # --- 关键逻辑：如果因子数不足被跳过，则直接保留唯一的因子 ---
                    if input_count > 0 and output_count == 0:
                        print(f"    ℹ️  未筛选出因子。检查是否因数量不足被跳过...")
                        if input_count < 2:
                            print(f"    ➡️  因子数量 ({input_count}) 不足2个，无法执行Lasso筛选。将直接保留该因子。")
                            selected_factors = data_dict['factor_data'].columns.tolist()
                            output_count = len(selected_factors)
                    # --- 修复结束 ---

                    total_input_factors += input_count
                    total_output_factors += output_count

                    if selected_factors:
                        filtered_results[context_key] = selected_factors
                        print(f"✅ {context_key} 筛选完成: {output_count} 个因子保留")

                        # 显示部分因子（前2个）
                        for j, factor in enumerate(selected_factors[:2], 1):
                            factor_str = str(factor)
                            if len(factor_str) > 60:
                                factor_str = factor_str[:57] + "..."
                            print(f"    {j}. {factor_str}")
                        if len(selected_factors) > 2:
                            print(f"    ... 及其他 {len(selected_factors) - 2} 个因子")
                    else:
                        print(f"⚠️  {context_key} 未筛选出有效因子")

            except Exception as e:
                print(f"❌ 处理上下文 {context_key} 时出错: {str(e)}")
                continue

        # 5. 统计和保存结果
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        filter_efficiency = (total_output_factors / total_input_factors * 100) if total_input_factors > 0 else 0

        print("\n🎉 时序L2Lasso筛选完成!")
        print("📊 处理统计:")
        print(f"    输入因子: {total_input_factors} 个")
        print(f"    输出因子: {total_output_factors} 个")
        print(f"    筛选效率: {filter_efficiency:.1f}%")
        print(f"    处理上下文: {len(filtered_results)} 个")
        print(f"    处理耗时: {processing_time:.1f} 秒")

        # 6. 保存筛选结果
        if filtered_results:
            save_filtering_results(filtered_results, current_date, global_uid)

            # 7. 持久化筛选后的因子值和入库
            persistence_success = persist_and_register_factors(
                filtered_results, context_factor_data, current_date, global_uid, lasso_params
            )

            if persistence_success:
                print("✅ 因子持久化和入库完成")
            else:
                print("⚠️  因子持久化和入库部分失败")

        # 8. 保存运行统计
        run_stats_data = {
            'total_input_factors': total_input_factors,
            'total_output_factors': total_output_factors,
            'batch_processed': len(target_batches),
            'contexts_processed': list(filtered_results.keys()),
            'processing_time': processing_time,
            'filter_efficiency': filter_efficiency,
            'lasso_params': lasso_params,
            'pipeline_step': pipeline_step
        }

        try:
            stats_path = REPORTS_DIR / 'gplearn' / f'l2_lasso_filter_stats_{current_date}_{global_uid[-6:]}.json'
            with open(stats_path, 'w', encoding='utf-8') as f:
                json.dump(run_stats_data, f, indent=2, ensure_ascii=False)
            print(f"📈 运行统计已保存: {stats_path}")
        except Exception as e:
            print(f"保存运行统计失败: {str(e)}")

        return filtered_results


if __name__ == "__main__":
    # 支持命令行参数或交互式运行
    import sys

    if len(sys.argv) > 1:
        # 命令行模式
        if sys.argv[1] == '--help' or sys.argv[1] == '-h':
            print("L2Lasso筛选脚本使用说明:")
            print("python l2_lasso_filter.py [--split 0.7] [--cv 5] [--iter 100000] [--tol 1e-4] [--stage L1]")
            print("  --split: 训练集比例，默认0.7")
            print("  --cv: 交叉验证折数，默认5")
            print("  --iter: 最大迭代次数，默认100000")
            print("  --tol: 收敛容忍度，默认1e-4")
            print("  --stage: 输入管道步骤，默认L1")
            exit(0)

        # 解析参数
        split_perc = 0.7
        cv_folds = 5
        max_iter = 100000
        tol = 1e-4
        pipeline_step = 'L1'

        for i, arg in enumerate(sys.argv[1:]):
            if arg == '--split' and i + 2 < len(sys.argv):
                split_perc = float(sys.argv[i + 2])
            elif arg == '--cv' and i + 2 < len(sys.argv):
                cv_folds = int(sys.argv[i + 2])
            elif arg == '--iter' and i + 2 < len(sys.argv):
                max_iter = int(sys.argv[i + 2])
            elif arg == '--tol' and i + 2 < len(sys.argv):
                tol = float(sys.argv[i + 2])
            elif arg == '--stage' and i + 2 < len(sys.argv):
                pipeline_step = sys.argv[i + 2]

        # 运行筛选
        results = run_l2_lasso_filter_pipeline(
            split_perc=split_perc,
            cv_folds=cv_folds,
            max_iter=max_iter,
            tol=tol,
            pipeline_step=pipeline_step
        )
    else:
        # 交互式模式
        results = run_l2_lasso_filter_pipeline()

    print("✅ L2Lasso筛选流程完成!")
