from pathlib import Path
import streamlit as st
from typing import List, Tuple
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.io as pio
import matplotlib.pyplot as plt
import numpy as np
import matplotlib as mpl
import toml
import scipy.stats as stats
import hashlib
from io import BytesIO
from scipy.stats import gaussian_kde
from matplotlib.ticker import FormatStrFormatter

from config import DATA_DIR_FEATHER, CONFIG_DIR
from datafeed.dataloader import Featherloader
from task_defs.task_fctsmine import Fcts_GP_Mining, TaskFctsMining
from enums import EXPR_NAME_EXCLUDED  # 假设在 enums.py 中定义了预定排除的特征

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号

def render_page(symbol_pairs: List[Tuple[str, str]]):
    """特征管理页面"""
    if 'task_mining' not in st.session_state:
        st.error("请先完成任务配置")
        return
    with st.sidebar:
        st.header("📁 品种列表")
        selected_pair = st.radio(
            "选择品种周期",
            [f"{s}_{p}" for s, p in symbol_pairs] or ["暂无品种"],
            index=0,
            key="feature_nav"
        )
    if selected_pair != "暂无品种":
        render_pair_features(selected_pair, st.session_state.task_mining)
    else:
        st.warning("请先在任务配置页面选择品种周期")


def _init_task_mining(symbol_pairs: List[Tuple[str, str]]):
    """从配置初始化任务实例"""
    # 1. 创建基础实例（内置特征/算子）
    task = Fcts_GP_Mining()
    
    # 2. 加载配置文件参数
    if 'last_config_path' in st.session_state:
        task.load_config(st.session_state.last_config_path)
    
    # 3. 初始化每个pair的排除特征集合(默认为空,表示全部选中)
    if 'excluded_features' not in st.session_state:
        st.session_state.excluded_features = {
            f"{symbol}_{period}": set() 
            for symbol, period in symbol_pairs
        }
    
    st.session_state.task_mining = task

def _need_reload_task() -> bool:
    """检查是否需要重新加载任务"""
    key_params = ['symbol_pairs', 'last_config_path', 'global_config']
    return any(st.session_state.get(k) != getattr(st.session_state.task_mining, k, None)
              for k in key_params)

def render_pair_features(symbol_period: str, task: TaskFctsMining):
    """渲染单个pair的特征管理，允许用户通过复选框选择保留的特征（未选中即排除）"""
    st.header(f"特征管理 - {symbol_period}")
    
    # 初始化 features_modified 状态
    if 'features_modified' not in st.session_state:
        st.session_state.features_modified = False

    # 顶部操作区域：生成特征图表和布局选择
    col1, col2 = st.columns([1, 2])
    with col1:
        if st.button("🔄 生成特征图表"):
            generate_feature_plots(symbol_period)
    with col2:
        layout_options = {
            "4行×2列 (8个)": (4, 2, 8),
            "6行×2列 (12个)": (6, 2, 12),
            "8行×2列 (16个)": (8, 2, 16)
        }
        selected_layout = st.selectbox("布局选择", options=list(layout_options.keys()), key="grid_layout")
        n_rows, n_cols, grid_size = layout_options[selected_layout]
    
    # 如果图表已经生成，则进行分页显示
    if symbol_period in st.session_state.get('feature_plots', {}):
        # 从 task 内置的特征序列中确定待显示的特征（保持顺序）
        features = [f for f in task.feature_names if f in st.session_state.feature_plots[symbol_period]]
        total_pages = (len(features) + grid_size - 1) // grid_size
        page = st.session_state.get(f'page_{symbol_period}', 0)
        
        # 页面导航
        nav_cols = st.columns([2, 1, 1])
        with nav_cols[0]:
            st.markdown(f"**第 {page + 1}/{total_pages} 页 (共{len(features)}个特征)**")
        with nav_cols[1]:
            if st.button("⬅️", disabled=(page == 0), key=f"prev_{symbol_period}"):
                st.session_state[f'page_{symbol_period}'] = max(0, page - 1)
                st.rerun()
        with nav_cols[2]:
            if st.button("➡️", disabled=(page == total_pages - 1), key=f"next_{symbol_period}"):
                st.session_state[f'page_{symbol_period}'] = min(total_pages - 1, page + 1)
                st.rerun()
                
        start_idx = page * grid_size
        current_features = features[start_idx:start_idx + grid_size]
        
        # 对于每个特征，显示图表和"保留"复选框
        for row in range(n_rows):
            cols = st.columns(2)
            for col_idx in range(2):
                feature_index = row * 2 + col_idx
                if feature_index < len(current_features):
                    feature = current_features[feature_index]
                    with cols[col_idx]:
                        container = st.container()
                        # 从 task.excluded_features 中获取当前 pair 的排除特征，
                        # 并转换为 set 类型（防止配置文件中保存为 list 导致类型问题）
                        current_excluded = set(task.excluded_features.get(symbol_period, []))
                        is_excluded = feature in current_excluded
                        # 复选框：勾选表示保留（即未被排除）
                        checked = container.checkbox("保留", value=not is_excluded,
                                                     key=f"chk_{symbol_period}_{feature}")
                        # 根据勾选结果更新 task.excluded_features
                        if checked:
                            current_excluded.discard(feature)
                        else:
                            current_excluded.add(feature)
                        task.excluded_features[symbol_period] = list(current_excluded)
                        # 显示特征图（引用已生成图表）
                        container.image(
                            st.session_state.feature_plots[symbol_period][feature],
                            use_container_width=True,
                            output_format='PNG'
                        )
    
    # 显示当前pair排除的特征列表，紧凑显示并标明每个特征所属的页码
    current_excluded = task.excluded_features.get(symbol_period, set())
    with st.expander(f"**📋 {symbol_period} 排除的特征 ({len(current_excluded)}个)**", expanded=True):
        if current_excluded:
            excluded_features_sorted = []
            # 遍历 task 内置特征以保持顺序，计算每个特征对应的页码（页码从1开始）
            for feature in task.feature_names:
                if feature in current_excluded:
                    # 根据当前布局每页显示 grid_size 个特征，计算页码
                    page_number = (task.feature_names.index(feature) // grid_size) + 1
                    excluded_features_sorted.append(f"`{feature}(第{page_number}页)`")
            st.markdown(" &nbsp;&nbsp; ".join(excluded_features_sorted))
        else:
            st.info("当前未排除任何特征")
    
    # 在显示框架全部内置特征的部分之前，获取预定排除的特征
    feature_names, _ = task.get_init_exprs_names()
    total_count = len(feature_names)

    # 过滤掉预定排除的特征
    filtered_feature_names = [name for name in feature_names if name not in EXPR_NAME_EXCLUDED]

    # 展示过滤后的特征
    with st.expander(f"**📚 框架内置特征集合 ({len(filtered_feature_names)}个)**", expanded=False):
        st.markdown(" &nbsp;&nbsp; ".join([f"`{name}`" for name in filtered_feature_names]))
        st.caption(f"共 {len(filtered_feature_names)} 个")

    # 新增展开组件展示被排除的特征
    excluded_features = [name for name in feature_names if name in EXPR_NAME_EXCLUDED]
    with st.expander(f"**📋 框架内置排除特征 ({len(excluded_features)}个)**", expanded=False):
        if excluded_features:
            st.markdown(" &nbsp;&nbsp; ".join([f"`{name}`" for name in excluded_features]))
        else:
            st.info("当前未排除任何特征")
    
    # 在页面底部增加"上一步"、"保存配置"、"下一步"导航按钮
    st.markdown("---")
    nav_cols = st.columns([1, 1, 1])
    with nav_cols[0]:
        if st.button("上一步"):
            st.session_state.current_page = "任务配置"
            st.switch_page("pages/1_任务配置.py")
    with nav_cols[1]:
        if st.button("保存配置"):
            save_excluded_features()
    with nav_cols[2]:
        if st.button("下一步"):
            st.session_state.current_page = "算子管理"
            st.switch_page("pages/3_算子管理.py")

def generate_feature_plots(symbol_period: str):
    """生成特征图表"""
    task = st.session_state.task_mining
    
    # 获取pair的个性化时间设置或使用全局设置
    pair_config = task.pair_configs.get(symbol_period, {})
    start_date = pair_config.get('start_date', task.start_date)
    end_date = pair_config.get('end_date', task.end_date)
    
    with st.spinner("正在生成特征图表..."):
        task = st.session_state.task_mining
        
        # 确保中文显示正确
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
        plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号
        
        # 创建保存目录
        save_dir = Path("cached/gui/plots_features").joinpath(symbol_period)
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # 准备数据加载器
        path = DATA_DIR_FEATHER.joinpath(task.data_path).resolve()
        loader = Featherloader(
            path=path, 
            symbols=[symbol_period],
            columns=task.columns,
            start_date=start_date,
            end_date=end_date
        )
        # 加载数据并获取实际的特征名称
        dfs_sample, base_features = loader.load_sample_as_norm(
            fields=task.feature_exprs,
            names=task.feature_names,
            n_sample=500
        )
        df_sample = dfs_sample[0].tail(400)  # 考虑预热期
        
        dfs_full, _ = loader.load_as_norm(
            fields=task.feature_exprs,
            names=task.feature_names
        )
        df_full = dfs_full[0]
        
        # 使用实际的特征列名
        available_features = [col for col in df_full.columns if col in task.feature_names]
        
        # 生成并保存图表
        feature_plots = {}
        for feature in available_features:
            plot_path = save_dir.joinpath(f"{feature}.png")
            
            # 计算当前数据的哈希值
            data_hash = hashlib.md5(
                df_sample[feature].values.tobytes() + df_full[feature].values.tobytes()
            ).hexdigest()
            
            # 检查是否存在缓存的哈希值
            hash_path = save_dir.joinpath(f"{feature}.hash")
            need_update = True
            
            if plot_path.exists() and hash_path.exists():
                with open(hash_path, 'r') as f:
                    cached_hash = f.read().strip()
                if cached_hash == data_hash:
                    need_update = False
            
            if not need_update:
                feature_plots[feature] = str(plot_path)
                continue
                
            # 需要更新,创建新图表
            plt.figure(figsize=(12, 4))
            
            # 左侧时序图
            plt.subplot(121)
            plt.plot(df_sample[feature].values, color='blue', linewidth=1)
            plt.title('时序采样数据', fontsize=10)
            plt.xlabel('样本序号', fontsize=9)
            plt.ylabel('特征值', fontsize=9)
            plt.grid(True, alpha=0.3)
            
            # 右侧分布图
            plt.subplot(122)
            valid_data = df_full[feature].dropna().values
            if len(valid_data) > 0:
                # 如果数据全部相同，则构造一个默认的bins，防止除零错误
                if np.min(valid_data) == np.max(valid_data):
                    bins = [np.min(valid_data) - 0.5, np.max(valid_data) + 0.5]
                else:
                    bins = np.linspace(np.min(valid_data), np.max(valid_data), 50)
                plt.hist(valid_data, bins=bins, density=True, color='skyblue', alpha=0.6)
                try:
                    if np.std(valid_data) > 1e-10 and len(np.unique(valid_data)) > 1:
                        kde = gaussian_kde(valid_data)
                        density = kde(bins)
                        plt.plot(bins, density, 'k-', linewidth=2, label='密度估计')
                        plt.fill_between(bins, 0, density, alpha=0.2)
                except (np.linalg.LinAlgError, ValueError) as e:
                    print(f"KDE计算错误: {e}")
                    pass
                
                plt.ylabel('密度', fontsize=9)
                
                # 3. 添加箱型图在分布图上
                ylim = plt.gca().get_ylim()[1]
                bp_position = ylim * 0.8
                bp = plt.boxplot(valid_data, vert=False, positions=[bp_position],
                               widths=ylim * 0.15, patch_artist=True)
                
                # 美化箱型图
                for box in bp['boxes']:
                    box.set(facecolor='lightgreen', alpha=0.6)
                    box.set(linewidth=2)
                for median in bp['medians']:
                    median.set(linewidth=2, color='red')
                for element in ['whiskers', 'caps']:
                    for item in bp[element]:
                        item.set(linewidth=2)
                
                # 4. 设置y轴格式和范围
                plt.gca().yaxis.set_major_formatter(FormatStrFormatter('%.2f'))
                
                # 5. 调整图形比例
                plt.gca().set_box_aspect(0.7)  # 调整图形高宽比
                
                # 6. 添加均值线和统计信息
                mean = np.mean(valid_data)
                std = np.std(valid_data)
                conf_interval = (mean - 2*std, mean + 2*std)  # 使用±2倍标准差作为置信区间

                skew = stats.skew(valid_data)
                kurt = stats.kurtosis(valid_data)
                
                plt.axvline(mean, color='r', linestyle='--', label=f'均值: {mean:.2f}')
                
                # 7. 添加统计信息文本框
                plt.text(0.95, 0.95, 
                        f'均值: {mean:.2f}\n置信区间: [{conf_interval[0]:.2f}, {conf_interval[1]:.2f}]',
                        transform=plt.gca().transAxes,
                        color='red',
                        fontsize=10,
                        fontweight='bold',
                        verticalalignment='top',
                        horizontalalignment='right',
                        bbox={'facecolor': 'white', 'alpha': 0.8, 'pad': 5})
                
                plt.text(0.05, 0.9, f'样本数: {len(valid_data)}',
                        transform=plt.gca().transAxes,
                        fontsize=10,
                        fontweight='bold')
                
                plt.text(0.05, 0.8, f'偏度: {skew:.2f}',
                        transform=plt.gca().transAxes,
                        fontsize=10,
                        fontweight='bold')
                
                plt.text(0.05, 0.7, f'峰度: {kurt:.2f}',
                        transform=plt.gca().transAxes,
                        fontsize=10,
                        fontweight='bold')
                
                plt.title('全量数据分布', fontsize=10)
                plt.xlabel('特征值', fontsize=9)
                plt.ylabel('百分比 (%)', fontsize=9)  # 更新y轴标签
                plt.grid(True, alpha=0.3)
            
            # 调整布局
            plt.suptitle(f"特征: {feature}", y=1.02, fontsize=11)
            plt.tight_layout()
            
            # 保存图片
            plt.savefig(plot_path, dpi=100, bbox_inches='tight')
            plt.close()
            
            # 保存当前数据的hash值到缓存文件
            with open(hash_path, 'w', encoding='utf-8') as f:
                f.write(data_hash)
            
            feature_plots[feature] = str(plot_path)
        
        # 保存到session state
        if 'feature_plots' not in st.session_state:
            st.session_state.feature_plots = {}
        st.session_state.feature_plots[symbol_period] = feature_plots
        
        st.success(f"已生成并保存 {len(feature_plots)} 个特征的图表")

def save_excluded_features():
    """保存所有pair的排除特征配置到 task_mining.toml"""
    config_path = CONFIG_DIR / 'task_mining.toml'
    
    try:
        # 读取现有配置
        with open(config_path, 'r', encoding='utf-8') as f:
            configs = toml.load(f)
        
        task = st.session_state.task_mining
        
        # 检查并初始化 symbol_pairs，如果不存在则使用 task.selected_pairs 作为后备
        symbol_pairs = st.session_state.get('symbol_pairs')
        if symbol_pairs is None:
            symbol_pairs = getattr(task, 'selected_pairs', [])
            st.session_state['symbol_pairs'] = symbol_pairs
        
        excluded_features = {}
        for symbol, period in symbol_pairs:
            pair_str = f"{symbol}_{period}"
            if pair_str in task.excluded_features and task.excluded_features[pair_str]:
                excluded_features[pair_str] = list(task.excluded_features[pair_str])
        
        if excluded_features:
            configs['excluded_features'] = excluded_features
        elif 'excluded_features' in configs:
            del configs['excluded_features']
        
        # 写回配置文件
        with open(config_path, 'w', encoding='utf-8') as f:
            toml.dump(configs, f)
            
        st.success("排除特征配置已保存")
        st.session_state.features_modified = False
    except Exception as e:
        st.error(f"保存配置失败: {str(e)}")
