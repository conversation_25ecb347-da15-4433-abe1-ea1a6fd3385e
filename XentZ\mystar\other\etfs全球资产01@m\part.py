
#!/usr/bin/env python
# -*- coding:utf-8 -*-

from hikyuu import *

author = "admin"
version = "20250418"

def part():
    """ etfs全球资产01@m """
    ret = [
        # 商品
        'sh518880',  # 黄金ETF
        'sz159985',  # 豆粕ETF
        # 海外
        'sh513100',  # 纳指ETF
        # 宽基
        'sh510300',  # 沪深300ETF
        'sz159915',  # 创业板
        # 窄基
        'sz159992',  # 创新药ETF
        'sh515700',  # 新能车ETF
        'sh510150',  # 消费ETF
        'sh515790',  # 光伏ETF
        'sh515880',  # 通信ETF
        'sh512720',  # 计算机ETF
        'sh512660',  # 军工ETF
        'sz159740',  # 恒生科技ETF
    ]
    return ret
    

if __name__ == "__main__":
    # 执行 testall 命令时，会多传入一个参数，防止测试时间过长
    # 比如如果在测试代码中执行了绘图操作，可以打开下面的注释代码
    # 此时执行 testall 命令时，将直接返回
    if len(sys.argv) > 1:
        ind = part()
        print(ind)
        exit(0)

    import sys
    if sys.platform == 'win32':
        import os
        os.system('chcp 65001')

    # 仅加载测试需要的数据，请根据需要修改
    options = {
        'stock_list': ['sz000001'],
        'ktype_list': ['day'],
        'preload_num': {'day_max': 100000},
        'load_history_finance': False,
        'load_weight': False,
        'start_spot': False,
        'spot_worker_num': 1,
    }
    load_hikyuu(**options)
    
    stks = tuple([sm[code] for code in options['stock_list']])
    
    # 请在下方编写测试代码
    ind = part()
    print(ind)
    
    # 显示图形
    import matplotlib.pylab as plt
    plt.show()    
