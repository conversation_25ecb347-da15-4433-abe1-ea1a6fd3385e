from hikyuu.interactive import *
import matplotlib.pyplot as plt
# 创建交易账户
my_tm = crtTM(Datetime(200101010000),init_cash=1000000)

# 创建交易系统
my_sys = SYS_Simple(tm=my_tm)

def TurtleSG(self,k):
    n1 = self.get_param("n1")
    n2 = self.get_param("n2")
    c = CLOSE(k)
    h = REF(HHV(c,n1),1)
    l = REF(LLV(c,n2),1)
    for i in range(h.discard,len(k)):
        if c[i] >= h[i]:
            self._add_buy_signal(k[i].datetime)
        elif c[i] <= l[i]:
            self._add_sell_signal(k[i].datetime)
# 创建交易信号
my_sg = crtSG(TurtleSG, {'n1': 20, 'n2': 10}, 'TurtleSG')
my_mm = MM_Nothing()
s = sm['sz000001']
query = Query(Datetime(200101010000), Datetime(201705010000), recover_type=Query.BACKWARD)
my_sys.mm = my_mm
my_sys.sg = my_sg
my_sys.run(s,query)

my_sys.performance()
plt.show()
