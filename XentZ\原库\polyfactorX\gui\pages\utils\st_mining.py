import streamlit as st
from typing import List, <PERSON><PERSON>
from pathlib import Path
from config import CONFIG_DIR  # 确保导入 CONFIG_DIR

from engine.gp_engine import FctsGPEngine
from task_defs.task_fctsmine import Fcts_GP_Mining, TaskFctsMining


def render_page(symbol_pairs: List[Tuple[str, str]]):
    """因子挖掘页面"""
    st.header("因子挖掘")
    
    # 检查任务对象是否存在
    if 'task_mining' not in st.session_state:
        st.error("请先完成任务配置")
        return
    task = st.session_state.task_mining

    # 任务设置确认：展示更加全面且紧凑的配置信息
    st.subheader("任务设置确认")
    with st.expander("点击查看完整配置详情", expanded=True):
        config_path = st.session_state.get('last_config_path', '未设置')
        st.markdown(f"**配置文件路径：** {config_path}")
        
        selected_pairs = getattr(task, 'selected_pairs', [])
        if selected_pairs:
            pairs_str = ", ".join([f"{s}_{p}" for s, p in selected_pairs])
            st.markdown(f"**选定品种：** {pairs_str}")
        else:
            st.markdown("**选定品种：** 未选定")
            
        feature_names = getattr(task, 'feature_names', [])
        st.markdown(f"**内置特征数量：** {len(feature_names)}")
        
        # 全局参数紧凑显示：key=value 格式，中间用分号分隔
        global_config = getattr(task, 'global_config', {})
        if global_config:
            params_str = "; ".join([f"{key}={value}" for key, value in global_config.items()])
            st.markdown(f"**全局参数：** {params_str}")
        else:
            st.markdown("**全局参数：** 未设置")
        
        # 各品种个别配置紧凑显示
        if hasattr(task, 'pair_configs') and task.pair_configs:
            pair_configs_str = " | ".join(
                [f"{pair}: " + ", ".join([f"{k}={v}" for k, v in cfg.items()])
                 for pair, cfg in task.pair_configs.items()]
            )
            st.markdown(f"**各品种个别配置：** {pair_configs_str}")
        else:
            st.markdown("**各品种个别配置：** 未配置")
        
        # 内置算子数量显示
        if hasattr(task, 'func_set') and task.func_set:
            st.markdown(f"**内置算子数量：** {len(task.func_set)}")
        else:
            st.markdown("**内置算子数量：** 未配置")
        
        # 显示排除特征信息（若存在）
        if hasattr(task, 'excluded_features'):
            excluded_info = []
            for pair, excluded in task.excluded_features.items():
                if excluded:
                    excluded_info.append(f"{pair}({len(excluded)}: {', '.join(excluded)})")
            if excluded_info:
                st.markdown("**排除特征：** " + " | ".join(excluded_info))
            else:
                st.markdown("**排除特征：** 无")
        else:
            st.markdown("**排除特征：** 未配置")
        
        # 核对 session 的 task 对象与配置文件持久化内容是否一致
        if config_path != "未设置":
            try:
                temp_task = Fcts_GP_Mining()
                temp_task.load_config(config_path)
                if (temp_task.global_config != task.global_config or
                    temp_task.pair_configs != task.pair_configs or
                    temp_task.selected_pairs != task.selected_pairs):
                    st.error("当前会话配置与持久化配置不一致！请重新保存配置或核对配置文件。")
                else:
                    st.success("会话配置与持久化配置一致。")
            except Exception as e:
                st.error(f"配置一致性检查失败: {e}")
    
    # 用户确认所有设置均正确后再执行操作
    confirmed = st.checkbox("我已确认以上所有配置信息均正确", key="confirm_mining")
    if not confirmed:
        st.warning("请确认配置后再执行挖掘任务")
        return
    
    # 提示用户手工执行后台任务，目前不在 GUI 中自动启动
    st.success("任务参数已确认。")
    st.info("请前往后台 task-run 目录下，手动执行以下命令以启动因子挖掘任务：")
    st.code("python task_runs/task_gplearn.py", language="bash")



