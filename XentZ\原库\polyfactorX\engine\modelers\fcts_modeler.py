import json
import numpy as np
from pathlib import Path
import pandas as pd
from typing import Union
from datetime import date
from sklearn.model_selection import TimeSeriesSplit
from typing import Tuple
import quantstats_lumi as qsl
from datetime import datetime
from copy import deepcopy

from jzal_pro.cls_common import BaseObj
from .model_strats import ModelStrategy
from enums import NORM_PARAMS_LABEL, NORM_PARAMS_POS
from jzal_pro.utils import factor_utils as plt_u
from jzal_pro.utils import expr_utils as eu

class FactorsModeler(BaseObj):
    ''' 因子模型师: 
        单品种条件下, 支持多因子 装配+分析+图形化, 支持单因子 装配+分析+图形化
        因子值 / 因子绩效
    '''
    def __init__(self, model_strats: ModelStrategy, base_path: str) -> None:
        """
        初始化: 传入模型对象, 模型集中存储的根路径
        """
        self.model_strats: ModelStrategy = model_strats
        self.base_path = base_path
        # 初始化变量
        self.y_hat_as_single_normed = pd.DataFrame() # 动态变量, 存放方法中临时保存的单因子装配模型的y_hat
        self.y_hat_as_combo_normed = pd.Series() # 动态变量, 存放方法中临时保存的因子组合模型的y_hat
        
    def save(self, model_name:str):
        pathname = Path(self.base_path).joinpath(model_name)
        self.model_strats.save(str(pathname))
        
    def show_fctval(self, fctval_df : pd.DataFrame, compr_col: list = ['ret'], 
                    compr_show_cum:bool = True, save_path:str = None):
        ''' 因子值展示, 基于多因子值矩阵
            fctval_df: 某个品种的因子值矩阵, 含ret
            compr_col: 用于标记的列名, ret, ret_open..
        '''
        fctval_df = fctval_df.replace([np.inf, -np.inf, np.nan], 0)
        n_sample = 400
        fctval_df_tail = fctval_df.tail(n_sample)
        fctval_df_tail.to_csv(save_path+'_tail.csv')    
        plt_u.plot_hist6x4(fctval_df, save_path=save_path)  # 全量看分布
        plt_u.plot_eq6x4(fctval_df_tail, compr_col=compr_col, 
                         compr_show_cum=compr_show_cum, save_path=save_path) # 抽样看因子值

    def show_yhat_as_single_normed(self, return_df, compr_col: list = ['ret'],
                                   compr_show_cum:bool = True, save_path:str = None):
        '''
            当前yhat df: self.y_hat_as_single_normed -- 动态临时变量(dataframe)
            return_df: return列数据(series, 长度要与y_hat_as_single_normed 一致)
            compr_col: 用于标记的列名, ret, ret_open..
            compr_show_cum: 对compr_col列是否进行累加操作
        '''
        n_sample = 400
        self.y_hat_as_single_normed.dropna(inplace=True)
        y_hat_as_single_normed_tail = self.y_hat_as_single_normed.tail(n=n_sample)
        y_hat_as_single_normed_tail.to_csv(save_path + '_tail.csv')
        plt_u.plot_hist6x4(self.y_hat_as_single_normed, save_path=save_path)  # 全量看分布
        return_df_tail = return_df.tail(n=n_sample)
        combined_df_tail = pd.concat([y_hat_as_single_normed_tail, return_df_tail], axis=1)
        plt_u.plot_eq6x4(combined_df_tail, compr_col=compr_col, 
                         compr_show_cum=compr_show_cum, save_path=save_path) # 抽样看因子值
        return

    def show_yhat_as_combo_normed(self, return_df, compr_col: list = ['ret'],
                                  compr_show_cum: bool = True, save_path: str = None):
        '''
            当前yhat df: self.y_hat_as_combo_normed -- 动态临时变量(series)
            return_df: return列数据(series, 长度要与y_hat_as_single_normed 一致)
            compr_col: 用于标记的列名, ret, ret_open..
            compr_show_cum: 对compr_col列是否进行累加操作
        '''        
        n_sample = 400
        self.y_hat_as_combo_normed.dropna(inplace=True)
        y_hat_as_combo_normed_tail = self.y_hat_as_combo_normed.tail(n=n_sample)
        y_hat_as_combo_normed_tail.to_csv(save_path + '_tail.csv')
        plt_u.plot_hist6x4(self.y_hat_as_combo_normed, save_path=save_path)  # 全量看分布
        return_df_tail = return_df.tail(n=n_sample)
        combined_df_tail = pd.concat([y_hat_as_combo_normed_tail, return_df_tail], axis=1)
        plt_u.plot_eq6x4(combined_df_tail, compr_col=compr_col, 
                         compr_show_cum=compr_show_cum, save_path=save_path) # 抽样看因子值
        return
        
    def show_fctperf_as_single(self, ret_df: pd.DataFrame, cost_df: pd.DataFrame, # day-level
                               benchmark: Union[pd.DataFrame, pd.Series], # day-level
                               df_ratios_in: pd.DataFrame,  # 结果矩阵,非时序
                               df_ratios_out: pd.DataFrame, # 结果矩阵,非时序
                               df_ratios_all: pd.DataFrame, # 结果矩阵,非时序
                               boundary_day: date, # 分界线的日期
                               save_path: str):  # noqa: E501
        '''
            ret_df:  "日度"因子收益率矩阵 [date], fct1_ret, fct2_ret,...
            cost_df: "日度"交易成本矩阵 [date], fct1_cost, fct2_cost,...
            bech_df: "日度"基准收益率矩阵
            df_ratio_xx: 绩效矩阵(按in/out/all各自一个矩阵)
            boundary_day: 训练集和测试集的分界线日期(date类型)
            batch_uid: 本批次因子的生产序号, 用于保存单因子绩效图表文件名构成用(文件名在本方法内写定)
            save_path: 因子绩效图表的保存路径
        '''
        for idx, (factor_name, factor_ret_se) in enumerate(ret_df.items()):
            factor_name_safe = str(factor_name).replace(':', '_').replace('*', '_')
            ''' =============== 绘制quantstats图 =============== '''
            try:
                qsl.reports.html(factor_ret_se, benchmark=benchmark, 
                                periods_per_year=252, title=factor_name_safe, rf=0.03,
                                output = save_path + '_{}_qsl.html'.format(idx+1)
                                )
            except ValueError as e:
                if "Cannot calculate a linear regression if all x values are identical" in str(e):
                    self.log(f"由于factor{idx}所有因子值相同, 跳过...", level = 'WARNING')
                else:
                    raise e
                continue
            ''' =============== 绘制单因子绩效图 =============== '''
            plt_u.plot_single_factor_stats(factor_ret_se, cost_df[factor_name_safe], benchmark,
                                           df_ratios_in, df_ratios_out, df_ratios_all,
                                           boundary_day, output = save_path + '_{}_perf.png'
                                                                .format(idx+1)
                                           ) 
        return
    
    def show_comboperf(self, ret_df: pd.DataFrame, cost_df: pd.DataFrame, # day-level
                             ret_df_r: pd.DataFrame, cost_df_r: pd.DataFrame, # day-level
                             ret_df_out_r: pd.DataFrame, cost_df_out_r: pd.DataFrame, # day-level
                               benchmark: Union[pd.DataFrame, pd.Series], # day-level
                               df_ratios_in: pd.DataFrame,  # 结果矩阵,非时序
                               df_ratios_out: pd.DataFrame, # 结果矩阵,非时序
                               df_ratios_all: pd.DataFrame, # 结果矩阵,非时序 (wfa+pure拼接)
                               df_ratios_all_r: pd.DataFrame, # 结果矩阵,非时序 (3m rolling)
                               df_ratios_out_r: pd.DataFrame, # 结果矩阵,非时序 (3m rolling)
                               boundary_day: date, # 分界线的日期
                               batch_uid: str, save_path: str):  # noqa: E501
        '''
            ret_df:  "日度"(每列一种组合)收益率矩阵 [date], combo1_ret combo2_ret (ret_se转化的)
            cost_df: "日度"(每列一种组合)交易成本矩阵 [date], combo1_cost combo2_cost (cost_se转化的)
            ret_df_r: "日度"(每列一种组合)收益(滚动)率矩阵 [date], combo1_ret combo2_ret (ret_se转化的)
            cost_df_r: "日度"(每列一种组合)交易成本(滚动)矩阵 [date], combo1_cost combo2_cost (cost_se转化的)
            ret_df_out_r: "日度"(每列一种组合)收益率(样本外滚动)矩阵 [date], combo1_ret combo2_ret (ret_se转化的), 长度比上述要短
            cost_df_out_r: "日度"(每列一种组合)交易成本(样本外滚动)矩阵 [date], combo1_cost combo2_cost (cost_se转化的), 长度比上述要短
            bech_df: "日度"基准收益率矩阵
            df_ratio_xx: 绩效矩阵(按in/out/all/all_r/out_r各自一个矩阵, 每行一种组合, shape同)
            boundary_day: 训练集和测试集的分界线日期(date类型)
            batch_uid: 本批次因子的生产序号, 用于保存单因子绩效图表文件名构成用(文件名在本方法内写定)
            save_path: 因子绩效图表的保存路径
        '''
        assert len(ret_df) == len(cost_df) == len(ret_df_r) == len(cost_df_r), "show_comboperf: 输入数据长度不同!"
        for idx, ((combo_name, combo_ret_se), (_, combo_ret_se_r)) in enumerate(zip(ret_df.items(), ret_df_r.items())):
            combo_name_safe = str(combo_name).replace(':', '_').replace('*', '_')
            ''' =============== 绘制quantstats图 =============== '''
            try:
                qsl.reports.html(combo_ret_se, benchmark=benchmark, 
                                periods_per_year=252, title=combo_name_safe, rf=0.03,
                                output = Path(save_path).joinpath('{}_combo({}-{})_qsl.html'
                                                            .format(batch_uid,5*(idx+1)-4, 5*(idx+1))),
                                )
                qsl.reports.html(combo_ret_se_r, benchmark=benchmark, 
                                periods_per_year=252, title=f"{combo_name_safe}_roll", rf=0.03,
                                output = Path(save_path).joinpath('{}_combo({}-{})_roll_qsl.html'
                                                            .format(batch_uid,5*(idx+1)-4, 5*(idx+1))),
                                )
                qsl.reports.html(ret_df_out_r[combo_name], benchmark=benchmark, 
                                periods_per_year=252, title=f"{combo_name_safe}_out_roll", rf=0.03,
                                output=Path(save_path).joinpath(f'{batch_uid}_combo({5*(idx+1)-4}-{5*(idx+1)})_out_roll_qsl.html'))
            except ValueError as e:
                if "Cannot calculate a linear regression if all x values are identical" in str(e):
                    self.log(f"由于combo{idx}所有因子值相同, 跳过...", level = 'WARNING')
                else:
                    raise e
                continue
            ''' =============== 绘制当前这一组合的绩效图 =============== '''
            plt_u.plot_single_combo_stats(combo_ret_se, cost_df[combo_name_safe], 
                                          combo_ret_se_r, cost_df_r[combo_name_safe], 
                                          ret_df_out_r.get(combo_name), cost_df_out_r.get(combo_name), 
                                          benchmark,
                                          df_ratios_in, df_ratios_out, df_ratios_all, df_ratios_all_r, df_ratios_out_r,
                                          boundary_day, output = Path(save_path).joinpath('{}_combo({}-{})_perf.png'
                                                            .format(batch_uid,5*(idx+1)-4, 5*(idx+1))),
                                        ) 
        return

    def pure_predict_as_single(self, fctval_df: pd.DataFrame, 
                      fee_rate: float, is_ret_open:bool = False) -> Tuple[pd.DataFrame, pd.DataFrame]:
        '''pure装配(单因子逐一), split数据集工作不在本函数操作(调用之前做好), 本函数不做滚动只装配模型并返回ret/cost矩阵
           fctval_df: 因子值矩阵, 含ret(y_raw, 未经norm)
           fee_rate: 交易手续费率
           is_ret_open: 传入的return_col是否为open_ret还是ret, 决定不同装配方式
           返回: 这批因子值各自的 ret 和 cost 矩阵
        '''
        fctval_df = fctval_df.copy()
        num_factors = fctval_df.shape[1] - 1  # 因子数量，减去'ret'列
        columns = fctval_df.columns
        # 初始化ret和cost矩阵
        ret_df = pd.DataFrame(index=fctval_df.index, columns=columns[1:])  # 除去'ret'列
        cost_df = pd.DataFrame(index=fctval_df.index, columns=columns[1:])
        # 获取因子值和目标值
        X = fctval_df.iloc[:, 1:].values  # 去掉'ret'列，保留因子值
        y_raw = fctval_df.iloc[:, 0].values.astype(float).reshape(-1, 1)  # 取'ret' 或 'ret_open' 列, 都是第0列
        
        y_hat_matrix = []  # 存放所有y_hat_normed的临时列表
        
        for j in range(num_factors):
            y_hat = self.model_strats.predict(X[:, j].reshape(-1, 1))
            pos_np = eu.norm(y_hat, params=NORM_PARAMS_POS)  # norm()结果是一维数组
            
            pos_se = pd.Series(pos_np, index=fctval_df.index)
            y_hat_matrix.append(pos_se)  # 将y_hat_normed添加到列表中
            
            if is_ret_open:
                cost_se = pos_se.shift(1).fillna(0).diff().abs() * fee_rate
                cost_se = cost_se.replace([np.inf, np.nan, -np.inf], 0.0)
                ret_se = pos_se.shift(1).fillna(0) * y_raw.flatten() - cost_se.fillna(0)
                ret_se = ret_se.replace([np.inf, np.nan, -np.inf], 0.0)
            else:  # 默认ret用close的ret, close出信号 close买卖
                bar_pct_cost = np.abs(np.diff(pos_np, prepend=pos_np[0])) * fee_rate
                cost_se = pd.Series(bar_pct_cost, index=fctval_df.index)
                cost_se = cost_se.replace([np.inf, np.nan, -np.inf], 0.0)
                bar_pct_ret = pos_np * y_raw.flatten() - bar_pct_cost
                ret_se = pd.Series(bar_pct_ret.flatten(), index=fctval_df.index)
                ret_se = ret_se.replace([np.inf, np.nan, -np.inf], 0.0)
            # 拼接ret和cost到结果DataFrame
            ret_df.loc[:, columns[j+1]] = ret_se.values
            cost_df.loc[:, columns[j+1]] = cost_se.values
        
        self.y_hat_as_single_normed = pd.concat(y_hat_matrix, axis=1)
        self.y_hat_as_single_normed.columns = columns[1:]
        
        ret_df.dropna(how='all', inplace=True)
        cost_df.dropna(how='all', inplace=True)
        
        return ret_df, cost_df

    def wfa_ensemble_as_single(self, fctval_df: pd.DataFrame, 
                                  step_bars: int, fee_rate: float, 
                                  is_ret_open:bool = False) -> Tuple[pd.DataFrame, pd.DataFrame]:
        ''' pure装配(单因子逐一), 不考虑传统的split, 只在这些全部时序上滚动
            df: 因子值矩阵, 含ret(y_raw, 未经norm)
            step_bars: 滚动步长(即测试集天数)比如1000, 则训练集每次固定为2000bars跟着滚动推移; 考虑头尾数据不足情况, 保头不保尾
            is_ret_open: 传入是否为open_ret, 决定不同装配方式
            返回这批因子值各自的 ret 和 cost, 主要用于这些单因子绩效的深度分析
        '''
        fctval_df = fctval_df.copy()
        # 初始化
        num_bars = fctval_df.shape[0]
        train_bars = step_bars * 2  # 训练集大小固定为测试集的两倍
        num_factors = fctval_df.shape[1] - 1  # 因子数量，减去'ret'列
        columns = fctval_df.columns
        ret_df = pd.DataFrame(index=fctval_df.index, columns=columns[1:])  # 除去'ret'列
        cost_df = pd.DataFrame(index=fctval_df.index, columns=columns[1:])
        self.y_hat_as_single_normed = pd.DataFrame(index=fctval_df.index)
        # 获取因子值和目标值
        X = fctval_df.iloc[:, 1:].values  # 去掉'ret'列，保留因子值
        y_raw = fctval_df.iloc[:, 0].values.astype(float).reshape(-1, 1)  # 取'ret' 或 'ret_open' 列, 都是第0列
        y = eu.norm(y_raw.copy(), params=NORM_PARAMS_LABEL).reshape(-1,1)  # 先做norm+clip, 装模前需二维
        
        # 手动实现滚动分割, TimeSeriesSplit实现不了
        for start in range(0, num_bars - train_bars, step_bars):
            end = start + train_bars
            test_start = end
            test_end = test_start + step_bars

            if test_end > num_bars:
                test_end = num_bars  # 尾部数据不够则直接保留剩余数据的测试集

            train_index = range(start, end)
            test_index = range(test_start, test_end)

            print(f"Split {start // step_bars + 1}:")
            print(f"  Train indices: {start} to {end - 1} (total {len(train_index)})")
            print(f"  Test indices:  {test_start} to {test_end - 1} (total {len(test_index)})")
            print()

            
            X_train, X_test = X[train_index], X[test_index]
            y_train, y_test = y[train_index], y[test_index]
            
            for j in range(num_factors):
                self.model_strats.fit_model(X_train[:, j].reshape(-1, 1), y_train)
                y_hat = self.model_strats.predict(X_test[:, j].reshape(-1, 1))
                pos_np = eu.norm(y_hat, params=NORM_PARAMS_POS) # norm()结果是一维数组
                
                # 将当前滚动窗口预测的y_hat拼接到完整列中
                if columns[j+1] not in self.y_hat_as_single_normed.columns:
                    self.y_hat_as_single_normed[columns[j+1]] = np.nan

                self.y_hat_as_single_normed.loc[fctval_df.iloc[list(test_index)].index, columns[j+1]] = pos_np
                
                if is_ret_open:
                    pos_se = pd.Series(pos_np, index=fctval_df.iloc[list(test_index)].index)
                    cost_se = pos_se.shift(1).fillna(0).diff().abs() * fee_rate
                    cost_se = cost_se.replace([np.inf, np.nan, -np.inf],0.0)
                    ret_se = pos_se.shift(1).fillna(0) * y_raw[test_index].flatten() - cost_se.fillna(0)
                    ret_se = ret_se.replace([np.inf, np.nan, -np.inf],0.0)
                else: # 默认ret用close的ret, close出信号 close买卖
                    bar_pct_cost = np.abs(np.diff(pos_np, prepend=pos_np[0])) * fee_rate
                    cost_se = pd.Series(bar_pct_cost, index=fctval_df.iloc[list(test_index)].index)
                    cost_se = cost_se.replace([np.inf, np.nan, -np.inf],0.0)
                    bar_pct_ret = pos_np * y_raw[test_index].flatten() - bar_pct_cost
                    ret_se = pd.Series(bar_pct_ret.flatten(), index=fctval_df.iloc[list(test_index)].index)
                    ret_se = ret_se.replace([np.inf, np.nan, -np.inf],0.0)
                    
                # 拼接ret和cost到结果DataFrame
                ret_df.loc[fctval_df.iloc[list(test_index)].index, columns[j+1]] = ret_se.values  # 隔开'ret'/'ret_open' 列
                cost_df.loc[fctval_df.iloc[list(test_index)].index, columns[j+1]] = cost_se.values
                
        ret_df.dropna(how='all', inplace=True)
        cost_df.dropna(how='all', inplace=True)
        
        return ret_df,cost_df

    def wfa_ensemble(self, fctval_df: pd.DataFrame, 
                    step_bars: int, fee_rate: float, 
                    is_ret_open: bool = False) -> Tuple[pd.Series, pd.Series]:
        ''' 滚动装配(fctval_df矩阵的所有因子(本方法调用的因子组合)一起fit模型), 不在本方法内考虑数据集切分split, 只在这些全部时序上滚动
            fctval_df: 因子值矩阵, 含ret(y_raw, 未经norm)
            step_bars: 滚动步长(即测试集天数)比如1000, 则训练集每次固定为2000bars跟着滚动推移; 考虑头尾数据不足情况, 保头不保尾
            is_ret_open: 传入是否为open_ret, 决定不同装配方式
            返回本因子组合装配模型的结果的 ret 和 cost, 主要用于本因子组合绩效的深度分析
        '''
        fctval_df = fctval_df.copy()
        num_bars = fctval_df.shape[0]
        train_bars = step_bars * 2  # 训练集大小固定为测试集的两倍
        
        if num_bars < train_bars:
            raise ValueError(f"数据集长度 {num_bars} 小于训练集长度 {train_bars}")
        
        # 初始化返回的 ret 和 cost Series
        ret_series = pd.Series(index=fctval_df.index, dtype=float)
        cost_series = pd.Series(index=fctval_df.index, dtype=float)
        self.y_hat_as_combo_normed = pd.Series(dtype=float)
        
        # 获取因子值和目标值
        X = fctval_df.iloc[:, 1:].values  # 去掉'ret'列，保留因子值
        y_raw = fctval_df.iloc[:, 0].values.astype(float).reshape(-1, 1)  # 取'ret' 或 'ret_open' 列, 都是第0列
        y = eu.norm(y_raw.copy(), params=NORM_PARAMS_LABEL).reshape(-1, 1)  # 先做norm+clip, 装模前需二维
        
        # 手动实现滚动分割, TimeSeriesSplit实现不了
        for start in range(0, num_bars - train_bars, step_bars):
            end = start + train_bars
            test_start = end
            test_end = test_start + step_bars

            if end > num_bars:
                raise ValueError(f"训练集结束索引 {end} 超出了数据集长度 {num_bars}")

            if test_end > num_bars:
                test_end = num_bars  # 尾部数据不够则直接保留剩余数据的测试集

            train_index = range(start, end)
            test_index = range(test_start, test_end)

            # print(f"Split {start // step_bars + 1}:")
            # print(f"  Train indices: {start} to {end - 1} (total {len(train_index)})")
            # print(f"  Test indices:  {test_start} to {test_end - 1} (total {len(test_index)})")
            # print()

            X_train, X_test = X[train_index], X[test_index]
            y_train, y_test = y[train_index], y[test_index]
            
            self.model_strats.fit_model(X_train, y_train)
            y_hat = self.model_strats.predict(X_test)
            pos_np = eu.norm(y_hat, params=NORM_PARAMS_POS)  # norm()结果是一维数组
            pos_se = pd.Series(pos_np, index=fctval_df.iloc[list(test_index)].index)
            
            self.y_hat_as_combo_normed = pd.concat([self.y_hat_as_combo_normed, pos_se]) # se拼接
            
            if is_ret_open:
                
                cost_se = pos_se.shift(1).fillna(0).diff().abs() * fee_rate
                cost_se = cost_se.replace([np.inf, np.nan, -np.inf], 0.0)
                ret_se = pos_se.shift(1).fillna(0) * y_raw[test_index].flatten() - cost_se.fillna(0)
                ret_se = ret_se.replace([np.inf, np.nan, -np.inf], 0.0)
            else:  # 默认ret用close的ret, close出信号 close买卖
                bar_pct_cost = np.abs(np.diff(pos_np, prepend=pos_np[0])) * fee_rate
                cost_se = pd.Series(bar_pct_cost, index=fctval_df.iloc[list(test_index)].index)
                cost_se = cost_se.replace([np.inf, np.nan, -np.inf], 0.0)
                bar_pct_ret = pos_np * y_raw[test_index].flatten() - bar_pct_cost
                ret_se = pd.Series(bar_pct_ret.flatten(), index=fctval_df.iloc[list(test_index)].index)
                ret_se = ret_se.replace([np.inf, np.nan, -np.inf], 0.0)
            
            # 拼接ret和cost到结果Series
            ret_series.loc[fctval_df.iloc[list(test_index)].index] = ret_se.values
            cost_series.loc[fctval_df.iloc[list(test_index)].index] = cost_se.values
            
        columns = fctval_df.columns
        self.y_hat_as_combo_normed.name = json.dumps(columns[1:].tolist())
        ret_series.dropna(how='all', inplace=True)
        cost_series.dropna(how='all', inplace=True)
        
        return ret_series, cost_series       

    def pure_predict(self, a_combo_df: pd.DataFrame, 
                      fee_rate: float, is_ret_open:bool = False) -> Tuple[pd.Series, pd.Series]:
        ''' 基于当前模型, pure预测, 不考虑split
            df: 某个组合的因子值, 含ret(y_raw, 未经norm)
            is_ret_open: 传入是否为open_ret, 决定不同装配方式
            返回ret 和 cost, 主要用于特征选择的评估函数调用
        '''
        a_combo_df = a_combo_df.copy()
        if is_ret_open:
            X = a_combo_df.drop('ret_open', axis=1).values.reshape(-1, len(a_combo_df.columns) - 1)
            y_raw = a_combo_df['ret_open'].values.astype(float).reshape(-1, 1)
        else: # 默认ret用close的ret, close出信号 close买卖
            X = a_combo_df.drop('ret', axis=1).values.reshape(-1, len(a_combo_df.columns) - 1)
            y_raw = a_combo_df['ret'].values.astype(float).reshape(-1, 1)
 
        # 直接predict
        y_hat = self.model_strats.predict(X)
        
        pos_np = eu.norm(y_hat, params=NORM_PARAMS_POS) # norm()结果是一维数组
        pos_se = pd.Series(pos_np, index=a_combo_df.index)
        self.y_hat_as_combo_normed = pos_se
        self.y_hat_as_combo_normed.name = json.dumps(a_combo_df.columns.tolist())
        
        if is_ret_open:
            cost_se = pos_se.shift(1).fillna(0).diff().abs() * fee_rate
            cost_se = cost_se.replace([np.inf, np.nan, -np.inf],0.0)
            ret_se = pos_se.shift(1).fillna(0) * y_raw.reshape(-1,) - cost_se.fillna(0)
            ret_se = ret_se.replace([np.inf, np.nan, -np.inf],0.0)
        else: # 默认ret用close的ret, close出信号 close买卖
            bar_pct_cost = np.abs(np.diff(pos_np, prepend=pos_np[0])) * fee_rate
            cost_se = pd.Series(bar_pct_cost)
            cost_se = cost_se.replace([np.inf, np.nan, -np.inf],0.0)
            bar_pct_ret = pos_np * y_raw.reshape(-1,) - bar_pct_cost
            ret_se = pd.Series(bar_pct_ret.flatten())
            ret_se = ret_se.replace([np.inf, np.nan, -np.inf],0.0)
               
        ret_se.index = a_combo_df.index
        cost_se.index = a_combo_df.index

        return ret_se, cost_se

    def pure_ensemble_as_single(self, fctval_df: pd.DataFrame, 
                      fee_rate: float, is_ret_open:bool = False) -> Tuple[pd.DataFrame, pd.DataFrame]:
        ''' ============================== 已废弃 =========================== '''
        '''pure装配(单因子逐一), split数据集工作不在本函数操作(调用之前做好), 本函数不做滚动只装配模型并返回ret/cost矩阵
           fctval_df: 因子值矩阵, 含ret(y_raw, 未经norm)
           fee_rate: 交易手续费率
           is_ret_open: 传入的return_col是否为open_ret还是ret, 决定不同装配方式
           返回: 这批因子值各自的 ret 和 cost 矩阵
        '''
        fctval_df = fctval_df.copy()
        num_factors = fctval_df.shape[1] - 1  # 因子数量，减去'ret'列
        columns = fctval_df.columns
        # 初始化ret和cost矩阵
        ret_df = pd.DataFrame(index=fctval_df.index, columns=columns[1:])  # 除去'ret'列
        cost_df = pd.DataFrame(index=fctval_df.index, columns=columns[1:])
        # 获取因子值和目标值
        X = fctval_df.iloc[:, 1:].values  # 去掉'ret'列，保留因子值
        y_raw = fctval_df.iloc[:, 0].values.astype(float).reshape(-1, 1)  # 取'ret' 或 'ret_open' 列, 都是第0列
        y = eu.norm(y_raw.copy(), params=NORM_PARAMS_LABEL).reshape(-1, 1)  # 先做norm+clip, 装模前需二维
        
        for j in range(num_factors):
            self.model_strats.fit_model(X[:, j].reshape(-1, 1), y)
            y_hat = self.model_strats.predict(X[:, j].reshape(-1, 1))
            pos_np = eu.norm(y_hat, params=NORM_PARAMS_POS)  # norm()结果是一维数组
            
            if is_ret_open:
                pos_se = pd.Series(pos_np, index=fctval_df.index)
                cost_se = pos_se.shift(1).fillna(0).diff().abs() * fee_rate
                cost_se = cost_se.replace([np.inf, np.nan, -np.inf], 0.0)
                ret_se = pos_se.shift(1).fillna(0) * y_raw.flatten() - cost_se.fillna(0)
                ret_se = ret_se.replace([np.inf, np.nan, -np.inf], 0.0)
            else:  # 默认ret用close的ret, close出信号 close买卖
                bar_pct_cost = np.abs(np.diff(pos_np, prepend=pos_np[0])) * fee_rate
                cost_se = pd.Series(bar_pct_cost, index=fctval_df.index)
                cost_se = cost_se.replace([np.inf, np.nan, -np.inf], 0.0)
                bar_pct_ret = pos_np * y_raw.flatten() - bar_pct_cost
                ret_se = pd.Series(bar_pct_ret.flatten(), index=fctval_df.index)
                ret_se = ret_se.replace([np.inf, np.nan, -np.inf], 0.0)
            # 拼接ret和cost到结果DataFrame
            ret_df.loc[:, columns[j+1]] = ret_se.values
            cost_df.loc[:, columns[j+1]] = cost_se.values
        
        ret_df.dropna(how='all', inplace=True)
        cost_df.dropna(how='all', inplace=True)
        
        return ret_df, cost_df

    def pure_ensemble(self, a_combo_df: pd.DataFrame, 
                      fee_rate: float, is_ret_open:bool = False) -> Tuple[pd.Series, pd.Series]:
        ''' ============================== 已废弃 =========================== '''
        ''' pure装配, 不考虑split
            df: 某个组合的因子值, 含ret(y_raw, 未经norm)
            is_ret_open: 传入是否为open_ret, 决定不同装配方式
            返回ret 和 cost, 主要用于特征选择的评估函数调用
        '''
        a_combo_df = a_combo_df.copy()
        if is_ret_open:
            X = a_combo_df.drop('ret_open', axis=1).values.reshape(-1, len(a_combo_df.columns) - 1)
            y_raw = a_combo_df['ret_open'].values.astype(float).reshape(-1, 1)            
        else: # 默认ret用close的ret, close出信号 close买卖
            X = a_combo_df.drop('ret', axis=1).values.reshape(-1, len(a_combo_df.columns) - 1)
            y_raw = a_combo_df['ret'].values.astype(float).reshape(-1, 1)
 
        y = eu.norm(y_raw.copy(), params=NORM_PARAMS_LABEL)  # 先做norm+clip, 从二维数组变成一维
        y = y.reshape(-1, 1)  # 装模型前变二维
        # TODO: 多种模型装配尝试
        self.model_strats.fit_model(X, y)
        y_hat = self.model_strats.predict(X)
        pos_np = eu.norm(y_hat, params=NORM_PARAMS_POS) # norm()结果是一维数组
        if is_ret_open:
            pos_se = pd.Series(pos_np)
            cost_se = pos_se.shift(1).fillna(0).diff().abs() * fee_rate
            cost_se = cost_se.replace([np.inf, np.nan, -np.inf],0.0)
            ret_se = pos_se.shift(1).fillna(0) * y_raw.reshape(-1,) - cost_se.fillna(0)
            ret_se = ret_se.replace([np.inf, np.nan, -np.inf],0.0)
        else: # 默认ret用close的ret, close出信号 close买卖
            bar_pct_cost = np.abs(np.diff(pos_np, prepend=pos_np[0])) * fee_rate
            cost_se = pd.Series(bar_pct_cost)
            cost_se = cost_se.replace([np.inf, np.nan, -np.inf],0.0)
            bar_pct_ret = pos_np * y_raw.reshape(-1,) - bar_pct_cost
            ret_se = pd.Series(bar_pct_ret.flatten())
            ret_se = ret_se.replace([np.inf, np.nan, -np.inf],0.0)
               
        ret_se.index = a_combo_df.index
        cost_se.index = a_combo_df.index

        return ret_se, cost_se
    
    def ensemble_and_analyze(self, symbol:str, fee_rate: float, 
                    a_fcts_final_df: pd.DataFrame, price_ret: pd.DataFrame, 
                    split_perc: float, f1: list, pj: list) -> Tuple[pd.DataFrame, pd.DataFrame]:
        ''' ============================== 已废弃 =========================== '''
        ''' ===================== 1. 装配; 2. 分析 =====================
            a_fcts_final_df: [f1 + pj + ret]
            price_ret: y_raw_close_ret or y_raw_open_ret, etc..
            f1: miner挖掘后筛选的因子
            pj: miner筛选的sharpe第二梯度因子, 混淆因子
            TODO: 外部因子数据引入
        '''
        a_fcts_final_df = a_fcts_final_df.copy()
        idx = int(len(a_fcts_final_df) * split_perc)
        pos_np = self._ensemble(symbol, a_fcts_final_df[f1+['ret']], split_idx=idx)
        perf_df,tot_nav_df = self._analysis(symbol, fee_rate, pos_np, price_ret, idx, len(f1), a_pj_idx=-1)
        perf_df_all = perf_df.T.copy()
        tot_nav_df_all = tot_nav_df.copy()
        # NOTE: pj逐一加入混淆后计算sr,结果排序
        for i, a_pj in enumerate(pj):
            pos_np = self._ensemble(symbol, a_fcts_final_df[f1 + [a_pj] + ['ret']], split_idx=idx)
            perf_df,tot_nav_df = self._analysis(symbol, fee_rate, pos_np, price_ret, idx, len(f1), a_pj_idx = i)
            perf_df_all = pd.concat([perf_df_all, perf_df.T], axis=0)
            tot_nav_df_all = pd.concat([tot_nav_df_all, tot_nav_df], axis=1)
        # 分组排序
        perf_df_all['group_key'] = perf_df_all.index.str.split('_').str[-1]
        perf_df_all.sort_values(by=['group_key', '夏普比率'], ascending=[False, False], inplace=True)
        perf_df_all.drop(columns=['group_key'],axis=1, inplace=True)
        # 保存混淆pj的所有perf  -- 在engine中
        # NOTE: fcts_norm绘图 -- final因子集  -- 在engiine中
        return perf_df_all, tot_nav_df_all
   
    def _ensemble(self, symbol:str, a_fcts_df:pd.DataFrame, split_idx: int):
        ''' ============================== 已废弃 =========================== '''
        '''
        symbol   : 品种code
        a_fcts_df: norm过的"准备装载"的因子值集合 -- 所有数据集(含norm过的y)
        split_perc: 训练集占比
        内含model保存本地功能
        '''
        a_fcts_df = a_fcts_df.copy()
        idx = split_idx
        X_train = a_fcts_df.iloc[:idx].drop('ret', axis=1).values.reshape(-1, len(a_fcts_df.columns) - 1)
        y_train = a_fcts_df['ret'].iloc[:idx].values.astype(float).reshape(-1, 1)
        # TODO: 多种模型装配尝试
        self.model_strats.fit_model(X_train, y_train)
        # 保存模型
        self.model_strats.save(self.path_to_save) # 序列化保存模型
        m = self.model_strats.load(self.path_to_save) # 经过check，保存和调用模型没问题
        # 三个数据集的predict -- hat -- norm + clip
        y_train_hat = m.predict(X_train)
        y_test_hat = m.predict(a_fcts_df.iloc[idx:].drop('ret', axis=1).values.reshape(-1, len(a_fcts_df.columns) - 1))
        y_all_hat = m.predict(a_fcts_df.drop('ret', axis=1).values.reshape(-1, len(a_fcts_df.columns) - 1))

        pos_np = {
            'train': eu.norm(y_train_hat, params=NORM_PARAMS_POS),
            'test': eu.norm(y_test_hat, params=NORM_PARAMS_POS),
            'total': eu.norm(y_all_hat, params=NORM_PARAMS_POS)
        }
        # TODO: ema: fit/select_by_sharpe/ensemble
        return pos_np

    def _analysis(self, symbol:str, fee_rate: float, pos_np:dict, price_ret:pd.DataFrame, 
                  split_idx:int = -1, f_len: int = -1, a_pj_idx:int = -1) -> Tuple[pd.DataFrame, pd.DataFrame]:
        ''' ============================== 已废弃 =========================== '''
        '''
        输入: 品种名, 仓位数组, pj序号(不含pj,则为-1), price_ret(可以是open_ret或close_ret)
        输出: 绩效统计(内/外/全/逐年), 全集净值df(列名替换成唯一str)
        '''
        price_ret = price_ret.copy()
        pos_train_np = deepcopy(pos_np['train'])
        pos_test_np = deepcopy(pos_np['test'])
        pos_total_np = deepcopy(pos_np['total'])
        ''' =============================  映射仓位  =============================== '''
        train_bar_pct_ret = pos_train_np * price_ret.iloc[:split_idx] - np.abs(np.diff(pos_train_np, prepend=pos_train_np[0])) * fee_rate
        train_bar_pct_ret = train_bar_pct_ret.replace([np.inf, np.nan, -np.inf],0.0)
        test_bar_pct_ret = pos_test_np * price_ret.iloc[split_idx:] - np.abs(np.diff(pos_test_np, prepend=pos_test_np[0])) * fee_rate
        test_bar_pct_ret = test_bar_pct_ret.replace([np.inf, np.nan, -np.inf],0.0)
        all_bar_pct_ret = pos_total_np * price_ret - np.abs(np.diff(pos_total_np, prepend=pos_total_np[0])) * fee_rate
        all_bar_pct_ret = all_bar_pct_ret.replace([np.inf, np.nan, -np.inf],0.0) # Series: 2006-06-01 14:45:00    0.005158
        ''' =============================  策略净值  =============================== '''
        train = pd.DataFrame()
        train['date'] = train_bar_pct_ret.index
        train['nav'] = (train_bar_pct_ret.cumsum()+1).values
        test = pd.DataFrame()
        test['date'] = test_bar_pct_ret.index
        test['nav'] = (test_bar_pct_ret.cumsum()+1).values
        total = pd.DataFrame()
        total['date'] = all_bar_pct_ret.index
        total['nav'] = (all_bar_pct_ret.cumsum()+1).values
        ''' =============================  风险指标  =============================== '''
        # 1：初始化
        indicators_frame = pd.DataFrame()
        year_list = set()
        dfs_nav = [train, test, total]
        for df_nav in dfs_nav:  # 最大范围year list
            year_list = set(i for i in pd.to_datetime(df_nav.iloc[:, 0]).dt.year.unique()) | year_list # 获取年份列表
        year_list = sorted(list(year_list),reverse=False) 
        indicators_frame['年份'] = year_list + ['样本内', '样本外', '总体']
        indicators_frame = indicators_frame.set_index('年份')  # 将年份列表设置为表格索引

        # 2：获取每个年份的dataframe
        name_li = ['样本内', '样本外', '总体'] + year_list
        df_li = dfs_nav
        for i in year_list:
            s = pd.Series(total.date).dt.year == i
            s.index = total.index
            df_li.append(total.loc[s])
            
        # 3：计算总体、样本内、样本外和每个年份的dataframe
        # f_len = len(self.best_fcts[symbol])
        all_perf_df = pd.DataFrame()
        
        import jzal_pro.utils.perf_utils as pu
        for df, name in zip(df_li, name_li):
            df_ratios = pu.calc_stats2(df)
            if a_pj_idx == -1:
                df_ratios.rename(columns={0: 'f{}+pjNA_{}'.format(f_len,name)}, inplace=True)
            else:
                df_ratios.rename(columns={0: 'f{}+pj{}_{}'.format(f_len, a_pj_idx, name)}, inplace=True)
            all_perf_df = pd.concat([all_perf_df, df_ratios], axis=1)
        
        tot_nav_df = pd.DataFrame()
        if a_pj_idx == -1:
            tot_nav_df = total.rename(columns={'nav': 'f{}+pjNA_nav'.format(f_len)}, inplace=False)
        else:
            tot_nav_df = total.rename(columns={'nav': 'f{}+pj{}_nav'.format(f_len, a_pj_idx)}, inplace=False)
             
        return all_perf_df, tot_nav_df
    
# if __name__ == '__main__':
#     # 示例数据
#     data = {
#         'date': pd.date_range(start='2005-01-01', periods=10000, freq='T'),
#         'ret': np.random.randn(10000),
#         'fct1': np.random.randn(10000),
#         'fct2': np.random.randn(10000),
#         'fct3': np.random.randn(10000)
#     }
#     fctval_df = pd.DataFrame(data).set_index('date')
#     model_path = DB_DIR_FACTOR.joinpath('factor_models')
#     model_strat = LinearRegressionModel()
#     modeler = FactorsModeler(model_strat, str(model_path))
#     ret_df, cost_df = modeler.wfa_ensemble_as_single(fctval_df, step_bars=1000, daily_bars=1, fee_rate=0.001)