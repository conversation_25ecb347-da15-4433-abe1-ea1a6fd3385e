#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告生成功能测试
验证Phase 3的可视化报告生成功能
"""

import unittest
import pandas as pd
import numpy as np
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import sys
import tempfile
import shutil

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from factor.report_generator import (
    QuantStatsReportGenerator, 
    CustomChartGenerator, 
    BatchReportManager
)


class TestQuantStatsReportGenerator(unittest.TestCase):
    """测试quantstats_lumi报告生成器"""
    
    def setUp(self):
        """设置测试环境"""
        self.generator = QuantStatsReportGenerator()
        
        # 创建模拟收益率数据
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=252, freq='D')
        returns = np.random.normal(0.001, 0.02, 252)  # 日收益率
        self.returns_series = pd.Series(returns, index=dates, name='returns')
        
        # 创建临时目录
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def tearDown(self):
        """清理测试环境"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_generate_metrics_summary_basic(self):
        """测试基础绩效指标计算"""
        metrics = self.generator._calculate_basic_metrics(self.returns_series)
        
        # 验证指标存在
        expected_metrics = [
            'total_return', 'annual_return', 'volatility', 
            'sharpe_ratio', 'max_drawdown', 'win_rate', 
            'skewness', 'kurtosis'
        ]
        
        for metric in expected_metrics:
            self.assertIn(metric, metrics)
            self.assertIsInstance(metrics[metric], (int, float))
        
        # 验证指标合理性
        self.assertGreater(metrics['volatility'], 0)
        self.assertLessEqual(metrics['win_rate'], 1.0)
        self.assertGreaterEqual(metrics['win_rate'], 0.0)
        self.assertLessEqual(metrics['max_drawdown'], 0.0)
    
    def test_generate_metrics_summary_empty_data(self):
        """测试空数据的指标计算"""
        empty_series = pd.Series([], dtype=float)
        metrics = self.generator._calculate_basic_metrics(empty_series)
        
        self.assertEqual(len(metrics), 0)
    
    def test_generate_html_report_without_quantstats(self):
        """测试没有quantstats_lumi时的HTML报告生成"""
        # 模拟quantstats_lumi不可用
        self.generator.qs_available = False
        
        result = self.generator.generate_html_report(
            self.returns_series,
            output_path=self.temp_dir / "test_report.html"
        )
        
        self.assertEqual(result, "")
    
    @patch('factor.report_generator.QuantStatsReportGenerator.qs_available', True)
    def test_generate_html_report_with_mock_quantstats(self):
        """测试使用模拟quantstats_lumi的HTML报告生成"""
        # 创建模拟的quantstats_lumi对象
        mock_qs = MagicMock()
        mock_qs.extend_pandas = MagicMock()
        mock_qs.reports.html = MagicMock()

        self.generator.qs = mock_qs

        output_path = self.temp_dir / "test_report.html"
        result = self.generator.generate_html_report(
            self.returns_series,
            output_path=str(output_path),
            title="测试报告"
        )

        # 验证quantstats_lumi方法被调用
        mock_qs.extend_pandas.assert_called_once()
        mock_qs.reports.html.assert_called_once()

        # 验证返回路径
        self.assertEqual(result, str(output_path))


class TestCustomChartGenerator(unittest.TestCase):
    """测试自定义图表生成器"""
    
    def setUp(self):
        """设置测试环境"""
        self.generator = CustomChartGenerator(figsize=(8, 6), dpi=100)
        
        # 创建模拟WFA结果
        self.wfa_results = [
            {
                'factor_info': {'factor_id': 'factor_1'},
                'performance_metrics': {
                    'sharpe_ratio': 1.2,
                    'max_drawdown': -0.15,
                    'annual_return': 0.08,
                    'win_rate': 0.55,
                    'volatility': 0.12
                }
            },
            {
                'factor_info': {'factor_id': 'factor_2'},
                'performance_metrics': {
                    'sharpe_ratio': 0.8,
                    'max_drawdown': -0.20,
                    'annual_return': 0.06,
                    'win_rate': 0.52,
                    'volatility': 0.15
                }
            },
            {
                'factor_info': {'factor_id': 'factor_3'},
                'performance_metrics': {
                    'sharpe_ratio': 1.5,
                    'max_drawdown': -0.10,
                    'annual_return': 0.12,
                    'win_rate': 0.58,
                    'volatility': 0.10
                }
            }
        ]
        
        # 创建临时目录
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def tearDown(self):
        """清理测试环境"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_generate_wfa_analysis_chart(self):
        """测试WFA分析图表生成"""
        output_path = self.temp_dir / "wfa_analysis.png"
        
        result = self.generator.generate_wfa_analysis_chart(
            self.wfa_results,
            output_path=str(output_path)
        )
        
        # 验证图表文件生成
        self.assertEqual(result, str(output_path))
        self.assertTrue(output_path.exists())
        self.assertGreater(output_path.stat().st_size, 1000)  # 文件大小合理
    
    def test_generate_wfa_analysis_chart_empty_data(self):
        """测试空数据的WFA分析图表生成"""
        result = self.generator.generate_wfa_analysis_chart([])
        
        self.assertEqual(result, "")
    
    def test_generate_time_window_analysis(self):
        """测试时间窗口分析图表生成"""
        # 添加时间序列数据
        wfa_results_with_time = []
        for result in self.wfa_results:
            result_copy = result.copy()
            result_copy['test_periods'] = [
                {
                    'start_date': '2023-01-01',
                    'end_date': '2023-03-31',
                    'period_return': 0.05,
                    'period_sharpe': 1.0
                },
                {
                    'start_date': '2023-04-01',
                    'end_date': '2023-06-30',
                    'period_return': 0.03,
                    'period_sharpe': 0.8
                }
            ]
            wfa_results_with_time.append(result_copy)
        
        output_path = self.temp_dir / "time_analysis.png"
        
        result = self.generator.generate_time_window_analysis(
            wfa_results_with_time,
            output_path=str(output_path)
        )
        
        # 验证图表文件生成
        self.assertEqual(result, str(output_path))
        self.assertTrue(output_path.exists())
    
    def test_generate_robustness_heatmap(self):
        """测试稳健性热力图生成"""
        output_path = self.temp_dir / "robustness.png"
        
        result = self.generator.generate_robustness_heatmap(
            self.wfa_results,
            output_path=str(output_path)
        )
        
        # 验证图表文件生成
        self.assertEqual(result, str(output_path))
        self.assertTrue(output_path.exists())


class TestBatchReportManager(unittest.TestCase):
    """测试批量报告管理器"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建临时目录
        self.temp_dir = Path(tempfile.mkdtemp())
        self.manager = BatchReportManager(base_dir=str(self.temp_dir))
        
        # 创建模拟WFA结果
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        
        self.wfa_results = []
        for i in range(3):
            returns = np.random.normal(0.001, 0.02, 100)
            pnl_series = pd.Series(returns, index=dates, name=f'factor_{i}')
            
            self.wfa_results.append({
                'factor_info': {
                    'factor_id': f'factor_{i}',
                    'factor_name': f'GP_L2_factor_{i}',
                    'symbols': ['510050.SH']
                },
                'performance_metrics': {
                    'sharpe_ratio': 1.0 + i * 0.2,
                    'max_drawdown': -0.1 - i * 0.05,
                    'annual_return': 0.08 + i * 0.02,
                    'win_rate': 0.55 + i * 0.02,
                    'volatility': 0.12 + i * 0.01
                },
                'pnl_series': pnl_series,
                'status': 'passed'
            })
    
    def tearDown(self):
        """清理测试环境"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_calculate_batch_summary(self):
        """测试批量汇总指标计算"""
        summary = self.manager._calculate_batch_summary(self.wfa_results)
        
        # 验证汇总指标
        expected_keys = [
            'total_factors', 'valid_factors', 'avg_sharpe_ratio',
            'median_sharpe_ratio', 'avg_max_drawdown', 'avg_win_rate'
        ]
        
        for key in expected_keys:
            self.assertIn(key, summary)
        
        self.assertEqual(summary['total_factors'], 3)
        self.assertEqual(summary['valid_factors'], 3)
        self.assertGreater(summary['avg_sharpe_ratio'], 0)
    
    def test_save_batch_data(self):
        """测试批量数据保存"""
        data_dir = self.temp_dir / "test_data"
        data_dir.mkdir()
        
        self.manager._save_batch_data(self.wfa_results, data_dir)
        
        # 验证文件生成
        summary_file = data_dir / "validation_summary.csv"
        pnl_file = data_dir / "pnl_series.csv"
        
        self.assertTrue(summary_file.exists())
        self.assertTrue(pnl_file.exists())
        
        # 验证数据内容
        summary_df = pd.read_csv(summary_file)
        self.assertEqual(len(summary_df), 3)
        self.assertIn('factor_id', summary_df.columns)
        self.assertIn('sharpe_ratio', summary_df.columns)
    
    @patch('factor.report_generator.quantstats_generator')
    @patch('factor.report_generator.chart_generator')
    def test_generate_batch_reports(self, mock_chart_gen, mock_qs_gen):
        """测试批量报告生成"""
        # 模拟报告生成器
        mock_qs_gen.generate_html_report.return_value = str(self.temp_dir / "test_report.html")
        mock_chart_gen.generate_wfa_analysis_chart.return_value = str(self.temp_dir / "test_chart.png")
        mock_chart_gen.generate_time_window_analysis.return_value = str(self.temp_dir / "time_chart.png")
        mock_chart_gen.generate_robustness_heatmap.return_value = str(self.temp_dir / "robust_chart.png")
        
        result = self.manager.generate_batch_reports(self.wfa_results)
        
        # 验证结果结构
        self.assertEqual(result['status'], 'success')
        self.assertIn('results', result)
        
        results = result['results']
        self.assertEqual(results['total_factors'], 3)
        self.assertIn('session_id', results)
        self.assertIn('generated_charts', results)
        self.assertIn('summary_metrics', results)
    
    def test_generate_batch_reports_empty_data(self):
        """测试空数据的批量报告生成"""
        result = self.manager.generate_batch_reports([])
        
        self.assertEqual(result['status'], 'failed')
        self.assertIn('error', result)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def tearDown(self):
        """清理测试环境"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_end_to_end_report_generation(self):
        """测试端到端的报告生成流程"""
        # 创建模拟数据
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=50, freq='D')
        returns = np.random.normal(0.001, 0.02, 50)
        pnl_series = pd.Series(returns, index=dates)
        
        wfa_result = {
            'factor_info': {'factor_id': 'test_factor'},
            'performance_metrics': {
                'sharpe_ratio': 1.2,
                'max_drawdown': -0.15,
                'annual_return': 0.08,
                'win_rate': 0.55,
                'volatility': 0.12
            },
            'pnl_series': pnl_series,
            'status': 'passed'
        }
        
        # 创建报告管理器
        manager = BatchReportManager(base_dir=str(self.temp_dir))
        
        # 生成报告
        with patch('factor.report_generator.quantstats_generator') as mock_qs:
            mock_qs.generate_html_report.return_value = str(self.temp_dir / "test.html")
            
            result = manager.generate_batch_reports([wfa_result])
        
        # 验证结果
        self.assertEqual(result['status'], 'success')
        
        # 验证目录结构
        session_dir = Path(result['results']['session_dir'])
        self.assertTrue(session_dir.exists())
        self.assertTrue((session_dir / "charts").exists())
        self.assertTrue((session_dir / "data").exists())


if __name__ == '__main__':
    unittest.main(verbosity=2)
