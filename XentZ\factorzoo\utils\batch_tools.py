#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FactorZoo批次管理工具
快速查看、管理和清理批次数据
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from factorzoo.factor_value_manager import get_factor_value_manager

class BatchTools:
    """批次管理工具"""
    
    def __init__(self):
        self.manager = get_factor_value_manager()
    
    def list_all_batches(self, detailed: bool = False):
        """列出所有批次"""
        batches = self.manager.get_available_batches()
        
        print(f"📋 FactorZoo批次总览 (共 {len(batches)} 个批次)")
        print("=" * 80)
        
        if not batches:
            print("❌ 暂无批次数据")
            return
        
        # 按日期分组
        date_groups = {}
        for batch_id in batches:
            try:
                parts = batch_id.split('_')
                if len(parts) >= 3:
                    date_str = parts[2]
                    if date_str not in date_groups:
                        date_groups[date_str] = []
                    date_groups[date_str].append(batch_id)
            except:
                pass
        
        # 显示分组信息
        for date_str in sorted(date_groups.keys(), reverse=True):
            print(f"\n📅 {date_str} ({len(date_groups[date_str])} 个批次):")
            print("-" * 60)
            
            for batch_id in date_groups[date_str]:
                if detailed:
                    self._show_batch_detail(batch_id)
                else:
                    self._show_batch_summary(batch_id)
    
    def _show_batch_summary(self, batch_id: str):
        """显示批次摘要信息"""
        try:
            batch_info = self.manager.get_batch_info(batch_id)
            symbol = batch_info.get('symbol', 'Unknown')
            creation_time = batch_info.get('creation_time', 'Unknown')
            factor_counts = batch_info.get('factor_counts', {})
            
            total_factors = sum(factor_counts.values()) if factor_counts else 0
            
            print(f"  🔹 {batch_id:<50} | {symbol:<12} | {total_factors:>3} 因子")
            
        except Exception as e:
            print(f"  ❌ {batch_id:<50} | 读取失败: {str(e)}")
    
    def _show_batch_detail(self, batch_id: str):
        """显示批次详细信息"""
        try:
            batch_info = self.manager.get_batch_info(batch_id)
            batch_dir = Path(self.manager.config['by_batch_dir']) / batch_id
            
            print(f"  🔹 {batch_id}")
            print(f"     品种: {batch_info.get('symbol', 'Unknown')}")
            print(f"     创建时间: {batch_info.get('creation_time', 'Unknown')}")
            print(f"     因子数量: {batch_info.get('factor_counts', {})}")
            
            # 文件大小统计
            if batch_dir.exists():
                total_size = sum(f.stat().st_size for f in batch_dir.rglob('*') if f.is_file())
                print(f"     存储大小: {total_size / 1024 / 1024:.2f} MB")
            
            print()
            
        except Exception as e:
            print(f"  ❌ {batch_id}: 读取失败 - {str(e)}")
    
    def list_by_symbol(self, symbol: str = None):
        """按品种列出批次"""
        if symbol:
            batches = self.manager.get_available_batches(symbol=symbol)
            print(f"📊 品种 {symbol} 的批次 (共 {len(batches)} 个)")
        else:
            # 统计所有品种
            all_batches = self.manager.get_available_batches()
            symbol_stats = {}
            
            for batch_id in all_batches:
                try:
                    parts = batch_id.split('_')
                    if len(parts) >= 2:
                        batch_symbol = parts[1]
                        if batch_symbol not in symbol_stats:
                            symbol_stats[batch_symbol] = []
                        symbol_stats[batch_symbol].append(batch_id)
                except:
                    pass
            
            print(f"📊 按品种统计 (共 {len(symbol_stats)} 个品种)")
            print("=" * 60)
            
            for sym, batch_list in sorted(symbol_stats.items()):
                print(f"  {sym:<15} : {len(batch_list):>3} 个批次")
            
            return
        
        print("=" * 80)
        for batch_id in batches:
            self._show_batch_summary(batch_id)
    
    def list_by_stage(self, stage: str):
        """按阶段列出批次"""
        batches = self.manager.get_available_batches(stage=stage)
        
        print(f"📈 {stage} 阶段批次 (共 {len(batches)} 个)")
        print("=" * 80)
        
        for batch_id in batches:
            self._show_batch_summary(batch_id)
    
    def show_batch_files(self, batch_id: str):
        """显示批次文件详情"""
        batch_dir = Path(self.manager.config['by_batch_dir']) / batch_id
        
        if not batch_dir.exists():
            print(f"❌ 批次目录不存在: {batch_id}")
            return []
        
        print(f"📁 批次文件详情: {batch_id}")
        print("=" * 80)
        
        total_size = 0
        file_count = 0
        file_list = []
        
        for file_path in sorted(batch_dir.rglob('*')):
            if file_path.is_file():
                size_mb = file_path.stat().st_size / 1024 / 1024
                total_size += size_mb
                file_count += 1
                
                # 文件类型标识
                if file_path.suffix == '.feather':
                    icon = '🪶'
                elif file_path.suffix == '.parquet':
                    icon = '📦'
                elif file_path.suffix == '.json':
                    icon = '📄'
                else:
                    icon = '📋'
                
                print(f"{file_count:>2}. {icon} {file_path.name:<40} {size_mb:>8.2f} MB")
                file_list.append(file_path)
        
        print("-" * 80)
        print(f"📊 总计: {file_count} 个文件, {total_size:.2f} MB")
        
        return file_list  # 返回文件列表供交互使用

    def show_file_content(self, file_path: Path, head_rows: int = 10):
        """显示文件内容"""
        try:
            print(f"\n📄 文件内容: {file_path.name}")
            print("=" * 80)
            
            if file_path.suffix == '.json':
                # JSON文件
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    print(json.dumps(data, indent=2, ensure_ascii=False))
            
            elif file_path.suffix in ['.feather', '.parquet']:
                # 数据文件
                import pandas as pd
                
                if file_path.suffix == '.feather':
                    df = pd.read_feather(file_path)
                else:
                    df = pd.read_parquet(file_path)
                
                print(f"📊 数据维度: {df.shape}")
                print(f"📋 列名: {list(df.columns)}")
                print()
                
                if len(df) > 0:
                    print(f"🔍 前 {min(head_rows, len(df))} 行数据:")
                    print("-" * 80)
                    
                    # 设置pandas显示选项
                    pd.set_option('display.max_columns', None)
                    pd.set_option('display.width', None)
                    pd.set_option('display.max_colwidth', 20)
                    
                    print(df.head(head_rows).to_string())
                    
                    if len(df) > head_rows:
                        print(f"\n... 共 {len(df)} 行，仅显示前 {head_rows} 行")
                    
                    # 数据类型信息
                    print(f"\n📈 数据类型:")
                    print("-" * 40)
                    for col, dtype in df.dtypes.items():
                        print(f"  {col:<20}: {dtype}")
                    
                    # 基本统计信息（仅数值列）
                    numeric_cols = df.select_dtypes(include=['number']).columns
                    if len(numeric_cols) > 0:
                        print(f"\n📊 数值列统计（前5列）:")
                        print("-" * 80)
                        print(df[numeric_cols[:5]].describe().round(4).to_string())
                
                else:
                    print("❌ 数据文件为空")
                
                return df  # 返回DataFrame
            
            else:
                # 其他文本文件
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                print(f"📄 文件行数: {len(lines)}")
                print(f"🔍 前 {min(head_rows, len(lines))} 行:")
                print("-" * 40)
                
                for i, line in enumerate(lines[:head_rows], 1):
                    print(f"{i:>3}: {line.rstrip()}")
                
                if len(lines) > head_rows:
                    print(f"\n... 共 {len(lines)} 行，仅显示前 {head_rows} 行")
                
                return lines
            
        except Exception as e:
            print(f"❌ 读取文件失败: {str(e)}")
            return None
    
    def cleanup_old_batches(self, days: int = 30, dry_run: bool = True):
        """清理旧批次"""
        from datetime import datetime, timedelta
        
        cutoff_date = datetime.now() - timedelta(days=days)
        all_batches = self.manager.get_available_batches()
        
        old_batches = []
        total_size = 0
        
        for batch_id in all_batches:
            try:
                batch_info = self.manager.get_batch_info(batch_id)
                creation_time_str = batch_info.get('creation_time')
                
                if creation_time_str:
                    creation_time = datetime.fromisoformat(creation_time_str.replace('Z', '+00:00'))
                    if creation_time < cutoff_date:
                        old_batches.append(batch_id)
                        
                        # 计算文件大小
                        batch_dir = Path(self.manager.config['by_batch_dir']) / batch_id
                        if batch_dir.exists():
                            size = sum(f.stat().st_size for f in batch_dir.rglob('*') if f.is_file())
                            total_size += size / 1024 / 1024
                            
            except Exception as e:
                print(f"⚠️  检查批次 {batch_id} 时出错: {str(e)}")
        
        print(f"🧹 批次清理分析 (超过 {days} 天)")
        print("=" * 60)
        print(f"发现旧批次: {len(old_batches)} 个")
        print(f"预计释放空间: {total_size:.2f} MB")
        
        if old_batches:
            print("\n将清理的批次:")
            for batch_id in old_batches[:10]:  # 只显示前10个
                batch_info = self.manager.get_batch_info(batch_id)
                creation_time = batch_info.get('creation_time', 'Unknown')
                print(f"  🗑️  {batch_id} (创建于: {creation_time[:10]})")
            
            if len(old_batches) > 10:
                print(f"  ... 及其他 {len(old_batches) - 10} 个批次")
        
        if not dry_run and old_batches:
            confirm = input(f"\n确认删除这 {len(old_batches)} 个旧批次? (y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                deleted_count = 0
                for batch_id in old_batches:
                    if self.manager.delete_batch_data(batch_id):
                        deleted_count += 1
                print(f"✅ 成功删除 {deleted_count} 个批次")
            else:
                print("❌ 取消删除操作")
        elif dry_run:
            print("\n💡 这是预览模式，要实际删除请使用 --no-dry-run 参数")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='FactorZoo批次管理工具')
    parser.add_argument('action', choices=['list', 'symbol', 'stage', 'files', 'cleanup'], 
                       help='操作类型')
    parser.add_argument('--target', '-t', help='目标参数（品种代码、阶段名、批次ID等）')
    parser.add_argument('--detailed', '-d', action='store_true', help='显示详细信息')
    parser.add_argument('--days', type=int, default=30, help='清理天数阈值')
    parser.add_argument('--no-dry-run', action='store_true', help='实际执行清理（非预览）')
    
    args = parser.parse_args()
    
    print("🛠️  FactorZoo批次管理工具")
    print("=" * 50)
    
    tools = BatchTools()
    
    try:
        if args.action == 'list':
            tools.list_all_batches(detailed=args.detailed)
        
        elif args.action == 'symbol':
            tools.list_by_symbol(args.target)
        
        elif args.action == 'stage':
            if not args.target:
                print("❌ 请指定阶段名，如: --target L0")
                return
            tools.list_by_stage(args.target)
        
        elif args.action == 'files':
            if not args.target:
                print("❌ 请指定批次ID，如: --target GP_510050.SH_20250627_L0_abc123")
                return
            tools.show_batch_files(args.target)
        
        elif args.action == 'cleanup':
            tools.cleanup_old_batches(days=args.days, dry_run=not args.no_dry_run)
            
    except Exception as e:
        print(f"❌ 执行出错: {str(e)}")

if __name__ == "__main__":
    main() 