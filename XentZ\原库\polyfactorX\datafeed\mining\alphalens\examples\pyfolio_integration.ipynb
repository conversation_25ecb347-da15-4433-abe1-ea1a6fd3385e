{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Pyfolio Integration"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alphalens can simulate the performance of a portfolio where the factor values are use to weight stocks. Once the portfolio is built, it can be analyzed by Pyfolio. For details on how this portfolio is built see:\n", "- alphalens.performance.factor_returns\n", "- alphalens.performance.cumulative_returns \n", "- alphalens.performance.create_pyfolio_input"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Imports & Settings"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2021-09-07T23:12:08.038529Z", "start_time": "2021-09-07T23:12:08.030383Z"}}, "source": ["import warnings\n", "warnings.filterwarnings('ignore')"], "outputs": []}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2021-09-07T23:12:10.475056Z", "start_time": "2021-09-07T23:12:09.637898Z"}, "scrolled": true}, "source": ["import alphalens\n", "import pyfolio\n", "import pandas as pd"], "outputs": []}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2021-09-07T23:12:11.858461Z", "start_time": "2021-09-07T23:12:11.840129Z"}}, "source": ["%matplotlib inline"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["First load some stocks data"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2021-09-07T23:12:18.651289Z", "start_time": "2021-09-07T23:12:18.626646Z"}}, "source": ["tickers = [ 'ACN', 'ATVI', 'ADBE', 'AMD', 'AKAM', 'ADS', 'GOOGL', 'GOOG', 'APH', 'ADI', 'ANSS', 'AAPL',\n", "'AVGO', 'CA', 'CDNS', 'CSCO', 'CTXS', 'CTSH', 'GLW', 'CSRA', 'DXC', 'EBAY', 'EA', 'FFIV', 'FB',\n", "'FLIR', 'IT', 'GPN', 'HRS', 'HPE', 'HPQ', 'INTC', 'IBM', 'INTU', 'JNPR', 'KLAC', 'LRCX', 'MA', 'MCHP',\n", "'MSFT', 'MSI', 'NTAP', 'NFLX', 'NVDA', 'ORCL', 'PAYX', 'PYPL', 'QRVO', 'QCOM', 'RHT', 'CRM', 'STX',\n", "'AMG', 'AFL', 'ALL', 'AXP', 'AIG', 'AMP', 'AON', 'AJG', 'AIZ', 'BAC', 'BK', 'BBT', 'BRK.B', 'BLK', 'HRB',\n", "'BHF', 'COF', 'CBOE', 'SCHW', 'CB', 'CIN<PERSON>', 'C', 'CFG', 'CME', 'CMA', 'DFS', 'ETFC', 'RE', 'FITB', 'BEN',\n", "'GS', 'HIG', 'HBA<PERSON>', 'ICE', 'IVZ', 'JP<PERSON>', 'KEY', 'LUK', 'LNC', 'L', 'MTB', 'MMC', 'MET', 'MCO', 'MS',\n", "'NDAQ', 'NAV<PERSON>', 'NTRS', 'PBCT', 'PNC', 'PFG', 'PGR', 'PRU', 'RJF', 'RF', 'SPGI', 'STT', 'STI', 'SYF', 'TROW',\n", "'ABT', 'ABBV', 'AET', 'A', 'ALXN', 'ALGN', 'AGN', 'ABC', 'AMGN', 'ANTM', 'BCR', 'BAX', 'BDX', 'BIIB', 'BSX',\n", "'BMY', 'CAH', 'CELG', 'CNC', 'CERN', 'CI', 'COO', 'DHR', 'DVA', 'XRAY', 'EW', 'EVHC', 'ESRX', 'GILD', 'HCA',\n", "'HSIC', 'HOLX', 'HUM', 'IDXX', 'ILMN', 'INCY', 'ISRG', 'IQV', 'JNJ', 'LH', 'LLY', 'MCK', 'MDT', 'MRK', 'MTD',\n", "'MYL', '<PERSON><PERSON>', 'P<PERSON><PERSON>', 'PRG<PERSON>', 'PF<PERSON>', 'DGX', 'REGN', 'RMD', 'SYK', 'TMO', 'UNH', 'UHS', 'VAR', 'VRTX', 'WAT',\n", "'MMM', 'AY<PERSON>', 'ALK', 'ALLE', 'AAL', 'AME', 'AOS', 'ARNC', 'BA', 'CHRW', 'CAT', 'CTAS', 'CSX', 'CMI', 'DE',\n", "'DAL', 'DOV', 'ETN', 'EMR', 'EFX', 'EXPD', 'FAST', 'FDX', 'FLS', 'FLR', 'FTV', 'FBHS', 'GD', 'GE', 'GWW',\n", "'HON', 'INFO', 'ITW', 'IR', 'J<PERSON>', 'J<PERSON><PERSON>', 'JCI', 'KSU', 'LLL', 'LMT', 'MAS', 'NLSN', 'NSC', 'NOC', 'PCAR',\n", "'PH', 'P<PERSON>', 'P<PERSON>', 'RTN', 'RSG', 'RHI', 'ROK', 'COL', 'ROP', 'LUV', 'SRCL', 'TXT', 'TDG', 'UNP', 'UAL',\n", "'AES', 'LNT', 'AEE', 'AEP', 'AWK', 'CNP', 'CMS', 'ED', 'D', 'DTE', 'DUK', 'EIX', 'ETR', 'ES', 'EXC']"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### YFinance Download"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2021-09-07T23:12:31.521200Z", "start_time": "2021-09-07T23:12:20.933573Z"}}, "source": ["import yfinance as yf\n", "import pandas_datareader.data as web\n", "yf.pdr_override()\n", "\n", "df = web.get_data_yahoo(tickers, start='2015-01-01',  end='2017-01-01')\n", "df.index = pd.to_datetime(df.index, utc=True)"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data Formatting"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2021-09-07T23:12:43.442139Z", "start_time": "2021-09-07T23:12:43.379118Z"}}, "source": ["df = df.stack()\n", "df.index.names = ['date', 'asset']\n", "df.info()"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Compute Factor"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We'll compute a simple mean reversion factor looking at recent stocks performance: stocks that performed well in the last 5 days will have high rank and vice versa."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2021-09-07T23:12:45.413650Z", "start_time": "2021-09-07T23:12:45.370509Z"}}, "source": ["factor = df.loc[:,'Open'].unstack('asset')\n", "factor = -factor.pct_change(5)\n", "factor = factor.stack()"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["The pricing data passed to alphalens should contain the entry price for the assets so it must reflect the next available price after a factor value was observed at a given timestamp. Those prices must not be used in the calculation of the factor values for that time. Always double check to ensure you are not introducing lookahead bias to your study.\n", "\n", "The pricing data must also contain the exit price for the assets, for period 1 the price at the next timestamp will be used, for period 2 the price after 2 timestats will be used and so on.\n", "\n", "There are no restrinctions/assumptions on the time frequencies a factor should be computed at and neither on the specific time a factor should be traded (trading at the open vs trading at the close vs intraday trading), it is only required that factor and price DataFrames are properly aligned given the rules above.\n", "\n", "In our example, before the trading starts every day, we observe yesterday factor values. The price we pass to alphalens is the next available price after that factor observation: the daily open price that will be used as assets entry price. Also, we are not adding additional prices so the assets exit price will be the following days open prices (how many days depends on 'periods' argument). The retuns computed by Alphalens will therefore based on assets open prices."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2021-09-07T23:12:45.706136Z", "start_time": "2021-09-07T23:12:45.672149Z"}}, "source": ["pricing = df.loc[:,'Open'].unstack('asset').iloc[1:]"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run Alphalens Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get Input Data"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2021-09-07T23:12:48.863288Z", "start_time": "2021-09-07T23:12:47.653078Z"}}, "source": ["factor_data = alphalens.utils.get_clean_factor_and_forward_returns(factor,\n", "                                                                   pricing,\n", "                                                                   periods=(1, 3, 5),\n", "                                                                   quantiles=5,\n", "                                                                   bins=None)"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Summary Tear Sheet"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2021-09-07T23:13:02.045516Z", "start_time": "2021-09-07T23:12:48.864382Z"}, "scrolled": true}, "source": ["alphalens.tears.create_summary_tear_sheet(factor_data);"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run Pyfolio Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get Input Data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see in Alphalens analysis that quantiles 1 and 5 are the most predictive so we'll build a portfolio data using only those quantiles."]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2021-09-07T23:13:16.537671Z", "start_time": "2021-09-07T23:13:02.046606Z"}}, "source": ["pf_returns, pf_positions, pf_benchmark = \\\n", "    alphalens.performance.create_pyfolio_input(factor_data,\n", "                                               period='1D',\n", "                                               capital=100000,\n", "                                               long_short=True,\n", "                                               group_neutral=False,\n", "                                               equal_weight=True,\n", "                                               quantiles=[1,5],\n", "                                               groups=None,\n", "                                               benchmark_period='1D')"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Pyfolio Tearsheet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that we have prepared the data we can run Pyfolio functions"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2021-09-07T23:13:22.863699Z", "start_time": "2021-09-07T23:13:16.538539Z"}, "scrolled": false}, "source": ["pyfolio.tears.create_full_tear_sheet(pf_returns,\n", "                                     positions=pf_positions,\n", "                                     benchmark_rets=pf_benchmark)"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Subset Performance"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Weekday Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Sometimes it might be useful to analyze subets of your factor data, for example it could be interesting to see the comparison of your factor in different days of the week. Below we'll see how to select and analyze factor data corresponding to Mondays, the positions will be held the for a period of 5 days"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2021-09-07T23:13:22.875368Z", "start_time": "2021-09-07T23:13:22.864898Z"}}, "source": ["monday_factor_data = factor_data[ factor_data.index.get_level_values('date').weekday == 0 ]"], "outputs": []}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2021-09-07T23:13:25.711516Z", "start_time": "2021-09-07T23:13:22.876258Z"}}, "source": ["pf_returns, pf_positions, pf_benchmark = \\\n", "    alphalens.performance.create_pyfolio_input(monday_factor_data,\n", "                                               period='5D',\n", "                                               capital=100000,\n", "                                               long_short=True,\n", "                                               group_neutral=False,\n", "                                               equal_weight=True,\n", "                                               quantiles=[1,5],\n", "                                               groups=None,\n", "                                               benchmark_period='1D')"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Pyfolio Tearsheet"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2021-09-07T23:13:31.792091Z", "start_time": "2021-09-07T23:13:25.712423Z"}, "scrolled": false}, "source": ["pyfolio.tears.create_full_tear_sheet(pf_returns,\n", "                                     positions=pf_positions,\n", "                                     benchmark_rets=pf_benchmark)"], "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 1}