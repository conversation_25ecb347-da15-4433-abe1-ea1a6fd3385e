# 特征工程 (Feature Engineering)

## 1. 核心设计理念

本框架的特征工程模块遵循"**定义与计算分离**"的核心设计理念，旨在实现最大程度的灵活性、可复现性和高性能。

-   **表达式即特征 (Expression as Feature)**:
    所有特征，无论简单或复杂，都通过人类可读的**字符串表达式**来定义。例如，`sma(close, 5)` 或 `(high - low) / close`。这使得特征的定义直观、易于版本控制，并且与计算逻辑解耦。

-   **特征集封装 (Alpha Classes as Feature Sets)**:
    一组逻辑上相关的特征表达式被封装在专门的 `Alpha` 类中（如 `AlphaOrigin`, `Alpha158`）。这使得特征可以被模块化地组织、复用和共享。

-   **统一计算引擎 (Unified Calculation Engine)**:
    提供一个统一的、高性能的计算引擎 (`FeatPreprocessing.calc_expr_df_all`)。该引擎负责解析表达式字符串，并调用底层高度优化的函数库（如 `Numpy`, `Talib`）来执行计算，用户无需关心具体的计算实现。

-   **配置驱动流程 (Configuration Driven Workflow)**:
    整个特征生成和预处理流程由 TOML 配置文件 (`config/tasks/ts_feat.toml`) 驱动。用户通过修改配置，即可精确控制使用哪些特征集、如何处理缺失值、如何进行数据归一化等，无需修改任何 Python 代码。

## 2. 关键组件与目录结构

```
datafeed/
├── features/
│   ├── alphas/                 # 存放特征集定义 (Alpha类)
│   │   ├── alpha.py            # Alpha 基类和基础特征
│   │   ├── alpha158.py         # WorldQuant 101 Alphas
│   │   └── ...
│   │
│   └── feature_utils.py        # 核心工具类 (计算, 预处理, 筛选)
│
└── expr_funcs/
    ├── expr.py                 # 表达式求值主引擎 (eval)
    ├── expr_utils.py           # 表达式计算工具 (如按symbol分组计算)
    ├── expr_unary.py           # 一元函数 (e.g., abs, log)
    ├── expr_binary.py          # 二元函数 (e.g., add, sub)
    └── expr_funcs_talib.py     # TA-Lib 函数封装
```

-   **`features/alphas/`**: 特征的"**定义层**"。每个 `alpha*.py` 文件包含一个或多个 `Alpha` 类，每个类都是一个特征篮子，其 `get_exprs_names()` 方法返回一批特征的表达式列表和名称列表。
-   **`features/feature_utils.py`**: 特征的"**处理层**"。包含了整个工作流的核心工具类 `FeatPreprocessing`，它封装了从特征计算、缺失值处理、异常值裁剪到数据归一化的所有核心方法。
-   **`expr_funcs/`**: 特征的"**实现层**"。这里存放着表达式中用到的所有底层函数（如 `sma`, `ts_delay`, `rank` 等）的具体实现。

## 3. 端到端工作流

一个典型的特征生成任务（如原 `F0特征提取.py`）遵循以下步骤：

1.  **加载基础数据**:
    使用 `HKUDataloader` 加载指定时间范围和品种的原始价量数据（OHLCV）。

2.  **加载特征定义**:
    -   从 `config.cfg_feat` 配置中读取 `enabled_alphas` 列表。
    -   遍历该列表，实例化每一个 `Alpha` 类（如 `AlphaOrigin()`, `Alpha158()`）。
    -   调用每个实例的 `get_exprs_names()` 方法，将返回的表达式和名称分别汇总到两个大的列表中。

3.  **计算特征**:
    -   调用核心方法 `FeatPreprocessing.calc_expr_df_all()`。
    -   该方法接收原始数据 DataFrame、特征表达式列表和特征名称列表作为输入。
    -   内部，它会遍历每一个表达式，调用 `calc_expr()` 函数。
    -   `calc_expr()` 使用 `eval()` 将字符串表达式动态地在 DataFrame 的上下文中求值，从而生成该特征的 Series。
    -   所有生成的特征 Series 被合并回原始的 DataFrame 中。

4.  **数据预处理**:
    -   调用 `FeatPreprocessing.normalize_df_all()` 方法。
    -   此方法会根据 `ts_feat.toml` 中的 `[missing]`, `[outliers]`, `[norm]` 等配置，自动完成一整套预处理流程：
        -   **填充缺失值**: 如 `ffill`。
        -   **处理异常值**: 如 `mad_clip` (中位数绝对偏差裁剪)。
        -   **方差筛选**: 移除低方差的无效特征。
        -   **数据归一化**: 对特征和标签列执行指定的归一化/标准化操作（如滚动Z-Score）。

5.  **保存结果**:
    将处理完成、包含所有特征的最终 DataFrame 保存为 Parquet 文件，供后续的因子挖掘、筛选和模型训练使用。

## 4. 如何定义与使用特征

### 步骤1：在 Alpha 类中定义表达式

打开或创建一个 `datafeed/features/alphas/` 下的 `alpha_*.py` 文件，添加你的特征。

```python
# datafeed/features/alphas/alpha_custom.py
class AlphaCustom(AlphaBase):
    def get_exprs_names(self):
        exprs = []
        names = []

        # 示例：计算5日和20日的动量
        names += ['mom_5', 'mom_20']
        exprs += ['close / ts_delay(close, 5) - 1', 'close / ts_delay(close, 20) - 1']
        
        # 示例：计算波动率
        names += ['volatility_20']
        exprs += ['ts_std_dev(close, 20)']
        
        return exprs, names
```

### 步骤2：在 TOML 中启用 Alpha 类

打开 `config/tasks/ts_feat.toml`，将你的新 Alpha 类名添加到 `enabled_alphas` 列表中。

```toml
# config/tasks/ts_feat.toml
[feat.set]
enabled_alphas = [
    "AlphaBase",
    "AlphaOrigin",
    # ... 其他已有的 Alpha
    "AlphaCustom"  # <-- 添加你的新类
]
```

### 步骤3：运行特征提取脚本

当特征提取工作流运行时，它会自动加载 `AlphaCustom`，计算其中定义的新特征，并将它们纳入后续的预处理流程中。

## 5. 如何添加自定义函数

如果你定义的特征表达式需要一个当前 `expr_funcs` 库中没有的函数（例如，一个特殊的平滑方法 `special_smooth`），可以按以下步骤添加：

1.  **在 `expr_funcs` 中实现函数**:
    打开一个相关的 `expr_*.py` 文件（如 `expr_unary.py`），添加你的函数实现。函数应该能处理 Pandas Series/DataFrame。

    ```python
    # datafeed/expr_funcs/expr_unary.py
    def special_smooth(s: pd.Series, window: int):
        # ... 你的实现逻辑 ...
        return s.ewm(span=window).mean()
    ```

2.  **在 `__init__.py` 中暴露函数**:
    确保你的新函数在 `datafeed/expr_funcs/__init__.py` 中被导入，这样 `eval()` 才能找到它。

    ```python
    # datafeed/expr_funcs/__init__.py
    from .expr_unary import abs, log, special_smooth # <-- 添加你的函数
    # ...
    ```

3.  **在表达式中使用**:
    现在你就可以在任何 `Alpha` 类的表达式中直接使用这个新函数了。

    ```python
    # in some Alpha class
    names += ['custom_smooth_mom']
    exprs += ['special_smooth(mom_5, 10)']
    ```
