import pandas as pd
from sklearn.linear_model import LinearRegression

class FactorModeler:
    ''' 单因子值在单品种上的训练和表现'''
    def __init__(self, a_symbol, a_fct_name, a_df, freq, split_dt, n_days=5): # n_days非常重要,5~6比较好
        self.a_symbol = a_symbol
        self.a_fct_name = a_fct_name
        self.a_df = a_df.dropna(axis=0).reset_index(drop=False) # a_df: date-tdate-close-fct(单因子), 统一去缺失值+去索引date
        self.freq = freq
        self.n_days = n_days
        self.split_dt = split_dt
        self.a_ret_name = 'ret'
        self.a_df_tret = pd.DataFrame() # 带ret的全集df, date不是索引
        self.a_df_train = pd.DataFrame()  # 训练集df, date不是索引
        self.a_df_test = pd.DataFrame() # 测试集df, date不是索引
        
    
    def _calc_t_ret(self):
        final_frame = self.a_df.copy() # 否则在原a_df上修改了!!!!
        # 计算实际时间步的索引间隔
        if self.freq == '15': 
            t_delta = int(1 * self.n_days) # n_days在这里首次出现
        else:
            t_delta = int(int(240 / int(self.freq)) * self.n_days) 
    
        for i in range(0, len(final_frame) - t_delta, 1):
            final_frame.loc[i, self.a_ret_name] = final_frame.loc[i + t_delta, 'close'] / final_frame.loc[i, 'close'] - 1
            # 可优化
        # 去除收益率为空的行并重置索引
        final_frame = final_frame.dropna(axis=0).reset_index(drop=True)
        '''
                            date       tdate   close   fct_xxx       ret
        0     2005-02-23 09:45:00  2005-02-23  0.6433  0.000000  0.002332
        '''
        return final_frame
    
    def _train(self):
        df = self.a_df_tret
        split_dt = pd.to_datetime(self.split_dt)
        split_dt_index = df[(df['date'].dt.year == split_dt.year) & (df['date'].dt.month == split_dt.month) & (df['date'].dt.day == split_dt.day)].index.values[0] #  & (data_for_model['etime'].dt.hour == 15) # 先省去
        # 划分训练集、测试集
        x_train = df.loc[: split_dt_index, self.a_fct_name].values.reshape(-1, 1) # reshape成为一个二维数组，其中每一个数字有一个[]为了方便后续回归运算
        y_train = df.loc[: split_dt_index, self.a_ret_name].values.reshape(-1, 1)
        x_test = df.loc[split_dt_index + 1: , self.a_fct_name].values.reshape(-1, 1)
        ''' [ [ 0.        ] ... [-0.14328977] ] '''
        self.a_df_train = self.a_df_tret.loc[:split_dt_index, :].copy() # 确保不是视图
        self.a_df_test = self.a_df_tret.loc[split_dt_index+1: , :].copy()
        # print(self.a_df_test)
        
        # dt_train = df.loc[: split_dt_index, 'date'].values  # train set ['2005-02-23T09:45:00.000000000' ... '2016-12-30T09:45:00.000000000']
        # dt_test = df.loc[split_dt_index+1: ,'date'].values  # test set
        # dt_train_test = df.loc[:,'date'].values # train + test set
        ''' 可以把模型变化为lightgbm, xgboost等, 通常line效果更好 '''
        model = LinearRegression(fit_intercept=True) # 初始化model, 极简主义，保证因子的原汁原味的成绩 weight,bias
        model.fit(x_train, y_train)
        # print(model.coef_) # weights [[-0.00014705]]
        # print(model.intercept_) # bias [3.42564021e-05]
        # 测试集预测-----
        y_test_hat = model.predict(x_test) # 计算出来预测的Y值, [[ 7.19351619e-05] ... [ 7.64291773e-05]]
        y_test_hat = [i[0] for i in y_test_hat]  # 变成一维list; 注意：此处不要试图通过归一化进行仓位映射 [[1], [2], [3]] 
        # 注意这里因为是linear regression，故选取了i[0]作为输出结果，如果是lgb，可能不需要这样做
        # i[0]的原因是reshape(-1,1)以后，是一列[[]……[]]，竖着的
        # 这里的问题是test数据集也全都是正数，这说明要么因子有问题，要么linearregression算法有问题
        # 训练集回归-----
        y_train_hat = model.predict(x_train)
        y_train_hat = [i[0] for i in y_train_hat]
        # print(y_train_hat) # 数据格式为正常的list
        # 把train部分也用模型计算一下，得出来的数据存在拟合，但是是模型输出的真是结果
        # 这里出现的问题是y_train_hat全部都是正数，这明显不对，明天用其他的代码试试
        return y_train_hat, y_test_hat # 返回训练集回归结果 与 测试集预测结果
        
    def backtest(self):
        y_train_hat = pd.DataFrame()
        y_test_hat = pd.DataFrame()
        self.a_df_tret = self._calc_t_ret()
        y_train_hat, y_test_hat = self._train()
        import jzal_pro.utils.perf_utils as pu
        # a_df_train_nav = pu.calc_nav_FP(self.a_df_train, y_train_hat, 1) # posizion_size = 1
        # a_df_test_nav = pu.calc_nav_FP(self.a_df_test, y_test_hat, 1)
        # a_df_train_nav2 = pu.calc_nav_FP2(self.a_df_train, y_train_hat, 1) # posizion_size = 1
        # a_df_test_nav2 = pu.calc_nav_FP2(self.a_df_test, y_test_hat, 1)
        a_df_train_nav3 = pu.calc_nav_FP3(self.a_df_train, y_train_hat, 1) # posizion_size = 1
        a_df_test_nav3 = pu.calc_nav_FP3(self.a_df_test, y_test_hat, 1) # posizion_size = 1
        a_df_all = pd.concat([self.a_df_train,self.a_df_test],axis=0)
        y_all_hat = y_train_hat + y_test_hat
        a_df_all_nav3 = pu.calc_nav_FP3(a_df_all, y_all_hat, 1) # posizion_size = 1
        # print(a_df_train_nav)
        # print(a_df_train_nav2)
        # a_df_train_nav3.to_csv('test_{}.csv'.format(self.a_fct_name), encoding='utf-8-sig')
        # print(a_df_test_nav3)
        self._analysis(a_df_train_nav3[['date','nav']], a_df_test_nav3[['date','nav']], a_df_all_nav3[['date','nav']])
        ''' dfs_nav: List [df_nav1, df_nav2, ... df_navn] '''
        return None
    
    def _analysis(self, train, test, total):
        # 0：设置无风险利率
        fixed_return = 0.0
        # 1：初始化
        indicators_frame = pd.DataFrame()
        year_list = set()
        dfs_nav = [train, test, total]
        for df_nav in dfs_nav:  # 最大范围year list
            year_list = set(i for i in pd.to_datetime(df_nav.iloc[:, 0]).dt.year.unique()) | year_list # 获取年份列表
        year_list = sorted(list(year_list),reverse=False) 
        indicators_frame['年份'] = year_list + ['样本内', '样本外', '总体']
        indicators_frame = indicators_frame.set_index('年份')  # 将年份列表设置为表格索引

        # 2：获取每个年份的dataframe
        name_li = ['样本内', '样本外', '总体'] + year_list
        df_li = dfs_nav
        for i in year_list:
            s = pd.Series(total.date).dt.year == i
            s.index = total.index
            df_li.append(total.loc[s])
            
        # 3：计算总体、样本内、样本外和每个年份的dataframe
        all_perf_df = pd.DataFrame()
        import jzal_pro.utils.perf_utils as pu
        for df, name in zip(df_li, name_li):
            df_ratios = pu.calc_stats2(df)
            df_ratios.rename(columns={0: '{}_{}'.format(self.a_fct_name,name)}, inplace=True)
            all_perf_df = pd.concat([all_perf_df, df_ratios], axis=1)
        
        print(all_perf_df.to_string(justify='right'))