#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WFA验证核心算法单元测试
测试每个函数的功能正确性、边界条件和性能
"""

import unittest
import numpy as np
import pandas as pd
import time
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from factor.wfa_eval_utils import (
    WFAParams, WFAResult,
    run_wfa_validation,
    check_wfa_criteria,
    validate_wfa_inputs,
    wfa_validator,
    load_wfa_config_from_toml
)
from factor.factor_perf import FactorPerf


class TestWFADataStructures(unittest.TestCase):
    """测试WFA数据结构"""
    
    def test_wfa_params_valid(self):
        """测试有效的WFA参数"""
        params = WFAParams(
            training_window=750,
            testing_window=60,
            step_size=60,
            tanh_k=5.0,
            hold_n=1,
            gap=1
        )
        self.assertEqual(params.training_window, 750)
        self.assertEqual(params.testing_window, 60)
        self.assertEqual(params.tanh_k, 5.0)
    
    def test_wfa_params_invalid(self):
        """测试无效的WFA参数"""
        with self.assertRaises(ValueError):
            WFAParams(training_window=5)  # 训练窗口过小

        with self.assertRaises(ValueError):
            WFAParams(testing_window=1)  # 测试窗口过小

        with self.assertRaises(ValueError):
            WFAParams(tanh_k=-1)  # tanh_k为负数
    
    def test_wfa_result_valid(self):
        """测试有效的WFA结果"""
        pnl_series = pd.Series([0.01, -0.005, 0.02], name='pnl')
        position_series = pd.Series([0.5, -0.3, 0.8], name='position')
        
        result = WFAResult(
            factor_id='test_factor',
            symbol='TEST',
            pnl_series=pnl_series,
            position_series=position_series,
            metrics={'sharpe_ratio': 1.5},
            test_periods=[],
            pass_status='L3_PASSED',
            fail_reasons=[]
        )
        
        self.assertEqual(result.factor_id, 'test_factor')
        self.assertEqual(result.pass_status, 'L3_PASSED')
        self.assertEqual(len(result.pnl_series), 3)


class TestECDFMapping(unittest.TestCase):
    """测试ECDF映射函数"""
    
    def setUp(self):
        """设置测试数据"""
        np.random.seed(42)
        self.factor_values = pd.Series(np.random.randn(100), name='factor')
    
    def test_ecdf_mapping_basic(self):
        """测试基本ECDF映射功能"""
        mapping_func = wfa_validator.calculate_ecdf_mapping(self.factor_values)

        # 测试映射函数
        test_values = pd.Series([self.factor_values.min(), self.factor_values.median(), self.factor_values.max()])
        percentiles = mapping_func(test_values)

        # 验证百分位范围
        self.assertTrue(all(0 <= p <= 1 for p in percentiles))

        # 验证单调性：较大的值应该有较高的百分位
        self.assertLessEqual(percentiles.iloc[0], percentiles.iloc[1])
        self.assertLessEqual(percentiles.iloc[1], percentiles.iloc[2])

    def test_ecdf_mapping_edge_cases(self):
        """测试ECDF映射边界情况"""
        # 测试数据不足的情况
        small_data = pd.Series([1, 2, 3])
        with self.assertRaises(ValueError):
            wfa_validator.calculate_ecdf_mapping(small_data)

        # 测试空数据
        empty_data = pd.Series([], dtype=float)
        with self.assertRaises(ValueError):
            wfa_validator.calculate_ecdf_mapping(empty_data)

        # 测试包含NaN的数据
        nan_data = pd.Series([1, 2, np.nan, 4, 5] * 10)
        mapping_func = wfa_validator.calculate_ecdf_mapping(nan_data)
        test_values = pd.Series([1, 3, 5])
        percentiles = mapping_func(test_values)
        self.assertEqual(len(percentiles), 3)


class TestTanhPositionMapping(unittest.TestCase):
    """测试S型仓位映射函数"""
    
    def test_tanh_mapping_basic(self):
        """测试基本S型映射功能"""
        percentiles = pd.Series([0.1, 0.5, 0.9])
        positions = wfa_validator.apply_tanh_position_mapping(percentiles, tanh_k=5.0, direction=1)

        # 验证仓位范围
        self.assertTrue(all(-1 <= p <= 1 for p in positions))

        # 验证中位数映射为0附近
        self.assertAlmostEqual(positions.iloc[1], 0, places=3)

        # 验证单调性
        self.assertLess(positions.iloc[0], positions.iloc[1])
        self.assertLess(positions.iloc[1], positions.iloc[2])

    def test_tanh_mapping_direction(self):
        """测试方向调整"""
        percentiles = pd.Series([0.1, 0.9])

        # 正方向
        pos_positions = wfa_validator.apply_tanh_position_mapping(percentiles, direction=1)
        # 负方向
        neg_positions = wfa_validator.apply_tanh_position_mapping(percentiles, direction=-1)

        # 验证方向相反
        np.testing.assert_array_almost_equal(pos_positions.values, -neg_positions.values)

    def test_tanh_mapping_k_parameter(self):
        """测试tanh_k参数影响"""
        percentiles = pd.Series([0.1, 0.9])

        # 小k值（平缓）
        gentle_positions = wfa_validator.apply_tanh_position_mapping(percentiles, tanh_k=1.0)
        # 大k值（陡峭）
        steep_positions = wfa_validator.apply_tanh_position_mapping(percentiles, tanh_k=10.0)

        # 大k值应该产生更极端的仓位
        self.assertGreater(abs(steep_positions.iloc[1]), abs(gentle_positions.iloc[1]))


class TestSpearmanCorrelation(unittest.TestCase):
    """测试Spearman相关性计算"""
    
    def setUp(self):
        """设置测试数据"""
        np.random.seed(42)
        self.n = 100
        self.factor = pd.Series(np.random.randn(self.n), name='factor')
        self.returns = pd.Series(np.random.randn(self.n), name='returns')
    
    def test_spearman_correlation_basic(self):
        """测试基本相关性计算"""
        correlation = wfa_validator.calculate_spearman_correlation(self.factor, self.returns)

        # 验证相关性范围
        self.assertTrue(-1 <= correlation <= 1)

        # 测试完全正相关
        perfect_positive = pd.Series(range(50))
        perfect_returns = pd.Series(range(50))
        corr_pos = wfa_validator.calculate_spearman_correlation(perfect_positive, perfect_returns)
        self.assertAlmostEqual(corr_pos, 1.0, places=2)

        # 测试完全负相关
        perfect_negative = pd.Series(range(50))
        reverse_returns = pd.Series(range(49, -1, -1))
        corr_neg = wfa_validator.calculate_spearman_correlation(perfect_negative, reverse_returns)
        self.assertAlmostEqual(corr_neg, -1.0, places=2)

    def test_spearman_correlation_edge_cases(self):
        """测试相关性计算边界情况"""
        # 测试数据不足
        small_factor = pd.Series([1, 2])
        small_returns = pd.Series([1, 2])
        correlation = wfa_validator.calculate_spearman_correlation(small_factor, small_returns)
        self.assertEqual(correlation, 0.0)

        # 测试包含NaN
        nan_factor = pd.Series([1, 2, np.nan, 4, 5])
        nan_returns = pd.Series([1, np.nan, 3, 4, 5])
        correlation = wfa_validator.calculate_spearman_correlation(nan_factor, nan_returns)
        self.assertTrue(-1 <= correlation <= 1)


class TestMetricsCalculation(unittest.TestCase):
    """测试绩效指标计算"""
    
    def setUp(self):
        """设置测试数据"""
        np.random.seed(42)
        # 创建有趋势的PnL序列
        self.pnl_series = pd.Series(np.random.normal(0.001, 0.02, 252), name='pnl')
    
    def test_basic_metrics_calculation(self):
        """测试基础绩效指标计算"""
        metrics = FactorPerf.calc_single_asset_metrics(self.pnl_series)

        # 验证必要指标存在
        required_metrics = ['sharpe_ratio', 'max_drawdown', 'calmar_ratio',
                          'total_return', 'annual_return', 'volatility']
        for metric in required_metrics:
            self.assertIn(metric, metrics)
            self.assertTrue(np.isfinite(metrics[metric]))

        # 验证指标合理性
        self.assertGreaterEqual(metrics['max_drawdown'], 0)
        self.assertGreaterEqual(metrics['volatility'], 0)

    def test_wfa_metrics_with_reuse(self):
        """测试WFA绩效指标计算（复用版本）"""
        metrics = FactorPerf.calc_single_asset_metrics(self.pnl_series)

        # 验证基础指标
        self.assertIn('sharpe_ratio', metrics)
        self.assertIn('win_rate', metrics)
        self.assertIn('skewness', metrics)
        self.assertIn('kurtosis', metrics)

        # 验证胜率范围
        self.assertTrue(0 <= metrics['win_rate'] <= 1)


class TestWFACriteriaCheck(unittest.TestCase):
    """测试WFA通过标准检查"""
    
    def test_criteria_check_pass(self):
        """测试通过标准检查"""
        good_metrics = {
            'sharpe_ratio': 1.0,
            'max_drawdown': 0.15,
            'win_rate': 0.60,
            'calmar_ratio': 1.5
        }
        
        criteria = {
            'min_sharpe': 0.5,
            'max_mdd': 0.30,
            'min_win_rate': 0.55,
            'min_calmar': 0.8
        }
        
        status, reasons = check_wfa_criteria(good_metrics, criteria)
        self.assertEqual(status, 'L3_PASSED')
        self.assertEqual(len(reasons), 0)
    
    def test_criteria_check_fail(self):
        """测试未通过标准检查"""
        bad_metrics = {
            'sharpe_ratio': 0.3,  # 过低
            'max_drawdown': 0.40,  # 过高
            'win_rate': 0.45,  # 过低
        }
        
        criteria = {
            'min_sharpe': 0.5,
            'max_mdd': 0.30,
            'min_win_rate': 0.55
        }
        
        status, reasons = check_wfa_criteria(bad_metrics, criteria)
        self.assertEqual(status, 'L3_FAILED')
        self.assertEqual(len(reasons), 3)  # 三个指标都不达标


class TestWFAValidation(unittest.TestCase):
    """测试完整WFA验证流程"""
    
    def setUp(self):
        """设置测试数据"""
        np.random.seed(42)
        
        # 创建模拟的因子和价格数据
        n_days = 1000
        dates = pd.date_range('2020-01-01', periods=n_days, freq='D')
        
        # 创建有一定预测能力的因子
        noise = np.random.randn(n_days)
        trend = np.cumsum(np.random.randn(n_days) * 0.01)
        self.factor_data = pd.Series(noise + trend * 0.1, index=dates, name='test_factor')
        
        # 创建价格数据（与因子有一定相关性）
        price_changes = np.random.randn(n_days) * 0.02 + self.factor_data.shift(1) * 0.001
        self.price_data = pd.Series(100 * np.exp(np.cumsum(price_changes)), index=dates, name='price')
        
        self.wfa_params = WFAParams(
            training_window=200,
            testing_window=50,
            step_size=50,
            tanh_k=3.0
        )
    
    def test_wfa_validation_complete(self):
        """测试完整WFA验证流程"""
        result = run_wfa_validation(self.factor_data, self.price_data, self.wfa_params)
        
        # 验证结果结构
        self.assertIsInstance(result, WFAResult)
        self.assertGreater(len(result.pnl_series), 0)
        self.assertGreater(len(result.test_periods), 0)
        self.assertIn('sharpe_ratio', result.metrics)
        
        # 验证测试期信息
        for period in result.test_periods:
            self.assertIn('window_id', period)
            self.assertIn('direction', period)
            self.assertIn('correlation', period)
    
    def test_wfa_validation_performance(self):
        """测试WFA验证性能"""
        start_time = time.time()
        result = run_wfa_validation(self.factor_data, self.price_data, self.wfa_params)
        elapsed_time = time.time() - start_time
        
        # 验证性能要求：单因子处理应在10秒内完成
        self.assertLess(elapsed_time, 10.0, f"WFA验证耗时过长: {elapsed_time:.2f}秒")
        
        print(f"WFA验证性能测试通过: {elapsed_time:.2f}秒")


class TestInputValidation(unittest.TestCase):
    """测试输入验证"""
    
    def test_validate_inputs_valid(self):
        """测试有效输入验证"""
        factor_data = pd.Series(np.random.randn(1000))
        price_data = pd.Series(np.random.randn(1000))
        wfa_params = WFAParams()
        
        # 应该不抛出异常
        validate_wfa_inputs(factor_data, price_data, wfa_params)
    
    def test_validate_inputs_invalid(self):
        """测试无效输入验证"""
        wfa_params = WFAParams()
        
        # 测试空数据
        with self.assertRaises(ValueError):
            validate_wfa_inputs(pd.Series([]), pd.Series([1, 2, 3]), wfa_params)
        
        # 测试数据长度不足
        short_data = pd.Series(np.random.randn(100))
        with self.assertRaises(ValueError):
            validate_wfa_inputs(short_data, short_data, wfa_params)
        
        # 测试数据缺失过多
        mostly_nan = pd.Series([np.nan] * 800 + [1] * 200)
        with self.assertRaises(ValueError):
            validate_wfa_inputs(mostly_nan, mostly_nan, wfa_params)


class TestMultiFrequencySupport(unittest.TestCase):
    """测试多周期数据支持"""

    def setUp(self):
        """设置测试数据"""
        np.random.seed(42)

        # 创建不同周期的测试数据
        self.daily_data = self._create_test_data(252, "D")  # 日线数据
        self.weekly_data = self._create_test_data(52, "W")  # 周线数据
        self.monthly_data = self._create_test_data(12, "M") # 月线数据

    def _create_test_data(self, periods, freq):
        """创建指定周期的测试数据"""
        dates = pd.date_range('2020-01-01', periods=periods, freq=freq)
        factor_data = pd.Series(np.random.randn(periods), index=dates, name='test_factor')
        price_data = pd.Series(100 * np.cumprod(1 + np.random.randn(periods) * 0.02),
                              index=dates, name='price')
        return factor_data, price_data

    def test_annualization_factor_calculation(self):
        """测试年化因子计算"""
        # 测试不同周期的年化因子
        self.assertEqual(WFAParams.get_annualization_factor("D"), 252)
        self.assertEqual(WFAParams.get_annualization_factor("W"), 52)
        self.assertEqual(WFAParams.get_annualization_factor("M"), 12)
        self.assertEqual(WFAParams.get_annualization_factor("Q"), 4)
        self.assertEqual(WFAParams.get_annualization_factor("Y"), 1)

        # 测试大小写不敏感
        self.assertEqual(WFAParams.get_annualization_factor("d"), 252)
        self.assertEqual(WFAParams.get_annualization_factor("w"), 52)

        # 测试未知周期返回默认值
        self.assertEqual(WFAParams.get_annualization_factor("X"), 252)

    def test_frequency_specific_params_creation(self):
        """测试根据周期创建参数"""
        # 测试日线参数
        daily_params = WFAParams.create_for_frequency("D")
        self.assertEqual(daily_params.training_window, 750)
        self.assertEqual(daily_params.testing_window, 60)

        # 测试周线参数
        weekly_params = WFAParams.create_for_frequency("W")
        self.assertEqual(weekly_params.training_window, 150)
        self.assertEqual(weekly_params.testing_window, 12)

        # 测试月线参数
        monthly_params = WFAParams.create_for_frequency("M")
        self.assertEqual(monthly_params.training_window, 36)
        self.assertEqual(monthly_params.testing_window, 6)

        # 测试自定义覆盖
        custom_params = WFAParams.create_for_frequency("D", {"training_window": 500})
        self.assertEqual(custom_params.training_window, 500)
        self.assertEqual(custom_params.testing_window, 60)  # 保持默认值

    def test_performance_metrics_with_different_frequencies(self):
        """测试不同周期下的绩效指标计算"""
        # 创建相同的PnL序列
        pnl_series = pd.Series([0.01, -0.005, 0.02, -0.01, 0.015] * 10)

        # 测试不同年化因子下的指标计算
        daily_metrics = FactorPerf.calc_single_asset_metrics(pnl_series, 252)
        weekly_metrics = FactorPerf.calc_single_asset_metrics(pnl_series, 52)
        monthly_metrics = FactorPerf.calc_single_asset_metrics(pnl_series, 12)

        # 验证年化收益率按比例缩放
        self.assertAlmostEqual(
            daily_metrics['annual_return'] / weekly_metrics['annual_return'],
            252 / 52, places=2
        )
        self.assertAlmostEqual(
            weekly_metrics['annual_return'] / monthly_metrics['annual_return'],
            52 / 12, places=2
        )

        # 验证波动率按平方根比例缩放
        self.assertAlmostEqual(
            daily_metrics['volatility'] / weekly_metrics['volatility'],
            np.sqrt(252 / 52), places=2
        )


class TestPerformanceCalculatorCorrectness(unittest.TestCase):
    """测试绩效计算器的算法正确性"""

    def setUp(self):
        """设置测试数据"""
        # 创建已知结果的测试数据
        self.simple_returns = pd.Series([0.01, -0.005, 0.02, -0.01, 0.015])  # 简单收益序列
        self.zero_returns = pd.Series([0.0] * 10)  # 零收益序列
        self.constant_positive = pd.Series([0.01] * 10)  # 恒定正收益
        self.extreme_drawdown = pd.Series([0.1, -0.5, 0.1, 0.1, 0.1])  # 包含大回撤

    def test_max_drawdown_calculation_correctness(self):
        """测试最大回撤计算的正确性"""
        # 测试已知回撤的序列
        returns = pd.Series([0.1, -0.2, -0.1, 0.05, 0.15])  # 累积: 1.1, 0.88, 0.792, 0.8316, 0.9563
        metrics = FactorPerf.calc_single_asset_metrics(returns)

        # 手动计算验证
        cumulative = (1 + returns).cumprod()  # [1.1, 0.88, 0.792, 0.8316, 0.9563]
        rolling_max = cumulative.expanding().max()  # [1.1, 1.1, 1.1, 1.1, 1.1]
        expected_drawdowns = (cumulative / rolling_max) - 1  # [0, -0.2, -0.28, -0.244, -0.131]
        expected_max_dd = abs(expected_drawdowns.min())  # 0.28

        self.assertAlmostEqual(metrics['max_drawdown'], expected_max_dd, places=4)
        self.assertGreater(metrics['max_drawdown'], 0.27)
        self.assertLess(metrics['max_drawdown'], 0.29)

    def test_sharpe_ratio_with_risk_free_rate(self):
        """测试夏普比率计算（包含无风险利率）"""
        returns = pd.Series([0.02, 0.01, 0.03, -0.01, 0.02])

        # 测试无风险利率为0的情况
        metrics_rf0 = FactorPerf.calc_single_asset_metrics(returns, risk_free_rate=0.0)

        # 测试无风险利率为2%的情况
        metrics_rf2 = FactorPerf.calc_single_asset_metrics(returns, risk_free_rate=0.02)

        # 有风险利率的夏普比率应该更低
        self.assertLess(metrics_rf2['sharpe_ratio'], metrics_rf0['sharpe_ratio'])

        # 手动验证计算
        annual_return = returns.mean() * 252
        volatility = returns.std() * np.sqrt(252)
        expected_sharpe_rf0 = annual_return / volatility
        expected_sharpe_rf2 = (annual_return - 0.02) / volatility

        self.assertAlmostEqual(metrics_rf0['sharpe_ratio'], expected_sharpe_rf0, places=4)
        self.assertAlmostEqual(metrics_rf2['sharpe_ratio'], expected_sharpe_rf2, places=4)

    def test_edge_cases_handling(self):
        """测试边界条件处理"""
        # 测试零波动率情况
        zero_vol_metrics = FactorPerf.calc_single_asset_metrics(self.zero_returns)
        self.assertEqual(zero_vol_metrics['sharpe_ratio'], 0.0)
        self.assertEqual(zero_vol_metrics['volatility'], 0.0)

        # 测试单一数据点
        single_point = pd.Series([0.01])
        single_metrics = FactorPerf.calc_single_asset_metrics(single_point)
        self.assertEqual(single_metrics, {})  # 应该返回空字典

        # 测试包含NaN的数据
        nan_data = pd.Series([0.01, np.nan, 0.02, np.nan, 0.03])
        nan_metrics = FactorPerf.calc_single_asset_metrics(nan_data)
        self.assertIn('sharpe_ratio', nan_metrics)  # 应该能正常计算
        self.assertTrue(np.isfinite(nan_metrics['sharpe_ratio']))

    def test_enhanced_metrics_multi_frequency(self):
        """测试增强指标的多周期支持"""
        # 创建净值序列
        nav_series = pd.Series([100, 102, 101, 105, 103, 108],
                              index=pd.date_range('2020-01-01', periods=6, freq='D'))

        # 测试日线年化因子
        df_ratios_daily, df_yearly_daily = perf_calculator.calculate_enhanced_metrics(nav_series, 252)

        # 测试周线年化因子
        df_ratios_weekly, df_yearly_weekly = perf_calculator.calculate_enhanced_metrics(nav_series, 52)

        # 年化收益应该按比例缩放
        daily_annual = df_ratios_daily.loc['年化收益', 'nav']
        weekly_annual = df_ratios_weekly.loc['年化收益', 'nav']

        self.assertAlmostEqual(daily_annual / weekly_annual, 252 / 52, places=1)

    def test_input_validation_robustness(self):
        """测试输入验证的健壮性"""
        # 测试None输入
        none_metrics = FactorPerf.calc_single_asset_metrics(None)
        self.assertEqual(none_metrics, {})

        # 测试空序列
        empty_series = pd.Series([], dtype=float)
        empty_metrics = FactorPerf.calc_single_asset_metrics(empty_series)
        self.assertEqual(empty_metrics, {})

        # 测试全NaN序列
        all_nan = pd.Series([np.nan] * 5)
        nan_metrics = FactorPerf.calc_single_asset_metrics(all_nan)
        self.assertEqual(nan_metrics, {})

        # 测试包含无穷值的序列
        inf_series = pd.Series([0.01, np.inf, 0.02, -np.inf, 0.03])
        inf_metrics = FactorPerf.calc_single_asset_metrics(inf_series)
        self.assertEqual(inf_metrics, {})  # 应该被验证拒绝


if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)
