2025-06-21 10:45 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-21 10:46 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-21 10:46 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-21 10:46 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-21 10:46 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-21 10:46 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-21 10:46 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-21 10:46 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-21 10:46 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-21 10:46 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-21 10:46 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-21 10:46 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-21 10:46 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-21 10:46 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['LOW1', 'QTLD5', 'OPEN0', 'BETA60', 'JZ012_21', 'OPEN2', 'b_atr_14', 'JZ014_troc_mul3_55', 'STD20', 'STD5']...
2025-06-21 10:46 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-21 10:46 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-21 10:46 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-21 10:46 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-21 10:46 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-21 10:46 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-21 10:46 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-21 10:46 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-21 10:46 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 10:46 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-21 10:46 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-21 10:46 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-21 10:46 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(67个)，跳过相关性过滤
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 67), y形状: (1043,)
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 56
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-21 10:46 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=8, 饱和法=18, 平坦法=8 -> 选择=8
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2577
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 56
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-21 10:46 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=15, 饱和法=14, 平坦法=7 -> 选择=14
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=14, 最大R²=0.2556
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 56
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-21 10:46 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=11, 饱和法=50, 平坦法=9 -> 选择=11
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=11, 最大R²=0.2537
2025-06-21 10:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-21 10:46 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-21 10:46 | WARNING  | FeatSelection.select_features: 缺失特征: {'ftool_PERCENTILE(WV_MA_20)', 'ftool_PERCENTILE(JZ010_9)', 'ftool_ABSOLUTE(JZ001_10_20)', 'ftool_LAG(JZ005_rs34, datetime)', 'ftool_ABSOLUTE(roc_10)', 'ftool_ABSOLUTE(b_cci_14)', 'ftool_PERCENTILE(JZ005_rs144)', 'ftool_PERCENTILE(label_1)'}
2025-06-21 10:46 | INFO     | FeatSelection.select_features: 选择特征: 1个 + base2keep: 4个 + 标签: 1个
2025-06-21 10:46 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-21 10:46 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_gain.png
2025-06-21 10:46 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_weight.png
2025-06-21 10:46 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_cover.png
2025-06-21 10:46 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-21 10:46 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-21 10:50 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-21 10:50 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-21 10:50 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-21 10:50 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-21 10:50 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-21 10:50 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-21 10:50 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-21 10:50 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-21 10:50 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-21 10:50 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-21 10:50 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-21 10:50 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-21 10:50 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-21 10:50 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ014_troc_mul2_55', 'MA10', 'QTLU5', 'tr_ma10', 'JZ014_trends_mul3_55', 'BETA20', 'JZ004_98', 'STD5', 'b_atr_60', 'LOW4']...
2025-06-21 10:50 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-21 10:50 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-21 10:50 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-21 10:50 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-21 10:50 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-21 10:50 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-21 10:50 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-21 10:50 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-21 10:50 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 10:50 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-21 10:50 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 294 -> 261
2025-06-21 10:50 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 261 个特征 (窗口=20)
2025-06-21 10:50 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-21 10:50 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 331 个特征中保留了 201 个特征 (base2keep: 4, 冗余筛选: 197, 相关性阈值=0.85, 聚类数=198)
2025-06-21 10:50 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征201
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余198个
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 198), y形状: (1043,)
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 65
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-21 10:50 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=4, 饱和法=9, 平坦法=23 -> 选择=9
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=9, 最大R²=0.9006
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 65
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-21 10:50 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=4, 饱和法=9, 平坦法=23 -> 选择=9
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=9, 最大R²=0.9028
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 65
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-21 10:50 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=5, 饱和法=13, 平坦法=13 -> 选择=13
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=13, 最大R²=0.9021
2025-06-21 10:50 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-21 10:50 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-21 10:56 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-21 10:56 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-21 10:56 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-21 10:56 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-21 10:56 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-21 10:56 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-21 10:56 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-21 10:56 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-21 10:56 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-21 10:56 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-21 10:56 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-21 10:56 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-21 10:56 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-21 10:56 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['HIGH4', 'JZ014_troc_mul3_9', 'b_atr_14', 'LOW3', 'JZ014_trends_mul3_55', 'JZ011_21', 'QTLD10', 'smadiff10', 'BETA20', 'tr_ma10']...
2025-06-21 10:56 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-21 10:56 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-21 10:56 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-21 10:56 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-21 10:56 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-21 10:56 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-21 10:56 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-21 10:56 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-21 10:56 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 10:56 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-21 10:57 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 294 -> 261
2025-06-21 10:57 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 261 个特征 (窗口=20)
2025-06-21 10:57 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-21 10:57 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 331 个特征中保留了 201 个特征 (base2keep: 4, 冗余筛选: 197, 相关性阈值=0.85, 聚类数=198)
2025-06-21 10:57 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征201
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余198个
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 198), y形状: (1043,)
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 65
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-21 10:57 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=4, 饱和法=9, 平坦法=23 -> 选择=9
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=9, 最大R²=0.9006
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 65
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-21 10:57 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=4, 饱和法=9, 平坦法=23 -> 选择=9
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=9, 最大R²=0.9007
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 65
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-21 10:57 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=5, 饱和法=13, 平坦法=13 -> 选择=13
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=13, 最大R²=0.9021
2025-06-21 10:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-21 10:57 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-21 11:00 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-21 11:00 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-21 11:00 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-21 11:00 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-21 11:00 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-21 11:00 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-21 11:00 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-21 11:00 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-21 11:00 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-21 11:00 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-21 11:00 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-21 11:00 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-21 11:00 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-21 11:00 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['MAX5', 'BETA30', 'JZ008_9', 'JZ012_89', 'JZ011_34', 'JZ014_troc_mul3_55', 'MIN5', 'JZ014_troc_mul2_9', 'STD20', 'V_2']...
2025-06-21 11:00 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-21 11:00 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-21 11:00 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-21 11:00 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-21 11:00 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-21 11:00 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-21 11:00 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-21 11:00 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-21 11:00 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 11:00 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-21 11:00 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 294 -> 261
2025-06-21 11:00 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 261 个特征 (窗口=20)
2025-06-21 11:00 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-21 11:00 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 331 个特征中保留了 201 个特征 (base2keep: 4, 冗余筛选: 197, 相关性阈值=0.85, 聚类数=198)
2025-06-21 11:00 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征201
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余198个
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 198), y形状: (1043,)
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 65
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-21 11:00 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=4, 饱和法=9, 平坦法=23 -> 选择=9
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=9, 最大R²=0.9006
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 65
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-21 11:00 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=4, 饱和法=9, 平坦法=50 -> 选择=9
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=9, 最大R²=0.9013
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 65
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-21 11:00 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=5, 饱和法=13, 平坦法=13 -> 选择=13
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=13, 最大R²=0.9021
2025-06-21 11:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-21 11:00 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-21 11:06 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-21 11:09 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-21 11:09 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-21 11:09 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-21 11:09 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-21 11:09 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-21 11:09 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-21 11:09 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-21 11:09 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-21 11:09 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-21 11:09 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-21 11:09 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-21 11:09 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-21 11:09 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-21 11:09 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['KSFT', 'HIGH0', 'R_4', 'QTLU10', 'JZ011_14', 'V_4', 'R_3', 'HIGH1', 'KUP', 'LOW0']...
2025-06-21 11:09 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-21 11:09 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-21 11:09 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-21 11:09 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-21 11:09 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-21 11:09 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-21 11:09 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-21 11:09 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-21 11:09 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 11:09 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-21 11:09 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 294 -> 261
2025-06-21 11:09 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 261 个特征 (窗口=20)
2025-06-21 11:09 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-21 11:09 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 331 个特征中保留了 201 个特征 (base2keep: 4, 冗余筛选: 197, 相关性阈值=0.85, 聚类数=198)
2025-06-21 11:09 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征201
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余198个
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 198), y形状: (1043,)
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 65
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-21 11:09 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=4, 饱和法=9, 平坦法=23 -> 选择=9
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=9, 最大R²=0.9006
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 65
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-21 11:09 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=4, 饱和法=9, 平坦法=50 -> 选择=9
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=9, 最大R²=0.8995
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 65
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-21 11:09 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=5, 饱和法=11, 平坦法=13 -> 选择=11
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=11, 最大R²=0.8991
2025-06-21 11:09 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-21 11:09 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-21 11:11 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-21 11:11 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-21 11:11 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-21 11:11 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-21 11:11 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-21 11:11 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-21 11:11 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-21 11:11 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-21 11:11 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-21 11:11 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-21 11:11 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-21 11:11 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-21 11:11 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-21 11:11 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['STD5', 'BETA30', 'roc_05', 'roc_02', 'JZ014_trends_mul3_55', 'CLOSE3', 'OPEN3', 'R_1', 'JZ008_89', 'HIGH4']...
2025-06-21 11:11 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-21 11:11 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-21 11:11 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-21 11:11 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-21 11:11 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-21 11:11 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-21 11:11 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-21 11:11 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-21 11:11 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征70
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(70个)，跳过相关性过滤
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 70), y形状: (1043,)
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 58
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-21 11:11 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=12, 饱和法=11, 平坦法=6 -> 选择=11
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=11, 最大R²=0.2606
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 58
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-21 11:11 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=15, 饱和法=14, 平坦法=11 -> 选择=14
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=14, 最大R²=0.2494
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 58
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-21 11:11 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=7, 饱和法=6, 平坦法=6 -> 选择=6
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2528
2025-06-21 11:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-21 11:11 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-21 11:12 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-21 11:12 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-21 11:12 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-21 11:12 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-21 11:12 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-21 11:12 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-21 11:12 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-21 11:12 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-21 11:12 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-21 11:12 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-21 11:12 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-21 11:12 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-21 11:12 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-21 11:12 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ014_troc_mul2_9', 'MA10', 'JZ008_89', 'tr_ma10', 'JZ014_troc_mul3_21', 'OPEN0', 'CLOSE4', 'JZ004_58', 'JZ011_9', 'JZ014_troc_mul2_34']...
2025-06-21 11:12 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-21 11:12 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-21 11:12 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-21 11:12 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-21 11:12 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-21 11:12 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-21 11:12 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-21 11:12 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-21 11:12 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 11:12 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-21 11:13 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 294 -> 261
2025-06-21 11:13 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 261 个特征 (窗口=20)
2025-06-21 11:13 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-21 11:13 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 331 个特征中保留了 201 个特征 (base2keep: 4, 冗余筛选: 197, 相关性阈值=0.85, 聚类数=198)
2025-06-21 11:13 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征201
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余198个
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 198), y形状: (1043,)
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 65
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-21 11:13 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=4, 饱和法=9, 平坦法=23 -> 选择=9
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=9, 最大R²=0.9006
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 65
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-21 11:13 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=4, 饱和法=9, 平坦法=23 -> 选择=9
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=9, 最大R²=0.9006
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 65
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-21 11:13 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=5, 饱和法=13, 平坦法=13 -> 选择=13
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=13, 最大R²=0.9021
2025-06-21 11:13 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-21 11:13 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-21 11:13 | INFO     | FeatSelection.select_features: 选择特征: 9个 + base2keep: 4个 + 标签: 1个
2025-06-21 11:13 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-21 11:13 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_gain.png
2025-06-21 11:13 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_weight.png
2025-06-21 11:13 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_cover.png
2025-06-21 11:13 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-21 11:13 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-21 11:15 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-21 11:15 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-21 11:15 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-21 11:15 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-21 11:15 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-21 11:15 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-21 11:15 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-21 11:15 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-21 11:15 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-21 11:15 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-21 11:15 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-21 11:15 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-21 11:15 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-21 11:15 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['MAX10', 'JZ004_6', 'KMID', 'HIGH3', 'MIN5', 'OPEN0', 'JZ004_10', 'VSUMP_60', 'JZ014_troc_mul2_34', 'JZ012_60']...
2025-06-21 11:15 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-21 11:15 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-21 11:15 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-21 11:15 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-21 11:15 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-21 11:15 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-21 11:15 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-21 11:15 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-21 11:15 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征70
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(70个)，跳过相关性过滤
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 70), y形状: (1043,)
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 58
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-21 11:15 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=12, 饱和法=11, 平坦法=6 -> 选择=11
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=11, 最大R²=0.2606
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 58
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-21 11:15 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=15, 饱和法=17, 平坦法=11 -> 选择=15
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=15, 最大R²=0.2512
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 58
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-21 11:15 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=7, 饱和法=6, 平坦法=6 -> 选择=6
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2528
2025-06-21 11:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-21 11:15 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-21 11:18 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-21 11:18 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-21 11:18 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-21 11:18 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-21 11:18 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-21 11:18 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-21 11:18 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-21 11:18 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-21 11:18 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-21 11:18 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-21 11:18 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-21 11:18 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-21 11:18 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-21 11:18 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ011_55', 'JZ008_18', 'STD20', 'JZ014_trends_mul2_55', 'OPEN2', 'JZ014_troc_mul3_21', 'R_1', 'BETA20', 'HIGH3', 'OPEN3']...
2025-06-21 11:18 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-21 11:18 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-21 11:18 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-21 11:18 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-21 11:18 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-21 11:18 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-21 11:18 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-21 11:18 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-21 11:18 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征70
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(70个)，跳过相关性过滤
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 70), y形状: (1043,)
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 58
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-21 11:18 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=12, 饱和法=11, 平坦法=6 -> 选择=11
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=11, 最大R²=0.2606
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 58
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-21 11:18 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=10, 饱和法=24, 平坦法=7 -> 选择=10
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=10, 最大R²=0.2550
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 58
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-21 11:18 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=7, 饱和法=6, 平坦法=6 -> 选择=6
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2528
2025-06-21 11:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-21 11:18 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-21 11:18 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-21 11:19 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 218 -> 188
2025-06-21 11:19 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 188 个特征 (窗口=20)
2025-06-21 11:19 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 258 个特征中保留了 170 个特征 (base2keep: 4, 冗余筛选: 166, 相关性阈值=0.85, 聚类数=167)
2025-06-21 11:19 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征170
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余167个
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 167), y形状: (1043,)
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 95
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-21 11:19 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=20, 饱和法=19, 平坦法=6 -> 选择=19
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=19, 最大R²=0.2603
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 95
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-21 11:19 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=12, 饱和法=28, 平坦法=9 -> 选择=12
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=12, 最大R²=0.2617
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 95
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-21 11:19 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=15, 饱和法=14, 平坦法=5 -> 选择=14
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=14, 最大R²=0.2549
2025-06-21 11:19 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-21 11:19 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-21 11:25 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-21 11:25 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-21 11:25 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-21 11:25 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-21 11:25 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-21 11:25 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-21 11:25 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-21 11:25 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-21 11:25 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-21 11:25 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-21 11:25 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-21 11:25 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-21 11:25 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-21 11:25 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['OPEN4', 'JZ004_120', 'JZ014_troc_mul2_55', 'JZ014_troc_mul3_34', 'b_atr_25', 'JZ014_trends_mul2_34', 'OPEN3', 'KLOW', 'HIGH3', 'STD60']...
2025-06-21 11:25 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-21 11:25 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-21 11:25 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-21 11:25 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-21 11:25 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-21 11:25 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-21 11:25 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-21 11:25 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-21 11:25 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 11:25 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-21 11:25 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 218 -> 188
2025-06-21 11:25 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 188 个特征 (窗口=20)
2025-06-21 11:25 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 258 个特征中保留了 170 个特征 (base2keep: 4, 冗余筛选: 166, 相关性阈值=0.85, 聚类数=167)
2025-06-21 11:25 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征170
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余167个
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 167), y形状: (1043,)
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 95
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-21 11:25 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=20, 饱和法=19, 平坦法=6 -> 选择=19
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=19, 最大R²=0.2603
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 95
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-21 11:25 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=12, 饱和法=24, 平坦法=9 -> 选择=12
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=12, 最大R²=0.2595
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 95
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-21 11:25 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=15, 饱和法=14, 平坦法=5 -> 选择=14
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=14, 最大R²=0.2549
2025-06-21 11:25 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-21 11:25 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-21 11:28 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-21 11:28 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-21 11:28 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-21 11:28 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-21 11:28 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-21 11:28 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-21 11:28 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-21 11:28 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-21 11:28 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-21 11:28 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-21 11:28 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-21 11:28 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-21 11:28 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-21 11:28 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ014_troc_mul2_34', 'roc_05', 'R_2', 'MAX5', 'V_4', 'R_5', 'STD5', 'QTLU5', 'JZ014_troc_mul3_14', 'JZ011_21']...
2025-06-21 11:28 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-21 11:28 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-21 11:28 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-21 11:28 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-21 11:28 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-21 11:28 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-21 11:28 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-21 11:28 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-21 11:28 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 11:28 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-21 11:28 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 218 -> 188
2025-06-21 11:28 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 188 个特征 (窗口=20)
2025-06-21 11:28 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 258 个特征中保留了 170 个特征 (base2keep: 4, 冗余筛选: 166, 相关性阈值=0.85, 聚类数=167)
2025-06-21 11:28 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征170
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余167个
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 167), y形状: (1043,)
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 95
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-21 11:28 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=20, 饱和法=19, 平坦法=6 -> 选择=19
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=19, 最大R²=0.2603
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 95
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-21 11:28 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=9, 饱和法=10, 平坦法=9 -> 选择=9
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=9, 最大R²=0.2607
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 95
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-21 11:28 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=15, 饱和法=14, 平坦法=5 -> 选择=14
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=14, 最大R²=0.2549
2025-06-21 11:28 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-21 11:28 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-21 11:29 | WARNING  | FeatSelection.select_features: 缺失特征: {'ftool_LAG(JZ005_rs34, datetime)', 'ftool_PERCENTILE(JZ005_rs144)', 'ftool_PERCENTILE(label_1)', 'ftool_PERCENTILE(WV_MA_20)', 'ftool_PERCENTILE(JZ010_9)'}
2025-06-21 11:29 | INFO     | FeatSelection.select_features: 选择特征: 4个 + base2keep: 4个 + 标签: 1个
2025-06-21 11:29 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-21 11:29 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_gain.png
2025-06-21 11:29 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_weight.png
2025-06-21 11:29 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_cover.png
2025-06-21 11:29 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-21 11:29 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
