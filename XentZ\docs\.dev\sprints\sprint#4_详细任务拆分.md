# Sprint #4 详细任务拆分 - 时序单因子第0步WFA验证

## 任务拆分原则
- 每个任务代表约20分钟的专业开发工作量
- 任务间依赖关系清晰，支持并行开发
- 每个任务都有明确的验收标准
- 遵循项目现有的技术栈和编程风格

---

## Phase 1: 核心算法实现 (3-4天)

### Task 1.1.1: WFA核心数据结构设计
**文件**: `factor/validation_utils.py`
**工期**: 30分钟
**依赖**: 无

**实现内容**:
```python
# WFA结果数据结构定义
@dataclass
class WFAResult:
    factor_id: str
    symbol: str
    pnl_series: pd.Series
    position_series: pd.Series
    metrics: Dict[str, float]
    test_periods: List[Dict]
    pass_status: str
    fail_reasons: List[str]

# WFA参数结构
@dataclass  
class WFAParams:
    training_window: int = 750
    testing_window: int = 60
    step_size: int = 60
    tanh_k: float = 5.0
    hold_n: int = 1
    gap: int = 1
    min_periods: int = 50
```

**验收标准**:
- [ ] 数据结构定义完整
- [ ] 类型注解正确
- [ ] 支持序列化/反序列化

### Task 1.1.2: 经验分布函数(ECDF)学习实现
**文件**: `factor/validation_utils.py`
**工期**: 45分钟
**依赖**: Task 1.1.1

**实现内容**:
```python
def calculate_ecdf_mapping(factor_values: pd.Series) -> callable:
    """
    学习因子值的经验累积分布函数
    
    Args:
        factor_values: 训练期因子值序列
        
    Returns:
        mapping_func: 将因子值映射为百分位排名的函数
    """
    # 实现ECDF学习逻辑
    # 返回可调用的映射函数
    pass

def apply_ecdf_mapping(factor_values: pd.Series, 
                      mapping_func: callable) -> pd.Series:
    """应用ECDF映射，将因子值转换为百分位排名"""
    pass
```

**技术要求**:
- 使用scipy.stats.rankdata进行高效排名计算
- 处理边界情况和异常值
- 支持向量化操作

**验收标准**:
- [ ] ECDF学习算法正确
- [ ] 百分位映射准确
- [ ] 边界情况处理完善

### Task 1.1.3: S型仓位映射实现
**文件**: `factor/validation_utils.py`
**工期**: 30分钟
**依赖**: Task 1.1.2

**实现内容**:
```python
def apply_tanh_position_mapping(percentiles: pd.Series, 
                               tanh_k: float = 5.0,
                               direction: int = 1) -> pd.Series:
    """
    使用tanh函数进行S型仓位映射
    
    Args:
        percentiles: 百分位排名序列 [0, 1]
        tanh_k: S型曲线陡峭度参数
        direction: 因子方向 (1 或 -1)
        
    Returns:
        positions: 最终仓位序列 [-1, 1]
    """
    # 核心公式: tanh(k * (percentile - 0.5)) * direction
    base_positions = np.tanh(tanh_k * (percentiles - 0.5))
    final_positions = base_positions * direction
    return pd.Series(final_positions, index=percentiles.index)
```

**验收标准**:
- [ ] S型映射公式正确
- [ ] 仓位范围控制在[-1, 1]
- [ ] 方向调整正确

### Task 1.1.4: 相关性计算优化
**文件**: `factor/validation_utils.py`
**工期**: 30分钟
**依赖**: 无

**实现内容**:
```python
def calculate_spearman_correlation_optimized(factor_values: pd.Series,
                                           returns: pd.Series) -> float:
    """
    优化的Spearman相关性计算
    复用factor_utils中的numba加速函数
    """
    from factor.factor_utils import fast_spearman, rank_1d
    
    # 数据对齐和清洗
    aligned_data = pd.concat([factor_values, returns], axis=1).dropna()
    if len(aligned_data) < 10:
        return 0.0
    
    # 使用numba加速的排名计算
    factor_ranks = rank_1d(aligned_data.iloc[:, 0].values.astype(np.float32))
    return_ranks = rank_1d(aligned_data.iloc[:, 1].values.astype(np.float32))
    
    correlation = np.corrcoef(factor_ranks, return_ranks)[0, 1]
    return correlation if not np.isnan(correlation) else 0.0
```

**验收标准**:
- [ ] 复用现有numba加速函数
- [ ] 相关性计算准确
- [ ] 异常情况处理完善

### Task 1.1.5: WFA主流程实现
**文件**: `factor/validation_utils.py`
**工期**: 90分钟
**依赖**: Task 1.1.1-1.1.4

**实现内容**:
```python
def run_wfa_validation(factor_data: pd.Series,
                      price_data: pd.Series,
                      wfa_params: WFAParams) -> WFAResult:
    """
    WFA验证主流程
    
    实现滚动窗口验证逻辑:
    1. 窗口切分
    2. 训练期ECDF学习和方向判定
    3. 测试期仓位映射和PnL计算
    4. 滚动前进
    5. 结果拼接
    """
    # 详细实现WFA算法流程
    pass
```

**技术要求**:
- 严格按照文档算法流程实现
- 使用向量化操作优化性能
- 完善的异常处理和边界检查
- 详细的中间结果记录

**验收标准**:
- [ ] WFA算法流程正确
- [ ] PnL序列计算准确
- [ ] 性能满足要求(<10秒/因子)

---

## Phase 2: 配置管理与业务集成 (2-3天)

### Task 2.1.1: 配置文件结构设计
**文件**: `config/tasks/ts_l3_wfa.toml`
**工期**: 30分钟
**依赖**: 无

**实现内容**:
```toml
# L3阶段WFA动态稳健性检验任务配置
dynaconf_include = ["_datasets/ts_single_etf.toml"]

[task]
name = "L3_WFA_Dynamic_Validation"
desc = "L3阶段因子的Walk-Forward Analysis动态稳健性检验"

[wfa]
training_window = 750
testing_window = 60
step_size = 60
tanh_k = 5
hold_n = 1
gap = 1
min_periods = 50
correlation_method = "spearman"

[criteria]
min_sharpe = 0.5
max_mdd = 0.30
min_win_rate = 0.55
min_calmar = 0.8
max_volatility = 0.25

[factor_query]
source_pipeline_step = "L2"
batch_limit = 10
factor_limit_per_batch = 100

[output]
save_detailed_reports = true
save_pnl_series = true
plot_format = "png"
plot_dpi = 300
```

**验收标准**:
- [ ] 配置结构清晰
- [ ] 参数分类合理
- [ ] 继承关系正确

### Task 2.1.2: 配置加载和验证
**文件**: `script/投研_因子挖掘集成/L3动态稳健性检验.py`
**工期**: 30分钟
**依赖**: Task 2.1.1

**实现内容**:
```python
def load_and_validate_config() -> Tuple[Dict, Dict, Dict]:
    """
    加载并验证WFA配置
    
    Returns:
        wfa_params: WFA算法参数
        criteria_params: 通过标准参数  
        query_params: 因子查询参数
    """
    from dynaconf import Dynaconf
    from config import settings
    
    # 加载任务配置
    task_config = Dynaconf(settings_files=["config/tasks/ts_l3_wfa.toml"])
    
    # 参数验证和转换
    wfa_params = validate_wfa_params(task_config.wfa)
    criteria_params = validate_criteria_params(task_config.criteria)
    query_params = validate_query_params(task_config.factor_query)
    
    return wfa_params, criteria_params, query_params
```

**验收标准**:
- [ ] 配置加载正常
- [ ] 参数验证完善
- [ ] 错误提示清晰

### Task 2.2.1: 因子查询和数据加载
**文件**: `script/投研_因子挖掘集成/L3动态稳健性检验.py`
**工期**: 45分钟
**依赖**: Task 2.1.2

**实现内容**:
```python
def query_l2_passed_factors(query_params: Dict) -> List[Dict]:
    """查询L2阶段通过的因子"""
    from factorzoo import factorzoo
    
    filters = {
        'pipeline_step': query_params['source_pipeline_step'],
        'status': 'L2_PASSED'
    }
    
    factors = factorzoo.search_factors(
        filters, 
        limit=query_params.get('factor_limit_per_batch', 100)
    )
    
    return factors

def load_factor_and_price_data(factor_id: str) -> Tuple[pd.Series, pd.Series]:
    """加载因子值和价格数据"""
    # 实现数据加载逻辑
    pass
```

**验收标准**:
- [ ] 因子查询正确
- [ ] 数据加载完整
- [ ] 异常处理完善

### Task 2.2.2: 批量处理框架
**文件**: `script/投研_因子挖掘集成/L3动态稳健性检验.py`
**工期**: 60分钟
**依赖**: Task 2.2.1

**实现内容**:
```python
def batch_wfa_validation(factors: List[Dict], 
                        wfa_params: Dict,
                        criteria_params: Dict) -> List[WFAResult]:
    """
    批量WFA验证处理
    
    支持:
    - 进度监控
    - 错误恢复
    - 结果缓存
    - 性能统计
    """
    results = []
    failed_factors = []
    
    for i, factor_info in enumerate(factors, 1):
        try:
            print(f"📊 处理因子 {i}/{len(factors)}: {factor_info['factor_id']}")
            
            # 加载数据
            factor_data, price_data = load_factor_and_price_data(factor_info['factor_id'])
            
            # WFA验证
            wfa_result = run_wfa_validation(factor_data, price_data, wfa_params)
            
            # 通过标准判定
            pass_status, fail_reasons = check_wfa_criteria(wfa_result.metrics, criteria_params)
            wfa_result.pass_status = pass_status
            wfa_result.fail_reasons = fail_reasons
            
            results.append(wfa_result)
            
        except Exception as e:
            print(f"❌ 因子 {factor_info['factor_id']} 处理失败: {str(e)}")
            failed_factors.append(factor_info['factor_id'])
            continue
    
    return results, failed_factors
```

**验收标准**:
- [ ] 批量处理稳定
- [ ] 进度监控清晰
- [ ] 错误恢复机制完善

---

## Phase 3: 可视化报告生成 (3-4天)

### Task 3.1.1: quantstats依赖安装和测试
**工期**: 20分钟
**依赖**: 无

**实现内容**:
```bash
# 安装quantstats
pip install quantstats --upgrade --no-cache-dir

# 测试基本功能
python -c "import quantstats as qs; print('quantstats安装成功')"
```

**验收标准**:
- [ ] quantstats安装成功
- [ ] 基本功能测试通过
- [ ] 与现有环境兼容

### Task 3.1.2: quantstats报告生成函数
**文件**: `factor/validation_utils.py`
**工期**: 60分钟
**依赖**: Task 3.1.1

**实现内容**:
```python
def generate_quantstats_reports(factor_id: str, 
                               returns: pd.Series, 
                               benchmark: pd.Series = None) -> Dict:
    """使用quantstats生成专业WFA绩效报告"""
    import quantstats as qs
    from config import REPORTS_DIR
    
    # 设置报告目录
    report_dir = REPORTS_DIR / 'wfa_validation' / 'factor_reports' / factor_id
    report_dir.mkdir(parents=True, exist_ok=True)
    
    # 1. 完整HTML报告
    qs.reports.html(returns,
                   benchmark=benchmark,
                   output=str(report_dir / 'quantstats_full_report.html'),
                   title=f'WFA Analysis - {factor_id}')
    
    # 2. 关键图表单独保存
    import matplotlib.pyplot as plt
    
    # 绩效快照
    fig = qs.plots.snapshot(returns, title=f'{factor_id} - Performance Snapshot',
                           figsize=(15, 8), show=False)
    fig.savefig(report_dir / 'performance_snapshot.png', dpi=300, bbox_inches='tight')
    plt.close(fig)
    
    # 回撤分析
    fig = qs.plots.drawdown(returns, title=f'{factor_id} - Drawdown Analysis',
                           figsize=(12, 6), show=False)
    fig.savefig(report_dir / 'drawdown_analysis.png', dpi=300, bbox_inches='tight')
    plt.close(fig)
    
    # 3. 关键指标摘要
    metrics = {
        'sharpe_ratio': qs.stats.sharpe(returns),
        'max_drawdown': qs.stats.max_drawdown(returns),
        'calmar_ratio': qs.stats.calmar(returns),
        'win_rate': qs.stats.win_rate(returns),
        'volatility': qs.stats.volatility(returns),
        'total_return': qs.stats.comp(returns)
    }
    
    # 保存指标到JSON
    import json
    with open(report_dir / 'quantstats_metrics.json', 'w') as f:
        json.dump(metrics, f, indent=2, default=str)
    
    print(f"✅ quantstats报告已生成: {report_dir}")
    return metrics
```

**验收标准**:
- [ ] HTML报告生成正常
- [ ] 图表质量符合要求
- [ ] 指标计算准确

### Task 3.2.1: 自定义matplotlib图表
**文件**: `factor/validation_utils.py`
**工期**: 75分钟
**依赖**: Task 3.1.2

**实现内容**:
```python
def generate_custom_wfa_charts(factor_id: str, wfa_result: WFAResult):
    """生成自定义的WFA补充图表"""
    import matplotlib.pyplot as plt
    import seaborn as sns
    from scipy import stats
    
    report_dir = REPORTS_DIR / 'wfa_validation' / 'factor_reports' / factor_id
    
    # 设置matplotlib样式
    plt.style.use('seaborn-v0_8-whitegrid')
    sns.set_palette("husl")
    
    returns = wfa_result.pnl_series.dropna()
    
    # 1. 滚动指标分析图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 滚动夏普比率
    rolling_sharpe = returns.rolling(60).apply(lambda x: x.mean() / x.std() * np.sqrt(252))
    axes[0,0].plot(rolling_sharpe.index, rolling_sharpe.values, linewidth=1.5)
    axes[0,0].axhline(y=0.5, color='red', linestyle='--', alpha=0.7, label='通过线')
    axes[0,0].set_title('滚动夏普比率 (60日)')
    axes[0,0].legend()
    
    # 滚动最大回撤
    cumulative = returns.cumsum()
    rolling_max = cumulative.expanding().max()
    rolling_dd = (cumulative - rolling_max) / rolling_max
    axes[0,1].fill_between(rolling_dd.index, 0, rolling_dd.values, alpha=0.3, color='red')
    axes[0,1].set_title('滚动回撤')
    
    # 收益分布对比
    axes[1,0].hist(returns, bins=50, alpha=0.7, density=True, label='实际分布')
    x = np.linspace(returns.min(), returns.max(), 100)
    normal_dist = stats.norm.pdf(x, returns.mean(), returns.std())
    axes[1,0].plot(x, normal_dist, 'r-', label='正态分布')
    axes[1,0].set_title('收益分布对比')
    axes[1,0].legend()
    
    # 年度收益条形图
    annual_returns = returns.resample('Y').sum()
    axes[1,1].bar(range(len(annual_returns)), annual_returns.values, alpha=0.7)
    axes[1,1].set_xticks(range(len(annual_returns)))
    axes[1,1].set_xticklabels([str(year.year) for year in annual_returns.index])
    axes[1,1].set_title('年度收益')
    
    plt.tight_layout()
    plt.savefig(report_dir / 'custom_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 自定义图表已生成: {report_dir}")
```

**验收标准**:
- [ ] 自定义图表生成正常
- [ ] 图表信息丰富
- [ ] 视觉效果良好

### Task 3.3.1: 批量报告生成管理
**文件**: `factor/validation_utils.py`
**工期**: 45分钟
**依赖**: Task 3.1.2, Task 3.2.1

**实现内容**:
```python
def batch_generate_wfa_reports(wfa_results: List[WFAResult], 
                              use_quantstats: bool = True):
    """批量生成WFA图表报告"""
    
    print(f"🚀 开始批量生成 {len(wfa_results)} 个因子的WFA报告...")
    
    success_count = 0
    failed_factors = []
    
    for i, wfa_result in enumerate(wfa_results, 1):
        try:
            print(f"📊 生成报告 {i}/{len(wfa_results)}: {wfa_result.factor_id}")
            
            if use_quantstats:
                # 使用quantstats生成专业报告
                metrics = generate_quantstats_reports(wfa_result.factor_id, wfa_result.pnl_series)
                
                # 生成自定义补充图表
                generate_custom_wfa_charts(wfa_result.factor_id, wfa_result)
            
            success_count += 1
            
        except Exception as e:
            print(f"❌ 因子 {wfa_result.factor_id} 报告生成失败: {str(e)}")
            failed_factors.append(wfa_result.factor_id)
            continue
    
    # 生成批次汇总报告
    generate_batch_summary_report(wfa_results, success_count, failed_factors)
    
    print(f"✅ 批量报告生成完成! 成功: {success_count}/{len(wfa_results)}")
    return success_count, failed_factors
```

**验收标准**:
- [ ] 批量处理稳定
- [ ] 进度监控清晰
- [ ] 汇总报告准确

---

## Phase 4: FactorZoo集成与数据管理 (2-3天)

### Task 4.1.1: WFA绩效数据入库函数
**文件**: `factor/validation_utils.py`
**工期**: 45分钟
**依赖**: Phase 1完成

**实现内容**:
```python
def save_wfa_to_factorzoo(wfa_result: WFAResult, wfa_params: Dict) -> bool:
    """将WFA结果保存到FactorZoo数据库"""
    from factorzoo import factorzoo
    from datetime import datetime
    import json

    # 生成评价ID
    eval_id = f"EVAL_WFA_{wfa_result.factor_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    # 准备评价数据
    evaluation_data = {
        'eval_id': eval_id,
        'factor_id': wfa_result.factor_id,
        'evaluation_name': 'WFA_L3_Validation',
        'evaluation_method': 'walk_forward_analysis',
        'evaluator': 'L3_WFA_System',
        'evaluation_params': json.dumps(wfa_params),

        # WFA核心指标映射
        'sharpe_ratio': wfa_result.metrics['sharpe_ratio'],
        'max_drawdown': wfa_result.metrics['max_drawdown'],
        'total_return': wfa_result.metrics['total_return'],
        'volatility': wfa_result.metrics['volatility'],
        'calmar_ratio': wfa_result.metrics['calmar_ratio'],
        'win_rate': wfa_result.metrics['win_rate'],

        # 详细结果JSON
        'detailed_results': json.dumps({
            'wfa_specific': {
                'training_window': wfa_params['training_window'],
                'testing_window': wfa_params['testing_window'],
                'tanh_k': wfa_params['tanh_k']
            },
            'pnl_statistics': {
                'skewness': wfa_result.metrics.get('skewness'),
                'kurtosis': wfa_result.metrics.get('kurtosis')
            }
        }),

        'evaluation_status': 'completed'
    }

    # 插入到FactorZoo
    success = factorzoo.add_evaluation(evaluation_data)

    if success:
        print(f"✅ WFA结果已保存到FactorZoo: {eval_id}")

        # 更新因子状态
        if wfa_result.pass_status == 'L3_PASSED':
            factorzoo.update_factor_status(wfa_result.factor_id, 'L3_PASSED', 'WFA验证通过')
        else:
            factorzoo.update_factor_status(wfa_result.factor_id, 'L3_FAILED', 'WFA验证未通过')

    return success
```

**验收标准**:
- [ ] 数据入库成功
- [ ] 状态更新正确
- [ ] 异常处理完善

### Task 4.2.1: 通过标准判定函数
**文件**: `factor/validation_utils.py`
**工期**: 30分钟
**依赖**: Phase 1完成

**实现内容**:
```python
def check_wfa_criteria(metrics: Dict, criteria_params: Dict) -> Tuple[str, List[str]]:
    """检查WFA是否通过统一标准"""
    fail_reasons = []

    # 主要标准检查
    if metrics['sharpe_ratio'] < criteria_params['min_sharpe']:
        fail_reasons.append(f"夏普比率过低: {metrics['sharpe_ratio']:.3f} < {criteria_params['min_sharpe']}")

    if metrics['max_drawdown'] > criteria_params['max_mdd']:
        fail_reasons.append(f"最大回撤过大: {metrics['max_drawdown']:.2%} > {criteria_params['max_mdd']:.2%}")

    if metrics['win_rate'] < criteria_params['min_win_rate']:
        fail_reasons.append(f"胜率过低: {metrics['win_rate']:.2%} < {criteria_params['min_win_rate']:.2%}")

    # 补充标准检查
    if metrics.get('calmar_ratio', 0) < criteria_params.get('min_calmar', 0):
        fail_reasons.append(f"卡玛比率过低: {metrics.get('calmar_ratio', 0):.3f} < {criteria_params.get('min_calmar', 0)}")

    # 判定结果
    if len(fail_reasons) == 0:
        return 'L3_PASSED', []
    else:
        return 'L3_FAILED', fail_reasons
```

**验收标准**:
- [ ] 标准判定逻辑正确
- [ ] 失败原因记录详细
- [ ] 支持多层级标准

---

## 关键里程碑检查点

### Milestone 1: 核心算法验证 (Phase 1完成)
- [ ] WFA算法实现正确
- [ ] 单因子验证功能正常
- [ ] 性能指标达标

### Milestone 2: 业务流程集成 (Phase 2完成)
- [ ] 配置系统集成完善
- [ ] 批量处理功能正常
- [ ] FactorZoo查询正常

### Milestone 3: 可视化报告完成 (Phase 3完成)
- [ ] quantstats报告生成正常
- [ ] 自定义图表丰富
- [ ] 批量报告管理完善

### Milestone 4: 端到端验证 (Phase 4完成)
- [ ] 完整流程运行正常
- [ ] 数据入库成功
- [ ] 状态管理完善

---

## 质量保证和测试策略

### 单元测试要求
- [ ] WFA核心算法单元测试
- [ ] 绩效计算函数测试
- [ ] 配置加载验证测试
- [ ] 数据库集成测试

### 集成测试要求
- [ ] 端到端流程测试
- [ ] 批量处理压力测试
- [ ] 异常恢复测试
- [ ] 性能基准测试

### 代码质量标准
- [ ] 遵循项目编程风格
- [ ] 函数注释完整
- [ ] 异常处理完善
- [ ] 日志输出规范

---

**注**: 每个任务完成后需要进行代码审查和功能测试，确保质量符合项目标准。建议使用渐进式开发方式，先实现核心功能，再逐步完善细节。
