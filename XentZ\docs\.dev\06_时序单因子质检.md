---
**第零步：样本内（IS）的动态稳健性检验 (WFA方法)**
此步骤旨在通过模拟真实交易环境，对每个候选因子进行高强度的压力测试，以剔除那些在静态回测中表现良好但缺乏动态稳健性的"伪因子"。

**A. WFA结构定义**
1.  **训练期 (`training_window`)**: 用于校准因子规则的回看窗口。推荐值：`750个交易日`（约3年），以确保覆盖多种市场状态。
2.  **测试期 (`testing_window`)**: 紧跟训练期之后，应用已校准规则进行前向测试的窗口。推荐值：`60个交易日`（约3个月），与季度调仓周期匹配。
3.  **步进 (`step_size`)**: 每次滚动时，窗口向前移动的长度。通常等于`testing_window`，即`60个交易日`。

**B. 滚动执行算法 (对每个因子独立执行)**
1.  **初始化**: 设定总样本内（IS）区间的起点和终点。初始化一个空的列表 `pnl_segments` 用于存储每个测试期的损益序列。
2.  **循环开始**: 从IS区间的起点开始，执行以下滚动循环，直到测试期的终点超出IS区间的终点。
    a. **数据切分**:
        -   `train_data`: 从当前位置取出长度为 `training_window` 的数据。
        -   `test_data`: 紧接着 `train_data`，取出长度为 `testing_window` 的数据。
    b. **训练阶段 (在`train_data`上操作)**:
        -   **学习因子分布**: 核心任务是学习因子值在当前训练窗口内的经验累积分布函数 (Empirical CDF)。这相当于创建一个"排序器"，用于将后续的因子值转换为0到1之间的百分位排名。
        -   **确定动态因子方向**: 计算因子值与未来N期收益（如`N=1`）的相关性 `corr`。若`corr > 0`，则因子方向 `direction = 1`；反之 `direction = -1`。此步骤保持不变，至关重要。
    c. **测试阶段 (在`test_data`上操作)**:
        -   **仓位映射 (核心步骤)**: 将原始因子值通过一个健壮的、非线性的函数转换为最终仓位。此过程取代了有缺陷的简单Z-Score映射。
            -   **1. 因子值 -> 排序百分位**: 对测试期内的每一个因子值，使用**训练阶段**学到的分布，计算其百分位排名 `rank_percentile` (一个0到1之间的值)。此方法对异常值极其不敏感。
            -   **2. 排序百分位 -> 基础仓位 (S型曲线映射 - 推荐实践)**: 将`rank_percentile`通过`tanh`函数转换为对称的、范围在[-1, 1]之间的基础仓位 `base_position`。
                `base_position = tanh(k * (rank_percentile - 0.5))`
                -   这是一个真正的**S型转换曲线**，能完美实现"信号微弱时降低交易频率，信号增强时逐步增加仓位，并在信号最强时平滑封顶"的理想效果。
                -   `k`: **交易积极性系数**。这是一个关键参数，用于控制S曲线的陡峭程度。
                    -   **中性值 (k≈4-5)**: 在排名中位数(0.5)附近曲线平缓，有效过滤噪声，避免频繁交易；当排名显著偏离中位数时，仓位才开始快速增加。
                    -   **高 `k` 值 (k>10)**: 曲线非常陡峭，接近于阶梯函数，策略反应更激进，微小信号即可能导致接近满仓。
                    -   **低 `k` 值 (k<3)**: 曲线更平缓，接近线性，策略反应更温和。
                -   此方法相比简单的线性映射，能更好地处理噪声、平滑仓位变化，是业界更稳健和标准的做法。
            -   **3. (可选/高级) 风险调整 (黄金标准)**: 为达到更稳定的权益曲线，可引入波动率目标调整。
                -   首先，需要一个独立的波动率预测模型（如GARCH）来估计资产的短期未来波动率 `predicted_vol`。
                -   设定一个全局的目标年化波动率 `target_vol` (例如 15%)。
                -   计算波动率缩放系数 `vol_scalar = target_vol / predicted_vol`。
                -   `final_position = base_position * vol_scalar`。
                在没有实现波动率预测模型前，可设定 `final_position = base_position`。
            -   **4. 结合方向**: `final_position = final_position * direction`。
        -   **计算损益**: 根据每日的最终仓位 `final_position` 和下一日的真实价格变动，计算出该测试期内每日的损益（PnL）。
        -   **记录结果**: 将此`testing_window`内的PnL序列作为一个片段，存入`pnl_segments`列表。
    d. **滚动前进**: 将当前位置向前移动 `step_size` 的长度，开始下一次循环。
3.  **拼接绩效**: 循环结束后，将`pnl_segments`列表中的所有PnL片段，按时间顺序拼接成一条完整的、无前视偏差的WFA净值曲线。

**C. 评估与筛选标准**
基于拼接后的WFA净值曲线，设立以下严格的、可量化的淘汰门槛。因子必须**同时满足所有主要标准**方可通过。
- **主要标准 (Pass/Fail)**:
    - **WFA夏普比率 (`WFA-Sharpe`)**: 年化夏普比率必须 `> 0.5`。此为核心标准，低于此值说明因子不稳定，直接淘汰。
    - **WFA最大回撤 (`WFA-MDD`)**: 必须 `< 30%`。控制风险，排除过程极端的因子。
    - **盈利周期占比**: 在所有测试期中，盈利的周期数占比应 `> 55%`。确保盈利的持续性而非依赖少数幸运周期。
    - **视觉检查**: 绘制WFA净值曲线，形态应大致稳步向上，剔除"一飞冲天后长期横盘或下跌"的"一招鲜"式因子。
- **次要标准 (用于排序优选)**:
    - **卡玛比率 (`Calmar Ratio`)**: 年化收益 / 最大回撤，越高越好。
    - **最长停滞期**: 净值曲线创出新高后，回到该高点所花费的最长时间，越短越好。

**D. 可选改进与补充（按实际需求酌情采纳）**

1. **训练 / 测试窗口自适应**：可按 *平均持仓周期* 动态设定窗口长度，例如 `training_window = hold_period × 10`，`testing_window = hold_period × 2`；并在切分前过滤低流动性交易日。

2. **因子方向判定改进**：使用 **Spearman 秩相关**代替 Pearson；若因子有先验方向可直接采用。

3. **仓位映射优化**：在训练窗口对 S 型参数 `k` 网格搜索（{3,5,7,9}），并对极端信号 `clip` 至 `[-1,1]`。

4. **费用与滑点模型**：在 IS 阶段纳入基础费用与简化滑点模型 `|Δposition| × fixed_slippage_perc`。

5. **交易频度与周转率约束**：记录 `turnover`，若 `年化 turnover > 30` 视作过度交易。

6. **绩效指标补充**：新增 **信息比率 (IR)**，常用阈值 IR ≥ 0.3。

7. **过拟合诊断 (PBO)**：将训练段再分 *k* 折（如5折）估计 PBO，PBO≈0.5 视为可接受。

8. **可视化与日志**：保存 `position_series` 与 `signal_percentile_series`；记录窗口边界、最优 `k`、费用、turnover 等信息，自动生成报告。

只有通过了这场高强度模拟面试的因子，才有资格进入后续更严苛的**第一步：时间维度OOS**考验。

---

**第一步：首轮筛选 - 时间维度OOS (样本外稳健性检验)**
对通过了**第零步WFA检验**的因子，进行真正的样本外测试。
- **方法**：逐个因子在"某品种"的**样本外（OOS）时段**，进行简单的仓位映射回测。
- **目标**：淘汰掉那些无法穿越未知时间周期、在全新市场环境下失效的因子。这是检验因子生命力的终极关卡。

---

**第二步：二轮筛选 - 跨品种OOS (普适性检验)**
将通过了**第一步时间维度OOS**的幸存因子，进行跨品种测试，以检验其逻辑的普适性。
- **方法**：选择几个与"某品种"高度相关或同属一个板块的品种（例如，如果原品种是螺纹钢，可以选择热卷、铁矿石作为测试对象）。在**相同的样本内时段**，用这些幸存因子去跑仓位映射回测。
- **目标**：观察因子在这些新测试品种上的表现，为后续的因子分类做准备。

---

**第三步：因子分类与定性**
完成上述所有考验后，你可以将最终幸存的因子分为两类，这对于你后续的模型构建至关重要：

- **A类 - 通用型因子 (Universal Factors)**：
  - **定义**：既通过了时间维度OOS，也在跨品种OOS上表现良好的因子。
  - **解读**：这是最宝贵的因子！它证明了其背后的逻辑既稳健（能穿越时间），又具有普适性（适用于同类品种）。它们是你构建核心策略模型的基石。

- **B类 - 特异性因子 (Specific Factors)**：
  - **定义**：通过了时间维度OOS，但在跨品种OOS上表现平平或无效的因子。
  - **解读**：这类因子同样有价值，不应轻易丢弃！它说明该因子捕捉的是"某品种"上一个真实存在的、能够持续的特有规律。在为"某品种"构建最终模型时，它依然是一个有效的输入特征，但你要清楚它的局限性——不要指望把它直接用在其他品种上。