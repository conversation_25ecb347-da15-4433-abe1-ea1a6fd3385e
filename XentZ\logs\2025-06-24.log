2025-06-24 11:05 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 11:05 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 11:05 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 11:05 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 11:05 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 11:05 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 11:05 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 11:05 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 11:05 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 11:05 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(9): 9个
2025-06-24 11:05 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 7 列
2025-06-24 11:05 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了2个低方差特征
2025-06-24 11:05 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['ftool_ABSOLUTE(JZ001_10_20)', 'ftool_ABSOLUTE(roc_10)']
2025-06-24 11:05 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 11:05 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 13 (基础5+归一化0+保持0+标签1+筛选后特征7)
2025-06-24 11:05 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 13
2025-06-24 11:05 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 11:07 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(9): 9个
2025-06-24 11:07 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 7 列
2025-06-24 11:07 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了2个低方差特征
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['ftool_ABSOLUTE(JZ001_10_20)', 'ftool_ABSOLUTE(roc_10)']
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 13 (基础5+归一化0+保持0+标签1+筛选后特征7)
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 13
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 11:07 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(9): 9个
2025-06-24 11:07 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 7 列
2025-06-24 11:07 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了2个低方差特征
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['ftool_ABSOLUTE(JZ001_10_20)', 'ftool_ABSOLUTE(roc_10)']
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 13 (基础5+归一化0+保持0+标签1+筛选后特征7)
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 13
2025-06-24 11:07 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 11:08 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 11:08 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 11:08 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 11:08 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 11:08 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 11:08 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 11:08 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 11:08 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 11:08 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 11:08 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(9): 9个
2025-06-24 11:08 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 7 列
2025-06-24 11:08 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了2个低方差特征
2025-06-24 11:08 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['ftool_ABSOLUTE(roc_10)', 'ftool_ABSOLUTE(JZ001_10_20)']
2025-06-24 11:08 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 11:08 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 13 (基础5+归一化0+保持0+标签1+筛选后特征7)
2025-06-24 11:08 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 13
2025-06-24 11:08 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 11:08 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 11:10 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 11:10 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 11:10 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 11:10 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 11:10 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 11:10 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 11:10 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 11:10 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 11:10 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 11:10 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(9): 9个
2025-06-24 11:10 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 7 列
2025-06-24 11:10 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了2个低方差特征
2025-06-24 11:10 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['ftool_ABSOLUTE(JZ001_10_20)', 'ftool_ABSOLUTE(roc_10)']
2025-06-24 11:10 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 11:10 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 13 (基础5+归一化0+保持0+标签1+筛选后特征7)
2025-06-24 11:10 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 13
2025-06-24 11:10 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 11:10 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 11:31 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 11:31 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 11:31 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 11:31 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 11:31 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 11:31 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 11:31 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 11:31 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 11:31 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 11:31 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(9): 9个
2025-06-24 11:31 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 7 列
2025-06-24 11:31 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了2个低方差特征
2025-06-24 11:31 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['ftool_ABSOLUTE(JZ001_10_20)', 'ftool_ABSOLUTE(roc_10)']
2025-06-24 11:31 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 11:31 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 13 (基础5+归一化0+保持0+标签1+筛选后特征7)
2025-06-24 11:31 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 13
2025-06-24 11:31 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 11:31 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 11:42 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 11:42 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 11:42 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 11:42 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 11:42 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 11:42 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 11:42 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 11:42 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 11:42 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 11:42 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(9): 9个
2025-06-24 11:42 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 7 列
2025-06-24 11:42 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了2个低方差特征
2025-06-24 11:42 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['ftool_ABSOLUTE(JZ001_10_20)', 'ftool_ABSOLUTE(roc_10)']
2025-06-24 11:42 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 11:42 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 13 (基础5+归一化0+保持0+标签1+筛选后特征7)
2025-06-24 11:42 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 13
2025-06-24 11:42 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 11:42 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 11:43 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 12:32 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 12:32 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 12:32 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 12:32 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 12:32 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 12:32 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 12:32 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 12:32 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 12:32 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(9): 9个
2025-06-24 12:32 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 7 列
2025-06-24 12:32 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了2个低方差特征
2025-06-24 12:32 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['ftool_ABSOLUTE(JZ001_10_20)', 'ftool_ABSOLUTE(roc_10)']
2025-06-24 12:32 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 12:32 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 13 (基础5+归一化0+保持0+标签1+筛选后特征7)
2025-06-24 12:32 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 13
2025-06-24 12:32 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 12:32 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 12:32 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:51 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 16:52 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 16:52 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 16:52 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 16:52 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 16:52 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 16:52 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 16:52 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 16:52 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 16:52 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 16:52 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(9): 9个
2025-06-24 16:52 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 7 列
2025-06-24 16:52 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了2个低方差特征
2025-06-24 16:52 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['ftool_ABSOLUTE(roc_10)', 'ftool_ABSOLUTE(JZ001_10_20)']
2025-06-24 16:52 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 16:52 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 13 (基础5+归一化0+保持0+标签1+筛选后特征7)
2025-06-24 16:52 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 13
2025-06-24 16:52 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 16:52 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True

2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True

2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True

2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True

2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True

2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=3, n_clip=2, smooth=1, demean=False
2025-06-24 16:52 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-24 16:54 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 16:54 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 16:54 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 16:54 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 16:54 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 16:54 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 16:54 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 16:54 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 16:54 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 16:54 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(9): 9个
2025-06-24 16:54 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 7 列
2025-06-24 16:54 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了2个低方差特征
2025-06-24 16:54 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['ftool_ABSOLUTE(roc_10)', 'ftool_ABSOLUTE(JZ001_10_20)']
2025-06-24 16:54 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 16:54 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 13 (基础5+归一化0+保持0+标签1+筛选后特征7)
2025-06-24 16:54 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 13
2025-06-24 16:54 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 16:54 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 16:59 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 16:59 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 16:59 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 16:59 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 16:59 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 16:59 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 16:59 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 16:59 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 16:59 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 16:59 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(9): 9个
2025-06-24 16:59 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 7 列
2025-06-24 16:59 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了2个低方差特征
2025-06-24 16:59 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['ftool_ABSOLUTE(roc_10)', 'ftool_ABSOLUTE(JZ001_10_20)']
2025-06-24 16:59 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 16:59 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 13 (基础5+归一化0+保持0+标签1+筛选后特征7)
2025-06-24 16:59 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 13
2025-06-24 16:59 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 16:59 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 17:04 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 17:04 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 17:04 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 17:04 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 17:04 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 17:04 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 17:04 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 17:04 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 17:04 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 17:04 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(9): 9个
2025-06-24 17:04 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 7 列
2025-06-24 17:04 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了2个低方差特征
2025-06-24 17:04 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['ftool_ABSOLUTE(roc_10)', 'ftool_ABSOLUTE(JZ001_10_20)']
2025-06-24 17:04 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 17:04 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 13 (基础5+归一化0+保持0+标签1+筛选后特征7)
2025-06-24 17:04 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 13
2025-06-24 17:04 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 17:04 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 17:04 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 17:04 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 17:46 | INFO     | FctLoader.get_fct_df: 开始计算2个因子表达式
2025-06-24 17:46 | INFO     | FctLoader.get_fct_df: 成功处理4个因子和收益率列
2025-06-24 17:46 | ERROR    | FctLoader.get_fct_df: 输入的base_df为空
2025-06-24 17:46 | WARNING  | FctLoader.get_fct_df: 因子表达式列表和文件列表都为空
2025-06-24 18:13 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 18:13 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 18:13 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 18:13 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 18:13 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 18:13 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 18:13 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 18:13 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 18:13 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 18:13 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(9): 9个
2025-06-24 18:13 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 7 列
2025-06-24 18:13 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了2个低方差特征
2025-06-24 18:13 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['ftool_ABSOLUTE(JZ001_10_20)', 'ftool_ABSOLUTE(roc_10)']
2025-06-24 18:13 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 18:13 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 13 (基础5+归一化0+保持0+标签1+筛选后特征7)
2025-06-24 18:13 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 13
2025-06-24 18:13 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 18:13 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 18:14 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 18:14 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 18:16 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 18:16 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 18:16 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 18:16 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 18:16 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 18:16 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 18:16 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 18:16 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 18:16 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 18:16 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(9): 9个
2025-06-24 18:16 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 7 列
2025-06-24 18:16 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了2个低方差特征
2025-06-24 18:16 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['ftool_ABSOLUTE(roc_10)', 'ftool_ABSOLUTE(JZ001_10_20)']
2025-06-24 18:16 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 18:16 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 13 (基础5+归一化0+保持0+标签1+筛选后特征7)
2025-06-24 18:16 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 13
2025-06-24 18:16 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 18:16 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 18:16 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_ema_8(ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_delta_3(ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_mean_10(ts_clear_by_cond3(ftool_PERCENTILE(WV_MA_20), ftool_PERCENTILE(WV_MA_20), close)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_delta_17(ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_dema_8(ts_tan1(close)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_rvi_7(ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_mom_25(ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_add2(ta_cci_25(ftool_ABSOLUTE(b_cci_14), low, ftool_PERCENTILE(label_1)), ts_maxmin_10(ftool_PERCENTILE(WV_MA_20))) 时出现异常: ts_add2(ta_cci_25(ftool_ABSOLUTE(df["b_cci_14"]), df["low"], df["ftool_PERCENTILE(label_1)"]), ts_maxmin_10(df["ftool_PERCENTILE(WV_MA_20)"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_slope_pair_89(ta_lr_slope_5(close), ts_std_40(close)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_udd_20(ts_median_20(ftool_ABSOLUTE(b_cci_14)), ts_midpoint_5(b_cci_14)) 时出现异常: ts_udd_20(ts_median_20(ftool_ABSOLUTE(df["b_cci_14"])), ts_midpoint_5(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_midpoint_5(ta_sar(low, low)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_adxr_14(ta_rocr_14(ftool_PERCENTILE(JZ010_9)), ts_midpoint_20(ts_rvi_7(low)), ts_slope_pair_18(ts_cons_30(low), ts_cons_n2(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_delta_17(ts_midpoint_5(ftool_PERCENTILE(WV_MA_20))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_3(ftool_LAG(JZ005_rs34, datetime)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_lag_8(ts_prod_3(ts_min_5(ftool_LAG(JZ005_rs34, datetime)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_inv1(ta_rsi_24(ftool_PERCENTILE(label_1))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_tema_8(ts_udd_10(ftool_LAG(JZ005_rs34, datetime), open)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_5(low) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argmax_10(ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_max2(ftool_PERCENTILE(JZ005_rs144), open) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(ta_natr_14(ta_cmo_25(low), close, ts_maxmin_5(ftool_PERCENTILE(JZ010_9)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_atr_14(ftool_PERCENTILE(label_1), low, high) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_elu1(close) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_1(b_cci_14) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_10(close) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_001(ftool_PERCENTILE(WV_MA_20)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(ftool_PERCENTILE(JZ005_rs144)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_001(ftool_LAG(JZ005_rs34, datetime)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n1(ftool_PERCENTILE(JZ005_rs144)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n5(low) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n05(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_n05(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_n10(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_n2(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_05(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_05(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n05(ftool_LAG(JZ005_rs34, datetime)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_2(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_2(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_001(ts_cons_n30(b_cci_14)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n5(ta_lr_slope_10(ftool_PERCENTILE(JZ010_9))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sign1(ts_cons_1(ftool_PERCENTILE(JZ010_9))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_mean_return_5(ts_cons_2(open)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_1(ts_cons_10(b_cci_14)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_05(ts_cons_001(ftool_PERCENTILE(label_1))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_slope_10(ts_cons_n10(ftool_PERCENTILE(label_1))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(ta_lr_angle_20(open)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(ts_cons_n30(ftool_PERCENTILE(WV_MA_20))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n1(ts_rvi_89(high)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocp_25(ts_cons_n001(close)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(ts_zscore_20(ta_lr_angle_5(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_cons_n2(ts_zscore_20(ta_lr_angle_5(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_tan1(ts_argrange_20(ts_cons_n30(ftool_PERCENTILE(label_1)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_5(ts_elu1(ts_mean_return_20(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_inv1(ts_cons_n001(ts_cons_10(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_inv1(ts_cons_n001(ts_cons_10(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_20(ts_cons_30(ta_lr_slope_5(b_cci_14))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n05(ts_rvi_89(ts_std_10(ftool_PERCENTILE(WV_MA_20)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(ts_argmax_5(ta_lr_angle_20(ftool_PERCENTILE(label_1)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_elu1(ts_mean_return_10(ts_cons_n2(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_elu1(ts_mean_return_10(ts_cons_n2(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_1(ts_inverse_cv_10(ts_cons_10(ftool_LAG(JZ005_rs34, datetime)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(ts_cbrt1(ts_argrange_20(open))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n05(ta_lr_slope_20(ta_lr_angle_5(b_cci_14))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_10(ts_kurt_20(ts_day_min_20(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_2(ts_max_5(ts_range_5(ftool_PERCENTILE(label_1)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_inverse_cv_40(ts_cons_5(ts_log1(ftool_PERCENTILE(WV_MA_20)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_prod_3(ts_tanh1(low)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_bop(ftool_PERCENTILE(JZ010_9), ftool_LAG(JZ005_rs34, datetime), ftool_LAG(JZ005_rs34, datetime), ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n1(ta_adxr_14(b_cci_14, open, open)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n1(ts_clear_by_cond3(ftool_PERCENTILE(JZ005_rs144), low, ftool_PERCENTILE(JZ005_rs144))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n30(ta_adx_25(b_cci_14, ftool_PERCENTILE(JZ010_9), ftool_PERCENTILE(WV_MA_20))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_beta_5(ts_cons_30(ts_sma_8(b_cci_14)), open) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_midpoint_10(ts_slope_pair_34(ts_cons_30(ftool_PERCENTILE(JZ005_rs144)), ts_rvi_89(ftool_PERCENTILE(JZ010_9)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_2(ts_delta_8(ts_clear_by_cond3(low, ftool_PERCENTILE(label_1), ftool_PERCENTILE(label_1)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_mean_return_20(ts_cons_05(ta_minus_di_25(ftool_ABSOLUTE(b_cci_14), b_cci_14, ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_mean_return_20(ts_cons_05(ta_minus_di_25(ftool_ABSOLUTE(df["b_cci_14"]), df["b_cci_14"], ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n1(ta_beta_5(ts_sum_17(ftool_PERCENTILE(JZ005_rs144)), ts_cons_05(open))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(ts_slope_pair_18(ts_cons_n5(ftool_PERCENTILE(WV_MA_20)), ts_min_5(b_cci_14))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_adx_25(ts_cons_1(ftool_ABSOLUTE(b_cci_14)), ts_cons_30(ftool_PERCENTILE(JZ010_9)), ts_min_20(ftool_PERCENTILE(label_1))) 时出现异常: ta_adx_25(ts_cons_1(ftool_ABSOLUTE(df["b_cci_14"])), ts_cons_30(df["ftool_PERCENTILE(JZ010_9)"]), ts_min_20(df["ftool_PERCENTILE(label_1)"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_maxmin_20(ta_beta_5(ts_sub2(b_cci_14, b_cci_14), ta_tema_8(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_2(ta_adxr_14(ts_mean_5(ftool_LAG(JZ005_rs34, datetime)), ts_cons_n2(close), ts_cube1(close))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_10(ts_if_then_else3(ts_cons_n10(low), ts_day_max_10(ftool_PERCENTILE(WV_MA_20)), ts_cons_n1(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_cons_10(ts_if_then_else3(ts_cons_n10(df["low"]), ts_day_max_10(df["ftool_PERCENTILE(WV_MA_20)"]), ts_cons_n1(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_midpoint_20(ta_dema_8(ta_lr_slope_20(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_beta_20(ts_rvi_55(ts_day_min_40(low)), ts_prod_3(ta_ad(ftool_PERCENTILE(JZ010_9), ftool_PERCENTILE(JZ010_9), high, ftool_PERCENTILE(JZ005_rs144)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_slope_pair_18(ts_wma_21(ts_day_max_40(ftool_PERCENTILE(label_1))), ta_minus_dm_14(ts_slope_pair_34(open, high), ts_cons_10(low))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_dx_14(ts_std_10(ts_cons_5(b_cci_14)), ts_cons_5(ts_maxmin_20(ftool_PERCENTILE(JZ010_9))), ta_rsi_24(ts_rvi_34(low))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n001(ta_mfi_25(ta_trima_21(close), ts_mul2(ftool_PERCENTILE(label_1), high), ts_lag_8(ftool_PERCENTILE(JZ005_rs144)), ts_zscore_10(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sub2(ta_tema_55(ts_median_20(low)), ts_cos1(ts_sum_8(ftool_PERCENTILE(label_1)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argmax_10(ts_kurt_40(ftool_PERCENTILE(JZ010_9))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_autocorr_20_17(ts_delta_17(close)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_5(ta_lr_intercept_5(ts_rvi_34(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_rvi_7(ta_lr_intercept_20(ts_min_5(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_5(ta_ema_8(close)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_cci_25(ftool_PERCENTILE(label_1), ftool_ABSOLUTE(b_cci_14), ftool_PERCENTILE(JZ010_9)) 时出现异常: ta_cci_25(df["ftool_PERCENTILE(label_1)"], ftool_ABSOLUTE(df["b_cci_14"]), df["ftool_PERCENTILE(JZ010_9)"])——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_aroonosc_25(high, ftool_ABSOLUTE(b_cci_14)) 时出现异常: ta_aroonosc_25(df["high"], ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cube1(ts_udd_20(ts_mean_return_20(open), ta_natr_25(ftool_PERCENTILE(JZ010_9), high, high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_10(ts_min_10(ts_cons_001(b_cci_14))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sma_21(ts_cons_n10(ta_sar(high, ftool_PERCENTILE(label_1)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_kurt_20(ts_cons_30(ts_max2(open, ftool_LAG(JZ005_rs34, datetime)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | INFO     | FactorLoader.get_fct_df: 成功处理7个因子和收益率列
2025-06-24 18:16 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 18:16 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_ema_8(ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_delta_3(ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_mean_10(ts_clear_by_cond3(ftool_PERCENTILE(WV_MA_20), ftool_PERCENTILE(WV_MA_20), close)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_delta_17(ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_dema_8(ts_tan1(close)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_rvi_7(ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_mom_25(ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_add2(ta_cci_25(ftool_ABSOLUTE(b_cci_14), low, ftool_PERCENTILE(label_1)), ts_maxmin_10(ftool_PERCENTILE(WV_MA_20))) 时出现异常: ts_add2(ta_cci_25(ftool_ABSOLUTE(df["b_cci_14"]), df["low"], df["ftool_PERCENTILE(label_1)"]), ts_maxmin_10(df["ftool_PERCENTILE(WV_MA_20)"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_slope_pair_89(ta_lr_slope_5(close), ts_std_40(close)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_udd_20(ts_median_20(ftool_ABSOLUTE(b_cci_14)), ts_midpoint_5(b_cci_14)) 时出现异常: ts_udd_20(ts_median_20(ftool_ABSOLUTE(df["b_cci_14"])), ts_midpoint_5(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_midpoint_5(ta_sar(low, low)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_adxr_14(ta_rocr_14(ftool_PERCENTILE(JZ010_9)), ts_midpoint_20(ts_rvi_7(low)), ts_slope_pair_18(ts_cons_30(low), ts_cons_n2(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_delta_17(ts_midpoint_5(ftool_PERCENTILE(WV_MA_20))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_3(ftool_LAG(JZ005_rs34, datetime)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_lag_8(ts_prod_3(ts_min_5(ftool_LAG(JZ005_rs34, datetime)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_inv1(ta_rsi_24(ftool_PERCENTILE(label_1))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_tema_8(ts_udd_10(ftool_LAG(JZ005_rs34, datetime), open)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_5(low) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argmax_10(ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_max2(ftool_PERCENTILE(JZ005_rs144), open) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_atr_14(ftool_PERCENTILE(label_1), low, high) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_elu1(close) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_1(b_cci_14) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n05(ftool_LAG(JZ005_rs34, datetime)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_10(close) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_001(ftool_PERCENTILE(WV_MA_20)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(ftool_PERCENTILE(JZ005_rs144)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_001(ftool_LAG(JZ005_rs34, datetime)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n1(ftool_PERCENTILE(JZ005_rs144)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n5(low) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n05(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_n05(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_n10(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_n2(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_05(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_05(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_2(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_2(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_001(ts_cons_n30(b_cci_14)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_1(ts_cons_10(b_cci_14)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n5(ta_lr_slope_10(ftool_PERCENTILE(JZ010_9))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sign1(ts_cons_1(ftool_PERCENTILE(JZ010_9))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_mean_return_5(ts_cons_2(open)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocp_25(ts_cons_n001(close)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_05(ts_cons_001(ftool_PERCENTILE(label_1))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_slope_10(ts_cons_n10(ftool_PERCENTILE(label_1))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(ta_lr_angle_20(open)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(ts_cons_n30(ftool_PERCENTILE(WV_MA_20))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n1(ts_rvi_89(high)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_tan1(ts_argrange_20(ts_cons_n30(ftool_PERCENTILE(label_1)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_1(ts_inverse_cv_10(ts_cons_10(ftool_LAG(JZ005_rs34, datetime)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(ts_zscore_20(ta_lr_angle_5(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_cons_n2(ts_zscore_20(ta_lr_angle_5(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_5(ts_elu1(ts_mean_return_20(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_inv1(ts_cons_n001(ts_cons_10(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_inv1(ts_cons_n001(ts_cons_10(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_20(ts_cons_30(ta_lr_slope_5(b_cci_14))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n05(ts_rvi_89(ts_std_10(ftool_PERCENTILE(WV_MA_20)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(ts_argmax_5(ta_lr_angle_20(ftool_PERCENTILE(label_1)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_elu1(ts_mean_return_10(ts_cons_n2(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_elu1(ts_mean_return_10(ts_cons_n2(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_2(ts_max_5(ts_range_5(ftool_PERCENTILE(label_1)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(ts_cbrt1(ts_argrange_20(open))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n05(ta_lr_slope_20(ta_lr_angle_5(b_cci_14))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_10(ts_kurt_20(ts_day_min_20(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_inverse_cv_40(ts_cons_5(ts_log1(ftool_PERCENTILE(WV_MA_20)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(ta_natr_14(ta_cmo_25(low), close, ts_maxmin_5(ftool_PERCENTILE(JZ010_9)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_prod_3(ts_tanh1(low)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_bop(ftool_PERCENTILE(JZ010_9), ftool_LAG(JZ005_rs34, datetime), ftool_LAG(JZ005_rs34, datetime), ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n1(ta_adxr_14(b_cci_14, open, open)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n1(ts_clear_by_cond3(ftool_PERCENTILE(JZ005_rs144), low, ftool_PERCENTILE(JZ005_rs144))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n30(ta_adx_25(b_cci_14, ftool_PERCENTILE(JZ010_9), ftool_PERCENTILE(WV_MA_20))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_beta_5(ts_cons_30(ts_sma_8(b_cci_14)), open) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_midpoint_10(ts_slope_pair_34(ts_cons_30(ftool_PERCENTILE(JZ005_rs144)), ts_rvi_89(ftool_PERCENTILE(JZ010_9)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_2(ts_delta_8(ts_clear_by_cond3(low, ftool_PERCENTILE(label_1), ftool_PERCENTILE(label_1)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_mean_return_20(ts_cons_05(ta_minus_di_25(ftool_ABSOLUTE(b_cci_14), b_cci_14, ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_mean_return_20(ts_cons_05(ta_minus_di_25(ftool_ABSOLUTE(df["b_cci_14"]), df["b_cci_14"], ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n1(ta_beta_5(ts_sum_17(ftool_PERCENTILE(JZ005_rs144)), ts_cons_05(open))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(ts_slope_pair_18(ts_cons_n5(ftool_PERCENTILE(WV_MA_20)), ts_min_5(b_cci_14))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_adx_25(ts_cons_1(ftool_ABSOLUTE(b_cci_14)), ts_cons_30(ftool_PERCENTILE(JZ010_9)), ts_min_20(ftool_PERCENTILE(label_1))) 时出现异常: ta_adx_25(ts_cons_1(ftool_ABSOLUTE(df["b_cci_14"])), ts_cons_30(df["ftool_PERCENTILE(JZ010_9)"]), ts_min_20(df["ftool_PERCENTILE(label_1)"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_maxmin_20(ta_beta_5(ts_sub2(b_cci_14, b_cci_14), ta_tema_8(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_2(ta_adxr_14(ts_mean_5(ftool_LAG(JZ005_rs34, datetime)), ts_cons_n2(close), ts_cube1(close))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_10(ts_if_then_else3(ts_cons_n10(low), ts_day_max_10(ftool_PERCENTILE(WV_MA_20)), ts_cons_n1(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_cons_10(ts_if_then_else3(ts_cons_n10(df["low"]), ts_day_max_10(df["ftool_PERCENTILE(WV_MA_20)"]), ts_cons_n1(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_midpoint_20(ta_dema_8(ta_lr_slope_20(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_beta_20(ts_rvi_55(ts_day_min_40(low)), ts_prod_3(ta_ad(ftool_PERCENTILE(JZ010_9), ftool_PERCENTILE(JZ010_9), high, ftool_PERCENTILE(JZ005_rs144)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_slope_pair_18(ts_wma_21(ts_day_max_40(ftool_PERCENTILE(label_1))), ta_minus_dm_14(ts_slope_pair_34(open, high), ts_cons_10(low))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_dx_14(ts_std_10(ts_cons_5(b_cci_14)), ts_cons_5(ts_maxmin_20(ftool_PERCENTILE(JZ010_9))), ta_rsi_24(ts_rvi_34(low))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n001(ta_mfi_25(ta_trima_21(close), ts_mul2(ftool_PERCENTILE(label_1), high), ts_lag_8(ftool_PERCENTILE(JZ005_rs144)), ts_zscore_10(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sub2(ta_tema_55(ts_median_20(low)), ts_cos1(ts_sum_8(ftool_PERCENTILE(label_1)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argmax_10(ts_kurt_40(ftool_PERCENTILE(JZ010_9))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_autocorr_20_17(ts_delta_17(close)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_5(ta_lr_intercept_5(ts_rvi_34(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_rvi_7(ta_lr_intercept_20(ts_min_5(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_5(ta_ema_8(close)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_cci_25(ftool_PERCENTILE(label_1), ftool_ABSOLUTE(b_cci_14), ftool_PERCENTILE(JZ010_9)) 时出现异常: ta_cci_25(df["ftool_PERCENTILE(label_1)"], ftool_ABSOLUTE(df["b_cci_14"]), df["ftool_PERCENTILE(JZ010_9)"])——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_aroonosc_25(high, ftool_ABSOLUTE(b_cci_14)) 时出现异常: ta_aroonosc_25(df["high"], ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cube1(ts_udd_20(ts_mean_return_20(open), ta_natr_25(ftool_PERCENTILE(JZ010_9), high, high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_10(ts_min_10(ts_cons_001(b_cci_14))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sma_21(ts_cons_n10(ta_sar(high, ftool_PERCENTILE(label_1)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_kurt_20(ts_cons_30(ts_max2(open, ftool_LAG(JZ005_rs34, datetime)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | INFO     | FactorLoader.get_fct_df: 成功处理7个因子和收益率列
2025-06-24 18:16 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 18:16 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_ema_8(ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_delta_3(ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_mean_10(ts_clear_by_cond3(ftool_PERCENTILE(WV_MA_20), ftool_PERCENTILE(WV_MA_20), close)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_delta_17(ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_dema_8(ts_tan1(close)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_rvi_7(ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_mom_25(ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_add2(ta_cci_25(ftool_ABSOLUTE(b_cci_14), low, ftool_PERCENTILE(label_1)), ts_maxmin_10(ftool_PERCENTILE(WV_MA_20))) 时出现异常: ts_add2(ta_cci_25(ftool_ABSOLUTE(df["b_cci_14"]), df["low"], df["ftool_PERCENTILE(label_1)"]), ts_maxmin_10(df["ftool_PERCENTILE(WV_MA_20)"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_slope_pair_89(ta_lr_slope_5(close), ts_std_40(close)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_udd_20(ts_median_20(ftool_ABSOLUTE(b_cci_14)), ts_midpoint_5(b_cci_14)) 时出现异常: ts_udd_20(ts_median_20(ftool_ABSOLUTE(df["b_cci_14"])), ts_midpoint_5(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_midpoint_5(ta_sar(low, low)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_adxr_14(ta_rocr_14(ftool_PERCENTILE(JZ010_9)), ts_midpoint_20(ts_rvi_7(low)), ts_slope_pair_18(ts_cons_30(low), ts_cons_n2(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_delta_17(ts_midpoint_5(ftool_PERCENTILE(WV_MA_20))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_3(ftool_LAG(JZ005_rs34, datetime)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_lag_8(ts_prod_3(ts_min_5(ftool_LAG(JZ005_rs34, datetime)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_inv1(ta_rsi_24(ftool_PERCENTILE(label_1))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_tema_8(ts_udd_10(ftool_LAG(JZ005_rs34, datetime), open)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_5(low) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argmax_10(ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_max2(ftool_PERCENTILE(JZ005_rs144), open) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_20(ta_natr_14(ta_cmo_25(low), close, ts_maxmin_5(ftool_PERCENTILE(JZ010_9)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_atr_14(ftool_PERCENTILE(label_1), low, high) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_elu1(close) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_1(b_cci_14) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_10(close) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_001(ftool_PERCENTILE(WV_MA_20)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(ftool_PERCENTILE(JZ005_rs144)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_001(ftool_LAG(JZ005_rs34, datetime)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n1(ftool_PERCENTILE(JZ005_rs144)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n5(low) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n05(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_n05(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_n10(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_n2(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_05(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_05(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n05(ftool_LAG(JZ005_rs34, datetime)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_2(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_2(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_001(ts_cons_n30(b_cci_14)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n5(ta_lr_slope_10(ftool_PERCENTILE(JZ010_9))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sign1(ts_cons_1(ftool_PERCENTILE(JZ010_9))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_mean_return_5(ts_cons_2(open)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_1(ts_cons_10(b_cci_14)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_05(ts_cons_001(ftool_PERCENTILE(label_1))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_lr_slope_10(ts_cons_n10(ftool_PERCENTILE(label_1))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(ta_lr_angle_20(open)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_10(ts_cons_n30(ftool_PERCENTILE(WV_MA_20))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n1(ts_rvi_89(high)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_rocp_25(ts_cons_n001(close)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(ts_zscore_20(ta_lr_angle_5(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_cons_n2(ts_zscore_20(ta_lr_angle_5(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_tan1(ts_argrange_20(ts_cons_n30(ftool_PERCENTILE(label_1)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_5(ts_elu1(ts_mean_return_20(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_inv1(ts_cons_n001(ts_cons_10(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_inv1(ts_cons_n001(ts_cons_10(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_zscore_20(ts_cons_30(ta_lr_slope_5(b_cci_14))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n05(ts_rvi_89(ts_std_10(ftool_PERCENTILE(WV_MA_20)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(ts_argmax_5(ta_lr_angle_20(ftool_PERCENTILE(label_1)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_elu1(ts_mean_return_10(ts_cons_n2(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_elu1(ts_mean_return_10(ts_cons_n2(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_1(ts_inverse_cv_10(ts_cons_10(ftool_LAG(JZ005_rs34, datetime)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(ts_cbrt1(ts_argrange_20(open))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n05(ta_lr_slope_20(ta_lr_angle_5(b_cci_14))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_10(ts_kurt_20(ts_day_min_20(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_2(ts_max_5(ts_range_5(ftool_PERCENTILE(label_1)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_inverse_cv_40(ts_cons_5(ts_log1(ftool_PERCENTILE(WV_MA_20)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_prod_3(ts_tanh1(low)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_bop(ftool_PERCENTILE(JZ010_9), ftool_LAG(JZ005_rs34, datetime), ftool_LAG(JZ005_rs34, datetime), ftool_PERCENTILE(label_1)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n1(ta_adxr_14(b_cci_14, open, open)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n1(ts_clear_by_cond3(ftool_PERCENTILE(JZ005_rs144), low, ftool_PERCENTILE(JZ005_rs144))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n30(ta_adx_25(b_cci_14, ftool_PERCENTILE(JZ010_9), ftool_PERCENTILE(WV_MA_20))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_beta_5(ts_cons_30(ts_sma_8(b_cci_14)), open) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_midpoint_10(ts_slope_pair_34(ts_cons_30(ftool_PERCENTILE(JZ005_rs144)), ts_rvi_89(ftool_PERCENTILE(JZ010_9)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_2(ts_delta_8(ts_clear_by_cond3(low, ftool_PERCENTILE(label_1), ftool_PERCENTILE(label_1)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_mean_return_20(ts_cons_05(ta_minus_di_25(ftool_ABSOLUTE(b_cci_14), b_cci_14, ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_mean_return_20(ts_cons_05(ta_minus_di_25(ftool_ABSOLUTE(df["b_cci_14"]), df["b_cci_14"], ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n1(ta_beta_5(ts_sum_17(ftool_PERCENTILE(JZ005_rs144)), ts_cons_05(open))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_8(ts_slope_pair_18(ts_cons_n5(ftool_PERCENTILE(WV_MA_20)), ts_min_5(b_cci_14))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_adx_25(ts_cons_1(ftool_ABSOLUTE(b_cci_14)), ts_cons_30(ftool_PERCENTILE(JZ010_9)), ts_min_20(ftool_PERCENTILE(label_1))) 时出现异常: ta_adx_25(ts_cons_1(ftool_ABSOLUTE(df["b_cci_14"])), ts_cons_30(df["ftool_PERCENTILE(JZ010_9)"]), ts_min_20(df["ftool_PERCENTILE(label_1)"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_maxmin_20(ta_beta_5(ts_sub2(b_cci_14, b_cci_14), ta_tema_8(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_2(ta_adxr_14(ts_mean_5(ftool_LAG(JZ005_rs34, datetime)), ts_cons_n2(close), ts_cube1(close))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_10(ts_if_then_else3(ts_cons_n10(low), ts_day_max_10(ftool_PERCENTILE(WV_MA_20)), ts_cons_n1(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_cons_10(ts_if_then_else3(ts_cons_n10(df["low"]), ts_day_max_10(df["ftool_PERCENTILE(WV_MA_20)"]), ts_cons_n1(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_midpoint_20(ta_dema_8(ta_lr_slope_20(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_beta_20(ts_rvi_55(ts_day_min_40(low)), ts_prod_3(ta_ad(ftool_PERCENTILE(JZ010_9), ftool_PERCENTILE(JZ010_9), high, ftool_PERCENTILE(JZ005_rs144)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_slope_pair_18(ts_wma_21(ts_day_max_40(ftool_PERCENTILE(label_1))), ta_minus_dm_14(ts_slope_pair_34(open, high), ts_cons_10(low))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_dx_14(ts_std_10(ts_cons_5(b_cci_14)), ts_cons_5(ts_maxmin_20(ftool_PERCENTILE(JZ010_9))), ta_rsi_24(ts_rvi_34(low))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n001(ta_mfi_25(ta_trima_21(close), ts_mul2(ftool_PERCENTILE(label_1), high), ts_lag_8(ftool_PERCENTILE(JZ005_rs144)), ts_zscore_10(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sub2(ta_tema_55(ts_median_20(low)), ts_cos1(ts_sum_8(ftool_PERCENTILE(label_1)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_argmax_10(ts_kurt_40(ftool_PERCENTILE(JZ010_9))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_autocorr_20_17(ts_delta_17(close)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_5(ta_lr_intercept_5(ts_rvi_34(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_rvi_7(ta_lr_intercept_20(ts_min_5(high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_min_5(ta_ema_8(close)) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_cci_25(ftool_PERCENTILE(label_1), ftool_ABSOLUTE(b_cci_14), ftool_PERCENTILE(JZ010_9)) 时出现异常: ta_cci_25(df["ftool_PERCENTILE(label_1)"], ftool_ABSOLUTE(df["b_cci_14"]), df["ftool_PERCENTILE(JZ010_9)"])——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_aroonosc_25(high, ftool_ABSOLUTE(b_cci_14)) 时出现异常: ta_aroonosc_25(df["high"], ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cube1(ts_udd_20(ts_mean_return_20(open), ta_natr_25(ftool_PERCENTILE(JZ010_9), high, high))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_day_max_10(ts_min_10(ts_cons_001(b_cci_14))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sma_21(ts_cons_n10(ta_sar(high, ftool_PERCENTILE(label_1)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_kurt_20(ts_cons_30(ts_max2(open, ftool_LAG(JZ005_rs34, datetime)))) 时出现异常: 'numpy.ndarray' object has no attribute 'values'
2025-06-24 18:16 | INFO     | FactorLoader.get_fct_df: 成功处理7个因子和收益率列
2025-06-24 18:19 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-24 18:19 | INFO     | FactorLoader.get_fct_df: 成功处理1个因子和收益率列
2025-06-24 18:19 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-24 18:19 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_median_5(low) 时出现异常: 'numpy.ndarray' object has no attribute 'index'
2025-06-24 18:19 | WARNING  | FactorLoader.get_fct_df: 没有成功处理任何因子
2025-06-24 19:05 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-24 19:05 | INFO     | FactorLoader.get_fct_df: 成功处理1个因子和收益率列
2025-06-24 19:05 | INFO     | FactorLoader.get_fct_df: 开始计算2个因子表达式
2025-06-24 19:05 | INFO     | FactorLoader.get_fct_df: 成功处理2个因子和收益率列
2025-06-24 19:05 | INFO     | FactorLoader.get_fct_df: 开始计算4个因子表达式
2025-06-24 19:05 | INFO     | FactorLoader.get_fct_df: 成功处理4个因子和收益率列
2025-06-24 19:05 | INFO     | FactorLoader.get_fct_df: 开始计算20个因子表达式
2025-06-24 19:05 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_std_5(close) 时出现异常: ts_std_5(df["close"])——eval异常
2025-06-24 19:05 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_sum_5(volume) 时出现异常: ts_sum_5(df["volume"])——eval异常
2025-06-24 19:05 | INFO     | FactorLoader.get_fct_df: 成功处理18个因子和收益率列
2025-06-24 19:11 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 8, 新增列数: 3
2025-06-24 19:11 | INFO     | FactorLoader.get_fct_df: 开始计算2个因子表达式
2025-06-24 19:11 | INFO     | FactorLoader.get_fct_df: 成功处理1个因子和收益率列
2025-06-24 19:11 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 8, 新增列数: 3
2025-06-24 19:11 | INFO     | FactorLoader.get_fct_df: 开始计算2个因子表达式
2025-06-24 19:11 | INFO     | FactorLoader.get_fct_df: 成功处理2个因子和收益率列
2025-06-24 19:12 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 9, 新增列数: 4
2025-06-24 19:12 | INFO     | FactorLoader.get_fct_df: 开始计算5个因子表达式
2025-06-24 19:12 | INFO     | FactorLoader.get_fct_df: 成功处理4个因子和收益率列
2025-06-24 19:18 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 22, 新增列数: 5
2025-06-24 19:18 | INFO     | FactorLoader.get_fct_df: 开始计算13个因子表达式
2025-06-24 19:18 | INFO     | FactorLoader.get_fct_df: 成功处理11个因子和收益率列
2025-06-24 19:22 | INFO     | FactorLoader.get_fct_df: 识别到3个被使用的auto特征: ['ftool_ABSOLUTE', 'ftool_PERCENTILE(label_1)', 'ftool_diff']
2025-06-24 19:22 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 11, 新增列数: 5
2025-06-24 19:22 | INFO     | FactorLoader.get_fct_df: 开始计算5个因子表达式
2025-06-24 19:22 | INFO     | FactorLoader.get_fct_df: 返回结果: 4个计算因子 + 0个用到的auto特征，共4列
2025-06-24 19:24 | INFO     | FactorLoader.get_fct_df: 识别到3个被使用的auto特征: ['ftool_PERCENTILE(label_1)', 'ftool_ABSOLUTE', 'ftool_diff']
2025-06-24 19:24 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 11, 新增列数: 5
2025-06-24 19:24 | INFO     | FactorLoader.get_fct_df: 开始计算5个因子表达式
2025-06-24 19:24 | INFO     | FactorLoader.get_fct_df: 返回结果: 4个计算因子 + 0个用到的auto特征，共4列
2025-06-24 19:25 | INFO     | FactorLoader.get_fct_df: 识别到3个被使用的auto特征: ['ftool_PERCENTILE(label_1)', 'ftool_ABSOLUTE', 'ftool_diff']
2025-06-24 19:25 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 11, 新增列数: 5
2025-06-24 19:25 | INFO     | FactorLoader.get_fct_df: 开始计算5个因子表达式
2025-06-24 19:25 | INFO     | FactorLoader.get_fct_df: 返回结果: 5个因子（含直接auto特征） + 2个表达式中用到的auto特征，共7列
2025-06-24 19:29 | INFO     | FactorLoader.get_fct_df: 识别到3个被使用的auto特征: ['ftool_PERCENTILE(label_1)', 'ftool_diff', 'ftool_ABSOLUTE']
2025-06-24 19:29 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 11, 新增列数: 5
2025-06-24 19:29 | INFO     | FactorLoader.get_fct_df: 开始计算5个因子表达式
2025-06-24 19:29 | INFO     | FactorLoader.get_fct_df: 返回结果: 5个指定因子
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 识别到1个被使用的auto特征: ['ftool_ABSOLUTE']
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 10, 新增列数: 4
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 开始计算3个因子表达式
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 返回结果: 3个指定因子
2025-06-24 19:30 | ERROR    | FactorLoader.get_fct_df: 输入的base_df为空
2025-06-24 19:30 | WARNING  | FactorLoader.get_fct_df: 因子表达式列表为空
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 识别到0个被使用的auto特征: []
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 10, 新增列数: 4
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-24 19:30 | ERROR    | FactorLoader.get_fct_df: 计算因子 invalid_column 时出现异常: invalid_column——eval异常
2025-06-24 19:30 | WARNING  | FactorLoader.get_fct_df: 没有成功处理任何因子
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 识别到0个被使用的auto特征: []
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 10, 新增列数: 4
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 开始计算2个因子表达式
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 识别到0个被使用的auto特征: []
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 10, 新增列数: 4
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 开始计算2个因子表达式
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 识别到1个被使用的auto特征: ['ftool_PERCENTILE(label_1)']
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 10, 新增列数: 4
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 识别到1个被使用的auto特征: ['ftool_with_special.chars']
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 10, 新增列数: 4
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 识别到2个被使用的auto特征: ['ftool_ABSOLUTE', 'ftool_diff']
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 10, 新增列数: 4
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-24 19:30 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-24 19:31 | INFO     | FactorLoader.get_fct_df: 识别到1个被使用的auto特征: ['ftool_ABSOLUTE']
2025-06-24 19:31 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 6, 新增列数: 2
2025-06-24 19:31 | INFO     | FactorLoader.get_fct_df: 开始计算2个因子表达式
2025-06-24 19:31 | INFO     | FactorLoader.get_fct_df: 识别到1个被使用的auto特征: ['ftool_ABSOLUTE']
2025-06-24 19:31 | WARNING  | FactorLoader.get_fct_df: base_df和auto_df的索引不一致，尝试对齐
2025-06-24 19:31 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 6, 新增列数: 2
2025-06-24 19:31 | INFO     | FactorLoader.get_fct_df: 开始计算2个因子表达式
2025-06-24 19:31 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-24 19:31 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-24 19:31 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-24 19:31 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-24 19:31 | INFO     | FactorLoader.get_fct_df: 识别到1个被使用的auto特征: ['ftool_ABSOLUTE']
2025-06-24 19:31 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 6, 新增列数: 2
2025-06-24 19:31 | INFO     | FactorLoader.get_fct_df: 开始计算3个因子表达式
2025-06-24 19:31 | INFO     | FactorLoader.get_fct_df: 返回结果: 3个指定因子
2025-06-24 19:31 | INFO     | FactorLoader.get_fct_df: 识别到1个被使用的auto特征: ['ftool_ABSOLUTE']
2025-06-24 19:31 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 6, 新增列数: 2
2025-06-24 19:31 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-24 19:31 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-24 19:32 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-24 19:32 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-24 19:32 | INFO     | FactorLoader.get_fct_df: 识别到1个被使用的auto特征: ['ftool_ABSOLUTE']
2025-06-24 19:32 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 6, 新增列数: 2
2025-06-24 19:32 | INFO     | FactorLoader.get_fct_df: 开始计算2个因子表达式
2025-06-24 19:32 | INFO     | FactorLoader.get_fct_df: 返回结果: 2个指定因子
2025-06-24 19:32 | INFO     | FactorLoader.get_fct_df: 识别到1个被使用的auto特征: ['ftool_ABSOLUTE']
2025-06-24 19:32 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 6, 新增列数: 2
2025-06-24 19:32 | INFO     | FactorLoader.get_fct_df: 开始计算3个因子表达式
2025-06-24 19:32 | INFO     | FactorLoader.get_fct_df: 返回结果: 3个指定因子
2025-06-24 19:32 | INFO     | FactorLoader.get_fct_df: 识别到2个被使用的auto特征: ['ftool_ABSOLUTE', 'ftool_diff']
2025-06-24 19:32 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 6, 新增列数: 2
2025-06-24 19:32 | INFO     | FactorLoader.get_fct_df: 开始计算3个因子表达式
2025-06-24 19:32 | INFO     | FactorLoader.get_fct_df: 返回结果: 3个指定因子
2025-06-24 19:33 | INFO     | FactorLoader.get_fct_df: 识别到4个被使用的auto特征: ['ftool_PERCENTILE(label_1)', 'ftool_diff', 'ftool_ABSOLUTE', 'ftool_special.name']
2025-06-24 19:33 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 12, 新增列数: 6
2025-06-24 19:33 | INFO     | FactorLoader.get_fct_df: 开始计算10个因子表达式
2025-06-24 19:33 | INFO     | FactorLoader.get_fct_df: 返回结果: 10个指定因子
2025-06-24 19:35 | INFO     | FactorLoader.get_fct_df: 识别到1个被使用的auto特征: ['ftool_VALUE']
2025-06-24 19:35 | WARNING  | FactorLoader.get_fct_df: base_df和auto_df的索引不一致，尝试对齐
2025-06-24 19:35 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 3, 新增列数: 1
2025-06-24 19:35 | INFO     | FactorLoader.get_fct_df: 开始计算3个因子表达式
2025-06-24 19:35 | INFO     | FactorLoader.get_fct_df: 返回结果: 3个指定因子
2025-06-24 19:46 | INFO     | FactorLoader.get_fct_df: 识别到1个被使用的auto特征: ['ftool_ADD']
2025-06-24 19:46 | WARNING  | FactorLoader.get_fct_df: base_df和auto_df的索引不一致，尝试对齐
2025-06-24 19:46 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 2, 新增列数: 1
2025-06-24 19:46 | INFO     | FactorLoader.get_fct_df: 开始计算1个因子表达式
2025-06-24 19:46 | INFO     | FactorLoader.get_fct_df: 返回结果: 1个指定因子
2025-06-24 19:48 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 19:48 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 19:48 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 19:48 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 19:48 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 19:48 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 19:48 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 19:48 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 19:48 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 19:48 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(9): 9个
2025-06-24 19:48 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 7 列
2025-06-24 19:48 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了2个低方差特征
2025-06-24 19:48 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['ftool_ABSOLUTE(JZ001_10_20)', 'ftool_ABSOLUTE(roc_10)']
2025-06-24 19:48 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 19:48 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 13 (基础5+归一化0+保持0+标签1+筛选后特征7)
2025-06-24 19:48 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 13
2025-06-24 19:48 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 19:48 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 19:49 | INFO     | FactorLoader.get_fct_df: 识别到6个被使用的auto特征: ['ftool_PERCENTILE(label_1)', 'ftool_PERCENTILE(JZ005_rs144)', 'ftool_ABSOLUTE(b_cci_14)', 'ftool_PERCENTILE(WV_MA_20)', 'ftool_LAG(JZ005_rs34, datetime)', 'ftool_PERCENTILE(JZ010_9)']
2025-06-24 19:49 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 11, 新增列数: 6
2025-06-24 19:49 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_add2(ta_cci_25(ftool_ABSOLUTE(b_cci_14), low, ftool_PERCENTILE(label_1)), ts_maxmin_10(ftool_PERCENTILE(WV_MA_20))) 时出现异常: ts_add2(ta_cci_25(ftool_ABSOLUTE(df["b_cci_14"]), df["low"], df["ftool_PERCENTILE(label_1)"]), ts_maxmin_10(df["ftool_PERCENTILE(WV_MA_20)"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_udd_20(ts_median_20(ftool_ABSOLUTE(b_cci_14)), ts_midpoint_5(b_cci_14)) 时出现异常: ts_udd_20(ts_median_20(ftool_ABSOLUTE(df["b_cci_14"])), ts_midpoint_5(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n05(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_n05(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_n10(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_n2(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_05(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_05(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_2(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_2(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(ts_zscore_20(ta_lr_angle_5(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_cons_n2(ts_zscore_20(ta_lr_angle_5(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_inv1(ts_cons_n001(ts_cons_10(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_inv1(ts_cons_n001(ts_cons_10(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_elu1(ts_mean_return_10(ts_cons_n2(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_elu1(ts_mean_return_10(ts_cons_n2(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_mean_return_20(ts_cons_05(ta_minus_di_25(ftool_ABSOLUTE(b_cci_14), b_cci_14, ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_mean_return_20(ts_cons_05(ta_minus_di_25(ftool_ABSOLUTE(df["b_cci_14"]), df["b_cci_14"], ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_adx_25(ts_cons_1(ftool_ABSOLUTE(b_cci_14)), ts_cons_30(ftool_PERCENTILE(JZ010_9)), ts_min_20(ftool_PERCENTILE(label_1))) 时出现异常: ta_adx_25(ts_cons_1(ftool_ABSOLUTE(df["b_cci_14"])), ts_cons_30(df["ftool_PERCENTILE(JZ010_9)"]), ts_min_20(df["ftool_PERCENTILE(label_1)"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_10(ts_if_then_else3(ts_cons_n10(low), ts_day_max_10(ftool_PERCENTILE(WV_MA_20)), ts_cons_n1(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_cons_10(ts_if_then_else3(ts_cons_n10(df["low"]), ts_day_max_10(df["ftool_PERCENTILE(WV_MA_20)"]), ts_cons_n1(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_cci_25(ftool_PERCENTILE(label_1), ftool_ABSOLUTE(b_cci_14), ftool_PERCENTILE(JZ010_9)) 时出现异常: ta_cci_25(df["ftool_PERCENTILE(label_1)"], ftool_ABSOLUTE(df["b_cci_14"]), df["ftool_PERCENTILE(JZ010_9)"])——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_aroonosc_25(high, ftool_ABSOLUTE(b_cci_14)) 时出现异常: ta_aroonosc_25(df["high"], ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | INFO     | FactorLoader.get_fct_df: 返回结果: 85个指定因子
2025-06-24 19:49 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 19:49 | INFO     | FactorLoader.get_fct_df: 识别到6个被使用的auto特征: ['ftool_PERCENTILE(label_1)', 'ftool_PERCENTILE(JZ005_rs144)', 'ftool_ABSOLUTE(b_cci_14)', 'ftool_PERCENTILE(WV_MA_20)', 'ftool_LAG(JZ005_rs34, datetime)', 'ftool_PERCENTILE(JZ010_9)']
2025-06-24 19:49 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 11, 新增列数: 6
2025-06-24 19:49 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_add2(ta_cci_25(ftool_ABSOLUTE(b_cci_14), low, ftool_PERCENTILE(label_1)), ts_maxmin_10(ftool_PERCENTILE(WV_MA_20))) 时出现异常: ts_add2(ta_cci_25(ftool_ABSOLUTE(df["b_cci_14"]), df["low"], df["ftool_PERCENTILE(label_1)"]), ts_maxmin_10(df["ftool_PERCENTILE(WV_MA_20)"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_udd_20(ts_median_20(ftool_ABSOLUTE(b_cci_14)), ts_midpoint_5(b_cci_14)) 时出现异常: ts_udd_20(ts_median_20(ftool_ABSOLUTE(df["b_cci_14"])), ts_midpoint_5(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_2(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_2(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_n2(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n05(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_n05(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_n10(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_05(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_05(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(ts_zscore_20(ta_lr_angle_5(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_cons_n2(ts_zscore_20(ta_lr_angle_5(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_inv1(ts_cons_n001(ts_cons_10(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_inv1(ts_cons_n001(ts_cons_10(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_elu1(ts_mean_return_10(ts_cons_n2(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_elu1(ts_mean_return_10(ts_cons_n2(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_mean_return_20(ts_cons_05(ta_minus_di_25(ftool_ABSOLUTE(b_cci_14), b_cci_14, ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_mean_return_20(ts_cons_05(ta_minus_di_25(ftool_ABSOLUTE(df["b_cci_14"]), df["b_cci_14"], ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_adx_25(ts_cons_1(ftool_ABSOLUTE(b_cci_14)), ts_cons_30(ftool_PERCENTILE(JZ010_9)), ts_min_20(ftool_PERCENTILE(label_1))) 时出现异常: ta_adx_25(ts_cons_1(ftool_ABSOLUTE(df["b_cci_14"])), ts_cons_30(df["ftool_PERCENTILE(JZ010_9)"]), ts_min_20(df["ftool_PERCENTILE(label_1)"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_10(ts_if_then_else3(ts_cons_n10(low), ts_day_max_10(ftool_PERCENTILE(WV_MA_20)), ts_cons_n1(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_cons_10(ts_if_then_else3(ts_cons_n10(df["low"]), ts_day_max_10(df["ftool_PERCENTILE(WV_MA_20)"]), ts_cons_n1(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_cci_25(ftool_PERCENTILE(label_1), ftool_ABSOLUTE(b_cci_14), ftool_PERCENTILE(JZ010_9)) 时出现异常: ta_cci_25(df["ftool_PERCENTILE(label_1)"], ftool_ABSOLUTE(df["b_cci_14"]), df["ftool_PERCENTILE(JZ010_9)"])——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_aroonosc_25(high, ftool_ABSOLUTE(b_cci_14)) 时出现异常: ta_aroonosc_25(df["high"], ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | INFO     | FactorLoader.get_fct_df: 返回结果: 85个指定因子
2025-06-24 19:49 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 19:49 | INFO     | FactorLoader.get_fct_df: 识别到6个被使用的auto特征: ['ftool_PERCENTILE(label_1)', 'ftool_PERCENTILE(JZ005_rs144)', 'ftool_ABSOLUTE(b_cci_14)', 'ftool_PERCENTILE(WV_MA_20)', 'ftool_LAG(JZ005_rs34, datetime)', 'ftool_PERCENTILE(JZ010_9)']
2025-06-24 19:49 | INFO     | FactorLoader.get_fct_df: 合并auto_df成功，总列数: 11, 新增列数: 6
2025-06-24 19:49 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_add2(ta_cci_25(ftool_ABSOLUTE(b_cci_14), low, ftool_PERCENTILE(label_1)), ts_maxmin_10(ftool_PERCENTILE(WV_MA_20))) 时出现异常: ts_add2(ta_cci_25(ftool_ABSOLUTE(df["b_cci_14"]), df["low"], df["ftool_PERCENTILE(label_1)"]), ts_maxmin_10(df["ftool_PERCENTILE(WV_MA_20)"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_udd_20(ts_median_20(ftool_ABSOLUTE(b_cci_14)), ts_midpoint_5(b_cci_14)) 时出现异常: ts_udd_20(ts_median_20(ftool_ABSOLUTE(df["b_cci_14"])), ts_midpoint_5(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n05(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_n05(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n10(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_n10(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_n2(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_05(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_05(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_2(ftool_ABSOLUTE(b_cci_14)) 时出现异常: ts_cons_2(ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_n2(ts_zscore_20(ta_lr_angle_5(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_cons_n2(ts_zscore_20(ta_lr_angle_5(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_inv1(ts_cons_n001(ts_cons_10(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_inv1(ts_cons_n001(ts_cons_10(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_elu1(ts_mean_return_10(ts_cons_n2(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_elu1(ts_mean_return_10(ts_cons_n2(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_mean_return_20(ts_cons_05(ta_minus_di_25(ftool_ABSOLUTE(b_cci_14), b_cci_14, ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_mean_return_20(ts_cons_05(ta_minus_di_25(ftool_ABSOLUTE(df["b_cci_14"]), df["b_cci_14"], ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_adx_25(ts_cons_1(ftool_ABSOLUTE(b_cci_14)), ts_cons_30(ftool_PERCENTILE(JZ010_9)), ts_min_20(ftool_PERCENTILE(label_1))) 时出现异常: ta_adx_25(ts_cons_1(ftool_ABSOLUTE(df["b_cci_14"])), ts_cons_30(df["ftool_PERCENTILE(JZ010_9)"]), ts_min_20(df["ftool_PERCENTILE(label_1)"]))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ts_cons_10(ts_if_then_else3(ts_cons_n10(low), ts_day_max_10(ftool_PERCENTILE(WV_MA_20)), ts_cons_n1(ftool_ABSOLUTE(b_cci_14)))) 时出现异常: ts_cons_10(ts_if_then_else3(ts_cons_n10(df["low"]), ts_day_max_10(df["ftool_PERCENTILE(WV_MA_20)"]), ts_cons_n1(ftool_ABSOLUTE(df["b_cci_14"]))))——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_cci_25(ftool_PERCENTILE(label_1), ftool_ABSOLUTE(b_cci_14), ftool_PERCENTILE(JZ010_9)) 时出现异常: ta_cci_25(df["ftool_PERCENTILE(label_1)"], ftool_ABSOLUTE(df["b_cci_14"]), df["ftool_PERCENTILE(JZ010_9)"])——eval异常
2025-06-24 19:49 | ERROR    | FactorLoader.get_fct_df: 计算因子 ta_aroonosc_25(high, ftool_ABSOLUTE(b_cci_14)) 时出现异常: ta_aroonosc_25(df["high"], ftool_ABSOLUTE(df["b_cci_14"]))——eval异常
2025-06-24 19:49 | INFO     | FactorLoader.get_fct_df: 返回结果: 85个指定因子
2025-06-24 20:01 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-24 20:01 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 20:01 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 20:01 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-24 20:01 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-24 20:01 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 20:01 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-24 20:01 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-24 20:01 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 20:01 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 20:01 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(278): 278个
2025-06-24 20:01 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 168 列
2025-06-24 20:01 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了110个低方差特征
2025-06-24 20:01 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ014_trends_mul2_55', 'JZ012_34', 'STD5', 'JZ014_troc_mul2_55', 'JZ012_21', 'V_2', 'V_0', 'VSUMP_60', 'JZ011_55', 'HIGH2']...
2025-06-24 20:01 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 20:01 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 178 (基础7+归一化2+保持0+标签1+筛选后特征168)
2025-06-24 20:01 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 178
2025-06-24 20:01 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-24 20:01 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 175 列(不含品种列)
2025-06-24 20:02 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 174 个特征中选择了 149 个特征 (base2keep: 4, 相关性选择: 145, 目标列: 1)
2025-06-24 20:02 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-24 20:02 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 149 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-24 20:02 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征69
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(69个)，跳过相关性过滤
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1045, 69), y形状: (1045,)
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 62
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-24 20:02 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=19, 饱和法=20, 平坦法=20 -> 选择=20
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=20, 最大R²=0.2532
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 62
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-24 20:02 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=16, 饱和法=15, 平坦法=6 -> 选择=15
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=15, 最大R²=0.2527
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 62
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-24 20:02 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=17, 饱和法=16, 平坦法=13 -> 选择=16
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=16, 最大R²=0.2508
2025-06-24 20:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-24 20:02 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-24 20:05 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-24 20:05 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 20:05 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 20:05 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-24 20:05 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-24 20:05 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 20:05 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-24 20:05 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-24 20:05 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 20:05 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 20:05 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-24 20:05 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-24 20:05 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-24 20:05 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['b_macd_hist', 'HIGH1', 'b_atr_25', 'MAX5', 'STD5', 'BETA10', 'QTLU5', 'JZ014_troc_mul3_14', 'LOW2', 'CLOSE2']...
2025-06-24 20:05 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 20:05 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-24 20:05 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-24 20:05 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-24 20:05 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-24 20:05 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-24 20:05 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-24 20:05 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-24 20:05 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征69
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(69个)，跳过相关性过滤
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1045, 69), y形状: (1045,)
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 53
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-24 20:05 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=7, 饱和法=7, 平坦法=13 -> 选择=7
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=7, 最大R²=0.2526
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 53
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-24 20:05 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=15, 饱和法=14, 平坦法=7 -> 选择=14
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=14, 最大R²=0.2498
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 53
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-24 20:05 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=9, 饱和法=8, 平坦法=10 -> 选择=9
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=9, 最大R²=0.2473
2025-06-24 20:05 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-24 20:05 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-24 20:06 | WARNING  | FeatSelection.select_features: 缺失特征: {'ftool_DIFF(high)', 'ftool_DIFF(low)', 'ftool_DIFF(alpha012)', 'ftool_ABSOLUTE(b_mom_14)', 'ftool_DIFF(open)', 'ftool_LAG(b_macd_wt_dea, datetime)', 'ftool_ABSOLUTE(alpha012)', 'ftool_LAG(alpha012, datetime)', 'ftool_ABSOLUTE(avg_amount_5_avg_amount_20)', 'ftool_ABSOLUTE(b_cci_14)', 'ftool_DIFF(JZ002_34)'}
2025-06-24 20:06 | INFO     | FeatSelection.select_features: 选择特征: 8个 + base2keep: 4个 + 标签: 1个
2025-06-24 20:06 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-24 20:06 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_gain.png
2025-06-24 20:06 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_weight.png
2025-06-24 20:06 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_cover.png
2025-06-24 20:06 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-24 20:06 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-24 20:09 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-24 20:10 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 20:10 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 20:10 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-24 20:10 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-24 20:10 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 20:10 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-24 20:10 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-24 20:10 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 20:10 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 20:10 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-24 20:10 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-24 20:10 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-24 20:10 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['MIN5', 'MAX5', 'R_0', 'OPEN3', 'b_atr_25', 'JZ014_troc_mul3_9', 'roc_05', 'MA5', 'OPEN2', 'CLOSE4']...
2025-06-24 20:10 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 20:10 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-24 20:10 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-24 20:10 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-24 20:10 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-24 20:10 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-24 20:10 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-24 20:10 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-24 20:10 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征69
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(69个)，跳过相关性过滤
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1045, 69), y形状: (1045,)
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 53
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-24 20:10 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=7, 饱和法=7, 平坦法=13 -> 选择=7
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=7, 最大R²=0.2526
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 53
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-24 20:10 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=15, 饱和法=16, 平坦法=7 -> 选择=15
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=15, 最大R²=0.2520
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 53
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-24 20:10 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=9, 饱和法=8, 平坦法=10 -> 选择=9
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=9, 最大R²=0.2487
2025-06-24 20:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-24 20:10 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-24 20:11 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-24 20:11 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 20:11 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 20:11 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-24 20:11 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-24 20:11 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 20:11 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-24 20:11 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-24 20:11 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 20:11 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 20:11 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-24 20:11 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-24 20:11 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-24 20:11 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ014_troc_mul3_55', 'STD20', 'CLOSE2', 'MA10', 'LOW1', 'b_atr_14', 'R_3', 'JZ012_89', 'BETA60', 'tr_ma20']...
2025-06-24 20:11 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 20:11 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-24 20:11 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-24 20:11 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-24 20:11 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-24 20:11 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-24 20:11 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-24 20:11 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-24 20:11 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征69
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis:   特征数量较少(69个)，跳过相关性过滤
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1045, 69), y形状: (1045,)
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第1/3次运行...
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第2/3次运行...
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 第3/3次运行...
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain Ensemble完成，平均3次运行，有效特征数: 53
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-24 20:11 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] gain 最优特征数算法: 衰减法=7, 饱和法=7, 平坦法=13 -> 选择=7
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=7, 最大R²=0.2526
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第1/3次运行...
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第2/3次运行...
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 第3/3次运行...
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight Ensemble完成，平均3次运行，有效特征数: 53
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-24 20:11 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] weight 最优特征数算法: 衰减法=15, 饱和法=14, 平坦法=7 -> 选择=14
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=14, 最大R²=0.2557
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第1/3次运行...
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第2/3次运行...
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 第3/3次运行...
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover Ensemble完成，平均3次运行，有效特征数: 53
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-24 20:11 | INFO     | FeatSelection._find_optimal_features_intelligent: [SH510050] cover 最优特征数算法: 衰减法=9, 饱和法=8, 平坦法=10 -> 选择=9
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=9, 最大R²=0.2487
2025-06-24 20:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-24 20:11 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-24 20:11 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-24 20:12 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_gain.png
2025-06-24 20:12 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_weight.png
2025-06-24 20:12 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2_cover.png
2025-06-24 20:12 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-24 20:12 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-24 20:13 | INFO     | FeatSelection.select_features: 选择特征: 14个 + base2keep: 4个 + 标签: 1个
2025-06-24 20:14 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 20:14 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 20:14 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 20:14 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 20:14 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 20:14 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 20:14 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 20:14 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 20:14 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 20:14 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-24 20:14 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-24 20:14 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-24 20:14 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 20:14 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-24 20:14 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-24 20:14 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 20:14 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 20:14 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 20:14 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-24 20:14 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 20:14 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 20:14 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-24 20:14 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 20:14 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 20:14 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-24 20:26 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 20:26 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 20:26 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 20:26 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 20:26 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 20:26 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 20:26 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 20:26 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 20:26 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 20:26 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-24 20:26 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-24 20:26 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-24 20:26 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 20:26 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-24 20:26 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-24 20:26 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 20:26 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 20:26 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 20:26 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-24 20:27 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 20:27 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 20:27 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 20:27 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 20:27 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 20:27 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 20:27 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 20:27 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 20:27 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 20:27 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-24 20:27 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-24 20:27 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-24 20:27 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 20:27 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-24 20:27 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-24 20:27 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 20:27 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 20:27 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 20:27 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-24 20:28 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-24 20:28 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-24 20:28 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 20:28 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 20:28 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 20:28 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-24 20:28 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-24 20:28 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-24 20:28 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-24 20:28 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 20:28 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 20:28 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 20:28 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-24 20:28 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 20:29 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 20:29 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-24 20:29 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 20:29 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 20:29 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-24 20:32 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 20:32 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 20:32 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 20:32 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 20:32 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 20:32 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 20:32 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 20:32 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 20:32 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 20:32 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-24 20:32 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-24 20:32 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-24 20:32 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 20:32 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-24 20:32 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-24 20:32 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 20:32 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 20:32 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 20:32 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-24 20:32 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 20:32 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 20:32 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-24 20:32 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 20:32 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 20:32 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
2025-06-24 20:34 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-24 20:34 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-24 20:34 | DEBUG    | BaseObj.normalize_df_all: X: {'algomode': 0, 'n_clip': 6, 'smooth': 0, 'demean': True}
2025-06-24 20:34 | DEBUG    | BaseObj.normalize_df_all: label: {'algomode': 0, 'n_clip': 2, 'smooth': 0, 'demean': False}
2025-06-24 20:34 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-24 20:34 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['open', 'close', 'high', 'low', 'symbol']
2025-06-24 20:34 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(0): []
2025-06-24 20:34 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-24 20:34 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-24 20:34 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(14): 14个
2025-06-24 20:34 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 14 列
2025-06-24 20:34 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了0个低方差特征
2025-06-24 20:34 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-24 20:34 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 20 (基础5+归一化0+保持0+标签1+筛选后特征14)
2025-06-24 20:34 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 20
2025-06-24 20:34 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=执行, label_norm=执行
2025-06-24 20:34 | INFO     | FctsGPMiner.mine: start mining on SH510050...
2025-06-24 20:34 | INFO     | FactorLoader.get_fct_df: 开始计算100个因子表达式
2025-06-24 20:34 | INFO     | FactorLoader.get_fct_df: 返回结果: 100个指定因子
