''' 任务: '轮动-斜率动量-RSRS'
    根据任务书,每日开盘执行买卖(1次)
'''
import pandas as pd

import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))

from live_trade.odb_engine import OrderDBEngine
from config import DB_DIR_LIVE
import shelve
# from xtquant.xttrader import XtQuantTrader
# from xtquant.xttype import StockAccount
# from qmt_trader import MyXtQuantTraderCallback
import time
import random
from datetime import datetime
import threading
global schedule_thread
schedule_thread = None

# 运行交易程序，参数有4个：交易端路径、资金账号、数据库路径、计划任务函数
# 例如：run_stock_trading_bot(r'D:\国金证券QMT交易端\userdata_mini', '*********', r'D:\wenjian\python\smart\data\guojin_account.db', schedule_jobs)
def run_stock_trading_bot(db_name, path, account, slippage):
    e = OrderDBEngine(db_name, path, account, slippage)
    global schedule_thread  # 使用 global 声明以引用全局变量
    lock = threading.Lock()
    while True:
        try:
            if e.is_trade_time():
                connect_result = e.connect()
                if connect_result != 0:
                    print('连接或订阅失败，程序即将重试')
                else:
                    # 检查计划任务线程是否活跃，如果不活跃则启动
                    with lock: # 保证下面代码同时只有一个线程执行
                        if schedule_thread is None or not schedule_thread.is_alive():
                            schedule_thread = threading.Thread(target=sched_bot01, args=(e,)) # todo: def func() 
                            schedule_thread.start()
                    while e.trader:
                        time.sleep(10)
                        if e.is_trade_time() == False:
                            break
                    print("连接断开，即将重新连接")
            else:
                print("当前时间不在运行时段内")
                time.sleep(3600)  # 睡眠一小时后再次检查
        except Exception as e:
            print(f"运行过程中发生错误: {e}")
            time.sleep(3600)  # 如果遇到异常，休息一小时再试  # todo: 需要那么久?

def sched_bot01(e: OrderDBEngine): # task.name = '轮动-斜率动量-RSRS'
    e.run()
    return         
if __name__ == '__main__':
    run_stock_trading_bot(db_name='轮动-斜率动量-RSRS', path= r'D:/p-program/gjQMT_sim/userdata_mini',
                  account='********', slippage=0.01)
  