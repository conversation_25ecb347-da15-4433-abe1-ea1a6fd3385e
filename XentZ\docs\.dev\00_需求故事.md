# 量化因子挖掘与策略研发：需求故事

### 1. 核心目标
本项目致力于构建一个自动化的量化因子（Alpha）挖掘与验证平台。核心任务是从原始市场数据（如开、高、收、低、量）出发，通过基因规划（Genetic Programming, 使用 `gplearn` 库）等方法，系统性地发现、筛选、并最终存储高质量、具有预测能力的交易因子，为下游的量化策略模型提供弹药。

### 2. 关键实体定义
*   **主因子 (f-factor)**: 在每一轮挖掘中，根据预设的适应度函数（如 IC、Sharpe）评价出的一批最优因子。
*   **备选因子 (pj-factor)**: 在挖掘过程中，表现良好但非最优的次级因子。它们同样具有价值，被视为潜在的补充或替代因子，与主因子分开独立存储和管理。
*   **因子动物园 (FactorZoo)**: 一个结构化的数据库系统，作为我们项目的中央因子仓库。所有通过了严格验证的因子（包括主因子和备选因子）都将被序列化、打上标签、并存入此处，以便于后续的查询、分析和模型构建。

当前阶段，我们主要聚焦于 **A股市场** 的 **时序特征因子 (Time-Series Features)**，未来计划将此框架扩展至期货等多品种市场，并支持截面因子。


整个流程是一个从海量可能性中层层筛选、去伪存真的漏斗模型，具体分为以下几个关键阶段：

*   **f-factor (Final Factor)**: 最终因子。通过基因规划（GP）引擎挖掘出的、在初步筛选中表现最优的一批因子。它们是进入下一轮严格验证的主要候选者。
*   **pj-factor (Project Factor / 备选因子)**: 备选因子。在与 `f-factor` 同一次挖掘中产生，表现略逊一筹但仍有潜力（例如，IC排名第二、第三）的因子。我们同样会对其进行跟踪和验证，作为因子库的储备力量。
*   **FactorZoo**: 因子动物园。一个中心化的数据库，作为我们项目的中央因子仓库，用于存储所有通过了最终验证的 `f-factor` 和 `pj-factor` 的元数据和历史因子值。

## 2. 当前技术栈与核心组件

*   **数据与回测引擎 (股票)**: 项目当前针对A股市场的研究，主要依托 `Hikyuu` 量化框架。它提供了底层的数据管理、指标计算和向量化回测能力。本项目的 `datafeed/hku_dataloader.py` 等模块是与 `Hikyuu` 交互的接口。
*   **因子挖掘引擎**: `core/polyfactor/` 目录下的模块，特别是基于 `gplearn` 的基因规划引擎，是本项目自研的核心，用于自动化生成因子。
*   **因子存储**: `FactorZoo` 是我们定制的因子库 (`factorzoo/` 目录)，用于存储、管理和版本化所有经过验证的因子。

## 3. 核心工作流：从因子挖掘到入库

> 这是一个通用的、与具体实现框架（如 `Hikyuu`）解耦的因子生命周期管理流程。

#### 阶段一：因子挖掘与初筛 (L0)
*   **方法**: 使用基因规划（GP）引擎，组合基础数据和数学算子（如 `add`, `ts_corr`, `rank` 等），生成成千上万个候选因子表达式。
*   **产出**: 针对每个挖掘任务，筛选出一批表现最优的 `f-factor` 和一批有潜力的 `pj-factor`。此阶段的筛选标准相对宽松，主要看重因子在样本内的初步表现（如滚动IC均值）。

#### 阶段二：因子精炼与验证 (L1, L2, ...)
*   **目标**: 对初筛通过的因子进行一系列严苛的稳健性测试，剔除过拟合、不稳定或无实际价值的因子。
*   **关键测试流程 (本文档后续内容详述)**:
    1.  **相关性分析**: 剔除与已有因子库中高度相关的冗余因子。
    2.  **特征重要性筛选 (如Lasso)**: 从通过相关性筛选的因子池中，构建一个多变量预测模型（如Lasso回归），进一步筛选出对目标（如未来收益）具有最强联合解释力的核心因子子集。此步骤旨在剔除在集体预测中贡献度低或信息重叠的因子，获得一个更精简、更稳健的因子组合。
    3.  **WFA动态稳健性检验**: 通过滚动窗口的"训练-测试"模拟，检验因子在不同市场阶段的适应性和表现一致性。这是淘汰"伪因子"的核心步骤。
    4.  **样本外(OOS)时间检验**: 在一段完全未见过的历史时段上测试因子，检验其穿越时间的能力。
    5.  **跨品种检验**: 在其他相关品种上测试因子，检验其逻辑的普适性。

#### 阶段三：因子入库
*   **流程**: 只有通过了上述所有验证流程的幸存因子，才会被授予"毕业资格"，命名后存入 `FactorZoo` 数据库。

#### 阶段四：因子应用与策略构建
*   **目标**: 利用 `FactorZoo` 中的因子，构建最终可用于实盘的交易策略。
*   **关键流程**:
    1.  **因子合成与模型训练**:
        *   **方法**: 从 `FactorZoo` 中选取一批高度有效且低相关的因子作为特征（`X`），以未来的N期收益率作为预测目标（`y`）。
        *   **建模**: 训练一个预测模型来学习因子与未来收益之间的关系。模型的复杂度可以从简单到复杂：
            *   **线性模型 (推荐起点)**: 如 `Ridge` 或 `Lasso` 回归。模型会为每个因子学到一个权重，最终的预测值是所有因子值的加权和。这提供了一个稳健且可解释性强的"合成因子" (Combo Factor)。
            *   **非线性模型 (高级选项)**: 如梯度提升树 (`LightGBM`) 或神经网络 (`MLP`)，可以捕捉因子间更复杂的非线性关系。
        *   **产出**: 一个训练好的模型，以及由该模型生成的对未来收益的综合预测信号序列。
    2.  **信号到仓位映射**:
        *   **方法**: 将上述模型产生的综合预测信号，通过我们之前讨论过的 `tanh` S型曲线函数，平滑地映射到 `[-1, 1]` 的目标仓位。此步骤与单因子验证时的逻辑一致，但作用于一个更强大、更鲁棒的合成信号。
    3.  **策略回测与评估**:
        *   **方法**: 在一个独立的、专业的事件驱动回测框架（如 `backtrader`）中，运行完整的交易策略（模型预测 -> 仓位映射 -> 交易执行）。
        *   **评估**: 使用夏普比率、最大回撤、卡玛比率等一系列指标，全面评估最终策略在样本内外的表现，确保其满足上线要求。
    4.  **交易成本与容量评估**:
        *   **方法**: 在回测中引入双向滑点、佣金、市场冲击成本模型；估算不同资金规模下的预期 slippage 与容量上限，确保策略在真实资金规模下仍具备 alpha。
    5.  **上线监控与周期性再训练**:
        *   **方法**: 部署实时监控仪表盘，对信号分布、因子暴露、实时 PnL 与基准偏离度进行监测；设立触发阈值（如 IR 半年下降 50%）自动提示再训练或下线。

## 4. 期货量化研究框架：未来扩展方向
> **目标**: 将当前对股票的研究能力，扩展至中国期货市场。
> **现状**: 此为规划阶段。当前股票部分已基于 `Hikyuu` 实现，而期货部分的技术选型尚在调研中，可能会考虑 `tqsdk` (天勤) 或其他类似框架来处理实时数据和交易接口，同时复用本项目已有的因子挖掘与验证流程。

### 1. 数据准备与质量控制
*   **品种代码(Master Symbol)**: 建立品种层面的唯一标识（如 IF, RB, AU），所有不同月份的合约（如 if2507, rb2510）都归属于一个唯一的"品种代码"。这是组织和查询数据的根基。
*   **点对点(Point-in-Time)元数据**: 除了行情，还必须存储每个合约在每个交易日的**点对点**元数据，如：合约乘数、最小变动价位、保证金率。这些数据可能会变更，回测必须使用历史当时的真实值。
*   **数据源与对齐**: 接入 CTP、交易所 FTP 等源，并将夜盘（21:00-次日02:30）与日盘（09:00-15:00）合并至同一交易日，统一时间戳。

### 2. 连续合约引擎：两大核心产物
> 这是期货研究的基石，应作为一个独立的、可配置的"拼接引擎"模块存在。其核心是产出两种不同用途的连续数据，服务于不同类型的因子。

*   **产物一：价格连续合约 (Price-Continuous Contract)**
    *   **目标**: 构建价格走势平滑的连续序列，用于**趋势、动量类因子**计算。
    *   **方法**: 主力合约切换时，使用**比率复权法 (Ratio Adjustment)**，将旧合约历史价格等比例缩放，消除换月跳空。
*   **产物二：期限结构序列 (Term-Structure Series)**
    *   **目标**: 保持真实的跨期价差，用于**期限结构、基差、套利类因子**计算。
    *   **方法**: 主力合约切换时，**价格不复权**，仅记录切换点前后合约的代码和价差(carry)。因子计算时，直接使用不同月份合约的真实价格。

### 3. 特色因子库：三大类别
*   **时序因子 (Time-Series)**: 直接在"价格连续合约"上计算，如：
    *   动量类: ROC, MACD, RSRS等。
    *   波动率类: ATR, Realized Volatility等。
*   **期限结构因子 (Term-Structure)**: 基于"期限结构序列"或多份合约计算，如：
    *   Carry (展期收益): 近月与远月合约的价差。
    *   Slope/Butterfly: 期限结构的斜率与曲率。
*   **截面与微观结构因子 (Cross-Sectional & Microstructure)**:
    *   持仓与资金流: OI变化率、大宗商品资金流向指数等。
    *   现货相关: 基差率 (Basis = Spot - Future)。

### 4. 稳健性检验
*   **成本模型**: 使用 `commission_per_lot + slippage_points × tick_value` 计算单手交易成本，再乘以成交手数 `lots`，并叠加合约乘数及交易所附加费用。
*   **跨品种检验**: 首选同系商品 (螺纹↔热卷, 豆粕↔菜粕) 或宏观关联强的品种。

### 5. 策略构建与杠杆风险管理
> 期货策略的核心是围绕杠杆进行风险归一化和额度控制。
*   **从"资金百分比"到"风险单位"**: 仓位不再是"分配10%资金"，而是"分配1个标准风险单位"。
    *   **方法**: 先计算单手合约波动金额 `v = ATR_N × 合约乘数`，再用

      ```python
      risk_unit = total_risk_budget / (v × target_sigma)
      ```

      其中 `target_sigma` 为账户允许的目标波动（如 1% NAV）。据此反算应开仓手数，实现跨品种风险等价。
*   **保证金动态校验**: 必须在下单前和持仓中，持续计算并监控**总保证金占用率**，严守上限。
*   **强制风控模块**: 涨跌停、流动性枯竭是常态。需要一个独立的 RiskManager 模块处理：
    *   下单前：止损/止盈单市价转化、交易通道可用性检查。
    *   成交后：`on_position_update` 回调监控保证金，触及红线自动减仓。

### 6. 上线监控
*   实时监控保证金波动、夜盘持仓风险，并对**持仓限额**及流动性阈值进行报警。
*   合约换月数据延迟或拼接异常时，自动告警并暂停交易。
*   **再训练触发**: 满足固定周期（如每 30 个自然日）或 IR 连续 6 周下降 50% 任一条件时，自动进入再训练流程。

### 你的任务
当我提供这段背景后，请理解我的目标是完善这个因子挖掘、验证、及应用的完整流程中的某一个具体环节。你的回答应聚焦于如何更好地实现上述流程中的某个步骤，提供代码建议，或对算法/流程提出改进意见。
