from functools import wraps

import pandas as pd

def calc_by_date(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        other_args = []
        se_args = []
        se_names = []
        for arg in args:
            if type(arg) is not pd.Series:
                other_args.append(arg)
            else:
                se_args.append(arg)
                se_names.append(arg.name)
        if len(se_args) == 1:
            ret = se_args[0].groupby(level=0, group_keys=False).apply(lambda x: func(x, *other_args, **kwargs))
        elif len(se_args) > 1:
            df = pd.concat(se_args, axis=1)
            df.index = se_args[0].index
            ret = df.groupby(level=0, group_keys=False).apply(
                lambda sub_df: func(*[sub_df[name] for name in se_names], *other_args))
            ret.index = df.index
        else:
            print('len(args)==0', func)
        return ret

    return wrapper

def calc_df_by_symbol(func):
    """装饰器：对DataFrame按symbol分组处理 , 不支持func(df1, df2,...) 多df参数情况!
    
    用于处理整个DataFrame的函数，会按symbol分组应用函数。
    函数的第一个参数必须是DataFrame。
    
    处理逻辑：
    1. 如果是多级索引，按第二级(symbol)分组处理
    2. 如果不是多级索引但有'symbol'列，按该列分组处理
    3. 如果既不是多级索引也没有'symbol'列，直接处理整个DataFrame
    """
    @wraps(func)
    def wrapper(df, *args, **kwargs):
        if not isinstance(df, pd.DataFrame):
            raise TypeError("输入必须是pandas DataFrame")
            
        # 检查是否为多级索引
        has_multi_index = hasattr(df.index, 'nlevels') and df.index.nlevels > 1
        
        # 如果是多级索引，按symbol分组处理
        if has_multi_index:
            # 创建结果容器
            result_parts = []
            dict_results = {}  # 新增：收集dict类型结果
            other_results = []  # 新增：收集其他类型结果
            
            # 保存原始索引
            original_index = df.index
            
            # 使用groupby按第二级索引(symbol)分组
            for name, group in df.groupby(level=1):
                # 提取分组，并用第一级索引重置索引，去除多级结构
                reset_group = group.droplevel(1)
                
                # 传递symbol信息给被装饰的函数
                kwargs_with_symbol = kwargs.copy()
                kwargs_with_symbol['current_symbol'] = name
            
                # 对每个分组应用函数
                group_result = func(reset_group, *args, **kwargs_with_symbol)
                
                # 根据返回值类型分别处理
                if isinstance(group_result, pd.DataFrame):
                    # DataFrame类型：保持原有逻辑完全不变
                    if len(group_result) == len(reset_group):
                        # 长度匹配：使用原始分组的索引创建多级索引
                        multi_index = pd.MultiIndex.from_arrays([
                            reset_group.index,
                            [name] * len(reset_group)
                        ])
                        group_result.index = multi_index
                    else:
                        # 长度不匹配：这是统计汇总类型的结果，添加symbol列
                        group_result['symbol'] = name
                    result_parts.append(group_result)
                    
                elif isinstance(group_result, dict):
                    # dict类型：新增处理逻辑
                    dict_results[name] = group_result
                    
                else:
                    # 其他类型：收集但不特殊处理
                    other_results.append((name, group_result))
            
            # 返回逻辑：优先级 DataFrame > dict > 其他
            if result_parts:
                # 有DataFrame结果，按原逻辑合并（保持向后兼容）
                result = pd.concat(result_parts)
                # 检查是否是统计汇总类型的结果（有symbol列）
                if 'symbol' in result.columns:
                    # 统计汇总结果：重置索引并按symbol排序
                    result = result.reset_index(drop=True)
                    return result.sort_values('symbol') if len(result) > 1 else result
                else:
                    # 原始数据格式结果：使用与原始数据相同的顺序
                    if hasattr(original_index, 'levels'):
                        result = result.sort_index(level=list(range(original_index.nlevels)))
                    else:
                        result = result.sort_index()
                return result
                
            elif dict_results:
                # 🔧 修改：始终返回{symbol: dict}格式，保持symbol信息
                return dict_results
                    
            elif other_results:
                # 有其他类型结果，返回最后一个（保持原有行为）
                return other_results[-1][1]
                
            return df  # 如果没有处理，返回原始数据
        
        # 如果不是多级索引，检查是否有symbol列
        elif 'symbol' in df.columns:
            # 创建结果容器
            result_parts = []
            dict_results = {}  # 新增：收集dict类型结果
            other_results = []  # 新增：收集其他类型结果
            
            # 记录原始索引
            original_index = df.index
            
            # 按symbol分组处理
            for name, group in df.groupby('symbol'):
                # 创建一个不包含symbol列的DataFrame副本
                group_no_symbol = group.drop(columns=['symbol'])
                
                # 传递symbol信息给被装饰的函数
                kwargs_with_symbol = kwargs.copy()
                kwargs_with_symbol['current_symbol'] = name
                
                # 应用原始函数
                group_result = func(group_no_symbol, *args, **kwargs_with_symbol)
                
                # 根据返回值类型分别处理
                if isinstance(group_result, pd.DataFrame):
                    # DataFrame类型：保持原有逻辑完全不变
                    # 创建 symbol 列数据
                    symbol_col = pd.DataFrame(
                        {'symbol': [name] * len(group_result)}, 
                        index=group_result.index
                    )
                    # 一次性合并，避免碎片化
                    group_result = pd.concat([group_result, symbol_col], axis=1)
                    result_parts.append(group_result)
                    
                elif isinstance(group_result, dict):
                    # dict类型：新增处理逻辑
                    dict_results[name] = group_result
                    
                else:
                    # 其他类型：收集但不特殊处理
                    other_results.append((name, group_result))
            
            # 返回逻辑：优先级 DataFrame > dict > 其他
            if result_parts:
                # 有DataFrame结果，按原逻辑合并（保持向后兼容）
                result = pd.concat(result_parts)
                # 检查是否有重复索引 - 修复重复索引问题
                if not original_index.duplicated().any():
                    # 如果原始索引没有重复，可以安全使用reindex
                    return result.reindex(original_index)
                else:
                    # 如果原始索引有重复，使用排序确保顺序接近原始数据
                    return result.sort_index()
                    
            elif dict_results:
                # 🔧 修改：始终返回{symbol: dict}格式，保持symbol信息
                return dict_results
                    
            elif other_results:
                # 有其他类型结果，返回最后一个（保持原有行为）
                return other_results[-1][1]
                
            return df  # 如果没有处理，返回原始数据
        
        # 如果既不是多级索引也没有symbol列，直接应用函数
        else:
            return func(df, *args, **kwargs)
            
    return wrapper

def calc_by_symbol(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        other_args = []
        se_args = []
        se_names = []
        for i, arg in enumerate(args):
            if type(arg) is not pd.Series:
                other_args.append(arg)
            else:
                se_args.append(arg)
                if arg.name:
                    se_names.append(str(arg.name))
                else:
                    name = f'arg_{i}'
                    arg.name = name
                    se_names.append(name)
                    
        if len(se_args) == 0:
            print('errors: no Series arguments')
            return None
            
        # 检查是否为多级索引
        has_multi_index = hasattr(se_args[0].index, 'nlevels') and se_args[0].index.nlevels > 1
            
        if not has_multi_index:
            return func(*args, **kwargs)
            
        # 处理多级索引情况
        if len(se_args) == 1:
            # 单个Series的情况
            data = se_args[0]
            result = data.groupby(level=1).apply(lambda x: func(x, *other_args, **kwargs))
            
            # 重建多级索引
            dates = data.index.get_level_values(0).unique()
            symbols = data.index.get_level_values(1).unique()
            multi_idx = pd.MultiIndex.from_product([dates, symbols], names=['datetime', 'symbol'])
            
            # 创建最终结果
            final_result = pd.Series(index=multi_idx, dtype=float)
            for symbol in symbols:
                final_result.loc[(slice(None), symbol)] = result[symbol]
                
            final_result.name = f"{func.__name__}_{se_names[0]}"
            if other_args:
                final_result.name += f"_{other_args[0]}"
            return final_result
            
        else:
            # 多个Series的情况
            # 确保所有Series具有相同的索引
            base_index = se_args[0].index
            for se in se_args[1:]:
                if not se.index.equals(base_index):
                    raise ValueError("All Series must have the same index")
            
            # 按symbol分组处理
            result = pd.Series(index=base_index, dtype=float)
            for symbol in base_index.get_level_values(1).unique():
                symbol_mask = base_index.get_level_values(1) == symbol
                symbol_data = [se[symbol_mask] for se in se_args]
                result[symbol_mask] = func(*symbol_data, *other_args)
            
            result.name = f"{func.__name__}_{'_'.join(se_names)}"
            return result

    return wrapper