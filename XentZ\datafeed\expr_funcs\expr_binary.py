import numpy as np
import pandas as pd
import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent.parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
from datafeed.expr_funcs.expr_utils import calc_by_symbol, calc_by_date

def pair(left, right, func, *args, **kwargs):
    return func(left, right, *args, **kwargs)

@calc_by_symbol
def Sub(left, right):
    return left - right

@calc_by_symbol
def Add(left, right):
    return left + right

@calc_by_symbol
def Mul(left, right):
    return left * right

@calc_by_symbol
def Div(left, right):
    return left / right

@calc_by_symbol
def greater(left, right):
    if type(right) is not pd.Series:
        right = pd.Series([right for _ in range(len(left))])
        right.index = left.index
    return pair(left, right, np.maximum)

@calc_by_symbol
def less(left, right):
    if type(right) is not pd.Series:
        right = pd.Series([right for _ in range(len(left))])
        right.index = left.index
    return pair(left, right, np.minimum)

def _cross_up(left, right):
    left = pd.Series(left)
    right = pd.Series(right)
    diff = left - right
    diff_shift = diff.shift(1)
    return (diff >= 0) & (diff_shift < 0)

@calc_by_symbol
def cross_up(left, right):
    return pair(left, right, _cross_up)

@calc_by_symbol
def cross_down(left, right):
    def _cross_down(left, right):
        left = pd.Series(left)
        right = pd.Series(right)
        diff = left - right
        diff_shift = diff.shift(1)
        return (diff <= 0) & (diff_shift > 0)

    return pair(left, right, _cross_down)

def _slope_pair(se_left, se_right, N=18): # 性能提升200倍!
    slopes = np.full(len(se_left), np.nan)  # 预先分配一个全是NaN的数组
    if len(se_left) < N:
        return pd.Series(slopes, index=se_left.index)
    # 使用向量化操作一次计算所有可能的x和y
    x = np.lib.stride_tricks.sliding_window_view(se_right.values, window_shape=N)
    y = np.lib.stride_tricks.sliding_window_view(se_left.values, window_shape=N)
    # 对每一个窗口计算斜率
    x_mean = x.mean(axis=1)
    y_mean = y.mean(axis=1)
    xy_cov = np.sum((x - x_mean[:, None]) * (y - y_mean[:, None]), axis=1)
    xx_var = np.sum((x - x_mean[:, None])**2, axis=1)

    # 计算斜率，避免除0操作
    with np.errstate(divide='ignore', invalid='ignore'):
        slopes_valid = np.where(xx_var != 0, xy_cov / xx_var, np.nan)
    # 将计算结果放到预分配的数组中
    slopes[N-1:] = slopes_valid
    # 将结果转换为Series并保持原索引
    return pd.Series(slopes, index=se_left.index)

@calc_by_symbol
def slope_pair(left, right, N=18):
    return pair(left, right, _slope_pair, N)

@calc_by_symbol
def udvd(price, volume, N=20):
    ''' 计算带滑动窗口的 Up/Down Volume Difference (UDVD), 
    在 volume 为 0 的位置对应的 UDVD 结果也置为 0 '''
    price_diff = np.diff(price, prepend=price.iloc[0])
    udvd = np.where(price_diff > 0, volume, -volume)
    # 使用 'same' 模式进行卷积计算，保持结果长度与原始数据一致
    udvd_windowed = np.convolve(udvd, np.ones(N), 'same')
    udvd_windowed[volume == 0] = 0
    
    return udvd_windowed

if __name__ == '__main__':
    import time

    # 生成测试数据
    np.random.seed(0)
    data_size = 100000
    se_left = pd.Series(np.random.randn(data_size))
    se_right = pd.Series(np.random.randn(data_size))

    # 原始函数性能测试
    start_time = time.time()
    original_slopes = _slope_pair_original(se_left, se_right, N=18)
    original_duration = time.time() - start_time
    print(f"原始函数耗时: {original_duration:.6f} 秒")

    # 优化后的函数性能测试
    start_time = time.time()
    optimized_slopes = _slope_pair(se_left, se_right, N=18)
    optimized_duration = time.time() - start_time
    print(f"优化后函数耗时: {optimized_duration:.6f} 秒")

    # 验证结果一致性
    result_difference = np.nanmean(np.abs(original_slopes - optimized_slopes))
    print(f"结果平均差异: {result_difference:.6e}")

    # 性能提升比率
    performance_gain = original_duration / optimized_duration
    print(f"性能提升: {performance_gain:.2f} 倍")
