{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# 1.调用linearregression,用来拟合因子值和未来收益率\n", "# TODO 思考用线性回归拟合因子值和未来收益率来得到仓位的映射,和直接用因子值作为仓位有什么不同？\n", "from sklearn.linear_model import LinearRegression\n", "import numpy as np\n", "model = LinearRegression()  \n", "y_pred = np.random.rand(100)\n", "y = np.random.rand(100)\n", "model.fit(y_pred.reshape(-1,1),y)\n", "y_hat = model.predict(y_pred.reshape(-1,1))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.56894426, 0.52391276, 0.49565857, 0.58948075, 0.45710196,\n", "       0.56952498, 0.45793157, 0.43907719, 0.43584637, 0.46139746,\n", "       0.48704323, 0.55666496, 0.42813158, 0.42645773, 0.54151775,\n", "       0.50452684, 0.45594319, 0.46690054, 0.56100661, 0.51987287,\n", "       0.60476981, 0.48843795, 0.47864031, 0.58032491, 0.4212535 ,\n", "       0.50124584, 0.4423327 , 0.54608364, 0.47335216, 0.54964602,\n", "       0.53880699, 0.56839465, 0.46043056, 0.58773578, 0.48169562,\n", "       0.43646743, 0.52180666, 0.52551165, 0.52107198, 0.47069939,\n", "       0.50533592, 0.51513804, 0.59533134, 0.45836344, 0.57965108,\n", "       0.42469484, 0.54188744, 0.5428156 , 0.49991787, 0.4587728 ,\n", "       0.59302868, 0.42680976, 0.48798287, 0.4794496 , 0.47576136,\n", "       0.55552611, 0.55864885, 0.52191055, 0.42914131, 0.58482694,\n", "       0.48694553, 0.45423889, 0.45516079, 0.55821094, 0.57696031,\n", "       0.47631682, 0.57404213, 0.58455723, 0.46468753, 0.45276398,\n", "       0.42980852, 0.51323761, 0.47236716, 0.48015063, 0.59561131,\n", "       0.59565063, 0.55788587, 0.51026549, 0.59076365, 0.47383718,\n", "       0.52978078, 0.42331999, 0.5345159 , 0.57851721, 0.58859319,\n", "       0.44659084, 0.47495565, 0.46762883, 0.49135462, 0.59806333,\n", "       0.46885351, 0.52689257, 0.45678432, 0.59465776, 0.50727996,\n", "       0.55173657, 0.53969458, 0.48693639, 0.4786526 , 0.54360824])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["y_hat"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def _DIVP(x1, x2):  # 零分母保护的除法\n", "    x1 = x1.flatten().astype(np.double)\n", "    x2 = x2.flatten().astype(np.double)\n", "\n", "    # x = np.nan_to_num(np.where(x2 != 0, np.divide(x1, x2), 0))\n", "    # 改进的写法，通过在x2为0的位置直接使用np.nan替代，然后进行除法，最后用np.nan_to_num处理可能的NaN值，可以避免除以0的问题\n", "    x = np.nan_to_num(x1 / np.where(x2 == 0, np.nan, x2))\n", "\n", "    return x"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import talib as ta\n", "def norm(x1, n=20):  # 均值标准差标准化,\n", "    x1 = x1.flatten().astype(np.double)\n", "    x1 = np.nan_to_num(x1)\n", "    x1 = x1-ta.MA(x1,n)\n", "    x_value = _DIVP(x1, ta.STDDEV(x1, n))\n", "    return np.nan_to_num(x_value)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.00000000e+00 0.00000000e+00 0.00000000e+00 0.00000000e+00\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 0.00000000e+00\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 0.00000000e+00\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 0.00000000e+00\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 0.00000000e+00\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 0.00000000e+00\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 0.00000000e+00\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 0.00000000e+00\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 0.00000000e+00\n", " 0.00000000e+00 0.00000000e+00 1.74806242e-01 9.48857073e-01\n", " 7.82255289e-01 1.86817410e-01 1.52803782e+00 2.75140190e+00\n", " 2.48504948e+00 3.18333463e+00 2.28128662e+00 3.20866752e-02\n", " 9.32491850e-01 7.58381178e-01 2.66380468e+00 3.12158089e+00\n", " 1.15355613e+00 8.98610551e-02 7.14710628e-02 1.56390093e+00\n", " 2.56089209e-03 7.61903189e-01 1.74855248e+00 2.89936457e+00\n", " 1.83603418e+00 5.36027258e-01 9.72811054e-02 1.89464701e+00\n", " 3.51984605e-01 2.00089836e+00 1.89593145e+00 1.09614257e-01\n", " 2.20042588e+00 2.10147291e-01 2.90742340e-01 1.48109858e+00\n", " 7.83643064e-01 1.51560141e-01 2.04640652e+00 8.92358232e-02\n", " 6.70131439e-01 8.39323893e-01 1.32681285e+00 2.05300714e+00\n", " 1.00222328e+00 1.87896203e+00 1.91426278e+00 7.77959039e-01\n", " 1.77008677e-01 2.50499181e+00 5.73666873e-01 4.94319783e-02\n", " 4.04687099e-01 1.80354834e+00 2.38871438e+00 1.05948356e+00\n", " 1.25106366e+00 2.34883918e+00 1.46901060e+00 8.89290812e-01\n", " 2.09121625e-01 1.00682577e+00 8.19773389e-02 1.25348983e+00]\n"]}], "source": ["# 2.把仓位大小做一次滚动标准化处理,得到接近100%的仓位\n", "pos = norm(y_hat)  \n", "pos = pos.clip(-2,2)  # 剪裁仓位，转换到-200%到200%之间\n", "position_changes = np.abs(np.concatenate((np.array([0]),np.diff(pos))))  # 计算仓位变动\n", "net_values = 1+(pos*y-position_changes*0.002).cumsum()  # 计算净值,其中0.002是仓位变动的磨损"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}