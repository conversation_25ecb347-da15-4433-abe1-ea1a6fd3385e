import streamlit as st
from pathlib import Path
import sys
# Add project root to path
root = Path(__file__).parent.parent.parent
if str(root) not in sys.path:
    sys.path.append(str(root))

from gui.pages.utils import render_task_page
from task_defs.task_fctsmine import Fcts_GP_Mining  # 使用已有的任务类
from config import CONFIG_DIR  # 配置文件目录

def main():
    """任务配置页面"""
    # 检查页面访问权限
    if st.session_state.get('current_page') != "任务配置":
        st.switch_page("Home.py")
        return
        
    # 初始化任务对象（全局唯一）
    if 'task_mining' not in st.session_state:
        st.session_state.task_mining = Fcts_GP_Mining()
        config_path = CONFIG_DIR / 'task_mining.toml'
        if config_path.exists():
            st.session_state.task_mining.load_config(str(config_path))
            
    # 渲染任务配置页面，传入 task 对象
    render_task_page(st.session_state.task_mining)

if __name__ == "__main__":
    main() 