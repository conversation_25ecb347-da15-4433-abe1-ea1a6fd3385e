import sqlite3
import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent.parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
    
from config import DB_DIR_FACTOR

db_path = DB_DIR_FACTOR.joinpath('jzal.sqlite')
conn = sqlite3.connect(db_path)
cursor = conn.cursor()
# 创建gp_tasks表
cursor.execute('''
    CREATE TABLE IF NOT EXISTS gp_tasks (
        task_uid TEXT PRIMARY KEY, -- 设定task_uid为主键,确保唯一性
        symbol TEXT NOT NULL,
        freqs TEXT NOT NULL,
        bgn_date TEXT NOT NULL,
        end_date TEXT NOT NULL,
        fee_rate REAL NOT NULL,
        split_perc REAL NOT NULL,
        UNIQUE (symbol, freqs, bgn_date, end_date, fee_rate, split_perc) -- 添加唯一索引以防止指定字段组合重复
    )
''')
# 创建gp_factors表

conn.commit()
conn.close()