import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error
import statsmodels.api as sm

# 生成示例数据
np.random.seed(0)
X = np.random.rand(100, 1)
y = 0.5 * X[:, 0] + np.random.randn(100) * 0.1

# 增加常数列
X = sm.add_constant(X)

# 使用 sklearn 的 LinearRegression
model_sklearn = LinearRegression()
model_sklearn.fit(X, y)

# sklearn 输出
print("sklearn LinearRegression 输出：")
print(f"Intercept: {model_sklearn.intercept_}")
print(f"Coefficients: {model_sklearn.coef_}")

# 使用 statsmodels 的 OLS
model_sm = sm.OLS(y, X)
results_sm = model_sm.fit()

# statsmodels 输出
print("\nstatsmodels OLS 输出：")
print(results_sm.summary())

# 比较系数和截距
intercept_sklearn = model_sklearn.intercept_
coefficients_sklearn = model_sklearn.coef_

intercept_sm = results_sm.params[0]
coefficients_sm = results_sm.params[1:]

print("\n对比输出：")
print(f"sklearn Intercept: {intercept_sklearn}")
print(f"statsmodels Intercept: {intercept_sm}")
print(f"sklearn Coefficients: {coefficients_sklearn}")
print(f"statsmodels Coefficients: {coefficients_sm}")