2025-06-19 14:27 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 14:27 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 14:27 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 14:27 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 14:27 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 14:27 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 14:27 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 14:27 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 14:27 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 14:27 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 14:27 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 14:27 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 14:27 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 14:27 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['R_4', 'V_4', 'QTLD10', 'VSUMP_60', 'JZ014_troc_mul2_21', 'LOW1', 'V_3', 'JZ004_10', 'JZ011_55', 'tr_ma5']...
2025-06-19 14:27 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 14:27 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 14:27 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 14:27 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 14:27 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 14:27 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-19 14:27 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 14:27 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-19 14:27 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 14:27 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 70 列, 1045 行
2025-06-19 14:29 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 290 -> 257
2025-06-19 14:29 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 257 个特征 (窗口=20)
2025-06-19 14:29 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-19 14:29 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 326 个特征中保留了 198 个特征 (base2keep: 4, 冗余筛选: 194, 相关性阈值=0.85, 聚类数=195)
2025-06-19 14:29 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 14:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析开始: 198个特征
2025-06-19 14:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight重要性分析完成: 最优特征数=3, 最大R²=0.9992
2025-06-19 14:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain重要性分析完成: 最优特征数=3, 最大R²=0.9991
2025-06-19 14:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover重要性分析完成: 最优特征数=4, 最大R²=0.9991
2025-06-19 14:29 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 14:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析4种类型
2025-06-19 14:38 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 14:39 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 14:39 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 14:39 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 14:39 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 14:39 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 14:39 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 14:39 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 14:39 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 14:39 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 14:39 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 14:39 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 14:39 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 14:39 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['R_0', 'MAX5', 'LOW2', 'HIGH1', 'STD5', 'HIGH4', 'JZ011_14', 'R_6', 'JZ008_55', 'JZ012_21']...
2025-06-19 14:39 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 14:39 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 14:39 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 14:39 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 14:39 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 14:39 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-19 14:39 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 14:39 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-19 14:39 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 14:39 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 70 列, 1045 行
2025-06-19 14:41 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 290 -> 257
2025-06-19 14:41 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 257 个特征 (窗口=20)
2025-06-19 14:41 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-19 14:41 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 326 个特征中保留了 198 个特征 (base2keep: 4, 冗余筛选: 194, 相关性阈值=0.85, 聚类数=195)
2025-06-19 14:41 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 14:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析开始: 198个特征
2025-06-19 14:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight重要性分析完成: 最优特征数=3, 最大R²=0.9992
2025-06-19 14:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain重要性分析完成: 最优特征数=3, 最大R²=0.9991
2025-06-19 14:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover重要性分析完成: 最优特征数=4, 最大R²=0.9991
2025-06-19 14:41 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 14:41 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析4种类型
2025-06-19 14:41 | ERROR    | FeatImportanceViz.plot_importance_comparison: 绘制重要性对比图失败: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-19 14:41 | WARNING  | FeatImportanceViz.plot_marginal_r2: 未找到gain重要性数据
2025-06-19 14:41 | ERROR    | FeatImportanceViz.plot_combined_analysis: 绘制综合分析图失败: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-19 14:41 | ERROR    | FeatImportanceViz.save_importance_table: 保存重要性表格失败: At least one sheet must be visible
2025-06-19 14:43 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 14:43 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 14:43 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 14:43 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 14:43 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 14:43 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 14:43 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 14:43 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 14:43 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 14:43 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 14:43 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 14:43 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 14:43 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 14:43 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ014_troc_mul2_34', 'BETA5', 'JZ011_21', 'JZ008_9', 'JZ012_89', 'R_1', 'JZ014_troc_mul2_9', 'JZ004_80', 'JZ012_21', 'STD10']...
2025-06-19 14:43 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 14:43 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 14:43 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 14:43 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 14:43 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 14:43 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-19 14:43 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 14:43 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-19 14:43 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 14:43 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 70 列, 1045 行
2025-06-19 14:46 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 290 -> 257
2025-06-19 14:46 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 257 个特征 (窗口=20)
2025-06-19 14:46 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-19 14:46 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 326 个特征中保留了 198 个特征 (base2keep: 4, 冗余筛选: 194, 相关性阈值=0.85, 聚类数=195)
2025-06-19 14:46 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 14:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析开始: 198个特征
2025-06-19 14:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight重要性分析完成: 最优特征数=3, 最大R²=0.9992
2025-06-19 14:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain重要性分析完成: 最优特征数=3, 最大R²=0.9991
2025-06-19 14:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover重要性分析完成: 最优特征数=4, 最大R²=0.9991
2025-06-19 14:46 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 14:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析4种类型
2025-06-19 14:46 | ERROR    | FeatImportanceViz.plot_importance_comparison: 绘制重要性对比图失败: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-19 14:46 | ERROR    | FeatImportanceViz.plot_marginal_r2: 绘制R²累积贡献图失败: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-19 14:46 | ERROR    | FeatImportanceViz.plot_combined_analysis: 绘制综合分析图失败: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-19 14:46 | ERROR    | FeatImportanceViz.save_importance_table: 保存重要性表格失败: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-19 14:49 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 14:49 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 14:49 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 14:49 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 14:49 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 14:49 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 14:49 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 14:49 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 14:49 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 14:49 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 14:49 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 14:49 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 14:49 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 14:49 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['R_6', 'JZ004_120', 'JZ014_troc_mul3_9', 'BETA20', 'JZ014_troc_mul2_9', 'LOW2', 'MAX10', 'HIGH0', 'V_2', 'OPEN3']...
2025-06-19 14:49 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 14:49 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 14:49 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 14:49 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 14:49 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 14:49 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-19 14:49 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 14:49 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-19 14:49 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 14:49 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 70 列, 1045 行
2025-06-19 14:51 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 290 -> 257
2025-06-19 14:51 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 257 个特征 (窗口=20)
2025-06-19 14:51 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-19 14:51 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 326 个特征中保留了 198 个特征 (base2keep: 4, 冗余筛选: 194, 相关性阈值=0.85, 聚类数=195)
2025-06-19 14:51 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 14:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析开始: 198个特征
2025-06-19 14:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight重要性分析完成: 最优特征数=3, 最大R²=0.9992
2025-06-19 14:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain重要性分析完成: 最优特征数=3, 最大R²=0.9991
2025-06-19 14:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover重要性分析完成: 最优特征数=4, 最大R²=0.9991
2025-06-19 14:51 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 14:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析4种类型
2025-06-19 14:51 | WARNING  | FeatImportanceViz.plot_importance_comparison: 分析结果为空，无法绘制图表
2025-06-19 14:51 | WARNING  | FeatImportanceViz.plot_marginal_r2: 未找到gain重要性数据
2025-06-19 14:51 | WARNING  | FeatImportanceViz.plot_combined_analysis: 分析结果为空
2025-06-19 14:51 | WARNING  | FeatImportanceViz.save_importance_table: 分析结果为空，无法保存表格
2025-06-19 15:13 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 15:13 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 15:13 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 15:13 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 15:13 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 15:13 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 15:13 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 15:13 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 15:13 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 15:13 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 15:13 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 15:13 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 15:13 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 15:13 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['KMID', 'VSUMN_60', 'smadiff5', 'BETA10', 'KLEN', 'b_atr_25', 'JZ014_trends_mul2_21', 'b_atr_60', 'JZ014_trends_mul3_34', 'MAX5']...
2025-06-19 15:13 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 15:13 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 15:13 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 15:13 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 15:13 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 15:13 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-19 15:13 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 15:13 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-19 15:13 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 15:13 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 70 列, 1045 行
2025-06-19 15:15 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 290 -> 257
2025-06-19 15:15 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 257 个特征 (窗口=20)
2025-06-19 15:15 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-19 15:15 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 326 个特征中保留了 198 个特征 (base2keep: 4, 冗余筛选: 194, 相关性阈值=0.85, 聚类数=195)
2025-06-19 15:15 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 15:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析开始: 198个特征
2025-06-19 15:15 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight重要性分析完成: 最优特征数=3, 最大R²=0.9992
2025-06-19 15:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain重要性分析完成: 最优特征数=3, 最大R²=0.9991
2025-06-19 15:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover重要性分析完成: 最优特征数=4, 最大R²=0.9991
2025-06-19 15:16 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 15:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析4种类型
2025-06-19 15:18 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 15:18 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 15:18 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 15:18 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 15:18 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 15:18 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 15:18 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 15:18 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 15:18 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 15:18 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 15:18 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 15:18 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 15:18 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 15:18 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ014_troc_mul3_34', 'smadiff5', 'JZ004_120', 'JZ008_9', 'KSFT', 'JZ011_21', 'MA5', 'KUP', 'R_5', 'JZ011_9']...
2025-06-19 15:18 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 15:18 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 15:18 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 15:18 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 15:18 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 15:18 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-19 15:18 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 15:18 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-19 15:18 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 15:18 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 70 列, 1045 行
2025-06-19 15:20 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 290 -> 257
2025-06-19 15:20 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 257 个特征 (窗口=20)
2025-06-19 15:20 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-19 15:20 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 326 个特征中保留了 198 个特征 (base2keep: 4, 冗余筛选: 194, 相关性阈值=0.85, 聚类数=195)
2025-06-19 15:20 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 15:20 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析开始: 198个特征
2025-06-19 15:20 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight重要性分析完成: 最优特征数=3, 最大R²=0.9992
2025-06-19 15:20 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain重要性分析完成: 最优特征数=3, 最大R²=0.9991
2025-06-19 15:20 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover重要性分析完成: 最优特征数=4, 最大R²=0.9991
2025-06-19 15:20 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 15:20 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析4种类型
2025-06-19 15:21 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-19 15:21 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 15:21 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 15:22 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.xlsx
2025-06-19 15:37 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 15:37 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 15:37 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 15:37 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 15:37 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 15:37 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 15:37 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 15:37 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 15:37 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 15:37 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 15:37 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 15:37 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 15:37 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 15:37 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['VSUMN_60', 'JZ011_9', 'V_2', 'b_atr_14', 'JZ014_troc_mul2_21', 'STD10', 'b_atr_25', 'QTLU10', 'JZ012_60', 'CLOSE2']...
2025-06-19 15:37 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 15:37 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 15:37 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 15:37 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 15:37 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 15:37 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-19 15:37 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 15:37 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-19 15:37 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 15:37 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 70 列, 1045 行
2025-06-19 15:39 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 290 -> 257
2025-06-19 15:39 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 257 个特征 (窗口=20)
2025-06-19 15:39 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-19 15:39 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 326 个特征中保留了 198 个特征 (base2keep: 4, 冗余筛选: 194, 相关性阈值=0.85, 聚类数=195)
2025-06-19 15:39 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 15:39 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析开始: 198个特征
2025-06-19 15:39 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight重要性分析完成: 最优特征数=3, 最大R²=0.9992
2025-06-19 15:39 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain重要性分析完成: 最优特征数=3, 最大R²=0.9991
2025-06-19 15:39 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover重要性分析完成: 最优特征数=4, 最大R²=0.9991
2025-06-19 15:39 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 15:39 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析4种类型
2025-06-19 15:39 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-19 15:40 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 15:40 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 15:45 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.xlsx
2025-06-19 15:54 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 15:54 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 15:54 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 15:54 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 15:54 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 15:54 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 15:54 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 15:54 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 15:54 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 15:54 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 15:54 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 15:54 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 15:54 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 15:54 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ011_14', 'JZ014_troc_mul3_34', 'JZ012_21', 'VSUMN_60', 'smadiff10', 'LOW4', 'JZ011_55', 'CLOSE3', 'b_atr_60', 'STD60']...
2025-06-19 15:54 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 15:54 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 15:54 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 15:54 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 15:54 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 15:54 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-19 15:54 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 15:54 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-19 15:54 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 15:54 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 70 列, 1045 行
2025-06-19 15:57 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 290 -> 257
2025-06-19 15:57 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 257 个特征 (窗口=20)
2025-06-19 15:57 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-19 15:57 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 326 个特征中保留了 198 个特征 (base2keep: 4, 冗余筛选: 194, 相关性阈值=0.85, 聚类数=195)
2025-06-19 15:57 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] ==================== 数据诊断开始 ====================
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 📊 基本信息:
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - 样本数量: 1045
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - 特征数量: 198
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - 目标列: ['label_1']
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 🎯 目标变量分析:
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - 数值范围: [-0.071995, 0.068308]
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - 均值: -0.000173
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - 标准差: 0.011500
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - 变异系数: 66.643372
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - 唯一值数量: 1017
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 🔗 特征-目标相关性分析:
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - VOLUME4: 0.036005
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - ftool_PERCENTILE(JZ001_20_30): -0.018343
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - JZ007_34: 0.095701
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - ftool_LAG(CNTN60, datetime): 0.021975
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - b_aroon_14: -0.014229
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - JZ009_89: -0.043178
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - ftool_PERCENTILE(JZ009_14): -0.002592
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - ftool_ABSOLUTE(low): -0.053977
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - CNTP20: -0.023854
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - ftool_DIFF(JZ009_89): -0.040664
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 🔗 高相关性特征对:
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - open <-> high: 0.998127
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - open <-> low: 0.997898
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - open <-> close: 0.995914
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - high <-> low: 0.997400
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - high <-> close: 0.998241
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 🔍 数据质量检查:
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   ✅ 无缺失值
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   ✅ 无常数特征
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis:   - 异常值总数(前10个特征): 76
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] ==================== 数据诊断结束 ====================
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析开始: 198个特征
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight重要性分析完成: 最优特征数=3, 最大R²=0.9992
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain重要性分析完成: 最优特征数=3, 最大R²=0.9991
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover重要性分析完成: 最优特征数=4, 最大R²=0.9991
2025-06-19 15:57 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 15:57 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析4种类型
2025-06-19 15:57 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-19 15:58 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 15:58 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 15:58 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.xlsx
2025-06-19 16:26 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 16:26 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 16:26 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 16:26 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 16:26 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 16:26 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 16:26 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 16:26 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 16:26 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 16:26 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 16:26 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 16:26 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 16:26 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 16:26 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['HIGH0', 'OPEN2', 'JZ014_troc_mul2_9', 'LOW1', 'JZ014_troc_mul3_14', 'QTLD10', 'CLOSE1', 'OPEN4', 'R_3', 'JZ004_10']...
2025-06-19 16:26 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 16:26 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 16:26 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 16:26 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 16:26 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 16:27 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-19 16:27 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 16:27 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-19 16:27 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 16:27 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 70 列, 1045 行
2025-06-19 16:29 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 290 -> 257
2025-06-19 16:29 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 257 个特征 (窗口=20)
2025-06-19 16:29 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-19 16:29 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 326 个特征中保留了 198 个特征 (base2keep: 4, 冗余筛选: 194, 相关性阈值=0.85, 聚类数=195)
2025-06-19 16:29 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 16:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征198
2025-06-19 16:29 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余195个
2025-06-19 16:29 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析0种类型
2025-06-19 16:29 | WARNING  | BaseObj.plot_importance_comparison: 未找到重要性数据
2025-06-19 16:29 | WARNING  | BaseObj.plot_marginal_r2: 未找到gain重要性数据
2025-06-19 16:29 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 16:30 | WARNING  | BaseObj.save_importance_table: 没有有效的重要性数据可保存
2025-06-19 16:34 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 16:34 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 16:34 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 16:34 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 16:34 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 16:34 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 16:34 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 16:34 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 16:34 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 16:34 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 16:34 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 16:34 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 16:34 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 16:34 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ011_34', 'CLOSE2', 'MIN10', 'JZ012_34', 'JZ011_55', 'JZ008_18', 'roc_02', 'R_1', 'JZ004_30', 'b_atr_25']...
2025-06-19 16:34 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 16:34 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 16:34 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 16:34 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 16:34 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 16:34 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-19 16:34 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 16:34 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-19 16:34 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 16:34 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 70 列, 1045 行
2025-06-19 16:37 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 290 -> 257
2025-06-19 16:37 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 257 个特征 (窗口=20)
2025-06-19 16:37 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-19 16:37 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 326 个特征中保留了 198 个特征 (base2keep: 4, 冗余筛选: 194, 相关性阈值=0.85, 聚类数=195)
2025-06-19 16:37 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 16:37 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征198
2025-06-19 16:37 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余195个
2025-06-19 16:37 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析0种类型
2025-06-19 16:37 | WARNING  | BaseObj.plot_importance_comparison: 未找到重要性数据
2025-06-19 16:37 | WARNING  | BaseObj.plot_marginal_r2: 未找到gain重要性数据
2025-06-19 16:37 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 16:37 | WARNING  | BaseObj.save_importance_table: 没有有效的重要性数据可保存
2025-06-19 16:39 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 16:40 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 16:40 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 16:40 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 16:40 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 16:40 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 16:40 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 16:40 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 16:40 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 16:40 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 16:40 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 16:40 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 16:40 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 16:40 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['CLOSE4', 'BETA20', 'JZ008_34', 'HIGH3', 'QTLD10', 'JZ014_troc_mul3_9', 'QTLU10', 'BETA5', 'b_atr_25', 'JZ004_20']...
2025-06-19 16:40 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 16:40 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 16:40 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 16:40 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 16:40 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 16:40 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 147 个特征 (base2keep: 4, 相关性选择: 143, 目标列: 1)
2025-06-19 16:40 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 16:40 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 147 个特征中保留了 69 个特征 (base2keep: 4, 冗余筛选: 65, 相关性阈值=0.85, 聚类数=66)
2025-06-19 16:40 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 16:40 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 70 列, 1045 行
2025-06-19 16:42 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 特征筛选: 290 -> 257
2025-06-19 16:42 | INFO     | FeatEngineering.featuretools_extract: [SH510050] 特征衍生完成: 新增 257 个特征 (窗口=20)
2025-06-19 16:42 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] 替换了 2 个时间特征为归一化版本: ['DAY(datetime)', 'MONTH(datetime)'] → ['DAY(datetime)_norm', 'MONTH(datetime)_norm']
2025-06-19 16:42 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 326 个特征中保留了 198 个特征 (base2keep: 4, 冗余筛选: 194, 相关性阈值=0.85, 聚类数=195)
2025-06-19 16:42 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1045, 原始特征198
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余195个
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['weight', 'gain', 'cover']
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1045, 195), y形状: (1045,)
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始训练模型...
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 模型训练完成
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始获取重要性...
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性字典长度: 40
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性键样例: ['VOLUME4', 'ftool_PERCENTILE(JZ001_20_30)', 'JZ007_34']
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始特征映射, 特征数: 195
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 映射完成, 有效特征数: 0
2025-06-19 16:42 | WARNING  | FeatSelection.xgb_importance_analysis: [SH510050] weight 特征映射失败
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始训练模型...
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 模型训练完成
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始获取重要性...
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性字典长度: 40
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性键样例: ['VOLUME4', 'ftool_PERCENTILE(JZ001_20_30)', 'JZ007_34']
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始特征映射, 特征数: 195
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 映射完成, 有效特征数: 0
2025-06-19 16:42 | WARNING  | FeatSelection.xgb_importance_analysis: [SH510050] gain 特征映射失败
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始训练模型...
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 模型训练完成
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始获取重要性...
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性字典长度: 40
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性键样例: ['VOLUME4', 'ftool_PERCENTILE(JZ001_20_30)', 'JZ007_34']
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始特征映射, 特征数: 195
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 映射完成, 有效特征数: 0
2025-06-19 16:42 | WARNING  | FeatSelection.xgb_importance_analysis: [SH510050] cover 特征映射失败
2025-06-19 16:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析0种类型
2025-06-19 16:42 | WARNING  | BaseObj.plot_importance_comparison: 未找到重要性数据
2025-06-19 16:42 | WARNING  | BaseObj.plot_marginal_r2: 未找到gain重要性数据
2025-06-19 16:42 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 16:42 | WARNING  | BaseObj.save_importance_table: 没有有效的重要性数据可保存
2025-06-19 21:54 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 21:54 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 21:54 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 21:54 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 21:54 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 21:54 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 21:54 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 21:54 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 21:54 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 21:54 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 21:54 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 21:54 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 21:54 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 21:54 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['tr_ma20', 'BETA10', 'BETA5', 'BETA60', 'JZ014_troc_mul3_14', 'JZ014_troc_mul3_34', 'KLEN', 'STD30', 'OPEN2', 'V_4']...
2025-06-19 21:54 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 21:54 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 21:54 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 21:54 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 21:54 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 21:54 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 21:54 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 21:54 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 21:54 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 21:54 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 21:54 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 21:54 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 21:54 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 21:54 | ERROR    | FeatSelection.xgb_importance_analysis: 导入错误: No module named 'xgboost'
2025-06-19 21:54 | WARNING  | BaseObj.plot_importance_comparison: 未找到重要性数据
2025-06-19 21:54 | WARNING  | BaseObj.plot_marginal_r2: 未找到gain重要性数据
2025-06-19 21:54 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 21:55 | WARNING  | BaseObj.save_importance_table: 没有有效的重要性数据可保存
2025-06-19 22:00 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 22:00 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 22:00 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 22:00 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 22:00 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 22:00 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 22:00 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 22:00 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 22:00 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 22:00 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 22:00 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 22:00 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 22:00 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 22:00 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['roc_05', 'OPEN0', 'HIGH4', 'CLOSE2', 'HIGH1', 'LOW1', 'OPEN2', 'CLOSE3', 'VSUMN_60', 'JZ014_troc_mul2_9']...
2025-06-19 22:00 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 22:00 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 22:00 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 22:00 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 22:00 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 22:00 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 22:00 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 22:00 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 22:00 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 22:00 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 22:00 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 22:00 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 22:00 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 22:30 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 22:30 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 22:30 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 22:30 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 22:30 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 22:30 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 22:30 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 22:30 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 22:30 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 22:30 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 22:30 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 22:30 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 22:30 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 22:30 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['HIGH0', 'JZ014_trends_mul3_34', 'QTLD10', 'roc_02', 'JZ004_6', 'LOW1', 'JZ014_trends_mul2_34', 'JZ014_troc_mul2_14', 'STD10', 'BETA10']...
2025-06-19 22:30 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 22:30 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 22:30 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 22:30 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 22:30 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 22:30 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 22:30 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 22:30 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 22:30 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 22:30 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 22:30 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 22:30 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 22:30 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 22:30 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 22:31 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 22:31 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 22:31 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 22:31 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 22:31 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 22:31 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 22:31 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 22:31 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 22:31 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 22:31 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 22:31 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 22:31 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 22:31 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['QTLD5', 'JZ004_58', 'JZ004_80', 'R_4', 'V_1', 'JZ004_98', 'JZ004_6', 'STD5', 'JZ011_21', 'JZ011_9']...
2025-06-19 22:31 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 22:31 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 22:31 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 22:31 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 22:31 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 22:31 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 22:31 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 22:31 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 22:31 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 22:31 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 22:31 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 22:31 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 22:31 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余64个
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['weight', 'gain', 'cover']
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 64), y形状: (1043,)
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始训练模型...
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 模型训练完成
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始获取重要性...
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性字典长度: 38
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始特征映射, 特征数: 64
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 映射完成, 有效特征数: 38
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=6, 最大R²=0.2526
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始训练模型...
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 模型训练完成
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始获取重要性...
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性字典长度: 38
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始特征映射, 特征数: 64
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 映射完成, 有效特征数: 38
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2629
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始训练模型...
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 模型训练完成
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始获取重要性...
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性字典长度: 38
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始特征映射, 特征数: 64
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 映射完成, 有效特征数: 38
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2584
2025-06-19 22:31 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-19 22:31 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 22:31 | WARNING  | BaseObj.plot_importance_comparison: 未找到重要性数据
2025-06-19 22:31 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 22:31 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 22:32 | WARNING  | BaseObj.save_importance_table: 没有有效的重要性数据可保存
2025-06-19 22:37 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 22:37 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 22:37 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 22:37 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 22:37 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 22:37 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 22:37 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 22:37 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 22:37 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 22:37 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 22:37 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 22:37 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 22:37 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 22:37 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ014_troc_mul3_21', 'tr_ma10', 'MAX5', 'b_macd_hist', 'MAX10', 'JZ014_trends_mul2_21', 'CLOSE4', 'QTLU5', 'V_2', 'R_4']...
2025-06-19 22:37 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 22:37 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 22:37 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 22:37 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 22:37 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 22:37 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 22:37 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 22:37 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 22:37 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 22:37 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 22:37 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 22:37 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 22:37 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 22:37 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-19 22:37 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余64个
2025-06-19 22:37 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['weight', 'gain', 'cover']
2025-06-19 22:37 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 64), y形状: (1043,)
2025-06-19 22:37 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 22:37 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-19 22:37 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始训练模型...
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 模型训练完成
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始获取重要性...
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性字典长度: 38
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始特征映射, 特征数: 64
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 映射完成, 有效特征数: 38
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=6, 最大R²=0.2526
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始训练模型...
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 模型训练完成
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始获取重要性...
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性字典长度: 38
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始特征映射, 特征数: 64
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 映射完成, 有效特征数: 38
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2629
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始训练模型...
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 模型训练完成
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始获取重要性...
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性字典长度: 38
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始特征映射, 特征数: 64
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 映射完成, 有效特征数: 38
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2584
2025-06-19 22:38 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-19 22:38 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 22:38 | WARNING  | BaseObj.plot_importance_comparison: 未找到重要性数据
2025-06-19 22:38 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 22:41 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 22:41 | WARNING  | BaseObj.save_importance_table: 没有有效的重要性数据可保存
2025-06-19 22:51 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 22:51 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 22:51 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 22:51 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 22:51 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 22:51 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 22:51 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 22:51 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 22:51 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 22:51 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 22:51 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 22:51 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 22:51 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 22:51 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ011_14', 'JZ014_troc_mul2_14', 'R_4', 'MIN5', 'BETA60', 'JZ012_34', 'JZ008_89', 'KSFT', 'JZ014_troc_mul2_55', 'VSUMP_60']...
2025-06-19 22:51 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 22:51 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 22:51 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 22:51 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 22:51 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 22:51 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 22:51 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 22:51 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 22:51 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 22:51 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 22:51 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 22:51 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 22:51 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余64个
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['weight', 'gain', 'cover']
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 64), y形状: (1043,)
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始训练模型...
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 模型训练完成
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始获取重要性...
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性字典长度: 38
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始特征映射, 特征数: 64
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 映射完成, 有效特征数: 38
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=6, 最大R²=0.2526
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始训练模型...
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 模型训练完成
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始获取重要性...
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性字典长度: 38
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始特征映射, 特征数: 64
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 映射完成, 有效特征数: 38
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2629
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始训练模型...
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 模型训练完成
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始获取重要性...
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性字典长度: 38
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始特征映射, 特征数: 64
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 映射完成, 有效特征数: 38
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2584
2025-06-19 22:51 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-19 22:51 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 22:51 | WARNING  | BaseObj.plot_importance_comparison: 未找到重要性数据
2025-06-19 22:51 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 22:51 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 22:51 | WARNING  | BaseObj.save_importance_table: 没有有效的重要性数据可保存
2025-06-19 22:52 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 22:52 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 22:52 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 22:52 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 22:52 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 22:52 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 22:52 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 22:52 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 22:52 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 22:52 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 22:52 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 22:52 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 22:52 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 22:52 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['R_0', 'R_5', 'JZ014_troc_mul3_34', 'JZ004_58', 'LOW2', 'QTLD5', 'KMID', 'OPEN1', 'JZ011_21', 'JZ014_troc_mul3_55']...
2025-06-19 22:52 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 22:52 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 22:52 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 22:52 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 22:52 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 22:52 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 22:52 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 22:52 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 22:52 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 22:52 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 22:52 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 22:52 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 22:52 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余64个
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['weight', 'gain', 'cover']
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 64), y形状: (1043,)
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始训练模型...
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 模型训练完成
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始获取重要性...
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性字典长度: 38
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始特征映射, 特征数: 64
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 映射完成, 有效特征数: 38
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=6, 最大R²=0.2526
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始训练模型...
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 模型训练完成
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始获取重要性...
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性字典长度: 38
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始特征映射, 特征数: 64
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 映射完成, 有效特征数: 38
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2629
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始训练模型...
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 模型训练完成
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始获取重要性...
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性字典长度: 38
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始特征映射, 特征数: 64
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 映射完成, 有效特征数: 38
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2584
2025-06-19 22:52 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-19 22:52 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 22:52 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-19 22:52 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 22:52 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 22:52 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-19 22:54 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 22:54 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 22:54 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 22:54 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ004_58', 'LOW3', 'QTLU10', 'JZ011_34', 'STD60', 'QTLU5', 'JZ011_9', 'V_0', 'V_3', 'R_2']...
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 22:54 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 22:54 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 22:54 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 22:54 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 22:54 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 22:54 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 22:54 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 22:54 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 22:54 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余64个
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['weight', 'gain', 'cover']
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 64), y形状: (1043,)
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始训练模型...
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 模型训练完成
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始获取重要性...
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性字典长度: 38
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始特征映射, 特征数: 64
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 映射完成, 有效特征数: 38
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=6, 最大R²=0.2526
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始训练模型...
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 模型训练完成
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始获取重要性...
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性字典长度: 38
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始特征映射, 特征数: 64
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 映射完成, 有效特征数: 38
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2629
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始训练模型...
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 模型训练完成
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始获取重要性...
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性字典长度: 38
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始特征映射, 特征数: 64
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 映射完成, 有效特征数: 38
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2584
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-19 22:54 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 22:54 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-19 22:54 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 22:54 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 22:54 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-19 22:54 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 22:54 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 22:54 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 22:54 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ014_troc_mul2_14', 'STD5', 'BETA10', 'CLOSE3', 'b_atr_25', 'KLEN', 'QTLU5', 'HIGH1', 'R_2', 'JZ014_trends_mul2_55']...
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 22:54 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 22:54 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 22:54 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 22:54 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 22:54 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 22:54 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 22:54 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 22:54 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 22:54 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 22:54 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余64个
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['weight', 'gain', 'cover']
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 64), y形状: (1043,)
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始训练模型...
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 模型训练完成
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始获取重要性...
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性字典长度: 38
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始特征映射, 特征数: 64
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 映射完成, 有效特征数: 38
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=6, 最大R²=0.2526
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始训练模型...
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 模型训练完成
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始获取重要性...
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性字典长度: 38
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始特征映射, 特征数: 64
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 映射完成, 有效特征数: 38
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2629
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始训练模型...
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 模型训练完成
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始获取重要性...
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性字典长度: 38
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始特征映射, 特征数: 64
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 映射完成, 有效特征数: 38
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2584
2025-06-19 22:54 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-19 22:54 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 22:54 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-19 22:55 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 22:55 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 22:55 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-19 23:00 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 23:00 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 23:00 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 23:00 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 23:00 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 23:00 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 23:00 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 23:00 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 23:00 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 23:00 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 23:00 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 23:00 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 23:00 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 23:00 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ011_21', 'R_4', 'JZ004_58', 'HIGH1', 'STD30', 'KSFT', 'JZ008_9', 'JZ004_120', 'CLOSE1', 'STD20']...
2025-06-19 23:00 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 23:00 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 23:00 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 23:00 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 23:00 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 23:00 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 23:00 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 23:00 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 23:00 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:00 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 23:00 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 23:00 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 23:00 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余64个
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['weight', 'gain', 'cover']
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 64), y形状: (1043,)
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始训练模型...
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 模型训练完成
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始获取重要性...
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性字典长度: 38
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始特征映射, 特征数: 64
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 映射完成, 有效特征数: 38
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=6, 最大R²=0.2526
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始训练模型...
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 模型训练完成
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始获取重要性...
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性字典长度: 38
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始特征映射, 特征数: 64
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 映射完成, 有效特征数: 38
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2629
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-19 23:00 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始训练模型...
2025-06-19 23:01 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 模型训练完成
2025-06-19 23:01 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始获取重要性...
2025-06-19 23:01 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性字典长度: 38
2025-06-19 23:01 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:01 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始特征映射, 特征数: 64
2025-06-19 23:01 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 映射完成, 有效特征数: 38
2025-06-19 23:01 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-19 23:01 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2584
2025-06-19 23:01 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-19 23:01 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 23:01 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-19 23:01 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 23:01 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 23:01 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-19 23:02 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 23:02 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 23:02 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 23:02 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 23:02 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 23:02 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 23:02 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 23:02 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 23:02 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 23:02 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 23:02 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 23:02 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 23:02 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 23:02 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ014_troc_mul3_21', 'CLOSE1', 'JZ014_troc_mul2_55', 'JZ012_60', 'JZ004_6', 'JZ004_80', 'roc_05', 'OPEN0', 'JZ014_trends_mul2_55', 'OPEN1']...
2025-06-19 23:02 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 23:02 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 23:02 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 23:02 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 23:02 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 23:02 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 23:02 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 23:02 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 23:02 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:02 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 23:02 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 23:02 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 23:02 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余64个
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['weight', 'gain', 'cover']
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 64), y形状: (1043,)
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始训练模型...
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 模型训练完成
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始获取重要性...
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性字典长度: 38
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始特征映射, 特征数: 64
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 映射完成, 有效特征数: 38
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=6, 最大R²=0.2526
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始训练模型...
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 模型训练完成
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始获取重要性...
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性字典长度: 38
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始特征映射, 特征数: 64
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 映射完成, 有效特征数: 38
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2629
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始训练模型...
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 模型训练完成
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始获取重要性...
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性字典长度: 38
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始特征映射, 特征数: 64
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 映射完成, 有效特征数: 38
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2584
2025-06-19 23:02 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-19 23:02 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 23:02 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-19 23:02 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 23:02 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 23:02 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-19 23:07 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 23:08 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 23:08 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 23:08 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 23:08 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 23:08 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 23:08 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 23:08 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 23:08 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 23:08 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 23:08 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 23:08 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 23:08 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 23:08 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['LOW0', 'JZ008_18', 'KMID', 'STD30', 'JZ014_troc_mul2_34', 'b_atr_14', 'STD60', 'JZ012_34', 'MIN10', 'BETA10']...
2025-06-19 23:08 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 23:08 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 23:08 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 23:08 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 23:08 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 23:08 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 23:08 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 23:08 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 23:08 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:08 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 23:08 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 23:08 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 23:08 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余64个
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['weight', 'gain', 'cover']
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 64), y形状: (1043,)
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始训练模型...
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 模型训练完成
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始获取重要性...
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性字典长度: 38
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始特征映射, 特征数: 64
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 映射完成, 有效特征数: 38
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=6, 最大R²=0.2526
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始训练模型...
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 模型训练完成
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始获取重要性...
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性字典长度: 38
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始特征映射, 特征数: 64
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 映射完成, 有效特征数: 38
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2629
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始训练模型...
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 模型训练完成
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始获取重要性...
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性字典长度: 38
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始特征映射, 特征数: 64
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 映射完成, 有效特征数: 38
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2584
2025-06-19 23:08 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-19 23:08 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 23:08 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-19 23:08 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 23:08 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 23:09 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-19 23:10 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 23:10 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 23:10 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 23:10 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 23:10 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 23:10 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 23:10 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 23:10 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 23:10 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 23:10 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 23:10 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 23:10 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 23:10 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 23:10 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ011_34', 'JZ012_34', 'JZ014_trends_mul2_55', 'MA5', 'JZ014_trends_mul3_34', 'OPEN2', 'OPEN1', 'R_6', 'OPEN4', 'CLOSE3']...
2025-06-19 23:10 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 23:10 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 23:10 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 23:10 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 23:10 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 23:10 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 23:10 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 23:10 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 23:10 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:10 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 23:10 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 23:10 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 23:10 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-19 23:10 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余64个
2025-06-19 23:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['weight', 'gain', 'cover']
2025-06-19 23:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 64), y形状: (1043,)
2025-06-19 23:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 23:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-19 23:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始训练模型...
2025-06-19 23:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 模型训练完成
2025-06-19 23:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始获取重要性...
2025-06-19 23:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性字典长度: 38
2025-06-19 23:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始特征映射, 特征数: 64
2025-06-19 23:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 映射完成, 有效特征数: 38
2025-06-19 23:10 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=6, 最大R²=0.2526
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始训练模型...
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 模型训练完成
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始获取重要性...
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性字典长度: 38
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始特征映射, 特征数: 64
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 映射完成, 有效特征数: 38
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2629
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始训练模型...
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 模型训练完成
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始获取重要性...
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性字典长度: 38
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始特征映射, 特征数: 64
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 映射完成, 有效特征数: 38
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2584
2025-06-19 23:11 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-19 23:11 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 23:11 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-19 23:11 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 23:11 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 23:11 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-19 23:12 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 23:12 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 23:12 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 23:12 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 23:12 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 23:12 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 23:12 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 23:12 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 23:12 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 23:12 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 23:12 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 23:12 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 23:12 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 23:12 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['V_4', 'QTLU10', 'JZ014_troc_mul2_14', 'CLOSE1', 'roc_05', 'JZ014_troc_mul3_9', 'JZ011_14', 'JZ004_10', 'OPEN3', 'STD30']...
2025-06-19 23:12 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 23:12 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 23:12 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 23:12 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 23:12 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 23:12 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 23:12 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 23:12 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 23:12 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:12 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 23:12 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 23:12 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 23:12 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余64个
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['weight', 'gain', 'cover']
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 64), y形状: (1043,)
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始训练模型...
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 模型训练完成
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始获取重要性...
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性字典长度: 38
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始特征映射, 特征数: 64
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 映射完成, 有效特征数: 38
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=6, 最大R²=0.2526
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始训练模型...
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 模型训练完成
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始获取重要性...
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性字典长度: 38
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始特征映射, 特征数: 64
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 映射完成, 有效特征数: 38
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2629
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始训练模型...
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 模型训练完成
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始获取重要性...
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性字典长度: 38
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始特征映射, 特征数: 64
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 映射完成, 有效特征数: 38
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2584
2025-06-19 23:12 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-19 23:12 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 23:12 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-19 23:12 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 23:13 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 23:13 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 数据诊断: 样本100, 原始特征3
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis:   移除0个高相关特征, 剩余3个
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析5种重要性类型: ['weight', 'gain', 'cover', 'total_gain', 'total_cover']
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] X形状: (100, 3), y形状: (100,)
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: weight
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 开始训练模型...
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 模型训练完成
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 开始获取重要性...
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 重要性字典长度: 3
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 重要性键样例: ['feature1', 'feature2', 'feature3']
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 开始特征映射, 特征数: 3
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 映射完成, 有效特征数: 3
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 开始后续处理...
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=3, 最大R²=0.4200
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: gain
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 开始训练模型...
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 模型训练完成
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 开始获取重要性...
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 重要性字典长度: 3
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 重要性键样例: ['feature1', 'feature2', 'feature3']
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 开始特征映射, 特征数: 3
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 映射完成, 有效特征数: 3
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 开始后续处理...
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=3, 最大R²=0.4000
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: cover
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 开始训练模型...
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 模型训练完成
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 开始获取重要性...
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 重要性字典长度: 3
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 重要性键样例: ['feature1', 'feature2', 'feature3']
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 开始特征映射, 特征数: 3
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 映射完成, 有效特征数: 3
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 开始后续处理...
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=3, 最大R²=0.4200
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: total_gain
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] total_gain 开始训练模型...
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] total_gain 模型训练完成
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] total_gain 开始获取重要性...
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] total_gain 重要性字典长度: 3
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] total_gain 重要性键样例: ['feature1', 'feature2', 'feature3']
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] total_gain 开始特征映射, 特征数: 3
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] total_gain 映射完成, 有效特征数: 3
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] total_gain 开始后续处理...
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis:   total_gain: 最优特征数=3, 最大R²=0.4200
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: total_cover
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] total_cover 开始训练模型...
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] total_cover 模型训练完成
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] total_cover 开始获取重要性...
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] total_cover 重要性字典长度: 3
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] total_cover 重要性键样例: ['feature1', 'feature2', 'feature3']
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] total_cover 开始特征映射, 特征数: 3
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] total_cover 映射完成, 有效特征数: 3
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] total_cover 开始后续处理...
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis:   total_cover: 最优特征数=3, 最大R²=0.4200
2025-06-19 23:15 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] XGBoost重要性分析完成，共分析5种类型
2025-06-19 23:15 | INFO     | FeatSelection._compare_importance_types: [Unknown] 特征对比: 共同特征0个
2025-06-19 23:16 | DEBUG    | FeatPreprocessing.norm: window=2000, logmode=0, algomode=0, n_clip=6, smooth=0, demean=True
2025-06-19 23:16 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 23:16 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 23:16 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 23:16 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 23:16 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 23:16 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 23:16 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 23:16 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 23:16 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 23:16 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 23:16 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 23:16 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 23:16 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 23:16 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ014_troc_mul2_34', 'smadiff5', 'HIGH3', 'b_atr_60', 'CLOSE1', 'JZ014_troc_mul2_21', 'JZ011_9', 'tr_ma5', 'JZ008_9', 'JZ014_trends_mul2_55']...
2025-06-19 23:16 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 23:16 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 23:16 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 23:16 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 23:16 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 23:16 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 23:16 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 23:16 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 23:16 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:16 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 23:16 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 23:16 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 23:16 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余64个
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析5种重要性类型: ['weight', 'gain', 'cover', 'total_gain', 'total_cover']
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 64), y形状: (1043,)
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始训练模型...
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 模型训练完成
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始获取重要性...
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性字典长度: 38
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始特征映射, 特征数: 64
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 映射完成, 有效特征数: 38
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=6, 最大R²=0.2526
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始训练模型...
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 模型训练完成
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始获取重要性...
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性字典长度: 38
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始特征映射, 特征数: 64
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 映射完成, 有效特征数: 38
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2629
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始训练模型...
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 模型训练完成
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始获取重要性...
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性字典长度: 38
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始特征映射, 特征数: 64
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 映射完成, 有效特征数: 38
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2584
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: total_gain
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_gain 开始训练模型...
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_gain 模型训练完成
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_gain 开始获取重要性...
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_gain 重要性字典长度: 38
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_gain 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_gain 开始特征映射, 特征数: 64
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_gain 映射完成, 有效特征数: 38
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_gain 开始后续处理...
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis:   total_gain: 最优特征数=6, 最大R²=0.2562
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: total_cover
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_cover 开始训练模型...
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_cover 模型训练完成
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_cover 开始获取重要性...
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_cover 重要性字典长度: 38
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_cover 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_cover 开始特征映射, 特征数: 64
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_cover 映射完成, 有效特征数: 38
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_cover 开始后续处理...
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis:   total_cover: 最优特征数=5, 最大R²=0.2610
2025-06-19 23:16 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析5种类型
2025-06-19 23:16 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 23:16 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-19 23:16 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 23:16 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 23:16 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-19 23:18 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 23:18 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 23:18 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 23:18 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 23:18 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 23:18 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 23:18 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 23:18 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 23:18 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 23:18 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 23:18 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 23:18 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 23:18 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 23:18 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ004_80', 'STD10', 'JZ004_30', 'tr_index', 'MIN5', 'V_2', 'b_atr_60', 'R_5', 'b_macd_hist', 'JZ011_9']...
2025-06-19 23:18 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 23:18 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 23:18 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 23:18 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 23:18 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 23:18 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 23:18 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 23:18 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 23:18 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:18 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 23:18 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 23:18 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 23:18 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余64个
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析5种重要性类型: ['weight', 'gain', 'cover', 'total_gain', 'total_cover']
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 64), y形状: (1043,)
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始训练模型...
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 模型训练完成
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始获取重要性...
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性字典长度: 38
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始特征映射, 特征数: 64
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 映射完成, 有效特征数: 38
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=6, 最大R²=0.2526
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始训练模型...
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 模型训练完成
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始获取重要性...
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性字典长度: 38
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始特征映射, 特征数: 64
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 映射完成, 有效特征数: 38
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2629
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始训练模型...
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 模型训练完成
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始获取重要性...
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性字典长度: 38
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始特征映射, 特征数: 64
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 映射完成, 有效特征数: 38
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2584
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: total_gain
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_gain 开始训练模型...
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_gain 模型训练完成
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_gain 开始获取重要性...
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_gain 重要性字典长度: 38
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_gain 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_gain 开始特征映射, 特征数: 64
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_gain 映射完成, 有效特征数: 38
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_gain 开始后续处理...
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis:   total_gain: 最优特征数=6, 最大R²=0.2562
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: total_cover
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_cover 开始训练模型...
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_cover 模型训练完成
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_cover 开始获取重要性...
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_cover 重要性字典长度: 38
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_cover 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_cover 开始特征映射, 特征数: 64
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_cover 映射完成, 有效特征数: 38
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] total_cover 开始后续处理...
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis:   total_cover: 最优特征数=5, 最大R²=0.2610
2025-06-19 23:18 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析5种类型
2025-06-19 23:18 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 23:18 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-19 23:18 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 23:18 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 23:19 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-19 23:21 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 23:21 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 23:21 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 23:21 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 23:21 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 23:21 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 23:21 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 23:21 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 23:21 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 23:21 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 23:21 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 23:21 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 23:21 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 23:21 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ014_troc_mul2_55', 'MIN5', 'LOW4', 'LOW1', 'V_2', 'b_atr_14', 'tr_index', 'JZ008_89', 'STD10', 'smadiff10']...
2025-06-19 23:21 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 23:21 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 23:21 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 23:21 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 23:21 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 23:21 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 23:21 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 23:21 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 23:21 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:21 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 23:21 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 23:21 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 23:21 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余64个
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析3种重要性类型: ['gain', 'weight', 'cover']
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 64), y形状: (1043,)
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始训练模型...
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 模型训练完成
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始获取重要性...
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性字典长度: 38
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始特征映射, 特征数: 64
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 映射完成, 有效特征数: 38
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2629
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始训练模型...
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 模型训练完成
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始获取重要性...
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性字典长度: 38
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始特征映射, 特征数: 64
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 映射完成, 有效特征数: 38
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=6, 最大R²=0.2526
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: cover
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始训练模型...
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 模型训练完成
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始获取重要性...
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性字典长度: 38
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始特征映射, 特征数: 64
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 映射完成, 有效特征数: 38
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] cover 开始后续处理...
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=6, 最大R²=0.2584
2025-06-19 23:21 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析3种类型
2025-06-19 23:21 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 23:21 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-19 23:21 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 23:22 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 23:22 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 数据诊断: 样本200, 原始特征10
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis:   移除0个高相关特征, 剩余10个
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析2种重要性类型: ['gain', 'permutation']
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] X形状: (200, 10), y形状: (200,)
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: gain
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 开始训练模型...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 模型训练完成
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 开始获取重要性...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 重要性字典长度: 9
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 重要性键样例: ['feature_0', 'feature_1', 'feature_2']
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 开始后续处理...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=4, 最大R²=0.9001
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: permutation
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] permutation 开始训练模型...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] permutation 模型训练完成
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] permutation 开始获取重要性...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始计算置换重要性...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 置换重要性计算完成, 有效特征数: 10
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] permutation 开始后续处理...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis:   permutation: 最优特征数=4, 最大R²=0.9001
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] XGBoost重要性分析完成，共分析2种类型
2025-06-19 23:32 | INFO     | FeatSelection._compare_importance_types: [Unknown] 特征对比: 共同特征0个
2025-06-19 23:32 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\test_permutation_comparison.png
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 数据诊断: 样本200, 原始特征10
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis:   移除0个高相关特征, 剩余10个
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析2种重要性类型: ['gain', 'shap']
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] X形状: (200, 10), y形状: (200,)
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: gain
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 开始训练模型...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 模型训练完成
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 开始获取重要性...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 重要性字典长度: 9
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 重要性键样例: ['feature_0', 'feature_1', 'feature_2']
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 开始后续处理...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=4, 最大R²=0.9001
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: shap
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] shap 开始训练模型...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] shap 模型训练完成
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] shap 开始获取重要性...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始计算SHAP值...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] SHAP计算完成, 有效特征数: 10
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] shap 开始后续处理...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis:   shap: 最优特征数=4, 最大R²=0.9001
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] XGBoost重要性分析完成，共分析2种类型
2025-06-19 23:32 | INFO     | FeatSelection._compare_importance_types: [Unknown] 特征对比: 共同特征0个
2025-06-19 23:32 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\test_shap_comparison.png
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 数据诊断: 样本200, 原始特征10
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis:   移除0个高相关特征, 剩余10个
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析5种重要性类型: ['gain', 'weight', 'cover', 'shap', 'permutation']
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] X形状: (200, 10), y形状: (200,)
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: gain
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 开始训练模型...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 模型训练完成
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 开始获取重要性...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 重要性字典长度: 9
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 重要性键样例: ['feature_0', 'feature_1', 'feature_2']
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] gain 开始后续处理...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=4, 最大R²=0.9001
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: weight
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 开始训练模型...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 模型训练完成
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 开始获取重要性...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 重要性字典长度: 9
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 重要性键样例: ['feature_0', 'feature_1', 'feature_2']
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] weight 开始后续处理...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=4, 最大R²=0.9001
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: cover
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 开始训练模型...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 模型训练完成
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 开始获取重要性...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 重要性字典长度: 9
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 重要性键样例: ['feature_0', 'feature_1', 'feature_2']
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] cover 开始后续处理...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis:   cover: 最优特征数=4, 最大R²=0.9001
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: shap
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] shap 开始训练模型...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] shap 模型训练完成
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] shap 开始获取重要性...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始计算SHAP值...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] SHAP计算完成, 有效特征数: 10
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] shap 开始后续处理...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis:   shap: 最优特征数=4, 最大R²=0.9001
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始分析重要性类型: permutation
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] permutation 开始训练模型...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] permutation 模型训练完成
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] permutation 开始获取重要性...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 开始计算置换重要性...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] 置换重要性计算完成, 有效特征数: 10
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] permutation 开始后续处理...
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis:   permutation: 最优特征数=4, 最大R²=0.9001
2025-06-19 23:32 | INFO     | FeatSelection.xgb_importance_analysis: [Unknown] XGBoost重要性分析完成，共分析5种类型
2025-06-19 23:32 | INFO     | FeatSelection._compare_importance_types: [Unknown] 特征对比: 共同特征0个
2025-06-19 23:32 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\test_full_comparison.png
2025-06-19 23:33 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\test_full_combined.png
2025-06-19 23:42 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 23:42 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 23:42 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 23:42 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 23:42 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 23:42 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 23:42 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 23:42 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 23:42 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 23:42 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 23:42 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 23:42 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 23:42 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 23:42 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['MA10', 'JZ014_trends_mul2_55', 'JZ014_troc_mul3_34', 'smadiff5', 'b_atr_14', 'JZ014_trends_mul2_21', 'QTLU10', 'HIGH2', 'KMID', 'MIN10']...
2025-06-19 23:42 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 23:42 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 23:42 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 23:42 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 23:42 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 23:42 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 23:42 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 23:42 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 23:42 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:42 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 23:42 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 23:42 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 23:42 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余64个
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析4种重要性类型: ['gain', 'weight', 'shap', 'permutation']
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 64), y形状: (1043,)
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始训练模型...
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 模型训练完成
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始获取重要性...
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性字典长度: 38
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2629
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始训练模型...
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 模型训练完成
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始获取重要性...
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性字典长度: 38
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=6, 最大R²=0.2526
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: shap
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] shap 开始训练模型...
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] shap 模型训练完成
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] shap 开始获取重要性...
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始计算SHAP值...
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] SHAP计算完成, 有效特征数: 64
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] shap 开始后续处理...
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis:   shap: 最优特征数=5, 最大R²=0.2588
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: permutation
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] permutation 开始训练模型...
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] permutation 模型训练完成
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] permutation 开始获取重要性...
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始计算置换重要性...
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 置换重要性计算完成, 有效特征数: 64
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] permutation 开始后续处理...
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis:   permutation: 最优特征数=6, 最大R²=0.2601
2025-06-19 23:42 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析4种类型
2025-06-19 23:42 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 23:42 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-19 23:42 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 23:42 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 23:42 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
2025-06-19 23:46 | INFO     | BaseObj.calc_expr_df_all: 开始计算特征，总数: 274
2025-06-19 23:46 | DEBUG    | BaseObj.get_outliers_params: mode: mad_clip, params: {'k': 3}
2025-06-19 23:46 | DEBUG    | BaseObj.normalize_df_all: norm_cols: {'base2keep': <BoxList: ['open', 'close', 'high', 'low']>, 'base2norm': <BoxList: ['volume', 'amount']>, 'feat2keep': '_ts_cons_', 'featlabel': 'label_'}
2025-06-19 23:46 | DEBUG    | BaseObj.normalize_df_all: X: skip
2025-06-19 23:46 | DEBUG    | BaseObj.normalize_df_all: label: skip
2025-06-19 23:46 | DEBUG    | BaseObj.normalize_df_all: 列分类结果:
2025-06-19 23:46 | DEBUG    | BaseObj.normalize_df_all:   基础保留列(5): ['symbol', 'open', 'high', 'low', 'close']
2025-06-19 23:46 | DEBUG    | BaseObj.normalize_df_all:   基础归一化列(2): ['amount', 'volume']
2025-06-19 23:46 | DEBUG    | BaseObj.normalize_df_all:   保持不变特征列(0): []
2025-06-19 23:46 | DEBUG    | BaseObj.normalize_df_all:   标签列(1): ['label_1']
2025-06-19 23:46 | DEBUG    | BaseObj.normalize_df_all:   待筛选特征列(273): 273个
2025-06-19 23:46 | INFO     | BaseObj.variance_filter: 方差筛选实际输出: 165 列
2025-06-19 23:46 | INFO     | BaseObj.normalize_df_all: 方差筛选: 移除了108个低方差特征
2025-06-19 23:46 | DEBUG    | BaseObj.normalize_df_all: 移除的特征: ['JZ008_89', 'JZ014_troc_mul2_14', 'JZ014_trends_mul2_55', 'smadiff5', 'MAX10', 'JZ012_21', 'JZ011_14', 'HIGH4', 'V_4', 'JZ004_98']...
2025-06-19 23:46 | DEBUG    | BaseObj.normalize_df_all: 最终输出验证:
2025-06-19 23:46 | DEBUG    | BaseObj.normalize_df_all:   期望总列数: 175 (基础7+归一化2+保持0+标签1+筛选后特征165)
2025-06-19 23:46 | DEBUG    | BaseObj.normalize_df_all:   实际总列数: 175
2025-06-19 23:46 | DEBUG    | BaseObj.normalize_df_all: 归一化策略: X_norm=跳过, label_norm=跳过
2025-06-19 23:46 | INFO     | FeatSelection.drop_features: [SH510050] 特征删除完成: 删除了 2 个特征列 ['volume', 'amount'], 剩余 172 列(不含品种列)
2025-06-19 23:46 | INFO     | FeatSelection.corr_filter: [SH510050] 相关性筛选完成: 从 171 个特征中选择了 148 个特征 (base2keep: 4, 相关性选择: 144, 目标列: 1)
2025-06-19 23:46 | DEBUG    | FeatSelection.corr_filter: [SH510050] 保留的base2keep列: ['open', 'close', 'high', 'low']
2025-06-19 23:46 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 148 个特征中保留了 70 个特征 (base2keep: 4, 冗余筛选: 66, 相关性阈值=0.85, 聚类数=67)
2025-06-19 23:46 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:46 | DEBUG    | FeatEngineering.featuretools_extract: [SH510050] featuretools输入: 71 列, 1043 行
2025-06-19 23:46 | WARNING  | FeatEngineering.featuretools_extract: featuretools 未安装，跳过特征衍生
2025-06-19 23:46 | INFO     | FeatSelection.redundancy_filter: [SH510050] 冗余去除完成: 从 70 个特征中保留了 67 个特征 (base2keep: 4, 冗余筛选: 63, 相关性阈值=0.85, 聚类数=64)
2025-06-19 23:46 | DEBUG    | FeatSelection.redundancy_filter: [SH510050] 保留的base2keep列: ['open', 'high', 'low', 'close']
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 数据诊断: 样本1043, 原始特征67
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis:   移除3个高相关特征, 剩余64个
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析4种重要性类型: ['gain', 'weight', 'shap', 'permutation']
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] X形状: (1043, 64), y形状: (1043,)
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 模型参数: {'objective': 'reg:squarederror', 'n_estimators': 20, 'max_depth': 3, 'learning_rate': 0.1, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'verbosity': 0}
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: gain
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始训练模型...
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 模型训练完成
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始获取重要性...
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性字典长度: 38
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] gain 开始后续处理...
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis:   gain: 最优特征数=8, 最大R²=0.2629
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: weight
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始训练模型...
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 模型训练完成
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始获取重要性...
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性字典长度: 38
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 重要性键样例: ['JZ001_5_10', 'low', 'JZ009_89']
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] weight 开始后续处理...
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis:   weight: 最优特征数=6, 最大R²=0.2526
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: shap
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] shap 开始训练模型...
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] shap 模型训练完成
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] shap 开始获取重要性...
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始计算SHAP值...
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] SHAP计算完成, 有效特征数: 64
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] shap 开始后续处理...
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis:   shap: 最优特征数=6, 最大R²=0.2619
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始分析重要性类型: permutation
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] permutation 开始训练模型...
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] permutation 模型训练完成
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] permutation 开始获取重要性...
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 开始计算置换重要性...
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] 置换重要性计算完成, 有效特征数: 64
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] permutation 开始后续处理...
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis:   permutation: 最优特征数=6, 最大R²=0.2601
2025-06-19 23:46 | INFO     | FeatSelection.xgb_importance_analysis: [SH510050] XGBoost重要性分析完成，共分析4种类型
2025-06-19 23:46 | INFO     | FeatSelection._compare_importance_types: [SH510050] 特征对比: 共同特征0个
2025-06-19 23:46 | INFO     | BaseObj.plot_importance_comparison: 重要性对比图已保存至: D:\myquant\reports\XentZ\importance_comparison.png
2025-06-19 23:46 | INFO     | BaseObj.plot_marginal_r2: 边际R²分析图已保存至: D:\myquant\reports\XentZ\marginal_r2.png
2025-06-19 23:46 | INFO     | BaseObj.plot_combined_analysis: 综合分析图已保存至: D:\myquant\reports\XentZ\complete_analysis.png
2025-06-19 23:47 | INFO     | BaseObj.save_importance_table: 重要性分析表格已保存至: D:\myquant\reports\XentZ\importance_tables.csv
