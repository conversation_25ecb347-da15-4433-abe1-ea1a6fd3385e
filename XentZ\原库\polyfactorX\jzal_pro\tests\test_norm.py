import numpy as np
import pandas as pd

def robust_standardize(data):
    """使用中位数和IQR对数据进行鲁棒标准化."""
    median = np.median(data, axis=0)
    iqr = np.percentile(data, 75, axis=0) - np.percentile(data, 25, axis=0)
    return (data - median) / iqr

def normalize(data):
    """将标准化后的数据除以其无穷范数，并将数据剪裁到[-1, 1]区间内."""
    norm = np.linalg.norm(data, ord=np.inf, axis=0)
    normalized_data = data / norm
    return np.clip(normalized_data, -1, 1)

def robust_normalize(data):
    lower_bound = np.percentile(data, 1, axis=0)
    upper_bound = np.percentile(data, 99, axis=0)
    return np.clip((data - lower_bound) / (upper_bound - lower_bound), 0, 1)

# # 示例数据包含异常值
# data = np.array([[1, 2, 3],
#                  [4, 5, 1000],  # 异常值在这里
#                  [7, 8, 9]])

# # 鲁棒标准化数据
# standardized_data = robust_standardize(data)

# # 正则化并剪裁数据
# normalized_data = normalize(standardized_data)

# print("鲁棒标准化后的数据:")
# print(standardized_data)

# print("\n正则化并剪裁后的数据:")
# print(normalized_data)


# from scipy.stats import median_abs_deviation

# # 计算 MAD
# mad = median_abs_deviation(data)

# # 标记异常值，通常用3倍MAD
# median = np.median(data)
# outliers = np.where(np.abs(data - median) > 3 * mad)
# print("MAD 方法识别的异常值索引:", outliers)
# print("MAD 方法识别的异常值:", data[outliers])


import numpy as np
import scipy.stats as stats

def zscore_to_cdf(z_scores):
    """将 z-score 转换为累积概率"""
    return stats.norm.cdf(z_scores)

# 示例数据
data = np.array([10, 12, 12, 13, 12, 11, 100, 13, 12, 11, 12])

# 计算 z-score
z_scores = (data - np.mean(data)) / np.std(data)

# 将 z-score 转换为累积概率
cdf_values = zscore_to_cdf(z_scores)

print("Z-scores:", z_scores)
print("CDF Values:", cdf_values)


np.random.seed(0)
data = np.random.rand(10000)

# 设置参数
window = 2000

# 计算 L2 范数
l2norm = pd.Series(data).rolling(window=window, min_periods=1).apply(
    lambda x: np.sqrt(np.sum(x**2)), raw=True).to_numpy()

# 归一化
factor_value = data / l2norm

# 查看归一化后的数据分布
import matplotlib.pyplot as plt

plt.figure(figsize=(10, 5))

# 显示原始数据分布
plt.subplot(1, 2, 1)
plt.hist(data, bins=50, alpha=0.5, label='Original Data')
plt.title('Original Data Distribution')
plt.legend()

# 显示归一化后的数据分布
plt.subplot(1, 2, 2)
plt.hist(factor_value, bins=50, alpha=0.5, label='Normalized Data')
plt.title('Normalized Data Distribution')
plt.legend()

plt.show()