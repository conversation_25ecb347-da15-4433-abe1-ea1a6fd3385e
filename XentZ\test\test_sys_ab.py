from hikyuu.interactive import *
from matplotlib import pyplot as plt

# 创建布林通道突破策略
def create_boll_break_sys(stock,query):
    # 创建模拟交易账户进行回测，初始资金100万
    my_tm = crtTM(init_cash=1000000)
    
    # 自定义布林带参数
    n = 20  # 移动平均天数
    band = 2.0  # 标准差倍数
    kdata = stock.get_kdata(query)
    ma = MA(CLOSE(kdata), n)
    sd = STDEV(CLOSE(kdata), n)
    upper = ma + band * sd
    lower = ma - band * sd
    
    my_sg = SG_Bool(CLOSE(kdata) > upper, CLOSE(kdata) < lower)
    
    # 固定每次买入1000股
    my_mm = MM_FixedCount(1000)
    
    # 创建交易系统
    sys = SYS_Simple(tm=my_tm, sg=my_sg, mm=my_mm)
    
    return sys  # 直接返回系统，不在这里运行

# 使用示例
def run_strategy_example():
    # 选择一只股票，例如平安银行
    stock = sm['sz000001']
    
    # 获取回测 K 线数据，修改为使用固定的时间范围
    query = Query(-2000)  # 保存查询对象以便复用
    
    
    # 创建并运行交易系统，使用相同的查询对象
    sys = create_boll_break_sys(stock,query)
    sys.run(stock, query)  # 只在这里运行一次系统
    
    # 输出交易记录
    tm = sys.tm
    print("交易记录：")
    for record in tm.get_trade_list():
        print(record)
    
    # 计算绩效统计
    print("\n绩效统计：")
    # 尝试恢复使用 Performance 对象进行统计
    sys.performance()
    # 获取最后一个交易日期作为统计结束日期
    trade_list = tm.get_trade_list()
    per = Performance()
    if trade_list:
        last_trade_date = trade_list[-1].datetime
        per.statistics(tm, last_trade_date)  # 使用 TradeManager 和结束日期进行统计
        print(f"总收益率: {per['总收益率']:.2f}%")
        print(f"年化收益率: {per['年化收益率']:.2f}%")
        print(f"最大回撤: {per['最大回撤']:.2f}%")
        print(f"胜率: {per['赢利交易比例%']:.2f}%")
    else:
        print("没有交易记录，无法计算绩效统计。")
        per = {} # 或者设置为一个空的字典，避免后续绘图出错
    sys.plot()
    # # 绘制K线图和交易点位
    # # 利用Hikyuu的动态参数功能计算布林带指标
    # n = 20
    # band = 2.0
    
    # # 获取K线数据的实体上下沿
    # open_price = OPEN(kdata)
    # close_price = CLOSE(kdata)
    # entity_top = IF(close_price > open_price, close_price, open_price)
    # entity_bottom = IF(close_price < open_price, close_price, open_price)
    # entity_mid = (entity_top + entity_bottom) / 2.0
    
    # # 使用动态参数方式计算布林带
    # ma = MA(entity_mid, n=n)  # 明确指定参数名
    # sd = STDEV(entity_mid, n=n)  # 明确指定参数名
    # upper = ma + band * sd
    # lower = ma - band * sd
    
    # # 设置上下文，确保指标可以正确计算
    # ma.set_context(kdata)
    # upper.set_context(kdata)
    # lower.set_context(kdata)
    
    # # 创建两个子图，上面显示K线和交易信号，下面显示绩效曲线
    # ax1, ax2 = create_figure(2)
    
    # # 绘制K线图
    # kdata.plot(axes=ax1)
    
    # # 在同一个图上绘制布林带
    # ma.plot(axes=ax1, label="MA")
    # upper.plot(axes=ax1, label="Upper")
    # lower.plot(axes=ax1, label="Lower")
    
    # # 显示图例
    # ax1.legend()
    
    # # 在K线图上添加交易记录
    # sys.plot(axes=ax1)
    
    # # 修正：手动绘制盈亏曲线
    # # 1. 获取回测期间的日期列表
    # plot_dates = kdata.get_datetime_list()
    
    # # 2. 调用 get_funds_curve 获取对应日期的总资产列表
    # if plot_dates:
    #     total_values = tm.get_funds_curve(plot_dates) # 传入日期列表
        
    #     # 3. 绘制总资产曲线
    #     if total_values:
    #         ax2.plot(plot_dates, total_values, label="总资产")
    #         ax2.set_title("账户资产曲线")
    #         ax2.legend()
    #         # 格式化X轴日期显示
    #         import matplotlib.dates as mdates
    #         ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    #         plt.setp(ax2.get_xticklabels(), rotation=30, ha="right") # 旋转标签避免重叠
    #     else:
    #         print("无法获取资金曲线数据。")
            
    # else:
    #     print("没有日期数据，无法绘制资产曲线。")

    # plt.tight_layout() # 调整布局防止重叠
    plt.show()

# 执行示例
run_strategy_example()