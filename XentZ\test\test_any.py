# test_featuretools_parallel.py
import featuretools as ft
import dask.dataframe as dd
import pandas as pd

print("=== Featuretools 并行测试 ===")

# 创建测试数据
df = pd.DataFrame({
    'id': range(100),
    'time': pd.date_range('2024-01-01', periods=100, freq='1H'),
    'value1': range(100),
    'value2': range(100, 200)
})

# 创建 EntitySet
es = ft.EntitySet('test')
es = es.add_dataframe(
    dataframe=df,
    dataframe_name='data',
    index='id',
    time_index='time'
)

# 测试并行计算
try:
    feature_matrix, _ = ft.dfs(
        entityset=es,
        target_dataframe_name='data',
        trans_primitives=['diff', 'absolute'],
        max_depth=1,
        n_jobs=4,  # 使用并行
        verbose=True
    )
    print(f"✅ 并行计算成功！特征矩阵形状: {feature_matrix.shape}")
    print(f"✅ 生成特征: {list(feature_matrix.columns)}")
    
except Exception as e:
    print(f"❌ 并行计算失败: {e}")