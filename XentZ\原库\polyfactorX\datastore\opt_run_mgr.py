import sqlite3
import os
from datetime import datetime
from dataclasses import dataclass,fields
from typing import List, Optional
from uuid import uuid4
from sqlite3 import IntegrityError, Error
import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
    
from jzal_pro.cls_common import BaseObj

@dataclass
class OptRunData: # 优化海选组合的数据类
    opt_uid: str # 该次优化选组合的唯一作业号
    task_uid: str # 关联哪套数据, 对应这套数据的因子值可以是随时变化的, 因此opt与run_id无直接关系
    symbol: str # 支持510050.SH_15 这种
    factor_size: Optional[int] = None # 海选池大小
    opt_method: Optional[str] = None # 优化方法, hyperopt, optuna, etc..
    opt_params: Optional[str] = None # 参数留痕, 算法相关的参数存储留痕
    # ----- bgn/end
    bgn_run: Optional[str] = None
    end_run: Optional[str] = None
    # ----- cost info
    cost_load: Optional[float] = None  # 大宽表加载时间
    cost_opt: Optional[float] = None  # 优化时间
    cost_save: Optional[float] = None  # 结果入库时间
    cost_total: Optional[float] = None  # 总耗时，单位秒
    cpu_usage: Optional[float] = None  # CPU使用率
    memory_usage: Optional[float] = None  # 内存使用量，单位可以自定义（例如MB)
    
class OptRunDataManager(BaseObj):
    def __init__(self, db_path: str):
        super().__init__()
        self.db_path = db_path
        self.table_name = 'opt_runs'  # 表名写死
        self._create_table()
    
    def _create_table(self):
        """创建OptRunData表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    CREATE TABLE IF NOT EXISTS [{self.table_name}] (
                        opt_uid TEXT PRIMARY KEY,
                        task_uid TEXT NOT NULL,
                        symbol TEXT NOT NULL,
                        factor_size INTEGER,
                        opt_method TEXT,
                        opt_params TEXT,
                        bgn_run TEXT,
                        end_run TEXT,
                        cost_load REAL,
                        cost_opt REAL,
                        cost_save REAL,
                        cost_total REAL,
                        cpu_usage REAL,
                        memory_usage REAL,
                        FOREIGN KEY (task_uid) REFERENCES gp_tasks(task_uid)
                    )
                ''')
                conn.commit()
        except Error as e:
            self.log(f"创建{self.table_name}表时发生错误: {e}")
            
    def add_data(self, opt_run_data: OptRunData) -> bool:
        """插入OptRunData记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    INSERT INTO {self.table_name} (
                        opt_uid, task_uid, symbol, factor_size, opt_method, 
                        opt_params, bgn_run, end_run, cost_load, cost_opt, cost_save, cost_total, 
                        cpu_usage, memory_usage
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    opt_run_data.opt_uid, opt_run_data.task_uid, opt_run_data.symbol,
                    opt_run_data.factor_size, opt_run_data.opt_method, opt_run_data.opt_params,
                    opt_run_data.bgn_run, opt_run_data.end_run,
                    opt_run_data.cost_load, opt_run_data.cost_opt, opt_run_data.cost_save,
                    opt_run_data.cost_total, opt_run_data.cpu_usage, opt_run_data.memory_usage
                ))
                conn.commit()
                return True
        except IntegrityError:
            self.log(f"尝试插入的opt_uid({opt_run_data.opt_uid})已存在，插入失败。")
            return False
        except Error as e:
            self.log(f"插入OptRunData记录时发生错误: {e}")
            return False

    def update_field(self, opt_uid: str, field: str, value):
        """更新单个字段的值，对于时长和CPU, memory使用，传入的value应已经是处理过的格式"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f"UPDATE [{self.table_name}] SET {field} = ? WHERE opt_uid = ?", (value, opt_uid))
                conn.commit()
        except Error as e:
            self.log(f"更新{field}时发生错误: {e}")
            
    def update_opt_run_data(self, opt_run_data: OptRunData, fields_to_update: List[str]) -> bool:
        """
        根据OptRunData实例及指定的字段列表更新数据库中的记录。
        :param opt_run_data: OptRunData实例，包含待更新的字段值。
        :param fields_to_update: 需要更新的字段名列表。
        :return: 如果成功更新返回True，否则返回False。
        """
        try:
            # 确保所有指定的更新字段都存在于OptRunData类中
            valid_fields = set(fields(OptRunData).name for _ in fields(OptRunData))
            if not all(field in valid_fields for field in fields_to_update):
                raise ValueError("Some fields to update are not valid.")
            
            # 构建更新字段的SET部分
            update_fields = ', '.join([f"{field} = ?" for field in fields_to_update])
            
            # 准备参数列表，首先准备opt_uid用于WHERE条件，然后是需要更新的字段值
            values = [getattr(opt_run_data, 'opt_uid')] + [getattr(opt_run_data, field) for field in fields_to_update]
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    UPDATE {self.table_name}
                    SET {update_fields}
                    WHERE opt_uid = ?
                ''', values)
                conn.commit()
                if cursor.rowcount > 0:
                    return True
                else:
                    self.log(f"没有找到opt_uid为{opt_run_data.opt_uid}的记录进行更新。")
                    return False
        except Error as e:
            self.log(f"更新OptRunData记录时发生错误: {e}")
            return False
        
    def update_opt_run_data2(self, opt_uid: str, update_data: dict) -> bool:
        """根据opt_uid更新OptRunData记录"""
        try:
            set_clause = ', '.join([f"{k} = ?" for k in update_data.keys()])
            values = list(update_data.values()) + [opt_uid]  # 注意添加opt_uid作为查询条件
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    UPDATE {self.table_name} 
                    SET {set_clause} 
                    WHERE opt_uid = ?
                ''', values)
                if cursor.rowcount > 0:
                    conn.commit()
                    return True
                else:
                    self.log(f"没有找到opt_uid为{opt_uid}的记录进行更新。")
                    return False
        except Error as e:
            self.log(f"根据opt_uid更新OptRunData记录时发生错误: {e}")
            return False

    def query_same_opt_runs(self, reference_data: OptRunData) -> List[OptRunData]:
        """
        查询与参考OptRunData实例具有相同任务标识、因子池大小、优化方法和优化参数的所有记录。
        :param reference_data: 用于匹配条件的OptRunData实例。
        :return: 匹配的OptRunData记录列表。
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                # 从reference_data中提取查询条件
                cursor.execute(f'''
                    SELECT * FROM [{self.table_name}] 
                    WHERE task_uid = ? 
                    AND factor_size = ? 
                    AND opt_method = ? 
                    AND opt_params = ?
                ''', (
                    reference_data.task_uid,
                    reference_data.factor_size,
                    reference_data.opt_method,
                    reference_data.opt_params
                ))
                rows = cursor.fetchall()
                return [OptRunData(*row) for row in rows]
        except Error as e:
            self.log(f"根据OptRunData查询同一批优化记录时发生错误: {e}")
            return []

    def set_bgn_run(self, opt_uid: str):
        """设置指定run_uid的bgn_run为当前日期时间"""
        current_dt = datetime.now().strftime('%y/%m/%d %H:%M:%S')
        self.update_field(opt_uid, 'bgn_run', current_dt)
        
    def set_end_run(self, opt_uid: str):
        """设置指定run_uid的end_run为当前日期时间"""
        current_dt = datetime.now().strftime('%y/%m/%d %H:%M:%S')
        self.update_field(opt_uid, 'end_run', current_dt)

        
if __name__ == '__main__':
    # 数据库路径和表名
    DB_PATH = "test_opt_run_data.db"
    TABLE_NAME = "opt_run_data"

    # 清除旧的数据库文件（如果存在）
    if os.path.exists(DB_PATH):
        os.remove(DB_PATH)
        
    # 创建OptRunDataManager实例
    manager = OptRunDataManager(DB_PATH, TABLE_NAME)

    # 测试数据
    test_data = [
        OptRunData(
            opt_uid=str(uuid4()),
            task_uid="test_task_uid",
            symbol="510050.SH",
            factor_size=100,
            opt_method="hyperopt",
            opt_params="{'max_evals': 100}",
        ),
        OptRunData(
            opt_uid=str(uuid4()),
            task_uid="test_task_uid",
            symbol="510050.SH",
            factor_size=100,
            opt_method="hyperopt",
            opt_params="{'max_evals': 100}",
        ),
    ]

    # 插入数据
    for data in test_data:
        assert manager.add_opt_run_data(data), f"Failed to add opt_run_data with opt_uid {data.opt_uid}"

    # 查询数据
    reference_data = OptRunData(
        opt_uid="",
        task_uid="test_task_uid",
        symbol="",
        factor_size=100,
        opt_method="hyperopt",
        opt_params="{'max_evals': 100}",
    )

    queried_data = manager.query_same_opt_runs(reference_data)
    assert len(queried_data) == len(test_data), "Query did not return the expected number of records."

    # 更新成本信息
    update_data = {
        "cost_load": 2.5,
        "cost_opt": 10.2,
        "cost_save": 1.3,
    }

    # 选择第一条记录进行更新
    first_record = queried_data[0]
    update_result = manager.update_opt_run_data2(first_record.opt_uid, update_data)
    assert update_result, "Update operation failed."

    # 验证更新
    updated_record = manager.query_same_opt_runs(first_record)
    assert updated_record[0].cost_load == update_data["cost_load"], "Cost load was not updated correctly."
    assert updated_record[0].cost_opt == update_data["cost_opt"], "Cost opt was not updated correctly."
    assert updated_record[0].cost_save == update_data["cost_save"], "Cost save was not updated correctly."

    print("All tests passed successfully.")