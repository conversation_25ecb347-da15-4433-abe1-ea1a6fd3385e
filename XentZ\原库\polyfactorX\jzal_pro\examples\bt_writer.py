import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent.parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))

from datetime import datetime
from tarfile import DEFAULT_FORMAT
import backtrader as bt

from engine.strategy import StrategyBase
'''
结论: next出信号, order是在下一个bar创建, trader是在0到非0 非0到0变化时发生
'''
class SMACross(StrategyBase):
    params = dict(period = 5)
    def __init__(self):
        self.move_avg = bt.ind.MovingAverageSimple(self.data,period=self.p.period)
        self.crossover = bt.ind.CrossOver(self.data,self.move_avg)
        self.crossover.csv = True
    def next(self):
        if not self.position:
            if self.crossover >0:
                self.log('创建买单')
                self.buy(size=100)
        elif self.crossover <0:
            self.log('创建卖单')
            self.sell(size = 100)
cb = bt.Cerebro()
datapath = 'D://myquant//btq_jzal//data//csvs//etfs//510880.SH.csv'
data = bt.feeds.GenericCSVData(
    dataname = datapath,
    datetime = 0,
    open=1,
    close=2,
    high=3,
    low=4,
    volume=5,
    openinerest=-1,
    dtformat=('%Y%m%d')
)
cb.adddata(data)
cb.addstrategy(SMACross)
cb.broker.setcash(10000.0)
cb.addwriter(bt.WriterFile,csv=True,out='mywriter.csv',rounding=2)
cb.run()
    