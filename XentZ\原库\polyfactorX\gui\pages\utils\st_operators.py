import streamlit as st
from pathlib import Path
import sys

root = Path(__file__).parent.parent.parent
if str(root) not in sys.path:
    sys.path.append(str(root))

from task_defs.task_fctsmine import Fcts_GP_Mining

def render_page():
    """算子管理页面"""
    st.header("算子管理")
    
    # 获取内置算子集合
    task = Fcts_GP_Mining()
    operators = task.func_set
    
    # 在展开区域标题中写明算子总数
    with st.expander(f"**📚 内置算子集合 (共 {len(operators)} 个)**", expanded=True):
        # 使用紧凑方式显示所有内置算子，每个算子用反引号包裹，多个算子之间以两个空格分隔
        operator_str = " &nbsp;&nbsp; ".join([f"`{op}`" for op in operators])
        st.markdown(operator_str, unsafe_allow_html=True)
    
    # 底部导航按钮
    st.markdown("---")
    footer_cols = st.columns([1, 1, 2])
    with footer_cols[0]:
        if st.button("⬅️ 上一步"):
            st.session_state.current_page = "特征管理"
            st.switch_page("pages/2_特征管理.py")
    with footer_cols[1]:
        if st.button("下一步 ➡️"):
            st.session_state.current_page = "因子挖掘"
            st.switch_page("pages/4_因子挖掘.py") 