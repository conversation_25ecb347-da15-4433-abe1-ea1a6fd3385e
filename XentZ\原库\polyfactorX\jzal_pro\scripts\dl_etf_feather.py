import akshare as ak
import pandas as pd
from datetime import datetime
import pyarrow.feather as feather
import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent.parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
from config import DATA_DIR_FEATHER, DATA_DIR_OTHER

def download_data(symbol):
    print(symbol)
    # symbol = '513500.SH'
    code = symbol[:6]
    df = ak.fund_etf_hist_em(symbol=code, period="daily", start_date="20000101",
                             end_date=datetime.now().strftime('%Y%m%d'), adjust="hfq")
    cols = {'日期': 'date', '开盘': 'open', '收盘': 'close', '最高': 'high', '最低': 'low', '成交量': 'volume',
            '换手率': 'turn_over', '成交额': 'amount'}
    df.rename(columns=cols, inplace=True)
    df['date'] = df['date'].apply(lambda x: str(x).replace('-', ''))
    df = df[cols.values()]
    df['symbol'] = symbol
    DATA_DIR_FEATHER.joinpath('etfs').mkdir(exist_ok=True)
    feather.write_feather(df, DATA_DIR_FEATHER.joinpath('etfs').joinpath(symbol + '.feather'))
    #df.to_csv(DATA_DIR_FEATHER.joinpath('etfs').joinpath(symbol + '.csv'), index=None)
    print(df)


symbols = ["513100.SH",  # 纳指  # RSRS
           "159934.SZ",  # 黄金
           "510880.SH",  # 红利
           "513520.SH",  # 日经
           "159915.SZ"   # 创业
           ]

# symbols = [  # ETF趋势
#         '516510.SH',  # 云计算(信息技术)
#         '159985.SZ',  # 豆粕
#         '515880.SH',  # 通讯
#         '513060.SH',  # 恒生医疗
#         '512660.SH',  # 军工(军工)
#         '513520.SH',  # 日经
#         '513330.SH',  # 恒生互联网
#         '515790.SH',  # 光伏
#         '512100.SH',  # 中证1000
#         '512510.SH',  # 中证500
#         '159998.SZ',  # 计算机
#         '513500.SH',  # 标普500
#         '162719.SZ',  # 石油LOF
#         '159949.SZ',  # 创业板50
#         '515030.SH',  # 新能车
#         '159928.SZ',  # 消费 (消费)
#     #    '513100.SH',  # 纳指100
#         '159995.SZ',  # 芯片
#     ] 

# symbols = [ # 小果池
#             '513100',  # 纳斯达克ETF
#             '513550',  # 标普500ETF
#             '510300',  # 沪深300ETF
#             '159937',  # 黄金ETF
#             '510510',  # 中证500ETF
#             '159659',  # 纳斯达克ETF
#             '510050',  # 上证50ETF
#             '159830',  # 上海黄金ETF
#             '511130',  # 30年债券ETF
#             '159655',  # 标普ETF
#             '561300',  # 300指数增强ETF
#             '513400',  # 道琼斯ETF
#             '159680',  # 中证1000ETF
#             '159501',  # 纳斯达克ETF
#             '159941',  # 纳斯达克ETF
#             '513850',  # 美国50ETF
#             '159581',  # 红利ETF
#             '159351',  # A500指数ETF
#             '513330',  # 纳斯达克ETF
#             '511090',  # 30年债券ETF
#             '159915',  # 创业板ETF
#             '159985',  # 豆粕ETF
#             '159981',  # 能源化工ETF
#             '159980',  # 有色ETF           
#            ]

# symbols = ["563300.SH", "510300.SH", "510500.SH", "512100.SH", "159915.SZ",   # RSRS_v2
#                 "159967.SZ", "159920.SZ", "513100.SH", "513500.SH", "518880.SH", 
#                 "159985.SZ", "513520.SH", "510050.SH"]
# symbols = ['159929.SZ',  # 消费
#            '512010.SH',  # 医药
#            '511220.SH',  # 城投债
#            '511010.SH',  # 国债
#           ]
# symbols = [
#         '511220.SH',  # 城投债
#         '512010.SH',  # 医药
#         '518880.SH',  # 黄金
#         '163415.SZ',  # 兴全商业
#         '159928.SZ',  # 消费
#         '161903.SZ',  # 万家行业优选
#         '513100.SH'  # 纳指
#     ]  # 证券池列表
# symbols = ["563300.SH", "510300.SH", "510500.SH",   # 大类-风险平价-目标波动率7%
#            "512100.SH", "159915.SZ", "159967.SZ", 
#            "159920.SZ", "513100.SH", "513500.SH", 
#            "518880.SH", "159985.SZ", "513520.SH", "510050.SH"]

# symbols = ["563300.SH", "510300.SH", "510500.SH", "512100.SH", "159915.SZ",   # 大类-风险平价
#             "159967.SZ", "159920.SZ", "513100.SH", "513500.SH", "518880.SH",
#             "159985.SZ", "513520.SH", "510050.SH"]

# symbols = ['159928.SZ','510050.SH','512010.SH','513100.SH','518880.SH',  # 大类-全天候
#             '511220.SH','511010.SH','161716.SZ']

# symbols = ["563300.SH", "510300.SH", "510500.SH", "512100.SH", "159915.SZ",
#                 "159967.SZ", "159920.SZ", "513100.SH", "513500.SH", "518880.SH", 
#                 "159985.SZ", "513520.SH", "510050.SH"]

# symbols = ['159967.SZ','512890.SH']  # 轮动-红利+成长
symbols = symbols + ['510300.SH']

# S1:下载策略相关数据 TODO:增量方式
for symbol in symbols:
    download_data(symbol)

# S2:check并增量更新交易日历文件
astock_calendar_path = DATA_DIR_OTHER.joinpath('astock_calendar.csv')
df_dl_astock_calendar = ak.tool_trade_date_hist_sina()
df_dl_astock_calendar['trade_date'] = pd.to_datetime(df_dl_astock_calendar['trade_date'])
''' >> 为pandas日期格式 timestamp64[ns]
     trade_date
0    1990-12-19
'''
# df_dl_astock_calendar.to_csv(astock_calendar_path,index=False,header=True) # 初始化交易日历(全量)

df_exist_astock_calendar = pd.read_csv(astock_calendar_path).dropna()
df_exist_astock_calendar['trade_date'] = pd.to_datetime(df_exist_astock_calendar['trade_date'])
df_new_astock_calendar = (df_dl_astock_calendar[~df_dl_astock_calendar.isin(df_exist_astock_calendar)]).dropna()

if not df_new_astock_calendar.empty:
    df_new_astock_calendar.to_csv(astock_calendar_path, mode='a', index=False, header=False)
    print('incr. calendar date: bgn -- {}'.format(df_new_astock_calendar['trade_date'].iloc[0].strftime('%Y-%m-%d')))
    print('incr. calendar date: end -- {}'.format(df_new_astock_calendar['trade_date'].iloc[-1].strftime('%Y-%m-%d')))