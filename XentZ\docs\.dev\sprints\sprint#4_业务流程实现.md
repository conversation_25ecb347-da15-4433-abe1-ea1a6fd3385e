# Sprint #4 业务流程实现 - L3动态稳健性检验脚本

## 主流程脚本模板

### 文件结构
```
script/投研_因子挖掘集成/L3动态稳健性检验.py
```

### 完整实现模板
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
L3动态稳健性检验脚本 - WFA验证主流程
基于Walk-Forward Analysis方法对L2阶段通过的因子进行动态稳健性检验
采用过程式编程风格，注重执行效率和可观测性
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Tuple, Optional

# 项目内部导入
from common.cls_base import BaseObj, MonitorContext
from config import REPORTS_DIR, settings
from factor.validation_utils import (
    WFAParams, WFAResult, 
    run_wfa_validation, 
    calculate_wfa_metrics_with_reuse,
    save_wfa_to_factorzoo,
    check_wfa_criteria,
    generate_quantstats_reports,
    generate_custom_wfa_charts,
    batch_generate_wfa_reports
)
from factor.factorloader import FactorLoader
from factorzoo import factorzoo
from factorzoo import FactorMonitorContext, FactorZooRunManager
from factorzoo.factor_value_manager import get_factor_value_manager


def load_and_validate_config() -> Tuple[WFAParams, Dict, Dict]:
    """加载并验证WFA配置"""
    from dynaconf import Dynaconf
    
    print("📋 加载WFA验证配置...")
    
    # 加载任务配置
    task_config = Dynaconf(settings_files=["config/tasks/ts_l3_wfa.toml"])
    
    # 转换为数据类
    wfa_params = WFAParams(
        training_window=task_config.wfa.training_window,
        testing_window=task_config.wfa.testing_window,
        step_size=task_config.wfa.step_size,
        tanh_k=task_config.wfa.tanh_k,
        hold_n=task_config.wfa.hold_n,
        gap=task_config.wfa.gap,
        min_periods=task_config.wfa.min_periods,
        correlation_method=task_config.wfa.correlation_method
    )
    
    # 参数验证
    if wfa_params.training_window < 100:
        raise ValueError("训练窗口过小，至少需要100个交易日")
    if wfa_params.testing_window < 20:
        raise ValueError("测试窗口过小，至少需要20个交易日")
    
    criteria_params = dict(task_config.criteria)
    query_params = dict(task_config.factor_query)
    
    print(f"✅ 配置加载成功:")
    print(f"   - 训练窗口: {wfa_params.training_window}日")
    print(f"   - 测试窗口: {wfa_params.testing_window}日")
    print(f"   - S型参数: {wfa_params.tanh_k}")
    print(f"   - 通过标准: 夏普>{criteria_params['min_sharpe']}, 回撤<{criteria_params['max_mdd']:.1%}")
    
    return wfa_params, criteria_params, query_params


def query_l2_passed_factors(query_params: Dict) -> List[Dict]:
    """查询L2阶段通过的因子"""
    print(f"🔍 查询L2阶段通过的因子...")
    
    filters = {
        'pipeline_step': query_params['source_pipeline_step'],
        'status': 'L2_PASSED'
    }
    
    factors = factorzoo.search_factors(
        filters, 
        limit=query_params.get('factor_limit_per_batch', 100)
    )
    
    if not factors:
        print("⚠️ 未找到L2_PASSED状态的因子")
        return []
    
    print(f"✅ 找到 {len(factors)} 个L2通过的因子")
    
    # 按品种分组统计
    symbol_counts = {}
    for factor in factors:
        symbols = factor.get('symbols', 'unknown')
        if isinstance(symbols, list) and symbols:
            symbol = symbols[0]
        else:
            symbol = str(symbols) if symbols else 'unknown'
        
        symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1
    
    print("📊 因子分布:")
    for symbol, count in sorted(symbol_counts.items()):
        print(f"   - {symbol}: {count}个")
    
    return factors


def load_factor_and_price_data(factor_id: str) -> Tuple[pd.Series, pd.Series, str]:
    """加载因子值和价格数据"""
    try:
        # 获取因子元数据
        factor_info = factorzoo.get_factor_info(factor_id)
        if not factor_info:
            raise ValueError(f"因子 {factor_id} 信息不存在")
        
        # 提取品种信息
        symbols = factor_info.get('symbols', 'unknown')
        if isinstance(symbols, list) and symbols:
            symbol = symbols[0]
        else:
            symbol = str(symbols) if symbols else 'unknown'
        
        # 加载因子值数据
        factor_value_manager = get_factor_value_manager()
        factor_data = factor_value_manager.load_factor_values(factor_id)
        
        if factor_data is None or len(factor_data) == 0:
            raise ValueError(f"因子 {factor_id} 数据为空")
        
        # 加载价格数据
        factor_loader = FactorLoader()
        price_data = factor_loader.load_price_data(symbol)
        
        if price_data is None or len(price_data) == 0:
            raise ValueError(f"品种 {symbol} 价格数据为空")
        
        # 数据对齐
        common_index = factor_data.index.intersection(price_data.index)
        if len(common_index) < 100:
            raise ValueError(f"有效数据点不足: {len(common_index)}")
        
        factor_aligned = factor_data.loc[common_index]
        price_aligned = price_data.loc[common_index]
        
        print(f"   ✅ 数据加载成功: {len(factor_aligned)}个数据点")
        return factor_aligned, price_aligned, symbol
        
    except Exception as e:
        print(f"   ❌ 数据加载失败: {str(e)}")
        raise


def batch_wfa_validation(factors: List[Dict], 
                        wfa_params: WFAParams,
                        criteria_params: Dict) -> Tuple[List[WFAResult], List[str]]:
    """批量WFA验证处理"""
    print(f"\n🚀 开始批量WFA验证 ({len(factors)}个因子)...")
    
    results = []
    failed_factors = []
    passed_count = 0
    
    start_time = datetime.now()
    
    for i, factor_info in enumerate(factors, 1):
        factor_id = factor_info['factor_id']
        
        try:
            print(f"\n📊 [{i}/{len(factors)}] 处理因子: {factor_id}")
            
            # 加载数据
            factor_data, price_data, symbol = load_factor_and_price_data(factor_id)
            
            # WFA验证
            print(f"   🔄 执行WFA验证...")
            wfa_result = run_wfa_validation(factor_data, price_data, wfa_params)
            wfa_result.factor_id = factor_id
            wfa_result.symbol = symbol
            
            # 通过标准判定
            pass_status, fail_reasons = check_wfa_criteria(wfa_result.metrics, criteria_params)
            wfa_result.pass_status = pass_status
            wfa_result.fail_reasons = fail_reasons
            
            # 输出结果摘要
            metrics = wfa_result.metrics
            print(f"   📈 绩效指标:")
            print(f"      - 夏普比率: {metrics['sharpe_ratio']:.3f}")
            print(f"      - 最大回撤: {metrics['max_drawdown']:.2%}")
            print(f"      - 胜率: {metrics['win_rate']:.2%}")
            print(f"      - 卡玛比率: {metrics.get('calmar_ratio', 0):.3f}")
            
            if pass_status == 'L3_PASSED':
                print(f"   ✅ 通过WFA验证")
                passed_count += 1
            else:
                print(f"   ❌ 未通过WFA验证:")
                for reason in fail_reasons:
                    print(f"      - {reason}")
            
            results.append(wfa_result)
            
            # 进度报告
            if i % 10 == 0:
                elapsed = (datetime.now() - start_time).total_seconds()
                avg_time = elapsed / i
                eta = avg_time * (len(factors) - i)
                print(f"\n📈 进度报告: {i}/{len(factors)} ({i/len(factors)*100:.1f}%)")
                print(f"   - 已通过: {passed_count}个")
                print(f"   - 平均耗时: {avg_time:.1f}秒/因子")
                print(f"   - 预计剩余: {eta/60:.1f}分钟")
            
        except Exception as e:
            print(f"   ❌ 处理失败: {str(e)}")
            failed_factors.append(factor_id)
            continue
    
    # 最终统计
    total_time = (datetime.now() - start_time).total_seconds()
    print(f"\n✅ 批量验证完成!")
    print(f"📊 最终统计:")
    print(f"   - 总处理: {len(factors)}个")
    print(f"   - 成功验证: {len(results)}个")
    print(f"   - 通过验证: {passed_count}个")
    print(f"   - 失败处理: {len(failed_factors)}个")
    print(f"   - 总耗时: {total_time/60:.1f}分钟")
    print(f"   - 通过率: {passed_count/len(results)*100:.1f}%" if results else "   - 通过率: 0%")
    
    return results, failed_factors


def save_results_to_factorzoo(wfa_results: List[WFAResult], 
                             wfa_params: WFAParams) -> int:
    """保存WFA结果到FactorZoo"""
    print(f"\n💾 保存WFA结果到FactorZoo...")
    
    success_count = 0
    
    for wfa_result in wfa_results:
        try:
            success = save_wfa_to_factorzoo(wfa_result, wfa_params.__dict__)
            if success:
                success_count += 1
            else:
                print(f"   ❌ 因子 {wfa_result.factor_id} 保存失败")
                
        except Exception as e:
            print(f"   ❌ 因子 {wfa_result.factor_id} 保存异常: {str(e)}")
            continue
    
    print(f"✅ FactorZoo保存完成: {success_count}/{len(wfa_results)}个成功")
    return success_count


def generate_batch_reports(wfa_results: List[WFAResult]) -> int:
    """生成批量WFA报告"""
    print(f"\n📊 生成WFA可视化报告...")
    
    try:
        success_count, failed_factors = batch_generate_wfa_reports(wfa_results, use_quantstats=True)
        
        print(f"✅ 报告生成完成: {success_count}/{len(wfa_results)}个成功")
        if failed_factors:
            print(f"❌ 失败因子: {failed_factors}")
        
        return success_count
        
    except Exception as e:
        print(f"❌ 批量报告生成失败: {str(e)}")
        return 0


def main():
    """L3动态稳健性检验主流程"""
    print("=" * 80)
    print("🎯 L3动态稳健性检验 - WFA验证系统")
    print("=" * 80)
    
    try:
        # 1. 加载配置
        wfa_params, criteria_params, query_params = load_and_validate_config()
        
        # 2. 查询待验证因子
        factors = query_l2_passed_factors(query_params)
        if not factors:
            print("⚠️ 没有找到待验证的因子，程序退出")
            return
        
        # 3. 批量WFA验证
        wfa_results, failed_factors = batch_wfa_validation(factors, wfa_params, criteria_params)
        
        if not wfa_results:
            print("⚠️ 没有成功验证的因子，程序退出")
            return
        
        # 4. 保存结果到FactorZoo
        saved_count = save_results_to_factorzoo(wfa_results, wfa_params)
        
        # 5. 生成可视化报告
        report_count = generate_batch_reports(wfa_results)
        
        # 6. 最终总结
        passed_results = [r for r in wfa_results if r.pass_status == 'L3_PASSED']
        
        print("\n" + "=" * 80)
        print("🎉 L3动态稳健性检验完成!")
        print("=" * 80)
        print(f"📊 处理统计:")
        print(f"   - 查询因子: {len(factors)}个")
        print(f"   - 成功验证: {len(wfa_results)}个")
        print(f"   - 通过验证: {len(passed_results)}个")
        print(f"   - 保存成功: {saved_count}个")
        print(f"   - 报告生成: {report_count}个")
        print(f"   - 失败因子: {len(failed_factors)}个")
        
        if passed_results:
            print(f"\n🏆 通过验证的优质因子:")
            for result in passed_results[:10]:  # 显示前10个
                metrics = result.metrics
                print(f"   - {result.factor_id}: 夏普={metrics['sharpe_ratio']:.3f}, 回撤={metrics['max_drawdown']:.2%}")
        
        print(f"\n📁 报告位置: {REPORTS_DIR}/wfa_validation/")
        print("🔍 可通过FactorZoo查询详细结果")
        
    except Exception as e:
        print(f"\n❌ 程序执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
```

## 使用说明

### 运行方式
```bash
# 直接运行脚本
python script/投研_因子挖掘集成/L3动态稳健性检验.py

# 或使用模块方式运行
python -m script.投研_因子挖掘集成.L3动态稳健性检验
```

### 输出结果
1. **控制台输出**: 详细的处理进度和结果统计
2. **FactorZoo数据库**: WFA绩效指标和状态更新
3. **可视化报告**: HTML报告和PNG图表文件
4. **批次汇总**: 批次处理的汇总分析

### 监控要点
- 处理进度和耗时统计
- 通过率和失败原因分析
- 绩效指标分布情况
- 异常因子识别和处理

---

**注**: 此脚本遵循项目的过程式编程风格，注重执行效率和可观测性。建议在正式使用前进行充分测试。
