"""Genetic Programming in Python, with a scikit-learn inspired API

``gplearn`` is a set of algorithms for learning genetic programming models.

"""
__version__ = '0.4.2'

# __all__ = ['genetic', 'functions', 'fitness']
from .functions import *
from .genetic import *
from .fitness import _fitness_map

# -----------------------------------------------------------------------------
# 兼容性补丁
# -----------------------------------------------------------------------------
# 在部分新版 / 精简版 scikit-learn 中, BaseEstimator 可能不包含 __sklearn_tags__ 方法。
# gplearn 内部的 _get_tags 调用会因此触发 AttributeError: 'super' object has no attribute '__sklearn_tags__'。
# 这里动态为 sklearn.base.BaseEstimator 注入一个空实现, 从而保证向后兼容。
try:
    from sklearn.base import BaseEstimator
except Exception as _e:  # pragma: no cover
    BaseEstimator = None  # 如果用户尚未安装 sklearn, 静默忽略, 后续导入会报更清晰的错误

if BaseEstimator is not None and not hasattr(BaseEstimator, "__sklearn_tags__"):
    def _dummy_sklearn_tags(self):  # noqa: D401
        """Return empty sklearn tags (动态注入)."""
        return {}

    # 动态打补丁
    setattr(BaseEstimator, "__sklearn_tags__", _dummy_sklearn_tags)

# -----------------------------------------------------------------------------