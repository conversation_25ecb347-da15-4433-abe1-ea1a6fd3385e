import sqlite3
from dataclasses import dataclass,asdict
from typing import List, Optional
from datetime import datetime
from sqlite3 import Error
import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
  
from jzal_pro.cls_common import BaseObj, ResMonitor

@dataclass
class RunData:
    run_uid: str
    task_uid: str # 关联到gp_tasks表
    symbol: str # 支持510050.SH_15 这种
    feature_amount: int
    func_amount: int
    bgn_run: Optional[str] = None
    end_run: Optional[str] = None
    gp_cost: Optional[float] = None  # GP挖掘时长，单位秒
    s1_cost: Optional[float] = None  # select by sharpe，单位秒, 显示方式为xx:xx:xx, 以下同
    s2_cost: Optional[float] = None  # select by corr，单位秒
    s3_cost: Optional[float] = None  # select by skew，单位秒
    s4_cost: Optional[float] = None  # select by kurt，单位秒
    save_cost: Optional[float] = None  # 存储时长，单位秒
    total_cost: Optional[float] = None  # 总耗时，单位秒
    cpu_usage: Optional[float] = None  # CPU使用率
    memory_usage: Optional[float] = None  # 内存使用量，单位可以自定义（例如MB)

class RunDataMgr(BaseObj):
    def __init__(self, db_path: str):
        super().__init__()
        self.db_path = db_path
        self.table_name = 'gp_runs'  # 表名写死
        self._create_table()

    def _create_table(self):        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    CREATE TABLE IF NOT EXISTS [{self.table_name}] (
                        run_uid TEXT PRIMARY KEY,
                        task_uid TEXT NOT NULL,
                        symbol TEXT NOT NULL,
                        feature_amount INTEGER NOT NULL,
                        func_amount INTEGER NOT NULL,
                        bgn_run TEXT,
                        end_run TEXT,
                        gp_cost TEXT,
                        s1_cost TEXT,
                        s2_cost TEXT,
                        s3_cost TEXT,
                        s4_cost TEXT,
                        save_cost TEXT,
                        total_cost TEXT,
                        cpu_usage TEXT,
                        memory_usage TEXT,
                        FOREIGN KEY (task_uid) REFERENCES gp_tasks(task_uid)
                    )
                ''')
                conn.commit()
        except Error as e:
            self.log(f"创建{self.table_name}表时发生错误: {e}")
            
    def add_data(self, run_data: RunData):
        """插入运行数据记录，假设run_data中的时长字段已经是xx:xx:xx格式，内存使用是MB格式"""
        run_data_dict = asdict(run_data)
        self._insert_data(run_data_dict)

    def _insert_data(self, data_dict):
        """通用插入数据方法"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                placeholders = ', '.join(['?'] * len(data_dict))
                columns = ', '.join(data_dict.keys())
                query = f"INSERT INTO [{self.table_name}] ({columns}) VALUES ({placeholders})"
                cursor.execute(query, tuple(data_dict.values()))
                conn.commit()
        except Error as e:
            self.log(f"插入数据时发生错误: {e}")
            
    def update_field(self, run_uid: str, field: str, value):
        """更新单个字段的值，对于时长和CPU, memory使用，传入的value应已经是处理过的格式"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f"UPDATE [{self.table_name}] SET {field} = ? WHERE run_uid = ?", (value, run_uid))
                conn.commit()
        except Error as e:
            self.log(f"更新{field}时发生错误: {e}")

    def get_data_by_uid(self, run_uid: str) -> Optional[RunData]:
        """通过run_uid获取运行数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'SELECT * FROM [{self.table_name}] WHERE run_uid = ?', (run_uid,))
                row = cursor.fetchone()
                if row:
                    return RunData(*row)
                return None
        except Error as e:
            self.log(f"获取运行数据时发生错误: {e}")

    def delete_data_by_uid(self, run_uid: str) -> None:
        """通过task_uid删除运行数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'DELETE FROM [{self.table_name}] WHERE run_uid = ?', (run_uid,))
        except Error as e:
            self.log(f"删除运行数据时发生错误: {e}")
            
    def get_all_data(self) -> List[RunData]:
        """获取表中的所有运行数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f"SELECT * FROM [{self.table_name}]")
                rows = cursor.fetchall()
                return [RunData(*row) for row in rows]
        except Error as e:
            self.log(f"获取所有运行数据时发生错误: {e}")
            return []
        
    def set_bgn_run(self, run_uid: str):
        """设置指定run_uid的bgn_run为当前日期时间"""
        current_dt = datetime.now().strftime('%y/%m/%d %H:%M:%S')
        self.update_field(run_uid, 'bgn_run', current_dt)
        
    def set_end_run(self, run_uid: str):
        """设置指定run_uid的end_run为当前日期时间"""
        current_dt = datetime.now().strftime('%y/%m/%d %H:%M:%S')
        self.update_field(run_uid, 'end_run', current_dt)

if __name__ == "__main__":
    import time

    # 初始化资源监控器和数据管理器
    res_monitor = ResMonitor()
    db_path = "test_run_data.db"
    table_name = "run_data_test"
    data_mgr = RunDataMgr(db_path, table_name)

    # 模拟资源消耗统计
    task_id = "TASK_001"
    res_monitor.start_timer(task_id)
    time.sleep(5)  # 模拟任务运行
    res_monitor.end_timer(task_id)
    mem_used = res_monitor.log_memory_usage("Memory Usage")
    cpu_used = res_monitor.log_cpu_usage("CPU Usage")

    # 准备并插入RunData
    run_id = "RUN_003"
    run_data = RunData(run_uid=run_id, task_uid=task_id, feature_amount=10, func_amount=5, 
                      gp_cost=res_monitor._to_time_format(5), total_cost=res_monitor._to_time_format(5),
                      cpu_usage=cpu_used, memory_usage=mem_used)
    data_mgr.add_data(run_data)

    # 查询并打印结果（这里简化处理，假设get_run_data方法已实现）
    print("查询并打印测试数据...")
    results = data_mgr.get_data_by_uid(run_uid=run_id)
    print(results)

