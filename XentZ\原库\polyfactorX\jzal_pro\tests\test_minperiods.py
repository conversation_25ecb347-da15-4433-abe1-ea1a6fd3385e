import numpy as np
import pandas as pd
import time

def norm(x):
    return x
def _flatten(x):
    if isinstance(x, pd.Series):
        x1 = x.to_numpy().flatten()
    elif isinstance(x, np.ndarray):
        x1 = x.flatten()
    return x1

def ts_mean_return_5(x1):
    t = 5
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    # x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        x_min_periods = x_min_periods[~np.isnan(x_min_periods)]
        # 计算百分比变化的均值
        pct_changes = np.diff(x_min_periods) / np.where(x_min_periods[:-1] != 0, x_min_periods[:-1], 1e-10)
        mean_returns = np.mean(pct_changes)
        result[i-1] = mean_returns
    
    # 对滑动窗口内的元素计算百分比变化的均值
    pct_changes = np.diff(x, axis=1) / np.where(x[:, :-1] != 0, x[:, :-1], 1e-10)
    mean_returns = np.nanmean(pct_changes, axis=1)
    result[t-1:] = mean_returns
    
    return norm(np.nan_to_num(result))

def ts_sum_3(x1):  # 性能提升1700倍!
    t = 5
    x1_flat = _flatten(x1)
    # 检查x1的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
        # return np.full(len(x1_flat), 0.0)
    
    x1_flat = np.nan_to_num(x1_flat) # for this func only...
    
    result = np.full(len(x1_flat), np.nan) # 预先分配一个全是NaN的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t): # min_periods处理
        x_min_periods = x1_flat[:i]
        # for sum
        sums = np.sum(x_min_periods)
        result[i-1] = sums        
        # if np.isnan(x_min_periods).any():
        #     result[i-1] = -1
        # else:
        #     try:
                # if x_min_periods[0] == 0:
                #     result[i-1] = -1
                # else:
                    # x_min_periods = x_min_periods / x_min_periods[0]  # 归一化
                    # slope = np.polyfit(range(len(x_min_periods)), x_min_periods, 1)[0]
                    # result[i-1] = slope
            # except:
            #     result[i-1] = -1
    
    sums = np.sum(x, axis=1)
    result[t-1:] = sums
    
    return norm(np.nan_to_num(result))

def ts_sum_3_orig(x1):  # the i_th element is the sum of the elements in the n-period time series from the past
    t = 5
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).apply(np.sum))
    return norm(x)

# 生成测试数据
np.random.seed(42)
data = np.random.randn(30000)
se = pd.Series(data)

# 原始函数测试
start_time = time.time()
result_original = ts_sum_3_orig(se)
original_time = time.time() - start_time

# 优化后的函数测试
start_time = time.time()
result_optimized = ts_sum_3(se)
optimized_time = time.time() - start_time

pd.concat([se, pd.Series(result_original),pd.Series(result_optimized)],axis=1).to_csv('out1.csv')
# 比较结果一致性
assert np.allclose(result_original, result_optimized, equal_nan=True), "Results are not consistent"

# 打印性能提升
print(f"Original function time: {original_time:.6f} seconds")
print(f"Optimized function time: {optimized_time:.6f} seconds")
print(f"Performance improvement: {original_time / optimized_time:.2f}x")

# 测试异常值情况
se_with_nans = se.copy()
se_with_nans[0] = np.nan
se_with_nans[100] = np.nan
se_with_nans[50] = np.nan

result_original_with_nans = ts_sum_3_orig(se_with_nans)
result_optimized_with_nans = ts_sum_3(se_with_nans)

pd.concat([se_with_nans, pd.Series(result_original_with_nans),pd.Series(result_optimized_with_nans)],axis=1).to_csv('out2.csv')
assert np.allclose(result_original_with_nans, result_optimized_with_nans, equal_nan=True), "Results with NaNs are not consistent"

# 测试短序列情况
short_se = pd.Series(np.random.randn(4))
result_original_short = ts_sum_3_orig(short_se)
result_optimized_short = ts_sum_3(short_se)

pd.concat([short_se, pd.Series(result_original_short),pd.Series(result_optimized_short)],axis=1).to_csv('out3.csv')

assert np.allclose(result_original_short, result_optimized_short, equal_nan=True), "Results with short series are not consistent"