#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FactorZoo 监控模块
提供FactorZoo专用的资源监控和性能分析功能
"""

from typing import Dict, Any
from contextlib import contextmanager
import sqlite3
from datetime import datetime

from common.cls_base import ResMonitor, MonitorContext
from .config_zoo import FACTOR_ZOO_DB

class FactorMonitorContext(MonitorContext):
    """FactorZoo专用监控上下文"""
    
    def __init__(self, label: str, operation_type: str = None, 
                 batch_id: str = None, factor_id: str = None, 
                 symbol: str = None, data_size: int = None, **kwargs):
        """初始化FactorZoo监控上下文
        
        Args:
            label: 监控标签
            operation_type: 操作类型 (如 'gp_mining', 'factor_calculation' 等)
            batch_id: 批次ID
            factor_id: 因子ID
            symbol: 交易标的
            data_size: 数据规模
            **kwargs: 其他参数
        """
        super().__init__(label, **kwargs)
        self.operation_type = operation_type or label
        self.factor_id = factor_id
        self.batch_id = batch_id
        self.symbol = symbol
        self.data_size = data_size
        self.extra_info = kwargs
        self.has_error = False
        self.error = None
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出时保存监控数据到FactorZoo数据库"""
        # 记录错误状态
        if exc_type is not None:
            self.has_error = True
            self.error = exc_val
        
        # 调用父类的退出逻辑
        super().__exit__(exc_type, exc_val, exc_tb)
        
        # 保存到FactorZoo数据库
        self._save_to_factorzoo_db()

        # 如果有factor_id且操作成功，更新因子性能数据
        if self.factor_id and not self.has_error:
            self._update_factor_performance()
        
    def _save_to_factorzoo_db(self):
        """保存监控数据到FactorZoo数据库"""
        try:
            with sqlite3.connect(FACTOR_ZOO_DB) as conn:
                cursor = conn.cursor()
                
                # 计算总耗时(秒)
                total_time_seconds = 0
                if self.start_time and self.end_time:
                    total_time_seconds = (self.end_time - self.start_time).total_seconds()
                
                # 构建插入数据，匹配数据库表结构，确保必需字段不为空
                run_uid = f"{self.operation_type}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
                
                data = {
                    'run_uid': run_uid,  # 确保有唯一标识
                    'operation_type': self.operation_type or 'unknown',
                    'factor_id': self.factor_id or f"factor_{run_uid}",
                    'batch_id': self.batch_id or f"batch_{run_uid}",
                    'start_time': self.start_time.isoformat() if self.start_time else None,
                    'end_time': self.end_time.isoformat() if self.end_time else None,
                    'total_time_ms': int(total_time_seconds * 1000) if total_time_seconds else 0,
                    'cpu_time': self.cpu_usage or '0.00s user, 0.00s system',
                    'memory_usage_mb': self.memory_delta if self.memory_delta else 0.0,
                    'data_size': self.data_size or 0,
                    'success': not self.has_error,
                    'error_message': str(self.error) if self.has_error else None,
                    'notes': str(self.extra_info) if self.extra_info else None,
                    'created_at': datetime.now().isoformat()
                }
                
                # 动态构建SQL
                columns = [k for k, v in data.items() if v is not None]
                values = [data[k] for k in columns]
                placeholders = ', '.join(['?' for _ in columns])
                columns_str = ', '.join(columns)
                
                cursor.execute(f"""
                    INSERT INTO factor_performance_logs ({columns_str})
                    VALUES ({placeholders})
                """, values)
                
                conn.commit()
                
        except Exception as e:
            print(f"保存监控数据到FactorZoo失败: {e}")

    def _update_factor_performance(self):
        """更新因子性能数据 - 顺带填充性能字段"""
        try:
            # 计算性能指标
            total_time_seconds = 0
            if self.start_time and self.end_time:
                total_time_seconds = (self.end_time - self.start_time).total_seconds()

            computation_time_ms = int(total_time_seconds * 1000) if total_time_seconds else None
            memory_usage_mb = self.memory_delta if hasattr(self, 'memory_delta') and self.memory_delta else None

            # 只有在有实际性能数据时才更新
            if computation_time_ms or memory_usage_mb:
                with sqlite3.connect(FACTOR_ZOO_DB) as conn:
                    cursor = conn.cursor()

                    # 构建更新参数
                    update_params = {}
                    if computation_time_ms:
                        update_params['last_computation_time_ms'] = computation_time_ms
                    if memory_usage_mb:
                        update_params['max_memory_usage_mb'] = memory_usage_mb

                    # 添加更新时间
                    update_params['last_performance_update'] = datetime.now()

                    if update_params:
                        # 构建SQL
                        set_clause = ', '.join([f"{k} = ?" for k in update_params.keys()])

                        cursor.execute(f"""
                            UPDATE factors SET {set_clause}
                            WHERE factor_id = ?
                        """, list(update_params.values()) + [self.factor_id])

                        conn.commit()

        except Exception as e:
            # 静默处理更新错误，不影响主流程
            pass

class FactorZooRunManager:
    """FactorZoo运行管理器，提供性能分析功能"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or str(FACTOR_ZOO_DB)
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    def get_gp_pipeline_analysis(self, batch_id: str = None, 
                                symbol: str = None) -> Dict[str, Any]:
        """获取GP流水线性能分析
        
        Args:
            batch_id: 批次ID，None表示所有批次
            symbol: 标的，None表示所有标的
            
        Returns:
            包含各步骤性能统计的字典
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = []
            params = []
            
            if batch_id:
                where_conditions.append("batch_id = ?")
                params.append(batch_id)
                
            if symbol:
                where_conditions.append("symbol = ?")
                params.append(symbol)
                
            where_clause = " AND " + " AND ".join(where_conditions) if where_conditions else ""
            
            # 查询各步骤统计
            cursor.execute(f"""
                SELECT 
                    operation_type,
                    COUNT(*) as run_count,
                    AVG(total_time_ms) as avg_time_ms,
                    MIN(total_time_ms) as min_time_ms,
                    MAX(total_time_ms) as max_time_ms,
                    AVG(memory_usage_mb) as avg_memory,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as success_rate
                FROM factor_performance_logs 
                WHERE operation_type IS NOT NULL{where_clause}
                GROUP BY operation_type
                ORDER BY AVG(total_time_ms) DESC
            """, params)
            
            steps_stats = {}
            for row in cursor.fetchall():
                steps_stats[row['operation_type']] = {
                    'run_count': row['run_count'],
                    'avg_time': round(row['avg_time_ms'] / 1000, 3) if row['avg_time_ms'] else 0,  # 转换为秒
                    'min_time': round(row['min_time_ms'] / 1000, 3) if row['min_time_ms'] else 0,
                    'max_time': round(row['max_time_ms'] / 1000, 3) if row['max_time_ms'] else 0,
                    'avg_memory': round(row['avg_memory'], 1) if row['avg_memory'] else 0,  # 已经是MB
                    'success_rate': round(row['success_rate'], 1) if row['success_rate'] else 0
                }
            
            # 计算总体统计
            cursor.execute(f"""
                SELECT 
                    COUNT(*) as total_operations,
                    AVG(total_time_ms) as avg_total_time_ms,
                    SUM(total_time_ms) as sum_total_time_ms
                FROM factor_performance_logs 
                WHERE operation_type IS NOT NULL{where_clause}
            """, params)
            
            overall = cursor.fetchone()
            
            return {
                'steps_performance': steps_stats,
                'overall_stats': {
                    'total_operations': overall['total_operations'],
                    'avg_total_time': round(overall['avg_total_time_ms'] / 1000, 3) if overall['avg_total_time_ms'] else 0,
                    'sum_total_time': round(overall['sum_total_time_ms'] / 1000, 3) if overall['sum_total_time_ms'] else 0,
                    'bottleneck_step': max(steps_stats.keys(), key=lambda k: steps_stats[k]['avg_time']) if steps_stats else None
                }
            }
    
    def get_operation_performance_stats(self, operation_type: str, 
                                      days: int = 7) -> Dict[str, Any]:
        """获取特定操作类型的性能统计
        
        Args:
            operation_type: 操作类型
            days: 查询最近天数
            
        Returns:
            性能统计数据
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_runs,
                    AVG(total_time_ms) as avg_time,
                    MIN(total_time_ms) as min_time,
                    MAX(total_time_ms) as max_time,
                    AVG(memory_usage_mb) as avg_memory,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_count,
                    COUNT(DISTINCT batch_id) as unique_batches
                FROM factor_performance_logs 
                WHERE operation_type = ? 
                AND created_at >= datetime('now', '-{} days')
            """.format(days), (operation_type,))
            
            row = cursor.fetchone()
            
            return {
                'operation_type': operation_type,
                'total_runs': row['total_runs'],
                'avg_time': round(row['avg_time'] / 1000, 3) if row['avg_time'] else 0,  # 转换为秒
                'min_time': round(row['min_time'] / 1000, 3) if row['min_time'] else 0,
                'max_time': round(row['max_time'] / 1000, 3) if row['max_time'] else 0,
                'avg_memory_mb': round(row['avg_memory'], 1) if row['avg_memory'] else 0,
                'success_rate': round(row['success_count'] * 100.0 / row['total_runs'], 1) if row['total_runs'] > 0 else 0,
                'unique_batches': row['unique_batches']
            }
    
    def get_batch_performance_summary(self, batch_id: str) -> Dict[str, Any]:
        """获取批次性能汇总
        
        Args:
            batch_id: 批次ID
            
        Returns:
            批次性能汇总数据
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 批次基本信息
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_operations,
                    COUNT(DISTINCT operation_type) as operation_types,
                    MIN(start_time) as batch_start,
                    MAX(end_time) as batch_end,
                    SUM(total_time_ms) as total_time,
                    AVG(memory_usage_mb) as avg_memory,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_count
                FROM factor_performance_logs 
                WHERE batch_id = ?
            """, (batch_id,))
            
            summary = cursor.fetchone()
            
            # 各步骤耗时占比
            cursor.execute("""
                SELECT 
                    operation_type,
                    SUM(total_time_ms) as step_total_time,
                    COUNT(*) as step_count
                FROM factor_performance_logs 
                WHERE batch_id = ?
                GROUP BY operation_type
                ORDER BY step_total_time DESC
            """, (batch_id,))
            
            steps_breakdown = []
            total_time = summary['total_time'] or 1  # 避免除零
            
            for row in cursor.fetchall():
                percentage = (row['step_total_time'] / total_time) * 100
                steps_breakdown.append({
                    'operation_type': row['operation_type'],
                    'total_time': round(row['step_total_time'], 3),
                    'count': row['step_count'],
                    'percentage': round(percentage, 1)
                })
            
            return {
                'batch_id': batch_id,
                'total_operations': summary['total_operations'],
                'operation_types': summary['operation_types'],
                'batch_duration': (
                    datetime.fromisoformat(summary['batch_end']) - 
                    datetime.fromisoformat(summary['batch_start'])
                ).total_seconds() if summary['batch_start'] and summary['batch_end'] else 0,
                'total_compute_time': round(summary['total_time'] / 1000, 3) if summary['total_time'] else 0,  # 转换为秒
                'avg_memory_mb': round(summary['avg_memory'], 1) if summary['avg_memory'] else 0,
                'success_rate': round(summary['success_count'] * 100.0 / summary['total_operations'], 1) if summary['total_operations'] > 0 else 0,
                'steps_breakdown': steps_breakdown
            }

# 向后兼容
FactorZooResMonitor = ResMonitor  # 别名保持兼容性 