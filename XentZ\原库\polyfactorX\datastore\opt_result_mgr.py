import sqlite3
from sqlite3 import Error
from datetime import datetime
import pandas as pd
from dataclasses import dataclass, fields
from typing import List, Optional
import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
    
from jzal_pro.cls_common import BaseObj
from config import DB_DIR_FACTOR

@dataclass
class OptResultData:
    model_file: str
    combination: str # 因子expr的组合, 不涉及之间的运算
    task_uid: str
    opt_uid: str
    scope: Optional[str] = None
    sr: Optional[float] = None
    cost_sr: Optional[float] = None
    tot_ret: Optional[float] = None
    ann_ret: Optional[float] = None
    ann_cost: Optional[float] = None
    ann_std: Optional[float] = None
    mdd: Optional[float] = None
    avg_dd: Optional[float] = None
    monthly_skew: Optional[float] = None
    lower_tail: Optional[float] = None
    upper_tail: Optional[float] = None
    profit_factor: Optional[float] = None
    calmar: Optional[float] = None
    trade_times: Optional[float] = None
    win_rate: Optional[float] = None
    saw_score: Optional[float] = None
    topsis_score: Optional[float] = None
    bgn_mdd: Optional[datetime] = None
    end_mdd: Optional[datetime] = None
    metric: Optional[float] = None # 暂时没用!  TODO: 重新设置 --- field #25
    def __post_init__(self):
        if self.scope is not None and self.scope not in ['in', 'out', 'all', 'all_r', 'out_r']:
            raise ValueError("scope字段必须是: 'in', 'out', 'all', 'all_r' or 'out_r'")

class OptResultDataMgr(BaseObj):
    def __init__(self, db_path: str, table_name: str):
        super().__init__()
        self.db_path = db_path
        self.tbl_name = table_name
        self._create_table() # 表不存在 则创建table
    def _create_table(self):
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    CREATE TABLE IF NOT EXISTS [{self.tbl_name}] (
                        model_file TEXT NOT NULL,
                        combination TEXT NOT NULL,
                        task_uid TEXT NOT NULL,
                        opt_uid TEXT NOT NULL,
                        scope TEXT,
                        sr REAL,
                        cost_sr REAL,
                        tot_ret REAL,
                        ann_ret REAL,
                        ann_cost REAL,
                        ann_std REAL,
                        mdd REAL,
                        avg_dd REAL,
                        monthly_skew REAL,
                        lower_tail REAL,
                        upper_tail REAL,
                        profit_factor REAL,
                        calmar REAL,
                        trade_times REAL,
                        win_rate REAL,
                        saw_score REAL,
                        topsis_score REAL,
                        bgn_mdd DATETIME,
                        end_mdd DATETIME,
                        metric REAL,
                        UNIQUE (model_file, combination, task_uid, opt_uid, scope),
                        FOREIGN KEY (task_uid) REFERENCES gp_tasks(task_uid)
                    )
                ''')
                conn.commit()
        except Error as e:
            self.log(f"创建{self.tbl_name}表时发生错误: {e}")

    def _is_close(self, value1, value2, tolerance=1e-5):
        return abs(value1 - value2) <= tolerance

    # 将 datetime 对象转换为 ISO 格式字符串
    def _datetime_to_str(self, dt: Optional[datetime]) -> Optional[str]:
        return dt.isoformat() if dt else None
            
    def add_data(self, opt_result_data: OptResultData) -> None:
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    INSERT INTO [{self.tbl_name}] (
                        model_file, combination, task_uid, opt_uid, scope, sr, cost_sr, tot_ret, ann_ret, ann_cost, 
                        ann_std, mdd, bgn_mdd, end_mdd, avg_dd, monthly_skew, 
                        lower_tail, upper_tail, profit_factor, calmar, trade_times, 
                        win_rate, saw_score, topsis_score, metric
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    opt_result_data.model_file, opt_result_data.combination, opt_result_data.task_uid, 
                    opt_result_data.opt_uid, opt_result_data.scope,
                    opt_result_data.sr, opt_result_data.cost_sr, opt_result_data.tot_ret, opt_result_data.ann_ret, 
                    opt_result_data.ann_cost, opt_result_data.ann_std, opt_result_data.mdd, 
                    self._datetime_to_str(opt_result_data.bgn_mdd), self._datetime_to_str(opt_result_data.end_mdd),
                    opt_result_data.avg_dd, opt_result_data.monthly_skew, opt_result_data.lower_tail, 
                    opt_result_data.upper_tail, opt_result_data.profit_factor, opt_result_data.calmar, 
                    opt_result_data.trade_times, opt_result_data.win_rate, opt_result_data.saw_score, 
                    opt_result_data.topsis_score, opt_result_data.metric
                ))
                conn.commit()
        except Error as e:
            self.log(f"插入优化结果数据时发生错误: {e}")

    def fetch_all_opt_results(self, scope:str = '') -> List[OptResultData]:
        """查询表中的所有优化结果数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                if scope == '':
                    cursor.execute(f"SELECT * FROM [{self.tbl_name}]")
                else:
                    cursor.execute(f"SELECT * FROM [{self.tbl_name}] WHERE scope = ?", (scope,))
                rows = cursor.fetchall()
                return [OptResultData(*row) for row in rows]
        except Error as e:
            self.log(f"查询所有优化结果数据时发生错误: {e}")
            return []
        
    def to_in_topsis_df(self, optresult_list: List[OptResultData]) -> pd.DataFrame:
        '''
        optresult_list: 通常是fetch_all_opt_results的输出(不用先保存到class的属性上)
        返回: 为用topsis算法计算分数而组装的df
        逻辑: 每行为唯一标识这行的字段值 和 OptResultData结构中的字段(除了topsis_score外)值   
        '''
        data = []
        for result in optresult_list:
            row = {
                "model_file": result.model_file,
                "combination": result.combination,
                "task_uid": result.task_uid,
                "opt_uid": result.opt_uid,
                "scope": result.scope,
                "sr": result.sr,
                "cost_sr": result.cost_sr,
                "tot_ret": result.tot_ret,
                "ann_ret": result.ann_ret,
                "ann_cost": result.ann_cost,
                "ann_std": result.ann_std,
                "mdd": result.mdd,
                "bgn_mdd": result.bgn_mdd,
                "end_mdd": result.end_mdd,
                "avg_dd": result.avg_dd,
                "monthly_skew": result.monthly_skew,
                "lower_tail": result.lower_tail,
                "upper_tail": result.upper_tail,
                "profit_factor": result.profit_factor,
                "calmar": result.calmar,
                "trade_times": result.trade_times,
                "win_rate": result.win_rate,
                "saw_score": result.saw_score
            }
            data.append(row)
        
        # 构建 DataFrame
        df = pd.DataFrame(data)
        df.set_index(['model_file', 'combination', 'task_uid', 'opt_uid', 'scope'], inplace=True)
        return df
        

    def update_metrics(self, opt_result_data: OptResultData, fields_to_update: List[str]) -> bool:
        """
        根据OptResultData实例及指定的字段列表更新数据库中的记录。
        :param opt_result_data: OptResultData实例，包含待更新的字段值。
        :param fields_to_update: 需要更新的字段名列表。
        :return: 如果成功更新返回True，否则返回False。
        """
        try:
            # 确保所有指定的更新字段都存在于OptResultData类中
            valid_fields = {field.name for field in fields(OptResultData)} - {'model_file', 'combination', 'task_uid', 'opt_uid', 'scope'}
            if not all(field in valid_fields for field in fields_to_update):
                raise ValueError("Some fields to update are not valid.")
            
            # 构建更新字段的SQL部分
            update_fields = ', '.join([f"{field} = ?" for field in fields_to_update])
            
            # 准备参数列表，先放置model_file, combination, task_uid, opt_uid，然后是需要更新字段的值
            values = [getattr(opt_result_data, field) for field in fields_to_update] + \
                    [getattr(opt_result_data, field) for field in ('model_file', 'combination', 'task_uid', 'opt_uid', 'scope')]
                     
                     
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    UPDATE [{self.tbl_name}]
                    SET {update_fields}
                    WHERE model_file = ? AND combination = ? AND task_uid = ? AND opt_uid = ? AND scope = ?
                ''', values)
                conn.commit()
                if cursor.rowcount > 0: 
                    return True
                else:
                    self.log("没有找到匹配的记录进行更新")
                    return False
        except Error as e:
            self.log(f"根据OptResultData及指定字段列表更新记录时发生错误: {e}")
            return False
        
    # def update_metrics2(self, model_file: str, combination: str, 
    #                     task_uid: str, opt_uid: str, **kwargs) -> bool:
    #     """更新指定优化结果数据的多项度量值"""
    #     try:
    #         update_fields = ', '.join([f"{key} = ?" for key in kwargs.keys()])
    #         values = list(kwargs.values()) + [model_file, combination, task_uid, opt_uid]
    #         with sqlite3.connect(self.db_path) as conn:
    #             cursor = conn.cursor()
    #             cursor.execute(f'''
    #                 UPDATE [{self.tbl_name}]
    #                 SET {update_fields}
    #                 WHERE model_file = ? AND combination = ? AND task_uid = ? AND opt_uid = ?
    #             ''', values)
    #             if cursor.rowcount > 0:
    #                 conn.commit()
    #                 return True
    #             else:
    #                 self.log("没有找到匹配的记录进行更新")
    #                 return False
    #     except Error as e:
    #         self.log(f"更新优化结果数据度量值时发生错误: {e}")
    #         return False

    def update_metrics_by_df(self, df: pd.DataFrame) -> bool:
        """
        根据DataFrame的索引和列更新数据库中的记录。
        DataFrame的索引应包含 ('model_file', 'combination', 'task_uid', 'opt_uid')，列是要更新的字段值。
        :param df: DataFrame，其中索引是用于匹配记录的主键，列是要更新的字段值。
        :return: 如果成功更新至少一条记录返回True，否则返回False。
        """
        try:
            # 检查索引是否包含所需的列
            if not all(col in df.index.names for col in ['model_file', 'combination', 'task_uid', 'opt_uid', 'scope']):
                raise ValueError("DataFrame的索引必须包含 ('model_file', 'combination', 'task_uid', 'opt_uid', 'scope')")
            
            # 确保所有更新字段都是OptResultData中的有效字段
            valid_fields = {field.name for field in fields(OptResultData)}
            for col in df.columns:
                if col not in valid_fields:
                    raise ValueError(f"字段 {col} 不是有效的更新字段")

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                for index, row in df.iterrows():
                    model_file, combination, task_uid, opt_uid, scope = index
                    update_fields = ', '.join([f"{col} = ?" for col in row.index])
                    values = list(row) + [model_file, combination, task_uid, opt_uid, scope]

                    cursor.execute(f'''
                        UPDATE [{self.tbl_name}]
                        SET {update_fields}
                        WHERE model_file = ? AND combination = ? AND task_uid = ? AND opt_uid = ? AND scope = ?
                    ''', values)
                
                conn.commit()
                if cursor.rowcount > 0:
                    return True
                else:
                    self.log("没有找到匹配的记录进行更新")
                    return False
        except sqlite3.Error as e:
            self.log(f"根据DataFrame更新记录时发生错误: {e}")
            return False
        
    def update_topsis_by_df(self, df: pd.DataFrame) -> bool:
        """
        根据DataFrame的索引和列更新数据表中topsis字段的所有值。
        DataFrame的索引应包含 ('model_file', 'combination', 'task_uid', 'opt_uid', 'scope')，列是要更新的字段值。
        :param df: DataFrame，其中索引是用于匹配记录的主键，列是要更新的字段值。
        :return: 如果成功更新至少一条记录返回True，否则返回False。
        """
        try:
            # 检查索引是否包含所需的列
            if not all(col in df.index.names for col in ['model_file', 'combination', 'task_uid', 'opt_uid', 'scope']):
                raise ValueError("DataFrame的索引必须包含 ('model_file', 'combination', 'task_uid', 'opt_uid', 'scope')")
            
            # 确保所有更新字段都是OptResultData中的有效字段
            valid_fields = {field.name for field in fields(OptResultData)}
            for col in df.columns:
                if col not in valid_fields:
                    raise ValueError(f"字段 {col} 不是有效的更新字段")

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                for index, row in df.iterrows():
                    model_file, combination, task_uid, opt_uid, scope = index
                    values = [round(row['topsis_score'], 3)] + [model_file, combination, task_uid, opt_uid, scope]

                    cursor.execute(f'''
                        UPDATE [{self.tbl_name}]
                        SET topsis_score = ?
                        WHERE model_file = ? AND combination = ? AND task_uid = ? AND opt_uid = ? AND scope = ?
                    ''', values)
                
                conn.commit()
                if cursor.rowcount > 0:
                    return True
                else:
                    self.log("没有找到匹配的记录进行更新")
                    return False
        except sqlite3.Error as e:
            self.log(f"根据DataFrame更新记录时发生错误: {e}")
            return False
        
    def delete_opt_result(self, model_file: str, combination: str, 
                          task_uid: str, opt_uid: str, scope: str) -> bool:
        """删除特定的优化结果数据记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    DELETE FROM [{self.tbl_name}]
                    WHERE model_file = ? AND combination = ? AND task_uid = ? AND opt_uid = ? AND scope = ?
                ''', (model_file, combination, task_uid, opt_uid, scope))
                if cursor.rowcount > 0:
                    conn.commit()
                    return True
                else:
                    self.log("没有找到匹配的记录进行删除")
                    return False
        except Error as e:
            self.log(f"删除优化结果数据记录时发生错误: {e}")
            return False

    def add_data_list(self, opt_result_list: List[OptResultData]) -> None:
        """
        批量添加优化结果数据到数据库中。
        :param opt_result_list: 包含多个OptResultData对象的列表
        """
        try:
            sql = f'''
                INSERT INTO [{self.tbl_name}] (
                        model_file, combination, task_uid, opt_uid, scope, sr, cost_sr, 
                        tot_ret, ann_ret, ann_cost, 
                        ann_std, mdd, bgn_mdd, end_mdd, avg_dd, monthly_skew, 
                        lower_tail, upper_tail, profit_factor, calmar, trade_times, 
                        win_rate, saw_score, topsis_score, metric
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            params = [
                (
                    opt_result_data.model_file, opt_result_data.combination, opt_result_data.task_uid, 
                    opt_result_data.opt_uid, opt_result_data.scope,
                    opt_result_data.sr, opt_result_data.cost_sr, opt_result_data.tot_ret, opt_result_data.ann_ret, 
                    opt_result_data.ann_cost, opt_result_data.ann_std, opt_result_data.mdd, 
                    self._datetime_to_str(opt_result_data.bgn_mdd), self._datetime_to_str(opt_result_data.end_mdd), 
                    opt_result_data.avg_dd, opt_result_data.monthly_skew, opt_result_data.lower_tail, 
                    opt_result_data.upper_tail, opt_result_data.profit_factor, opt_result_data.calmar, 
                    opt_result_data.trade_times, opt_result_data.win_rate, opt_result_data.saw_score,
                    opt_result_data.topsis_score, opt_result_data.metric
                ) for opt_result_data in opt_result_list
            ]
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.executemany(sql, params)
                conn.commit()
        except Error as e:
            self.log(f"批量插入优化结果数据时发生错误: {e}")

if __name__ == "__main__":
    def create_test_db(db_path: str, tbl_name: str):
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute(f'''
            CREATE TABLE IF NOT EXISTS {tbl_name} (
                model_file TEXT,
                combination TEXT,
                task_uid TEXT,
                opt_uid TEXT,
                sr REAL,
                ann_ret REAL,
                ann_cost REAL,
                ann_std REAL,
                mdd REAL,
                avg_dd REAL,
                monthly_skew REAL,
                lower_tail REAL,
                upper_tail REAL,
                profit_factor REAL,
                PRIMARY KEY (model_file, combination, task_uid, opt_uid)
            )
        ''')
        conn.commit()
        cursor.close()
        conn.close()

    def populate_test_db(db_path: str, tbl_name: str):
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute(f'''
            INSERT INTO {tbl_name} (model_file, combination, task_uid, opt_uid, in_sr, in_ann_ret, in_ann_cost, in_ann_std, in_mdd, in_avg_dd, in_monthly_skew, in_lower_tail, in_upper_tail, in_profit_factor)
            VALUES ('model1', 'comb1', 'task1', 'opt1', 0.5, 0.6, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8)
        ''')
        conn.commit()
        cursor.close()
        conn.close()

    def test_update_methods():
        db_path = 'test3.db'
        tbl_name = 'opt_results'
        
        # create_test_db(db_path, tbl_name)
        
        
        db_manager = OptResultDataMgr(db_path, tbl_name)
        # populate_test_db(db_path, tbl_name)
        
        
        # 测试 update_metrics_by_df 方法
        data = {
            'in_sr': [0.65],
            'in_ann_ret': [0.80],
            'in_ann_cost': [0.25]
        }
        index = pd.MultiIndex.from_tuples(
            [('model1', 'comb1', 'task1', 'opt1')],
            names=['model_file', 'combination', 'task_uid', 'opt_uid']
        )
        df = pd.DataFrame(data, index=index)
        
        success = db_manager.update_metrics_by_df(df)
        print("update_metrics_by_df success:", success)
        
    test_update_methods()