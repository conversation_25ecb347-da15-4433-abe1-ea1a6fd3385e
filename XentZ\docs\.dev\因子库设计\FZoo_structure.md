# FactorZoo 目录结构设计文档

## 📁 整体架构

```
D:/myquant/FZoo/
├── database/                          # 数据库文件
│   ├── factorzoo.sqlite               # 主数据库文件
│   ├── backups/                       # 数据库备份
│   │   ├── factorzoo_20241225_001.sqlite
│   │   └── factorzoo_20241225_002.sqlite
│   └── migrations/                    # 数据库迁移脚本
│       ├── v1_to_v2.sql
│       └── schema_updates.log
│
├── factor_values/                     # 因子值数据文件
│   ├── by_batch/                      # 按批次组织
│   │   ├── GP_20241225_001/
│   │   │   ├── factors_001_010.parquet
│   │   │   └── factors_011_020.parquet
│   │   └── MANUAL_20241225_001/
│   │       └── custom_factors.parquet
│   ├── by_symbol/                     # 按标的组织
│   │   ├── 510050.SH/
│   │   │   ├── 15min/
│   │   │   └── 1h/
│   │   └── CSI300/
│   │       └── daily/
│   └── cross_section/                 # 截面数据
│       ├── CSI300_20241225.parquet
│       └── CSI500_20241225.parquet
│
├── evaluations/                       # 评价结果
│   ├── reports/                       # 评价报告
│   │   ├── F_GP_20241225_001_001_eval_20241225.html
│   │   └── batch_GP_20241225_001_summary.html
│   ├── plots/                         # 图表文件
│   │   ├── factor_performance/
│   │   │   ├── F_GP_20241225_001_001_performance.png
│   │   │   └── F_GP_20241225_001_001_drawdown.png
│   │   └── batch_analysis/
│   │       └── GP_20241225_001_overview.png
│   └── cache/                         # 评价缓存
│       ├── backtest_results/
│       └── temp_calculations/
│
├── models/                            # 训练模型和预测
│   ├── factor_models/                 # 因子模型文件
│   │   ├── F_GP_20241225_001_001_model.pkl
│   │   └── ensemble_model_v1.pkl
│   ├── performance_models/            # 性能预测模型
│   │   └── computation_time_predictor.pkl
│   └── selection_models/              # 因子选择模型
│       └── pipeline_selector_v1.pkl
│
├── exports/                           # 导出文件
│   ├── factor_lists/                  # 因子清单
│   │   ├── active_factors_20241225.csv
│   │   └── l3_factors_momentum.csv
│   ├── configurations/                # 配置导出
│   │   ├── batch_configs/
│   │   └── pool_configs/
│   └── reports/                       # 定期报告
│       ├── monthly/
│       └── quarterly/
│
├── temp/                              # 临时文件
│   ├── preprocessing/                 # 预处理临时文件
│   ├── calculations/                  # 计算过程文件
│   └── imports/                       # 导入临时文件
│
├── logs/                              # 日志文件
│   ├── system.log                     # 系统日志
│   ├── performance.log                # 性能日志
│   ├── errors.log                     # 错误日志
│   └── archived/                      # 历史日志
│       ├── 2024/
│       │   ├── 12/
│       │   │   ├── system_20241225.log
│       │   │   └── performance_20241225.log
│       │   └── 11/
│       └── 2023/
│
└── config/                            # 配置文件
    ├── database_config.toml           # 数据库配置
    ├── storage_config.toml            # 存储配置
    ├── evaluation_config.toml         # 评价配置
    └── system_settings.toml           # 系统设置
```

## 🎯 设计理念

### 1. 按功能分离
- **`database/`** - 核心数据库，元数据管理
- **`factor_values/`** - 实际因子值数据，支持多种组织方式
- **`evaluations/`** - 评价结果和可视化
- **`models/`** - 机器学习模型存储

### 2. 按使用频率分层
- **高频访问**：`database/`, `factor_values/by_symbol/`
- **中频访问**：`evaluations/reports/`, `exports/`
- **低频访问**：`logs/archived/`, `temp/`

### 3. 支持多种数据组织
- **按批次**：GP生成的大批量因子
- **按标的**：特定标的的时序因子
- **按截面**：横截面因子数据

### 4. 便于运维管理
- **`logs/`** - 完整的日志体系
- **`temp/`** - 临时文件隔离
- **`config/`** - 配置集中管理
- **`database/backups/`** - 数据备份

## 🗂️ 详细说明

### database/ 目录
- **主要功能**：存储SQLite数据库和相关管理文件
- **关键文件**：
  - `factorzoo.sqlite` - 主数据库文件（基于factor_zoo_schema.sql创建）
  - `backups/` - 定期备份文件
  - `migrations/` - 数据库版本升级脚本

### factor_values/ 目录
- **主要功能**：存储实际的因子计算值（Parquet格式）
- **组织方式**：
  - `by_batch/` - 按GP批次组织，便于批量操作
  - `by_symbol/` - 按交易标的组织，便于时序分析
  - `cross_section/` - 截面因子数据，按日期组织

### evaluations/ 目录
- **主要功能**：因子评价结果存储
- **子目录**：
  - `reports/` - HTML格式的评价报告
  - `plots/` - 图表和可视化文件
  - `cache/` - 计算缓存，提升重复评价效率

### models/ 目录
- **主要功能**：机器学习模型存储
- **应用场景**：
  - 因子预测模型
  - 性能估算模型
  - 自动选择模型

### exports/ 目录
- **主要功能**：数据导出和报告生成
- **使用场景**：
  - 定期报告生成
  - 因子列表导出
  - 配置备份

### temp/ 目录
- **主要功能**：临时文件存储
- **清理策略**：定期自动清理，避免磁盘空间浪费

### logs/ 目录
- **主要功能**：系统日志管理
- **日志类型**：
  - 系统运行日志
  - 性能监控日志
  - 错误调试日志
- **归档策略**：按年月归档，便于问题追踪

### config/ 目录
- **主要功能**：配置文件集中管理
- **配置类型**：
  - 数据库连接配置
  - 存储路径配置
  - 评价参数配置
  - 系统运行配置

## 🚀 使用建议

### 1. 性能优化
- 高频访问的数据放在SSD上
- 大文件（如factor_values）可考虑分盘存储
- temp目录设置自动清理

### 2. 备份策略
- 数据库：每日自动备份
- 重要模型：版本化管理
- 配置文件：Git版本控制

### 3. 扩展性
- 支持多市场数据存储
- 支持分布式部署
- 支持云存储集成

## 📊 目录大小预估

| 目录 | 预估大小 | 增长速度 | 清理策略 |
|------|----------|----------|----------|
| database/ | 1-10GB | 中等 | 定期备份清理 |
| factor_values/ | 10-100GB | 快速 | 按时间清理 |
| evaluations/ | 5-50GB | 中等 | 保留重要报告 |
| models/ | 1-10GB | 慢速 | 版本化管理 |
| logs/ | 1-5GB | 中等 | 按月归档 |
| temp/ | 0-10GB | 快速 | 每日清理 |

## 🔧 配置示例

```python
# XentZ/config/settings.py 中的配置
FACTOR_ZOO_DIR = Path("D:/myquant/FZoo/")
FACTOR_ZOO_DB = FACTOR_ZOO_DIR / "database" / "factorzoo.sqlite"
FACTOR_VALUES_DIR = FACTOR_ZOO_DIR / "factor_values"
EVALUATIONS_DIR = FACTOR_ZOO_DIR / "evaluations"
MODELS_DIR = FACTOR_ZOO_DIR / "models"
```

---

**创建时间**：2024-12-25  
**版本**：v1.0  
**状态**：已实施  
**相关文件**：`factor/factor_zoo_schema.sql` 