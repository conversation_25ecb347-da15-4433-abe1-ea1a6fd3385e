TODO:
改bug: 
因子/特征/算子: demean, 不ma
label: 不demean, 不ma
yhat: demean与否试下取优, 要ma
基于单表达式计算结果...(用于gp过程中报错检查)

QQ图; label[1,2,3,4,5,7,9,12] 不超过6(xk); 不demean, 除以std/l2norm/minmax; 设计好label重要,比如期权方式过滤小bar置为0;  1:1 demean + 不demean都组合模型
yhat0.1内clip为0, A/Btest下; 样本外拟合效果图; 策略10%停,组合8%停;单因子sr样本内高于1.5不要(过拟合),样本内0-1的考虑; label用maxmin, 因子/特征 保持zscore; label:1.保号2.增强(期权定价 或 降噪小bar设0)demean和非demean都用1:1; t期变1-6都试试; 导入模型前sort fctname,importance_feature; 因子择时: 1.滚动SR(sliding_window, 从当下回看起, 一个月一个月推),2.PnL-ACF检验(低为噪音), 3.回看3-4月corr(并不重点); quantile regression对2sigma内也鲁棒(防止运气); 不超过ret的kurt,新课提6; 因子或算子单位根检验平稳性(不平稳的做差分); 日常跟踪(每月)模型中的因子weights*fctval 因子获利在总体获利占比得重要性排名,重要因子(20%?)都不行的时候就有那问题了; 10%pj放到基本的池子显著性检测; 复杂因子拆分;大周期看高概率信号小周期网格它; 
alphalens集成; ewstd研发; bt期货数据拼接rollover(doc); gp实现多metric函数并列; 多策略组合绩效报告; vwap能做label吗(有历史信息?);
拼接参考: 
https://blog.sina.com.cn/s/blog_12c3192a50102xxg8.html
https://zhuanlan.zhihu.com/p/51880559

2025-02
挖掘任务切换为toml配置文件化; 注释掉一些累积特征和算子; pp代码吸收(func/); 取消吸收generic(内存消耗问题); 实现滚动rankic的高性能算法; 挖掘后单因子筛选切换为滚动rankic平均值法; 增分位数norm(非线性模型用); 修复fitness函数bug(多一次norm); exclude_col修复, 增加t期收益率为了算ic, 整理了enums中的一些参数;

2025-01
从btq剥离, gui生成挖掘task的参数

2024-10
TBQ knn找in/out优参; 增加etf趋势策略; 

2024-09 - 优化效果: 每轮gp由4小时左右提升到10分钟左右
L2corr支持taskuid参数; gptasks表支持None日期; load函数np.float32, talib函数astype(64); gp结果入库后返去重list来保存parquet; 随机森林n_jobs=-1; 优化intel的sklearn库算法; 缩pj空间(重要性10%); 升级metric函数: 支持intercept<y.mean * 0.5筛选; 整体优化rolling.apply,总体效率提升36倍!; 添加rsrs算子到gp_func; 修改gp_func里没norm的bugs; a_expr_checker()完成; 修复selectbykurt()bug; 初始数据加载和特征计算move到run()方法; 每次run增加pj类因子的筛选(缩小结果数量); 去掉gp中的一些csv打印

2024-08
单因子样本内zero_by_bins() ok; 优化slope_pair(提升200倍); sr_inout_ratio完成; JZ003-011; fct_evaluator编写; norm增强(robust/mad_clip/cdf/demean/ma/l2norm/minmax); 增量下载15min数据脚本完成; y_hat图形化(单因子/因子组合); L2库表之间分离; y_hat/ret有关的label不demean且ema, 因子/特种不用ema; dive修改:predict; 修bug:引入pure_predict; dive中,增加全局滚动图vs测试集(pure)和训练集(滚); 组合图中实现全局滚vs训练集(滚)+测试集(pure); combo_opt中, 增加out_r(数据表 + 图形) - 滚动测试(3-m)看test集; 全局滚动的3-m滚动sr图; 最终看测试集(向训练集借2000bar)的滚动来选组合; combo_opt重构ok: 图形化,opt_result表结构修改; rollingsr用1000, norm因子用2000; 组合评估函数修改为滚动; 单因子deepdive完成; L2库重新设计(in/out/all +topsis)

2024-07
JZ002_er算子; L2corr/L2lasso完成存图流程; exper_checker筛选算子并完成清理; constant从算子move到gp funcs; kurt修复bug(不加abs);L2因子库(corred/lasso/etc过的); 绩效评分体系(SAW,TOPSIS(用测试集数据));修复corr: ret/ret_open判断且不norm的corr; label(Alphabase新增方法)与ret/ret_open区分完成(ret/ret_open不norm只1期,用于回测装pos); 特征选择: 随机抽样/deap完成; 中间计算结果存盘,优化load; 

2024-06
slz- opt写文件bug修复; hurst;
task/run/因子库/因子值库存储建立;expr结构调整完成;func_map以及对应的expr命名为ts_/ta_; select_corr修改bug完; norm优化失败!!! task剥离; 多因子装配分析重构到单独类fctmodeler; ResMonitor类加入; 统一norm_np到norm函数, 引入enums配置常量入参元组化; alaph158/wq101集成(不含rank); 重构load_as_norm; 支持常数特征; 归拢特征到AlphBase类; 优化load_fcts函数; 加入VWAP_xx特征; 修复func.py的少norm的bugs和norm()重大bug; 组装的模型的nav保持csv; funcs的min/max修复; base_features重命名; fee为0.002; quantstats-lumi; calc_stats去pct_change;文件夹结构微调;

2024-05
增加RV特征; 核对gp的sharpe与load因子后计算的sharpe对比, 并修复load_as_norm/load_a_symbol_fcts等bugs;修复load_as_norm,改为统一norm features; fit传y_raw(kwargs), 修改metric(要用真实y算ret); 修复计算因子的bug(+原始feature_exprs); 修复sendtask中weight_show的bug; expr_set(1.func_gpquant 2.func_X); gplearn自带functions.py修改(norm_np, 函数名等); load/load_as_norm 拼接cols缺陷修复; zscore windows过长修复; miniqmt模拟实盘完成; gp fitness加入sharp(初版); 因ta.RSI错误,整体修改gp数据为float64

2024-04
qmt实盘开发ing; 修复zscore对新品种的反应; 新增轮动/大类/择时等各策略, 增加df_close, 优化weightERC和targetVol; 
单因子分析框架集成(算法待优化); order等bt细节补充