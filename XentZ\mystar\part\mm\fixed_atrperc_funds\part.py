
#!/usr/bin/env python
# -*- coding:utf-8 -*-
'''
基于ATR的头寸管理(风险自控), pf中要用PF.WithoutAF, 否则tm传入的是子账户
'''
from hikyuu import *

author = "admin"
version = "20250513"

class ATRPositionSizingMM(MoneyManagerBase):  
    def __init__(self, risk_percent = 0.01, atr_period = 14, atr_multiplier = 1, 
                 max_trade_number=None, min_unit=100):  
        super(ATRPositionSizingMM, self).__init__("ATRPositionSizingMM")  
        # Set default parameters  
        self.set_param("risk_percent", risk_percent)  # Risk 1% of equity per trade  
        self.set_param("atr_period", atr_period)      # ATR calculation period  
        self.set_param("atr_multiplier", atr_multiplier)   # Multiply ATR by this value for stop distance  
        self.max_trade_number = max_trade_number
        self.set_param("min_unit", min_unit)  # Minimum trading unit
          
    def _get_buy_num(self, datetime, stock, price, risk, part_from):  
        """Calculate position size based on ATR value"""  
        # Get the KData for the stock  
        kdata = stock.get_kdata(self.query)  
          
        # Calculate ATR  
        atr_ind = ATR(kdata, self.get_param("atr_period"))  
        if len(atr_ind) == 0:  
            return 0  # Not enough data to calculate ATR  
              
        current_atr = atr_ind[-1]  # Get the latest ATR value  
          
        # Calculate risk amount in cash (% of portfolio to risk) 
        ktype = self.query.ktype 
        account_value = self.tm.get_funds(datetime, ktype).total_assets
             
        risk_amount = account_value * self.get_param("risk_percent")  
          
        # Calculate stop loss distance based on ATR  
        stop_distance = current_atr * self.get_param("atr_multiplier")  
          
        # Calculate position size: risk_amount / stop_distance  
        # (i.e., how many shares can we buy if we're risking risk_amount with a stop loss at price - stop_distance)  
        if stop_distance <= 0:  
            return 0  
              
        shares_to_buy = risk_amount / stop_distance
        
        min_unit = self.get_param("min_unit")
        shares_to_buy = (int(shares_to_buy) // min_unit) * min_unit
        
        # 获取最大可交易数量并进行限制
        max_num = self.max_trade_number
        if max_num is None:
            max_num = stock.max_trade_number
        
        # 确保最大交易数量也是最小交易单位的整数倍
        max_num = (max_num // min_unit) * min_unit
        
        if shares_to_buy > max_num:
            shares_to_buy = max_num * 0.9  # 使用90%的最大交易数量，避免边界问题
            # 再次确保是最小交易单位的整数倍
            shares_to_buy = (int(shares_to_buy) // min_unit) * min_unit
 
        return int(shares_to_buy)  
          
    def _reset(self):  
        """Reset internal state if needed"""  
        pass  
          
    def _clone(self):  
        """Create a clone of this money manager"""  
        mm = ATRPositionSizingMM(risk_percent=self.get_param("risk_percent"), 
                                 atr_period=self.get_param("atr_period"), 
                                 atr_multiplier=self.get_param("atr_multiplier"), 
                                 max_trade_number=self.max_trade_number, 
                                 min_unit=self.get_param("min_unit"))   
        return mm

def part(risk_percent = 0.01, atr_period = 14, atr_multiplier = 1, 
         max_trade_number=None, min_unit=100) -> MoneyManagerBase:
    """doc"""
    ret = ATRPositionSizingMM()
    ret.set_param("risk_percent", risk_percent)
    ret.set_param("atr_period", atr_period)
    ret.set_param("atr_multiplier", atr_multiplier)
    ret.max_trade_number = max_trade_number
    ret.set_param("min_unit", min_unit)
    return ret
    

if __name__ == "__main__":
    # 执行 testall 命令时，会多传入一个参数，防止测试时间过长
    # 比如如果在测试代码中执行了绘图操作，可以打开下面的注释代码
    # 此时执行 testall 命令时，将直接返回
    if len(sys.argv) > 1:
        ind = part()
        print(ind)
        exit(0)

    import sys
    if sys.platform == 'win32':
        import os
        os.system('chcp 65001')

    # 仅加载测试需要的数据，请根据需要修改
    options = {
        'stock_list': ['sz000001'],
        'ktype_list': ['day'],
        'preload_num': {'day_max': 100000},
        'load_history_finance': False,
        'load_weight': False,
        'start_spot': False,
        'spot_worker_num': 1,
    }
    load_hikyuu(**options)
    
    stks = tuple([sm[code] for code in options['stock_list']])
    
    # 请在下方编写测试代码
    ind = part()
    print(ind)
    
    # 显示图形
    import matplotlib.pylab as plt
    plt.show()    
