import streamlit as st
from typing import List, Tuple
from pathlib import Path
import pandas as pd
import toml
from datetime import datetime

from config import DATA_DIR_FEATHER, CONFIG_DIR
from task_defs.task_fctsmine import TaskFctsMining

def is_valid_pair_file(filename: str) -> bool:
    """检查文件名是否符合 symbol_period 格式"""
    parts = filename.split('_')
    return len(parts) == 2 and parts[1].isdigit()

def render_page(task):
    """任务配置页面"""
    st.header("任务基础配置")
    
    # 1. 全局参数设置，用紧凑的6列布局
    st.subheader("全局参数设置")
    cols = st.columns(6)
    with cols[0]:
        fee_rate = st.number_input(
            "手续费率", 0.0, 1.0,
            value=task.global_config.get('fee_rate', 0.002),
            step=0.0001,
            format="%.4f",
            key="global_fee_rate"
        )
        task.global_config['fee_rate'] = fee_rate
    with cols[1]:
        split_perc = st.slider(
            "训练集比例", 0.5, 0.9,
            value=task.global_config.get('split_perc', 0.75),
            step=0.05,
            key="global_split_perc"
        )
        task.global_config['split_perc'] = split_perc
    with cols[2]:
        t_delay = st.number_input(
            "预测步长", 1, 10,
            value=task.global_config.get('t_delay', 1),
            step=1,
            key="global_t_delay",
            help="预测步长"
        )
        task.global_config['t_delay'] = t_delay
    with cols[3]:
        start_date = st.date_input(
            "开始日期", 
            pd.to_datetime(task.global_config.get('start_date', '20050101')),
            help="默认2005-01-01",
            key="global_start_date"
        )
        task.global_config['start_date'] = start_date.strftime('%Y%m%d')
    with cols[4]:
        end_date = st.date_input(
            "结束日期", 
            pd.to_datetime(task.global_config.get('end_date', datetime.now().strftime('%Y%m%d'))),
            help="默认当前日期",
            key="global_end_date"
        )
        task.global_config['end_date'] = end_date.strftime('%Y%m%d')
    with cols[5]:
        job_num = st.number_input(
            "作业数量", 1, 16,
            value=task.global_config.get('job_num', 8),
            step=1,
            key="global_job_num",
            help="默认作业数量"
        )
        task.global_config['job_num'] = job_num

    # 2. 品种选择与配置
    st.subheader("品种选择与参数配置")
    
    # 2.1 品种文件选择
    if not DATA_DIR_FEATHER.exists():
        st.error(f"数据目录不存在: {DATA_DIR_FEATHER}")
        return
        
    feather_files = list(DATA_DIR_FEATHER.rglob("*.feather"))  # 使用rglob递归搜索所有子目录
    if not feather_files:
        st.warning(f"未在 {DATA_DIR_FEATHER} 目录及其子目录下找到任何 .feather 文件")
        return
        
    selected_files = st.multiselect(
        "选择品种文件",
        options=feather_files,
        format_func=lambda x: x.stem,  # 只显示文件名，不显示路径
        help="从feather文件夹及其子目录选择数据文件，支持多选",
        key="selected_files"
    )
    
    if selected_files:
        # 2.2 生成symbol_pairs，过滤掉不符合格式的文件
        valid_files = [f for f in selected_files if is_valid_pair_file(f.stem)]
        
        if len(valid_files) != len(selected_files):
            st.warning("部分文件名格式不正确，已被过滤。正确格式示例：510050_15")
            
        new_pairs = [(f.stem.split('_')[0], f.stem.split('_')[1]) 
                    for f in valid_files]
        
        # 如果 task 没有 selected_pairs 属性，则初始化为空列表
        if not hasattr(task, 'selected_pairs'):
            task.selected_pairs = []
        
        # 如果pairs发生变化，更新 task 中的 selected_pairs
        if new_pairs != task.selected_pairs:
            task.selected_pairs = new_pairs
            clean_invalid_configs(task, new_pairs, CONFIG_DIR / "task_mining.toml")
        
        # 2.3 显示已选品种的参数配置
        st.markdown("##### 已选品种配置")
        st.caption("若不设置则使用全局默认参数")
        
        # 2.3.1 各个pair的独立参数配置
        for symbol, period in task.selected_pairs:
            symbol_period = f"{symbol}_{period}"
            
            with st.expander(f"{symbol}（{period}分钟）", expanded=False):
                # 使用紧凑的网格布局，增加 job_num 的配置项
                cols = st.columns(6)
                with cols[0]:
                    pair_fee = st.number_input(
                        "手续费率", 0.0, 1.0,
                        task.pair_configs.get(symbol_period, {}).get('fee_rate', task.global_config.get('fee_rate', 0.002)),
                        0.0001,
                        format="%.4f",  # 显示4位小数
                        key=f"fee_{symbol_period}",
                        help="留空则使用全局设置"
                    )
                    task.pair_configs.setdefault(symbol_period, {})['fee_rate'] = pair_fee
                with cols[1]:
                    pair_split = st.slider(
                        "训练集比例", 0.5, 0.9,
                        task.pair_configs.get(symbol_period, {}).get('split_perc', task.global_config.get('split_perc', 0.75)),
                        0.05,
                        key=f"split_{symbol_period}",
                        help="留空则使用全局设置"
                    )
                    task.pair_configs[symbol_period]['split_perc'] = pair_split
                with cols[2]:
                    pair_delay = st.number_input(
                        "预测步长", 1, 10,
                        task.pair_configs.get(symbol_period, {}).get('t_delay', task.global_config.get('t_delay', 1)),
                        1,
                        key=f"delay_{symbol_period}",
                        help="留空则使用全局设置"
                    )
                    task.pair_configs[symbol_period]['t_delay'] = pair_delay
                with cols[3]:
                    pair_start = st.date_input(
                        "开始日期", pd.to_datetime(task.pair_configs.get(symbol_period, {}).get('start_date', task.global_config.get('start_date', '20050101'))),
                        key=f"start_{symbol_period}",
                        help="留空则使用全局设置"
                    )
                    task.pair_configs[symbol_period]['start_date'] = pair_start.strftime('%Y%m%d')
                with cols[4]:
                    pair_end = st.date_input(
                        "结束日期", pd.to_datetime(task.pair_configs.get(symbol_period, {}).get('end_date', task.global_config.get('end_date', datetime.now().strftime('%Y%m%d')))),
                        key=f"end_{symbol_period}",
                        help="留空则使用全局设置"
                    )
                    task.pair_configs[symbol_period]['end_date'] = pair_end.strftime('%Y%m%d')
                with cols[5]:
                    pair_job_num = st.number_input(
                        "作业数量", 1, 16,
                        task.pair_configs.get(symbol_period, {}).get('job_num', task.global_config.get('job_num', 8)),
                        1,
                        key=f"job_{symbol_period}",
                        help="留空则使用全局设置"
                    )
                    task.pair_configs[symbol_period]['job_num'] = pair_job_num

    # 检查配置是否有变化
    config_changed = is_config_changed(task)
    
    # 添加分隔线，并在下一步按钮前增加"保存配置"按钮
    st.markdown("---")
    cols = st.columns([2, 2, 1])
    with cols[0]:
        if config_changed:
            st.warning("配置有修改但尚未保存！")
        elif not task.selected_pairs:
            st.warning("请先选择至少一个品种")
    with cols[1]:
        if st.button("保存配置"):
            config_path = export_configs(task)
            st.success(f"配置已保存到: {config_path}")
    with cols[2]:
        button_disabled = config_changed or not task.config_saved or not task.selected_pairs
        if st.button("下一步", disabled=button_disabled):
            st.session_state.current_page = "特征管理"
            st.switch_page("pages/2_特征管理.py")

def save_pair_config(task, symbol_period: str, config: dict):
    """保存pair的配置"""
    # 1. 更新 task 中的配置
    if symbol_period not in task.pair_configs:
        task.pair_configs[symbol_period] = {}
    
    old_config = task.pair_configs[symbol_period].copy()
    task.pair_configs[symbol_period].update(config)
    
    # 3. 标记配置状态
    if old_config != task.pair_configs[symbol_period]:
        task.config_saved = False

def clean_invalid_configs(task, valid_pairs: List[Tuple[str, str]], config_path: Path = None):
    """清理已失效的配置
    
    Args:
        valid_pairs: 有效的品种周期对列表
        config_path: 配置文件路径（可选）
    """
    valid_keys = [f"{symbol}_{period}" for symbol, period in valid_pairs]
    
    # 清理task_configs
    invalid_keys = [k for k in task.pair_configs.keys() 
                   if k not in valid_keys]
    for k in invalid_keys:
        del task.pair_configs[k]
        st.info(f"已清理无效的task配置: {k}")
    
    # 清理 features，如果 task 有 features 属性时再处理
    if hasattr(task, 'features'):
        invalid_keys = [k for k in task.features.keys() if k not in valid_keys]
        for k in invalid_keys:
            del task.features[k]
            st.info(f"已清理无效的feature配置: {k}")
    
    # 如果提供了配置文件路径，清理配置文件中的无效配置
    if config_path and config_path.exists():
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                configs = toml.load(f)
            
            if 'pairs' in configs:
                invalid_keys = [k for k in configs['pairs'].keys() 
                              if k not in valid_keys]
                for k in invalid_keys:
                    del configs['pairs'][k]
                    st.info(f"已清理配置文件中的无效配置: {k}")
                
                with open(config_path, 'w', encoding='utf-8') as f:
                    toml.dump(configs, f)
        except Exception as e:
            st.error(f"清理配置文件失败: {str(e)}")

def reset_all_configs(task):
    """重置所有pair为全局默认参数"""
    task.pair_configs = {}
    st.success("已重置所有品种参数为全局默认值")

def export_configs(task):
    """导出当前配置到toml文件"""
    configs = {
        'global': task.global_config,
        'pairs': task.pair_configs,
        'selected_pairs': task.selected_pairs
    }
    
    config_path = CONFIG_DIR / 'task_mining.toml'
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(config_path, 'w', encoding='utf-8') as f:
        toml.dump(configs, f)
    
    # 更新配置状态
    task.last_config_path = str(config_path)
    save_current_config(task) 
    
    st.success(f"配置已导出到: {config_path}")
    return config_path

def import_configs(task):
    """从toml文件导入配置"""
    # 文件选择
    config_path = CONFIG_DIR / 'task_mining.toml'
    if not config_path.exists():
        st.warning("未找到配置文件 task_mining.toml")
        return
        
    # 读取并导入配置
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            configs = toml.load(f)
            
        # 更新全局配置和品种配置
        if 'global' in configs:
            for key, value in configs['global'].items():
                task.global_config[key] = value
                
        if 'pairs' in configs:
            task.pair_configs = configs['pairs']
            
        if 'selected_pairs' in configs:
            task.selected_pairs = configs['selected_pairs']
            
        # 保存当前配置状态
        save_current_config(task)
        
        st.success("配置导入成功!")
        
    except Exception as e:
        st.error(f"配置导入失败: {str(e)}")

def init_config_state(task):
    """初始化配置状态"""
    if not hasattr(task, 'config_saved'):
        task.config_saved = False
    if not hasattr(task, 'last_saved_config'):
        task.last_saved_config = {}

def is_config_changed(task):
    """检查配置是否有变化"""
    selected_pairs = getattr(task, 'selected_pairs', [])
    current_config = {
        'global': task.global_config.copy(),
        'pairs': task.pair_configs.copy(),
        'selected_pairs': selected_pairs.copy()  # 使用已存在的 selected_pairs，否则空列表
    }
    
    if not hasattr(task, 'last_saved_config'):
        return True
    
    return current_config != task.last_saved_config

def save_current_config(task):
    """保存当前配置状态"""
    task.last_saved_config = {
        'global': task.global_config.copy(),
        'pairs': task.pair_configs.copy(),
        'selected_pairs': task.selected_pairs.copy()
    }
    task.config_saved = True
