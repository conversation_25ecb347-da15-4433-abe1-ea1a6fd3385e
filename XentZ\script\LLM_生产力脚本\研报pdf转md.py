"""pdf_to_md_unstructured.py
================================================
功能: 
1. 使用 PyMuPDF 将 PDF 解析为完整的 Markdown，包含文本和图片
2. 智能排序页面元素，按阅读顺序输出
3. 支持两种图片策略:
   a) 若提供 img_base_url，则图片保存到本地并使用外链
   b) 否则根据文件大小自适应：小图片Base64内联，大图片压缩后Base64
4. 清洗输出，去除噪声，适配 LLM 阅读

使用示例:
直接运行脚本，会处理指定的PDF文件
"""

import base64
import io
import os
import re
import fitz  # PyMuPDF
from pathlib import Path
from typing import List, Optional, Tuple, Dict, Any
import hashlib

from PIL import Image, ImageFile
from common.cls_base import BaseObj
from common.cls_base import WORKDIR

ImageFile.LOAD_TRUNCATED_IMAGES = True

# 在文件顶部添加配置类
class PDFProcessConfig:
    """PDF处理配置类，支持不同类型文档的定制化处理"""
    
    def __init__(self, doc_type="research_report"):
        self.doc_type = doc_type
        
        # 图片过滤配置
        self.min_image_size = 10000  # 最小图片大小(bytes)
        self.min_image_width = 200   # 最小图片宽度
        self.min_image_height = 100  # 最小图片高度
        self.max_aspect_ratio = 10   # 最大长宽比
        
        # 文本去重配置
        self.enable_duplicate_filter = True
        self.duplicate_context_lines = 2  # 重复行上下文检查行数
        
        # 页眉页脚过滤配置
        self.header_footer_keywords = self._get_header_footer_keywords()
        self.header_footer_max_length = 50  # 页眉页脚最大长度
        
        # 数字行过滤配置
        self.filter_standalone_numbers = True
        self.standalone_number_max_length = 10
        
        # 保护性关键词（即使重复也要保留）
        self.protected_keywords = self._get_protected_keywords()
    
    def _get_header_footer_keywords(self):
        """根据文档类型返回页眉页脚关键词"""
        if self.doc_type == "research_report":
            return [
                "华泰证券", "研究报告", "资料来源：wind",
                "敬请阅读末页重要声明", "证券研究报告"
            ]
        elif self.doc_type == "legal_document":
            return ["页码", "文件编号"]
        elif self.doc_type == "academic_paper":
            return ["page", "doi:", "issn"]
        else:
            return []  # 通用文档不过滤页眉页脚
    
    def _get_protected_keywords(self):
        """返回保护性关键词，包含这些词的行即使重复也要保留"""
        if self.doc_type == "research_report":
            return ["风险提示", "重要声明", "投资建议", "目标价", "评级"]
        elif self.doc_type == "legal_document":
            return ["第", "条", "款", "项", "协议", "合同", "条款"]
        elif self.doc_type == "academic_paper":
            return ["abstract", "conclusion", "reference", "摘要", "结论", "参考文献"]
        else:
            return []

# ------------------------- 图片处理 ---------------------------- #

def compress_image_bytes(image_bytes: bytes, max_dim: int = 800, quality: int = 80) -> bytes:
    """压缩图片字节流"""
    try:
        img = Image.open(io.BytesIO(image_bytes))
        img = img.convert("RGB")
        
        # 按最长边缩放
        ratio = max(img.size) / max_dim if max(img.size) > max_dim else 1
        if ratio > 1:
            new_size = (int(img.width / ratio), int(img.height / ratio))
            img = img.resize(new_size, Image.LANCZOS)
        
        buf = io.BytesIO()
        img.save(buf, format="JPEG", quality=quality)
        return buf.getvalue()
    except Exception as e:
        BaseObj.log(f"图片压缩失败: {e}", level="WARNING")
        return image_bytes

def image_to_markdown(
    image_bytes: bytes,
    image_name: str,
    alt_text: str,
    images_dir: Path,
    base_url: Optional[str] = None,
    embed_thresh: int = 150_000,
) -> str:
    """将图片转换为 Markdown 格式"""
    
    # 保存图片到本地
    image_path = images_dir / image_name
    with open(image_path, "wb") as f:
        f.write(image_bytes)
    
    if base_url:
        # 使用外链
        url = base_url.rstrip("/") + "/" + image_name
        return f"![{alt_text}]({url})\n\n"
    
    # 使用 Base64
    size = len(image_bytes)
    if size > embed_thresh:
        compressed_bytes = compress_image_bytes(image_bytes)
        BaseObj.log(f"压缩图片 {image_name}: {size/1024:.1f}KB -> {len(compressed_bytes)/1024:.1f}KB")
        b64_data = base64.b64encode(compressed_bytes).decode()
    else:
        b64_data = base64.b64encode(image_bytes).decode()
    
    return f"![{alt_text}](data:image/jpeg;base64,{b64_data})\n\n"

# ----------------------- 文本清洗 ------------------------------ #

_re_zeros = re.compile(r"^0{5,}$")
_re_header_footer = re.compile(r"^\d+$|^第\s*\d+\s*页|^页\s*\d+|^\s*\d+\s*/\s*\d+\s*$")

def clean_text_lines(lines: List[str]) -> List[str]:
    """清洗文本行"""
    cleaned = []
    blank_count = 0
    
    for line in lines:
        line = line.strip()
        
        # 过滤噪声行
        if not line:
            blank_count += 1
            if blank_count <= 2:  # 最多保留2个连续空行
                cleaned.append("")
            continue
        
        blank_count = 0
        
        # 过滤全0行
        if _re_zeros.match(line):
            continue
        
        # 过滤页眉页脚（简单的页码）
        if _re_header_footer.match(line):
            continue
        
        cleaned.append(line)
    
    return cleaned

def is_valid_image(image_bytes: bytes, config: PDFProcessConfig) -> bool:
    """根据配置判断图片是否有效"""
    if len(image_bytes) < config.min_image_size:
        return False
    
    try:
        img = Image.open(io.BytesIO(image_bytes))
        width, height = img.size
        
        # 检查尺寸
        if width < config.min_image_width or height < config.min_image_height:
            return False
            
        # 检查长宽比
        aspect_ratio = max(width, height) / min(width, height)
        if aspect_ratio > config.max_aspect_ratio:
            return False
            
        return True
    except Exception:
        return False

def get_image_hash(image_bytes: bytes) -> str:
    """获取图片的MD5哈希值，用于去重"""
    return hashlib.md5(image_bytes).hexdigest()

def clean_duplicate_lines(lines: List[str], config: PDFProcessConfig) -> List[str]:
    """智能清理重复行和无意义行"""
    if not config.enable_duplicate_filter:
        return lines
        
    cleaned = []
    seen_lines = {}  # 改为字典，记录行号和上下文
    prev_line = ""
    
    for i, line in enumerate(lines):
        line = line.strip()
        
        # 跳过空行（保留适当间距）
        if not line:
            if prev_line != "":
                cleaned.append("")
                prev_line = ""
            continue
        
        # 页眉页脚过滤（增加长度限制和更精确匹配）
        if (len(line) <= config.header_footer_max_length and 
            any(keyword in line.lower() for keyword in config.header_footer_keywords) and
            not any(protected in line for protected in config.protected_keywords)):
            continue
        
        # 检查是否为重复行
        line_key = line.lower().strip()
        
        # 保护性检查：包含数字、保护关键词或上下文相关的行
        is_protected = (
            any(c.isdigit() for c in line) or  # 包含数字
            any(keyword in line for keyword in config.protected_keywords) or  # 保护关键词
            len(line) > 50 or  # 较长的行通常是重要内容
            any(char in line for char in ['。', '！', '？', '.', '!', '?'])  # 包含句号的完整句子
        )
        
        # 如果是重复行但受保护，检查上下文
        if line_key in seen_lines and not is_protected:
            # 检查上下文相关性
            prev_occurrence = seen_lines[line_key]
            if abs(i - prev_occurrence) < config.duplicate_context_lines * 10:  # 距离较近，可能是表格
                continue
        
        # 过滤单独的数字或符号（增加上下文检查）
        if (config.filter_standalone_numbers and 
            len(line) <= config.standalone_number_max_length and
            (line.replace('.', '').replace('%', '').replace('-', '').replace(',', '').isdigit() or 
             line in ['%', '-', '|', '/', '\\']) and
            not is_protected):
            continue
        
        # 记录行位置
        seen_lines[line_key] = i
        cleaned.append(line)
        prev_line = line
    
    return cleaned

# ------------------------- 主要逻辑 ---------------------------- #

def extract_pdf_content(pdf_path: Path) -> Tuple[List[Dict[str, Any]], int]:
    """提取PDF内容，返回元素列表和总页数"""
    doc = fitz.open(str(pdf_path))
    all_elements = []
    
    BaseObj.log(f"PDF共有 {len(doc)} 页，开始提取内容...")
    
    for page_num in range(len(doc)):
        page = doc[page_num]
        page_elements = []
        
        BaseObj.log(f"处理第 {page_num + 1} 页...")
        
        # 提取文本块
        text_blocks = page.get_text("blocks")
        for block in text_blocks:
            if len(block) >= 5 and block[4].strip():  # block[4] 是文本内容
                bbox = block[:4]  # (x0, y0, x1, y1)
                text = block[4].strip()
                page_elements.append({
                    "type": "text",
                    "bbox": bbox,
                    "content": text,
                    "page": page_num + 1
                })
        
        # 提取图片
        image_list = page.get_images()
        for img_index, img in enumerate(image_list):
            xref = img[0]
            try:
                # 获取图片数据
                base_image = doc.extract_image(xref)
                image_bytes = base_image["image"]
                image_ext = base_image["ext"]
                
                # 获取图片在页面上的位置
                # 注意：get_images() 不直接提供bbox，需要通过其他方式获取
                # 这里使用一个简化的方法
                img_rects = page.get_image_rects(xref)
                if img_rects:
                    bbox = img_rects[0]  # 取第一个矩形
                else:
                    # 如果无法获取确切位置，假设在页面中间
                    bbox = (100, 100 + img_index * 50, 400, 200 + img_index * 50)
                
                page_elements.append({
                    "type": "image",
                    "bbox": bbox,
                    "content": image_bytes,
                    "ext": image_ext,
                    "xref": xref,
                    "page": page_num + 1,
                    "image_name": f"page_{page_num+1}_img_{img_index+1}.{image_ext}"
                })
                
            except Exception as e:
                BaseObj.log(f"提取第 {page_num+1} 页图片 {img_index+1} 失败: {e}", level="WARNING")
        
        # 按位置排序（从上到下，从左到右）
        page_elements.sort(key=lambda x: (x["bbox"][1], x["bbox"][0]))
        all_elements.extend(page_elements)
    
    total_pages = len(doc)
    doc.close()
    return all_elements, total_pages

def pdf_to_markdown(
    pdf_path: Path,
    out_dir: Path,
    img_base_url: Optional[str] = None,
    embed_thresh: int = 150_000,
    doc_type: str = "research_report",
    custom_config: Optional[PDFProcessConfig] = None,
):
    """将PDF转换为Markdown
    
    Args:
        pdf_path: PDF文件路径
        out_dir: 输出目录
        img_base_url: 图片基础URL（可选）
        embed_thresh: 图片内嵌阈值
        doc_type: 文档类型 ('research_report', 'legal_document', 'academic_paper', 'general')
        custom_config: 自定义配置（可选）
    """
    out_dir.mkdir(parents=True, exist_ok=True)
    imgs_dir = out_dir / "images"
    imgs_dir.mkdir(exist_ok=True)
    
    # 使用自定义配置或根据文档类型创建配置
    config = custom_config or PDFProcessConfig(doc_type=doc_type)
    
    BaseObj.log(f"开始提取PDF内容... (文档类型: {doc_type})")
    
    try:
        elements, total_pages = extract_pdf_content(pdf_path)
        BaseObj.log(f"成功提取 {len(elements)} 个元素")
        
        # 转换为Markdown
        md_lines = []
        image_count = 0
        image_hashes = set()  # 用于图片去重
        
        for element in elements:
            if element["type"] == "text":
                # 处理文本
                text = element["content"]
                # 简单判断是否为标题（短、大写较多、或以数字开头）
                if len(text) < 100 and (text.isupper() or re.match(r'^\d+\.?\s', text)):
                    md_lines.append(f"## {text}\n")
                else:
                    md_lines.append(f"{text}\n")
                
            elif element["type"] == "image":
                # 处理图片
                image_bytes = element["content"]
                
                # 检查图片是否有效
                if not is_valid_image(image_bytes, config):
                    BaseObj.log(f"跳过无效图片: {element['image_name']}")
                    continue
                    
                # 检查图片是否重复
                img_hash = get_image_hash(image_bytes)
                if img_hash in image_hashes:
                    BaseObj.log(f"跳过重复图片: {element['image_name']}")
                    continue
                    
                image_hashes.add(img_hash)
                image_count += 1
                image_name = element["image_name"]
                alt_text = f"图{image_count}"
                
                md_img = image_to_markdown(
                    image_bytes, image_name, alt_text, imgs_dir, img_base_url, embed_thresh
                )
                md_lines.append(md_img)
        
        # 清洗文本
        cleaned_lines = clean_duplicate_lines(md_lines, config)
        
        # 写入文件
        md_out = out_dir / f"{pdf_path.stem}.md"
        with open(md_out, "w", encoding="utf-8") as f:
            f.write("\n".join(cleaned_lines))
        
        BaseObj.log(f"✅ 转换完成！")
        BaseObj.log(f"📄 Markdown: {md_out}")
        BaseObj.log(f"🖼️  提取图片: {image_count} 张")
        BaseObj.log(f"📊 总元素数: {len(elements)}")
        BaseObj.log(f"⚙️  使用配置: {doc_type}")
        
    except Exception as e:
        BaseObj.log(f"转换失败: {e}", level="ERROR")
        raise

# ------------------------- 主函数 ------------------------------ #

def detect_document_type(pdf_path: Path, pdf_text_sample: str = "") -> str:
    """根据文件名和内容自动检测文档类型"""
    filename = pdf_path.name.lower()
    
    # 研报关键词
    research_keywords = ["研报", "研究", "证券", "投资", "分析师", "华泰", "中信", "海通"]
    if any(keyword in filename for keyword in research_keywords):
        return "research_report"
    
    # 法律文档关键词
    legal_keywords = ["合同", "协议", "条款", "法律", "合规", "声明", "条例"]
    if any(keyword in filename for keyword in legal_keywords):
        return "legal_document"
    
    # 学术论文关键词
    academic_keywords = ["论文", "期刊", "学报", "研究", "journal", "paper", "research"]
    if any(keyword in filename for keyword in academic_keywords):
        return "academic_paper"
    
    # 检查内容（如果提供了样本文本）
    if pdf_text_sample:
        if any(keyword in pdf_text_sample for keyword in ["证券研究报告", "投资建议", "分析师"]):
            return "research_report"
        elif any(keyword in pdf_text_sample for keyword in ["合同条款", "甲方", "乙方", "协议"]):
            return "legal_document"
        elif any(keyword in pdf_text_sample for keyword in ["abstract", "keywords", "摘要", "关键词"]):
            return "academic_paper"
    
    return "general"

def batch_process_pdfs(input_dir: Path, doc_type: str = "auto"):
    """批量处理PDF文件"""
    if not input_dir.exists():
        BaseObj.log(f"输入目录不存在: {input_dir}", level="ERROR")
        return
    
    pdf_files = list(input_dir.glob("*.pdf"))
    if not pdf_files:
        BaseObj.log(f"在 {input_dir} 中未找到PDF文件", level="WARNING")
        return
    
    BaseObj.log(f"📁 找到 {len(pdf_files)} 个PDF文件")
    
    success_count = 0
    for pdf_path in pdf_files:
        try:
            BaseObj.log(f"\n📄 开始处理: {pdf_path.name}")
            
            # 自动检测文档类型
            if doc_type == "auto":
                detected_type = detect_document_type(pdf_path)
                BaseObj.log(f"🔍 检测到文档类型: {detected_type}")
            else:
                detected_type = doc_type
            
            # 输出目录
            out_dir = WORKDIR / "citeds" / pdf_path.stem
            
            # 转换
            pdf_to_markdown(pdf_path, out_dir, doc_type=detected_type)
            success_count += 1
            
        except Exception as e:
            BaseObj.log(f"❌ 处理失败 {pdf_path.name}: {e}", level="ERROR")
    
    BaseObj.log(f"\n✨ 批量处理完成！成功: {success_count}/{len(pdf_files)}")

def main():
    """主函数"""
    
    # 配置区域 - 用户可以根据需要修改
    
    # 方式1: 处理单个文件
    SINGLE_FILE_MODE = True
    
    if SINGLE_FILE_MODE:
        # 单文件处理配置
        input_dir = Path(r"D:\JG-files\07_量化交易\@@知识库\遗传因子")
        pdf_filename = "华泰证券-金工深度研究：基于趋势和拐点的市值因子择时模型-250525.pdf"
        pdf_path = input_dir / pdf_filename
        
        if not pdf_path.exists():
            BaseObj.log(f"PDF文件不存在: {pdf_path}", level="ERROR")
            return
        
        # 自动检测文档类型（或手动指定）
        doc_type = detect_document_type(pdf_path)
        BaseObj.log(f"🔍 检测到文档类型: {doc_type}")
        
        # 自定义配置示例（可选）
        if doc_type == "research_report":
            # 研报可能需要更严格的去重
            custom_config = PDFProcessConfig(doc_type="research_report")
            custom_config.enable_duplicate_filter = True
        elif doc_type == "legal_document":
            # 法律文档应保留重复条款
            custom_config = PDFProcessConfig(doc_type="legal_document")
            custom_config.enable_duplicate_filter = False
        else:
            custom_config = None
        
        # 输出目录
        out_dir = WORKDIR / "citeds" / pdf_path.stem
        
        BaseObj.log(f"📄 PDF文件: {pdf_path}")
        BaseObj.log(f"📁 输出目录: {out_dir}")
        
        # 转换
        pdf_to_markdown(pdf_path, out_dir, doc_type=doc_type, custom_config=custom_config)
        
    else:
        # 方式2: 批量处理
        input_dir = Path(r"D:\JG-files\07_量化交易\@@知识库\遗传因子")
        batch_process_pdfs(input_dir, doc_type="auto")  # "auto"自动检测, 或指定具体类型
    
    BaseObj.log("✨ 任务完成")

if __name__ == "__main__":
    main()