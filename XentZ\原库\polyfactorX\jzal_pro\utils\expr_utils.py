''' 加工或显示特征值的诸多工具 '''
import pandas as pd
import numpy as np
from typing import Union
from numpy.typing import NDArray
import time
import talib
from datetime import datetime
import matplotlib.pyplot as plt
from scipy.stats import zscore
import scipy.stats as stats
from numpy.lib.stride_tricks import sliding_window_view
from numba import njit
import warnings
import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent.parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
from enums import NORM_PARAMS_DEFAULT, NormParams

def norm_plot(x: np.ndarray, title:str = ''):
    # 绘制直方图
    plt.figure(figsize=(10, 6))
    plt.hist(x, bins=50, color='blue', alpha=0.7)
    plt.title(title)
    plt.xlabel("X: normed_values")
    plt.ylabel("Y: frequency")
    plt.grid(True, which="both", linestyle="--", linewidth=0.5)
    plt.show()

def rolling_stats(x, window, func=np.mean, min_periods=1):
    """
    计算输入序列的滚动统计量（如均值、标准差等）。
    """
    n = len(x)
    window_view = np.lib.stride_tricks.sliding_window_view(x, window)
    # 初始化结果数组，考虑min_periods
    results = np.full(n, np.nan)
    for i in range(n):
        end = i + window
        start = max(0, end - window)
        if end - start >= min_periods:
            results[i] = func(window_view[start:end], axis=-1)
    return results

def mad_clip(df: Union[NDArray, pd.DataFrame], k: int = 3, axis=0):
    if isinstance(df, pd.DataFrame):
        df = df.values
    
    df = np.nan_to_num(df, nan=0.0, posinf=0.0, neginf=0.0)
    if df.ndim > 1:
        if df.shape[1] == 1:
            df = df.ravel()
        else:
            raise ValueError("mad_clip 函数只支持一维数组作为输入")
   # 检查轴方向
    if axis not in [0, 1]:
        raise ValueError("轴方向(axis)必须是0或1")
     
    with warnings.catch_warnings():
        warnings.filterwarnings('error', category=RuntimeWarning)
        try:
            med = np.median(df, axis=axis)
            mad = np.median(np.abs(df - med), axis=axis)
        except RuntimeWarning:
            return df  # 返回原始数据，避免进一步计算

    mad = np.where(mad == 0, np.nan, mad)
    
    # 计算上下界
    with warnings.catch_warnings():
        warnings.filterwarnings('error', category=RuntimeWarning)
        try:
            if axis == 0:
                lower_bound = med - k * 1.4826 * mad
                upper_bound = med + k * mad
            elif axis == 1:
                lower_bound = med[:, np.newaxis] - k * 1.4826 * mad[:, np.newaxis]
                upper_bound = med[:, np.newaxis] + k * mad[:, np.newaxis]
        except RuntimeWarning:
            return df  # 返回原始数据，避免进一步计算

    lower_bound = np.clip(lower_bound, -np.inf, np.inf)
    upper_bound = np.clip(upper_bound, -np.inf, np.inf)

    return np.clip(df, lower_bound, upper_bound)

# def mad_clip(df: Union[NDArray, pd.DataFrame], k: int = 3, axis=0): #  性能不如原版本
#     ''' 使用 MAD 3 倍截断法去极值
#     Args:
#     df: 输入数据，要求索引为日期，资产名为列，单元格值为因子的宽表
#     k: 截断倍数。
#     axis:截断方向
#     '''
#     med = np.median(df,axis=axis).reshape(df.shape[0], -1)
#     mad = np.median(np.abs(df - med), axis=axis)
#     return np.clip(df.T, med.flatten() - k * 1.4826 * mad, med.flatten() + k * mad).T

def norm(x, params=NORM_PARAMS_DEFAULT):
    """
    滚动归一化函数, 支持NumPy数组和Pandas Series作为输入。
    参数: 
    - x: 输入数据,可以是NumPy数组或Pandas Series。
    - window: 滚动窗口大小,默认2000。
    - clip_num: 归一化后数据裁剪的最大绝对值,默认2。
    - log_method: 对数处理方法,0为不对数处理, 1为正数对数化,2为保留正负数的对数化。
    - MA_type: 是否使用移动平均,1为使用,其他为不使用。
    - is_demean: 是否减去均值,默认False。
    - algo_norm: 归一化算法,0为z-score, 1为L2-norm, 2为MinMax, 3为鲁棒, 4为累计概率, 5为分位数norm
    - algo_clip: norm之前进行clip方法: 0为不先clip, 1为mad_clip
    返回: 归一化后的数组。
    """
    window, clip_num, log_method, MA_type, is_demean, algo_norm, algo_clip = params
    try:
        # 统一处理NaN和inf值 + 确保输入是一维数组
        if isinstance(x, np.ndarray):
            x = np.nan_to_num(x, nan=0.0, posinf=0.0, neginf=0.0)
            if x.ndim > 1 and x.shape[1] == 1:
                x = x.ravel()  # 展平为一维数组
            elif x.ndim > 1:
                raise ValueError("norm 函数只支持一维数组作为输入")
        elif isinstance(x, pd.Series):
            x = x.replace([np.inf, np.nan, -np.inf], 0.0).astype(np.float64)
            if x.shape != (len(x),):  # 检查形状是否为(n,)
                x = x.values.reshape(-1)  # 如果不是，展平它
            else:
                x = x.to_numpy()
        ''' ==== 直接处理非线性模型的 分位数norm 并直接返回结果 ==== '''
        if algo_norm == 5:
            n = len(x)
            result = np.zeros(n)
            if n <= window:
                return result

            np.random.seed(42)
            random_seq = np.random.random(n)

            # 构造滑动窗口，shape 为 (n-window+1, window)
            total_windows = sliding_window_view(x, window_shape=window)
            # 对应 x[window:] 的窗口为 total_windows[0:n-window]
            windows = total_windows[: n - window]

            # 当前值对应 x[window:]
            current_vals = x[window:]

            # 统计窗口中比当前值小的个数和等于当前值的个数
            ranks = np.sum(windows < current_vals[:, None], axis=1)
            ties = np.sum(windows == current_vals[:, None], axis=1)

            rank_adj = ranks + np.where(ties > 0, random_seq[window:] * (ties + 1), random_seq[window:])
            quantiles = rank_adj / (window + 1)
            q = np.where(quantiles < 0.5, quantiles, 1.0 - quantiles)
            sign = np.where(quantiles < 0.5, -1.0, 1.0)

            r = np.sqrt(-2.0 * np.log(q))
            r2 = r * r
            r3 = r2 * r
            numerator = 2.515517 + 0.802853 * r + 0.010328 * r2
            denominator = 1.0 + 1.432788 * r + 0.189269 * r2 + 0.001308 * r3
            transformed = sign * (r - numerator / denominator)
            result[window:] = transformed
            
            return np.clip(result, -clip_num, clip_num)
        
        ''' ==== 处理线性模型的正常norm逻辑 ==== '''
        # 对数处理
        if log_method == 1:
            if np.any(x < 0):
                raise ValueError("Log method 1 requires all values to be non-negative")
            x = np.log1p(x)
        elif log_method == 2:
            mean_abs_x = np.nanmean(np.abs(x))
            x = np.sign(x) * np.log1p(np.abs(x)) / np.log1p(mean_abs_x)
            x = np.nan_to_num(x)

        # 先行去极值
        if algo_clip == 1: # mad_clip
            x = mad_clip(x, k=3, axis=0)
            
        # 计算滚动统计量
        factor_mean = pd.Series(x).rolling(window=window, min_periods=1).mean().to_numpy()
        factor_std = pd.Series(x).rolling(window=window, min_periods=1).std().to_numpy()

        # 去均值操作
        if is_demean:
            x = x - factor_mean

        # 归一化处理
        with np.errstate(divide='ignore', invalid='ignore'):
            if algo_norm == 0:  # zscore -- 性能提升2倍
                factor_value = x / factor_std
                factor_value[factor_std == 0] = 0.0
            elif algo_norm == 1:  # divisor: L2norm --- 性能提升100倍, [-1,1] 可保号
                # l2norm = pd.Series(x).rolling(window=window, min_periods=1).apply(
                #     lambda x: np.sqrt(np.sum(x**2)), raw=True).to_numpy()
                # factor_value = x / l2norm
                # factor_value[l2norm == 0] = 0.0
                squared_sum = np.lib.stride_tricks.sliding_window_view(x**2, window_shape=window).sum(axis=-1)
                l2norm = np.sqrt(squared_sum)
                prefix = np.sqrt(np.cumsum(x[:window]**2))
                l2norm = np.concatenate((prefix, l2norm))[:len(x)]
                factor_value = np.divide(x, l2norm, out=np.zeros_like(x), where=l2norm != 0)
            elif algo_norm == 2:  # MinMax -- 性能提升2倍 [-0.5,0.5]
                min_val = pd.Series(x).rolling(window=window, min_periods=1).min().to_numpy()
                max_val = pd.Series(x).rolling(window=window, min_periods=1).max().to_numpy()
                factor_value = ((x - min_val) / (max_val - min_val)) - 0.5
                factor_value[(max_val - min_val) == 0] = 0.0
            elif algo_norm == 3: # robust [-0.5,0.5]
                median = np.median(x, axis=0)
                iqr = np.percentile(x, 75, axis=0) - np.percentile(x, 25, axis=0)
                x = (x - median) / iqr
                lower_bound = np.percentile(x, 1, axis=0) # 基于标准化后的x
                upper_bound = np.percentile(x, 99, axis=0)
                factor_value = np.clip((x - lower_bound) / (upper_bound - lower_bound), 0, 1)
                factor_value = factor_value - 0.5
                factor_value[(upper_bound - lower_bound) == 0] = 0.0 
            elif algo_norm == 4: # 累计概率 [-0.5,0.5]
                factor_value = x / factor_std
                factor_value = stats.norm.cdf(factor_value)
                factor_value = factor_value - 0.5
            else:
                raise ValueError("Invalid value for algo_norm")

        # 剔除异常值
        factor_value[np.isnan(factor_value) | np.isinf(factor_value)] = 0.0
        factor_value = np.clip(factor_value, -clip_num, clip_num)
        
        # 移动平均处理
        if MA_type == 1:
            factor_value = talib.MA(factor_value, timeperiod=5, matype=1)
        
        factor_value = np.nan_to_num(factor_value)
            
        return factor_value.flatten()

    except Exception as e:
        print(f'排查: norm()计算异常: {e}')
        return x if isinstance(x, np.ndarray) else x.values
    
def norm_original(x, params=NORM_PARAMS_DEFAULT):
    """
    滚动归一化函数, 支持NumPy数组和Pandas Series作为输入。
    参数: 
    - x: 输入数据,可以是NumPy数组或Pandas Series。
    - window: 滚动窗口大小,默认2000。
    - clip_num: 归一化后数据裁剪的最大绝对值,默认2。
    - log_method: 对数处理方法,0为不对数处理, 1为正数对数化,2为保留正负数的对数化。
    NOTE: zsocre为标准化不能去量纲,会提升在0附近的响应灵敏性; 正则化和归一化才去量纲
    返回: 归一化后的数组。
    """
    window, clip_num, log_method, MA_type, is_demean, algo_norm = params
    try:
        if isinstance(x, pd.Series):
            x = x.replace([np.inf, np.nan, -np.inf], 0.0).astype(np.float64)
        elif isinstance(x, np.ndarray):
            x = np.nan_to_num(x, nan=0.0, posinf=0.0, neginf=-0.0)
        else:
            raise ValueError("Unsupported input type. Expected numpy.ndarray or pandas.Series.")
        # 对数处理
        if log_method == 1:
            x = np.log1p(x)
        elif log_method == 2:
            abs_x = np.abs(x)
            mean_abs_x = np.nanmean(abs_x)
            x = np.sign(x) * np.log1p(abs_x) / np.log1p(mean_abs_x)
            x = np.nan_to_num(x)
        else:
            x = np.asarray(x)
        
        # 转换为DataFrame以便使用rolling
        factor_data = pd.DataFrame(x, columns=['factor'])
        factor_data = factor_data.replace([np.inf, np.nan, -np.inf], 0.0)
        
        factor_mean = factor_data.rolling(window=window, min_periods=1).mean()
        factor_std = factor_data.rolling(window=window, min_periods=1).std()
        factor_data = factor_data.replace([np.inf, np.nan, -np.inf], 0.0)
        
        if is_demean:
            factor_data_2 = factor_data - factor_mean
        else:
            factor_data_2 = factor_data
        
        # 避免除以0
        with np.errstate(divide='ignore', invalid='ignore'):
            if algo_norm == 0: # zscore
                factor_value = factor_data_2 / factor_std
                factor_value[factor_std == 0] = 0.0  # 处理除以0的情况
            elif algo_norm == 1: # divisor: L2norm
                l2norm = factor_data_2.rolling(window=window, min_periods=1).apply(
                    lambda x: np.sqrt(np.sum(x**2)), raw=True)
                factor_value = factor_data_2 / l2norm
                factor_value[l2norm == 0] = 0.0  # 处理除以0的情况
            elif algo_norm == 2: # MinMax
                min_val = factor_data_2.rolling(window=window, min_periods=1).min()
                max_val = factor_data_2.rolling(window=window, min_periods=1).max()
                factor_value = (factor_data_2 - min_val) / (max_val - min_val)
                factor_value[(max_val - min_val) == 0] = 0.0  # 处理除以0的情况
            else:
                print('norm()函数的algo_norm参数错误')
            factor_value[np.isnan(factor_value) | np.isinf(factor_value)] = 0
            
        if MA_type == 1:
            factor_value['factor'] = talib.MA(factor_value['factor'].to_numpy(), timeperiod=5, matype=1)
        
        factor_value = factor_value.replace([np.inf, np.nan, -np.inf],0.0)
        factor_value = factor_value.clip(-clip_num, clip_num)
        
        return factor_value['factor'].to_numpy().flatten() # 变一维数组
    
    except Exception as e:
        print('norm()计算异常:{}'.format(e))
        if isinstance(x, pd.Series):
            return x.values
        else:
            return x
    
# @profile
# def norm(x, params=NORM_PARAMS_DEFAULT):
#     """
#     滚动归一化函数, 支持NumPy数组和Pandas Series作为输入。
#     参数: 
#     - x: 输入数据,可以是NumPy数组或Pandas Series。
#     - window: 滚动窗口大小,默认2000。
#     - clip_num: 归一化后数据裁剪的最大绝对值,默认2。
#     - log_method: 对数处理方法,0为不对数处理, 1为正数对数化,2为保留正负数的对数化。
#     返回: 归一化后的数组。
#     """
#     window, clip_num, log_method = params
#     try:
#         if isinstance(x, pd.Series):
#             x = x.replace([np.inf, np.nan, -np.inf], 0.0).astype(np.float64)
#         elif isinstance(x, np.ndarray):
#             x = np.nan_to_num(x, nan=0.0, posinf=0.0, neginf=-0.0)
#         else:
#             raise ValueError("Unsupported input type. Expected numpy.ndarray or pandas.Series.")
#         # 对数处理
#         if log_method == 1:
#             x = np.log1p(x)
#         elif log_method == 2:
#             abs_x = np.abs(x)
#             mean_abs_x = np.nanmean(abs_x)
#             x = np.sign(x) * np.log1p(abs_x) / np.log1p(mean_abs_x)
#             x = np.nan_to_num(x)
#         else:
#             x = np.asarray(x)
            
#         # 转换为DataFrame以便使用rolling
#         factor_data = pd.DataFrame(x, columns=['factor'])
#         factor_data = factor_data.replace([np.inf, np.nan, -np.inf], 0.0)
        
#         factor_mean = factor_data.rolling(window=window, min_periods=1).mean()
#         factor_std = factor_data.rolling(window=window, min_periods=1).std()
#         factor_data = factor_data.replace([np.inf, np.nan, -np.inf], 0.0)
        
#         # 避免除以0
#         with np.errstate(divide='ignore', invalid='ignore'):
#             factor_value = (factor_data - factor_mean) / factor_std
#             factor_value[np.isnan(factor_value) | np.isinf(factor_value)] = 0
            
#         factor_value = factor_value.replace([np.inf, np.nan, -np.inf],0.0)
#         factor_value = factor_value.clip(-clip_num, clip_num)
        
#         return factor_value['factor'].to_numpy().flatten()
    
#     except Exception as e:
#         print('norm()计算异常:{}'.format(e))
#         if isinstance(x, pd.Series):
#             return x.values
#         else:
#             return x
    
# def norm_np(x: np.ndarray, window: int = 2000, clip_num: float = 2, log_method :int =1):
#     ''' 滚动norm TODO: ma化? max/min化? print分布
#     参数: 仅x类型不同norm; x的nan, inf已替换为0; 对数化方法让数据分布更接近正态
#     返回: zscore的np数组
#     '''
#     try:
#         x = np.nan_to_num(x, nan=0.0, posinf=0.0, neginf=-0.0)
#         epsilon = 1e-12
        
#         if log_method == 1:  # 方法1: 对数化 正数 单峰
#             # x = np.maximum(x, epsilon)
#             x = np.log1p(x) # log1p处理[-1,+inf],但一般建议[0,+inf]
            
#         elif log_method == 2: # # 方法2: 对数化 保留正负数, 但有双峰(正负)
#             abs_x = np.abs(x)
#             mean_abs_x = np.nanmean(abs_x)  # 使用np.nanmean处理可能的NaN
#             x = np.sign(x) * np.log1p(abs_x) / np.log1p(mean_abs_x)
#             x = np.nan_to_num(x)
           
#         factor_data = pd.DataFrame(x, columns=['factor'])
#         factor_data = factor_data.replace([np.inf, np.nan, -np.inf],0.0)   # x的nan, inf已替换为0  
#         factor_mean = factor_data.rolling(window=window, min_periods=1).mean()
#         factor_std = factor_data.rolling(window=window, min_periods=1).std()
#         # 避免除以0的情况
#         with np.errstate(divide='ignore', invalid='ignore'):
#             factor_value = (factor_data - factor_mean) / factor_std
#             factor_value[np.isnan(factor_value) | np.isinf(factor_value)] = 0  # 处理Nan和Inf
#         # factor_value = np.where(
#         #     factor_std >= epsilon,
#         #     (factor_data - factor_mean) / factor_std,
#         #     factor_mean
#         # )
#         # factor_value = (factor_data - factor_mean) / factor_std    
#         factor_value = factor_value.replace([np.inf, np.nan, -np.inf],0.0)
#         factor_value = factor_value.clip(-clip_num, clip_num)
#         return factor_value.to_numpy().flatten()
#     except Exception as e:
#         print('norm()计算异常:{}'.format(e))
#         return x  
    
# def norm(se: pd.Series, window: int = 2000, clip_num: float = 2, log_method :int =1) :
#     ''' 滚动norm TODO: ma化? max/min化? print分布
#     参数: x的nan, inf已替换为0; 对数化方法让数据分布更接近正态
#     返回: zscore的np数组
#     '''
#     try:
#         se = se.replace([np.inf, np.nan, -np.inf],0.0)
#         epsilon = 1e-12
        
#         if log_method == 1:  # 方法1: 对数化 正数 单峰 log(1+x) 用于收益率求对数
#             # x = np.maximum(se, epsilon)
#             # x = np.log1p(x) # log1p处理[-1,+inf],但一般建议[0,+inf]
#             x = np.log1p(se)
            
#         elif log_method == 2: # # 方法2: 对数化 保留正负数, 但有双峰(正负)
#             abs_x = np.abs(se)
#             mean_abs_x = np.nanmean(abs_x)  # 使用np.nanmean处理可能的NaN
#             x = np.sign(se) * np.log1p(abs_x) / np.log1p(mean_abs_x)
#             x = np.nan_to_num(x)
#         else:
#             x = np.asarray(se)
                
#         factor_data = pd.DataFrame(x, columns=['factor'])
#         factor_data = factor_data.replace([np.inf, np.nan, -np.inf],0.0).astype(np.float64)   # x的nan, inf已替换为0  
#         factor_mean = factor_data.rolling(window=window, min_periods=1).mean().astype(np.float64)
#         factor_std = factor_data.rolling(window=window, min_periods=1).std().astype(np.float64)
#         # 避免除以0的情况
#         with np.errstate(divide='ignore', invalid='ignore'):
#             factor_value = (factor_data - factor_mean) / factor_std
#             factor_value[np.isnan(factor_value) | np.isinf(factor_value)] = 0  # 处理Nan和Inf
            
#         # factor_value = np.where(
#         #     factor_std >= epsilon,
#         #     (factor_data - factor_mean) / factor_std,
#         #     factor_mean
#         # )
#         # factor_value = (factor_data - factor_mean) / factor_std   
#         factor_value = factor_value.replace([np.inf, np.nan, -np.inf],0.0)
#         factor_value = factor_value.clip(-clip_num, clip_num)
#         return factor_value.to_numpy().flatten()
#     except Exception as e:
#         print('norm()计算异常:{}'.format(e))
#         return se.values        

def norm_X(x):
    days = 250
    window = days * 16
    clip_num = 2
    # 方法一会将数据都变为正数...
    x = np.log1p(np.asarray(x))  # 处理日收益率或非常小的收益率, 对数变换可以将非正态分布的数据转换为更接近正态分布的形式, 有助于模型假设
    # 可能会使数据更加集中在均值附近，这在某些情况下可能更有用，但也可能不适用于所有数据; 复杂的对数变换，这可能会改变数据的分布特性
    # 方法二不会改变数据的分布特性，但它可能会改变数据的分布形状，使其变得更加紧凑，可能包含一些关于原始数据方差的信息。在某些情况下，这种方法可能会提供更稳健的标准化结果，特别是在处理包含负值的数据时。
    # arr = np.asarray(x)
    # x = np.sign(arr)*np.log1p(np.abs(arr)) / np.log1p(np.abs(np.mean(arr)))
    factor_data = pd.DataFrame(x, columns=['factor'])
    factor_data = factor_data.replace([np.inf, np.nan, -np.inf],0.0)
    factor_mean = factor_data.rolling(window=window, min_periods=1).mean()
    factor_std = factor_data.rolling(window=window, min_periods=1).std()
    factor_value = (factor_data - factor_mean)/factor_std
    factor_value = factor_value.replace([np.inf, np.nan, -np.inf],0.0)
    factor_value = factor_value.clip(-clip_num, clip_num)
    return np.nan_to_num(factor_value).flatten()

def norm_tongyi(x, params=NORM_PARAMS_DEFAULT):
    window, clip_num, log_method = params
    
    # 确保输入为NumPy数组
    if isinstance(x, pd.Series):
        x = x.values.astype(np.float64)
    elif not isinstance(x, np.ndarray):
        raise ValueError("Unsupported input type. Expected numpy.ndarray or pandas.Series.")
    
    # 对数处理
    if log_method > 0:
        safe_x = np.where(x > 0, x, 1)  # 避免对负数直接取对数
        if log_method == 1:
            x = np.log1p(safe_x)
        elif log_method == 2:
            mean_abs_x = np.nanmean(np.abs(x))
            x = np.sign(x) * np.log1p(safe_x) / np.log1p(mean_abs_x)
    
    # 使用Scipy的zscore函数进行标准化，这一步同时计算了均值和标准差
    # 注意：这里直接处理了NaN和无穷大，因此不需要额外的预处理
    with np.errstate(invalid='ignore'):
        norm_x = zscore(x, ddof=1)
    
    # 裁剪标准化后的值
    norm_x = np.clip(norm_x, -clip_num, clip_num)
    
    return norm_x

# def quick_rolling(se: pd.Series, d: int, func=None, **kwargs) -> np.ndarray:
#     # TODO: 有错误, 需要检查
#     incomplete_w = np.lib.stride_tricks.sliding_window_view(se.values, d)[..., ::-1]
#     window = pd.DataFrame(np.vstack((np.full([d - 1, d], np.nan), incomplete_w)))
#     if func is None:  # just window
#         return window.values # 返回ndarray
#     else:
#         result = func(window, **kwargs)  # add other arguments needed in function
#         for i in range(d - 1):
#             result[i] = np.nan
#         return result
# @njit
def quick_rolling(arr: np.ndarray, window: int, min_period: int, func=None, **kwargs):
    if len(arr) < window:
        return np.full_like(arr, np.nan)
    sliding_windows = sliding_window_view(arr, window_shape=window)
    result = np.full_like(arr, np.nan) 
    if func is None:
        result[window-1:] = sliding_windows
        return sliding_windows
    else:
        ''' TODO: 性能开销太大, 考虑进一步优化''' 
        full_window_result  = func(sliding_windows, axis=-1, **kwargs) # 执行np.mean这类开销大
        # 将有效期小于 min_periods 的部分设为 NaN
        # result = np.full_like(arr, np.nan)
        for i in range(window - 1):  # for开销大
            if i + 1 >= min_period:
                result[i] = func(arr[:i + 1], **kwargs)
        result[window - 1:] = full_window_result
        return result

@njit
def quick_rolling_mean_std(arr: np.ndarray, window: int, min_period: int):
    if len(arr) < window:
        return np.full(len(arr), np.nan), np.full(len(arr), np.nan)
    
    result_mean = np.empty(len(arr), dtype=np.float64)
    result_std = np.empty(len(arr), dtype=np.float64)
    result_mean[:window-1] = np.nan
    result_std[:window-1] = np.nan
    
    cumsum = np.cumsum(arr)
    cumsum_sq = np.cumsum(arr**2)
    count = np.arange(len(arr)) + 1
    
    for i in range(window - 1, len(arr)):
        window_sum = cumsum[i] - (cumsum[i - window] if i >= window else 0)
        window_sq_sum = cumsum_sq[i] - (cumsum_sq[i - window] if i >= window else 0)
        valid_count = count[i] - (count[i - window] if i >= window else 0)
        
        if valid_count >= min_period:
            result_mean[i] = window_sum / valid_count
            result_std[i] = np.sqrt((window_sq_sum / valid_count) - (window_sum**2 / valid_count**2))
        else:
            result_mean[i] = np.nan
            result_std[i] = np.nan
    
    return result_mean, result_std

@njit
def rolling_std_numba(arr: np.ndarray, window: int, min_periods: int = 1):
    """
    使用Numba计算滑动窗口的标准差。
    :param arr: 输入的NumPy数组。
    :param window: 滑动窗口大小。
    :param min_periods: 窗口内最小非NaN值的数量，默认为1。
    :return: 滑动标准差的数组。
    """
    if len(arr) < window:
        return np.full(len(arr), np.nan)
    
    # 计算滑动窗口视图
    sliding_windows = sliding_window_view(arr, window_shape=window)
    
    # 初始化结果数组
    result = np.full(len(arr), np.nan)
    
    # 手动计算每个窗口的均值和方差
    for i in range(len(arr) - window + 1):
        window_data = sliding_windows[i]
        valid_count = np.sum(~np.isnan(window_data))
        if valid_count >= min_periods:
            # 计算均值
            mean = np.nansum(window_data) / valid_count
            # 计算方差，进而得到标准差
            variance = np.nansum((window_data - mean) ** 2) / (valid_count - 1)
            result[i + window - 1] = np.sqrt(variance)
    
    return result

if __name__ == "__main__":
    np.random.seed(0)  # 设置随机种子以便结果可复现
    dates = pd.date_range(start=datetime(1980, 1, 1), periods=100000, freq='D')
    se = pd.Series(np.random.randn(100000), index=dates)  # 正态分布数据Series
    arr = se.values  # 转换为NumPy数组
    
    def test_norm():
        x_norm = norm(se, params=NORM_PARAMS_DEFAULT)
        print(x_norm[:5], len(x_norm))  
        
    def test_norm_tongyi():
        x_norm = norm_tongyi(se, params=NORM_PARAMS_DEFAULT) 
        print(x_norm[:5], len(x_norm)) 
    # 定义测试rolling
    def func_pd():
        normed_array = se.rolling(window=1000,min_periods=1).std()
        print(normed_array[:5])  # 打印前5个归一化后的值

    def func_duckdb():
        import duckdb
        se.index.name = 'date'
        df = se.reset_index().rename(columns={'index': 'date'})
        df.columns = ['date','value']
        con = duckdb.connect(database=':memory:')
        con.register("my_data", df )
        sql_query = """
        SELECT 
            value,
            STDDEV_SAMP(value) OVER (ORDER BY date ROWS BETWEEN 999 PRECEDING AND CURRENT ROW) AS rolling_std
        FROM my_data
        """
        duck_res = con.execute(sql_query).fetchdf()
        print(duck_res[:5])
        
    def func_modin_ray():
        normed_array = mse.rolling(window=1000,min_periods=1).std()
        print(normed_array[:5])
    
    def test_norm_algo():
        # 测试数据
        test_data = pd.Series([1, 2, 3, 4, 5, np.nan, np.inf, -np.inf, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])

        # 测试参数
        test_params = [
            NormParams(window=5, clip_num=3, log_method=0, MA_type=0, is_demean=True, algo_norm=0),
            NormParams(window=5, clip_num=3, log_method=1, MA_type=0, is_demean=True, algo_norm=1),
            NormParams(window=5, clip_num=3, log_method=2, MA_type=1, is_demean=False, algo_norm=2)
        ]

        # 测试函数
        for params in test_params:
            result = norm(test_data, params)
            print(f"Params: {params}")
            print(result)
            print("="*50)

    def test_norm_algo2():
        # 测试数据
        test_data_series = pd.Series(np.random.randn(1000))
        test_data_array = np.random.randn(1000)

        # 测试不同类型的输入
        print("Testing with Pandas Series:")
        result_series = norm(test_data_series)
        print(result_series[:5])

        print("\nTesting with Numpy Array:")
        result_array = norm(test_data_array)
        print(result_array[:5])

        # 测试不同的归一化方法
        print("\nTesting different normalization methods:")
        params_zscore = NormParams(window=200, clip_num=3, log_method=0, MA_type=0, is_demean=True, algo_norm=0)
        params_L2norm = NormParams(window=200, clip_num=3, log_method=0, MA_type=0, is_demean=True, algo_norm=1)
        params_MinMax = NormParams(window=200, clip_num=3, log_method=0, MA_type=0, is_demean=True, algo_norm=2)

        result_zscore = norm(test_data_series, params=params_zscore)
        result_L2norm = norm(test_data_series, params=params_L2norm)
        result_MinMax = norm(test_data_series, params=params_MinMax)

        print("Z-Score normalization results:", result_zscore[:5])
        print("L2-Norm normalization results:", result_L2norm[:5])
        print("MinMax normalization results:", result_MinMax[:5])

        # 测试对数处理
        print("\nTesting logarithmic preprocessing:")
        params_log1 = NormParams(window=200, clip_num=3, log_method=1, MA_type=0, is_demean=True, algo_norm=0)
        params_log2 = NormParams(window=200, clip_num=3, log_method=2, MA_type=0, is_demean=True, algo_norm=0)

        result_log1 = norm(test_data_series, params=params_log1)
        result_log2 = norm(test_data_series, params=params_log2)

        print("Logarithmic preprocessing (method 1) results:", result_log1[:5])
        print("Logarithmic preprocessing (method 2) results:", result_log2[:5])

        # 测试边界情况：窗口大小与数据长度相同
        print("\nTesting edge case: window size equal to data length")
        params_edge = NormParams(window=len(test_data_series), clip_num=3, log_method=0, MA_type=0, is_demean=True, algo_norm=0)
        result_edge = norm(test_data_series, params=params_edge)
        print("Edge case results:", result_edge[:5])

        # 测试边界情况：窗口大小大于数据长度
        print("\nTesting edge case: window size greater than data length")
        params_large_window = NormParams(window=len(test_data_series) + 10, clip_num=3, log_method=0, MA_type=0, is_demean=True, algo_norm=0)
        result_large_window = norm(test_data_series, params=params_large_window)
        print("Large window results:", result_large_window[:5])

        # 测试异常情况：非数值输入
        try:
            print("\nTesting with non-numeric input:")
            result_non_numeric = norm(['a', 'b', 'c'])
        except ValueError as ve:
            print(ve)

        # 测试异常情况：空输入
        try:
            print("\nTesting with empty input:")
            result_empty = norm([])
        except Exception as e:
            print(e)
        
    start_time = time.time()
    test_norm_algo2()
    # test_norm() # 0.067
    # test_norm_tongyi() # 0.011
    # test_norm_tongyi()
    # func_pd() # 0.016
    # func_duckdb() # 0.157
    # func_modin_ray() # 2.32
    end_time = time.time()
    print('Time cost:=======', end_time - start_time)