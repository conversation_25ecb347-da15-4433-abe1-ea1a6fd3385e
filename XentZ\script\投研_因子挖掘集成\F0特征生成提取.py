from datetime import datetime
from pathlib import Path
import pandas as pd
import numpy as np
from config import REPORTS_DIR, cfg_feat as cfg
from datafeed.features.feature_utils import FeatDistribution, FeatPreprocessing, FeatSelection, FeatEngineering
from datafeed.hku_dataloader import HKUDataloader
from datafeed.features.alphas import AlphaBase, AlphaOrigin, Alpha158, AlphaLite, AlphaJZAL

if __name__ == "__main__":
    ''' ====================== 0. 加载时序特征任务的配置 ============================= '''
    # 🎯 从配置获取基础参数
    symbols = cfg.get('data_source.symbols', ['510050.SH'])  # 修正路径
    train_start_date = cfg.get('time_split.train_start_date', '20100101')  # 修正路径
    train_end_date = cfg.get('time_split.train_end_date', datetime.now().strftime('%Y%m%d'))
    freq = cfg.get('data_source.freq', 'D')  # 修正路径
    recover = cfg.get('data_source.hku_loader.recover', 'EQUAL_BACKWARD')  # 修正路径
    
    # 🎯 根据enabled_alphas动态生成特征表达式
    enabled_alphas = cfg.get('feat.set.enabled_alphas', ['AlphaBase', 'AlphaOrigin'])

    # ✨ 注册表模式，将特殊逻辑配置化，更优雅
    alpha_registry = {
        'AlphaBase': lambda: AlphaBase().get_a_label(
            tdelay=cfg.get('label.single.tdelay', 1),
            names=cfg.get('label.single.names', ['label_1'])
        ) if cfg.get('label.mode', 'single') == 'single' else AlphaBase().get_labels(
            tdelay=cfg.get('label.multiple.tdelay', [1, 5, 10, 20]),
            names=cfg.get('label.multiple.names', ['label_1', 'label_5', 'label_10', 'label_20'])
        ),
        'AlphaOrigin': lambda: AlphaOrigin().get_exprs_names(), 
        'Alpha158': lambda: Alpha158().get_exprs_names(),
        'AlphaLite': lambda: AlphaLite().get_exprs_names(),
        'AlphaJZAL': lambda: AlphaJZAL().get_exprs_names()
    }

    # 🎯 使用推导式动态生成特征，更简洁
    known_alphas = set(alpha_registry.keys())
    enabled_and_known = [name for name in enabled_alphas if name in known_alphas]
    results = [alpha_registry[name]() for name in enabled_and_known]
    if results:
        exprs_lists, names_lists = zip(*results)
        feature_exprs = [expr for sublist in exprs_lists for expr in sublist]
        feature_names = [name for sublist in names_lists for name in sublist]

    print(f"✅ 已加载 {len(enabled_and_known)} 类Alpha, 总计 {len(feature_exprs)} 个特征")
    
    # 🎯 设置报告输出目录
    report_dir = REPORTS_DIR / 'features'
    report_dir.mkdir(parents=True, exist_ok=True)
    ''' ====================== 1. 抽取train数据集和特征集 ============================= '''
    # 🎯 使用配置化的数据加载方式
    df_all = HKUDataloader.load_df_all(symbols, set_index=True, 
                                       start_date=train_start_date, 
                                       end_date=train_end_date,
                                       freq=freq, recover=recover)
    df_all = FeatPreprocessing.calc_expr_df_all(df_all, feature_exprs, feature_names)
    df_all.dropna(inplace=True)  # 对结果影响非常重要! 会删掉一些新品种的数据
    df_all = FeatPreprocessing.normalize_df_all(df_all, model_type="robust", cfg=cfg) # 特征筛选用的模型不用norm!
    ''' ====================== 2. 可视化: 特征数据分布图 ============================= '''
    # FeatDistribution.plot_curve(df_all, save_path=report_dir, benchmark_cols=['close'])
    # FeatDistribution.plot_hist(df_all, save_path=report_dir)
    # FeatDistribution.plot_qq(df_all, save_path=report_dir)
    # df_all.to_csv(report_dir / 'df_all_normed.csv', index=True)
    ''' ====================== 3. 特征变换(对需要的特征进行变换) ============================== '''
    # df_all = FeatPreprocessing.transform_features(df_all) # norm后其实已经做了变换
    ''' ====================== 4. 特征移除: 更新toml后执行 ============================== '''
    df_all = FeatSelection.drop_features(df_all,cfg=cfg)    # 基于toml的配置信息
    ''' ====================== 5. 特征筛选: 初步筛选 ============================== '''
    df_all = FeatSelection.corr_filter(df_all,cfg=cfg) # 相关性筛选
    df_all = FeatSelection.redundancy_filter(df_all,cfg=cfg) # 聚类, 去除多重共线性
    print(len(df_all.columns))
    ''' ====================== 6. 可视化: 特征数据分布图 ============================= '''
    # FeatDistribution.plot_curve(df_all, save_path=report_dir, benchmark_cols=['close'])
    # FeatDistribution.plot_hist(df_all, save_path=report_dir)
    # FeatDistribution.plot_qq(df_all, save_path=report_dir)
    # df_all.to_csv(report_dir / 'df_all_filtered.csv', index=True)
    ''' ====================== 7. featuretools衍生 ============================== '''
    # # 🚨 使用安全的featuretools原语，避免数据泄漏风险
    # agg_primitives = []
    # trans_primitives = ['absolute', 'diff', 'lag']  
    # # 注释掉可能导致数据泄漏的原语:
    # # - 'time_since_previous': 在规整时间序列中可能产生常数特征，导致异常R²跳跃
    # # - 'percentile': 小窗口百分位数可能引入未来信息
    # # - 'hour', 'day', 'month': 时间特征可能让模型学到时间效应而非价格效应
    # groupby_trans_primitives=['cum_sum', 'cum_mean'] # 有categ类型才有效
    # print("⚠️  使用安全的featuretools原语: ['absolute', 'diff', 'lag']，已排除数据泄漏风险原语")
    # df_all = FeatEngineering.featuretools_extract(df_all, window_size=20, max_depth=1, #单表只能1层
    #                                               agg_primitives=agg_primitives, trans_primitives=trans_primitives,
    #                                               groupby_trans_primitives=[], # 清空(没分类列)
    #                                               var_threshold=0.0005, n_jobs=1, verbose=True)
    ''' ====================== 8. 工具衍生后的特征筛选: 只去除多重共线性 ========================= '''
    # df_all = FeatSelection.redundancy_filter(df_all) # 聚类, 去除多重共线性
    # df_all.to_csv(report_dir / 'df_all_ftools_filtered.csv', index=True)
    ''' ====================== 9. 特征筛选: xgb结合'前向选择' ============================== '''
    # 使用默认推荐的3种重要性类型：['gain', 'weight', 'cover']
    analysis_results = FeatSelection.xgb_importance_analysis(df_all)
    
    # 保存每个算法类型的最优特征(基于R²拐点动态确定数量)
    topn_features = {}
    if analysis_results:
        num_symbols = len(analysis_results)
        print(f"📊 检测到{num_symbols}个symbol，采用{'单symbol直接选择' if num_symbols == 1 else '多symbol平均投票'}策略")
        
        for imp_type in ['gain', 'weight', 'cover']:
            # 1. 收集所有symbol的拐点数量和特征重要性
            symbol_optimal_counts = []
            symbol_features_dict = {}
            
            for symbol, symbol_data in analysis_results.items():
                if imp_type in symbol_data:
                    data = symbol_data[imp_type]
                    if 'feature_importance' in data and 'optimal_n_features' in data:
                        optimal_count = data['optimal_n_features']
                        features = list(data['feature_importance'].keys())
                        symbol_optimal_counts.append(optimal_count)
                        symbol_features_dict[symbol] = {
                            'features': features,
                            'optimal_count': optimal_count,
                            'scores': list(data['feature_importance'].values())
                        }
                    elif 'feature_importance' in data:
                        features = list(data['feature_importance'].keys())
                        fallback_count = max(5, len(features) // 2)
                        symbol_optimal_counts.append(fallback_count)
                        symbol_features_dict[symbol] = {
                            'features': features,
                            'optimal_count': fallback_count,
                            'scores': list(data['feature_importance'].values())
                        }
            
            if symbol_optimal_counts:
                if num_symbols == 1:
                    # 单symbol情况：直接使用该symbol的拐点数量
                    symbol_name = list(symbol_features_dict.keys())[0]
                    info = symbol_features_dict[symbol_name]
                    optimal_count = info['optimal_count']
                    final_features = info['features'][:optimal_count]
                    topn_features[imp_type] = final_features
                    print(f"📊 {imp_type}: {symbol_name}拐点={optimal_count}个→直接选择{len(final_features)}个特征")
                else:
                    # 多symbol情况：使用平均拐点数量和加权投票
                    unified_count = int(np.mean(symbol_optimal_counts))
                    unified_count = max(5, min(unified_count, 50))  # 限制在合理范围
                    
                    # 基于统一数量选择特征(按重要性加权投票)
                    feature_votes = {}
                    for symbol, info in symbol_features_dict.items():
                        top_features = info['features'][:unified_count]
                        top_scores = info['scores'][:unified_count]
                        for feat, score in zip(top_features, top_scores):
                            if feat not in feature_votes:
                                feature_votes[feat] = []
                            feature_votes[feat].append(score)
                    
                    # 计算每个特征的平均得分并排序
                    feature_avg_scores = {feat: np.mean(scores) for feat, scores in feature_votes.items()}
                    sorted_features = sorted(feature_avg_scores.items(), key=lambda x: x[1], reverse=True)
                    
                    # 选择最终特征(实际可用数量)
                    available_count = len(sorted_features)  # 实际可用的不重复特征数量
                    actual_count = min(unified_count, available_count)  # 取理论和实际的最小值
                    final_features = [feat for feat, _ in sorted_features[:actual_count]]
                    topn_features[imp_type] = final_features
                    
                    # 显示详细的symbol拐点信息
                    symbol_names = list(symbol_features_dict.keys())
                    symbol_info = [f"{name}={count}" for name, count in zip(symbol_names, symbol_optimal_counts)]
                    print(f"📊 {imp_type}: 各symbol拐点 {symbol_info}, 平均={unified_count:.1f}→统一{unified_count}个→实际{actual_count}个特征")

    # 写入cfg文件
    cfg_path = report_dir / 'topn_features.cfg'
    strategy_desc = "直接选择R²拐点" if num_symbols == 1 else "多symbol平均投票"
    with open(cfg_path, 'w', encoding='utf-8') as f:
        for imp_type, features in topn_features.items():
            f.write(f"{imp_type} = {features}  # {strategy_desc}选出{len(features)}个特征\n")
    print(f"✅ 最优特征已保存至: {cfg_path} (合并{len(analysis_results)}个symbol)")
    
    ''' ====================== 10. 特征筛选: 高级重要性可视化 ============================== '''
    # 🌟 可选：使用高级重要性分析方法 !!! shap做了防未来改动但有些牵强; 置换重要性本身就随机采样有一定风险
    # analysis_results = FeatSelection.xgb_importance_analysis(
    #     df_all, 
    #     importance_types=['gain', 'weight', 'shap', 'permutation']  # 包含SHAP和置换重要性
    # )
    
    from datafeed.features.feature_viz import FeatImportanceViz
    
    # 重要性对比图
    FeatImportanceViz.plot_importance_comparison(
        analysis_results, 
        save_path=report_dir / 'importance_comparison.png' 
    )

    # R²累积贡献图 - 为每种算法分别生成 (注意：图表只显示第一个symbol的拐点)
    # 获取第一个品种的数据用于可视化
    if analysis_results:
        first_symbol = list(analysis_results.keys())[0]
        symbol_data = analysis_results[first_symbol]
        print(f"📊 注意：R²拐点图表仅展示第一个symbol ({first_symbol}) 的数据，cfg文件基于所有symbol计算")
        for imp_type in ['gain', 'weight', 'cover' ]:
            if imp_type in symbol_data:
                FeatImportanceViz.plot_marginal_r2(
                    analysis_results,
                    save_path=report_dir / f'marginal_r2_{imp_type}.png',
                    importance_type=imp_type
                )

    # 综合分析图
    FeatImportanceViz.plot_combined_analysis(
        analysis_results,
        save_path=report_dir / 'complete_analysis.png'
    )

    # 保存表格
    FeatImportanceViz.save_importance_table(
        analysis_results,
        save_path=report_dir / 'importance_tables.csv'
    )
    
    ''' ====================== 11. 特征选择: 根据toml配置选择最终特征 ========================= '''
    for imp_type, features in topn_features.items():
        print(f"   {imp_type}: {len(features)}个特征 ({strategy_desc})")
    print("📋 请根据cfg文件内容更新toml配置，完成后输入'OK'继续...")
    input("等待输入: ").strip().upper()
    
    # 🔄 重新加载配置，确保使用最新的toml设置
    cfg.reload()
    df_selected = FeatSelection.select_features(df_all,cfg=cfg)
    df_selected.to_csv(report_dir / 'df_all_selected.csv', index=True)
    print(f"✅ 最终特征集已保存: {report_dir / 'df_all_selected.csv'}")
    num_label = len([c for c in df_selected.columns if c.startswith('label_')])
    num_symbol = 1 if 'symbol' in df_selected.columns else 0
    num_feat = df_selected.shape[1] - num_label - num_symbol
    print(f"📊 选择特征: {num_feat}个 + 标签: {num_label}个")
    