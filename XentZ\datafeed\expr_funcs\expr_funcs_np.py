import pandas as pd
import numpy as np
from typing import Callable, Union
import talib
'''     240519: 153算子online 2算子offline      '''
'''     NOTE: 算子中暂时不用norm,留在load data时统一进行   '''
def norm(x):  
    # ...
    # ...
    return x

def _flatten(x):
    if isinstance(x, pd.Series):
        x1 = x.to_numpy().flatten()
    elif isinstance(x, np.ndarray):
        x1 = x.flatten()
    return x1

def _to_numpy(x):
    if isinstance(x, pd.Series):
        x1 = x.to_numpy()
    elif isinstance(x, np.ndarray):
        x1 = x
    return x1 

def _ts_constant(v: float) -> Callable[[np.ndarray], np.ndarray]:
    """
    返回一个与给定数组 x 长度相同的常数数组，每个元素都为 v
    """
    def constant_function(x: np.ndarray) -> np.ndarray:
        return np.full(x.shape, v)
    return constant_function
 
def _protected_division(x1, x2):
    """Closure of division (x1/x2) for zero denominator."""
    with np.errstate(divide='ignore', invalid='ignore'):
        return np.where(np.abs(x2) > 0.001, np.divide(x1, x2), 1.)
    
''' ==========================   正式对应 gplearn的 func map ======================= '''
# ====  JZAL: RSRS - 240911
def ts_slope_pair(window_size):
    def _ts_slope_pair(x1, x2): # 性能提升200倍!
        N = window_size
        x1 = _flatten(x1)
        x2 = _flatten(x2)
        slopes = np.full(len(x1), np.nan)  # 预先分配一个全是NaN的数组
        if len(x1) < N:
            return slopes
        # 使用向量化操作一次计算所有可能的x和y
        x = np.lib.stride_tricks.sliding_window_view(x2, window_shape=N)
        y = np.lib.stride_tricks.sliding_window_view(x1, window_shape=N)
        # 对每一个窗口计算斜率
        x_mean = x.mean(axis=1)
        y_mean = y.mean(axis=1)
        xy_cov = np.sum((x - x_mean[:, None]) * (y - y_mean[:, None]), axis=1)
        xx_var = np.sum((x - x_mean[:, None])**2, axis=1)

        with np.errstate(divide='ignore', invalid='ignore'):
            slopes_valid = np.where(xx_var != 0, xy_cov / xx_var, np.nan)
        # 将计算结果放到预分配的数组中
        slopes[N-1:] = slopes_valid
        return norm(slopes)
    
    return _ts_slope_pair

ts_slope_pair_9 = ts_slope_pair(9)
ts_slope_pair_18 = ts_slope_pair(18)
ts_slope_pair_34 = ts_slope_pair(34)
ts_slope_pair_55 = ts_slope_pair(55)
ts_slope_pair_89 = ts_slope_pair(89)

# ====  知守溪 - 240825
def _ts_rvi_(period: int) -> Callable[[Union[np.ndarray, pd.Series]], np.ndarray]: # 闭包方式
    def _ts_rvi_func(x: Union[np.ndarray, pd.Series]) -> np.ndarray:
        if isinstance(x, pd.Series):
            x = x.values.astype(np.float64)
        elif isinstance(x, np.ndarray):
            x = x.astype(np.float64)
        else:
            raise TypeError("输入必须是 pd.Series 或 np.ndarray 类型")

        std = talib.STDDEV(x, timeperiod=period)
        # 计算价格的变化
        x_diff = np.diff(x, prepend=x[0])
        # 正的和负的标准差
        pos_std = np.where(x_diff > 0, std, 0)
        neg_std = np.where(x_diff <= 0, std, 0)
        
        # 使用 EMA 计算平均正标准差和负标准差
        pos_std = np.nan_to_num(pos_std, nan=0.0)
        neg_std = np.nan_to_num(neg_std, nan=0.0)
        avg_pos_std = talib.EMA(pos_std, timeperiod=period)
        avg_neg_std = talib.EMA(neg_std, timeperiod=period)
        # 计算 RVI
        avg_pos_std = np.nan_to_num(avg_pos_std, nan=0.0)
        avg_neg_std = np.nan_to_num(avg_neg_std, nan=0.0)
        epsilon = 1e-10
        denominator = avg_pos_std + avg_neg_std
        denominator[denominator == 0] = epsilon
        rvi = 100 * avg_pos_std / denominator
        return norm(rvi)
    return _ts_rvi_func

ts_rvi_7 = _ts_rvi_(7)
ts_rvi_14 = _ts_rvi_(14)
ts_rvi_21 = _ts_rvi_(21)
ts_rvi_34 = _ts_rvi_(34)
ts_rvi_55 = _ts_rvi_(55)
ts_rvi_89 = _ts_rvi_(89)

def ts_udd_10(x1,x2):
    N = 10
    if isinstance(x1, pd.Series):
        x_diff = np.diff(x1, prepend=x1.iloc[0])
    else:
        x_diff = np.diff(x1, prepend=x1[0])
    udvd = np.where(x_diff > 0, x2, -x2)
    # 使用 'same' 模式进行卷积计算，保持结果长度与原始数据一致
    udvd_windowed = np.convolve(udvd, np.ones(N), 'same')
    udvd_windowed[x2 == 0] = 0
    return norm(udvd_windowed)

def ts_udd_20(x1,x2):
    N = 20
    if isinstance(x1, pd.Series):
        x_diff = np.diff(x1, prepend=x1.iloc[0])
    else:
        x_diff = np.diff(x1, prepend=x1[0])
        
    udvd = np.where(x_diff > 0, x2, -x2)
    # 使用 'same' 模式进行卷积计算，保持结果长度与原始数据一致
    udvd_windowed = np.convolve(udvd, np.ones(N), 'same')
    udvd_windowed[x2 == 0] = 0
    return norm(udvd_windowed)

def ts_udd_40(x1,x2):
    N = 40
    if isinstance(x1, pd.Series):
        x_diff = np.diff(x1, prepend=x1.iloc[0])
    else:
        x_diff = np.diff(x1, prepend=x1[0])
    udvd = np.where(x_diff > 0, x2, -x2)
    # 使用 'same' 模式进行卷积计算，保持结果长度与原始数据一致
    udvd_windowed = np.convolve(udvd, np.ones(N), 'same')
    udvd_windowed[x2 == 0] = 0
    return norm(udvd_windowed)

def ts_udd_60(x1,x2):
    N = 60
    if isinstance(x1, pd.Series):
        x_diff = np.diff(x1, prepend=x1.iloc[0])
    else:
        x_diff = np.diff(x1, prepend=x1[0])
    udvd = np.where(x_diff > 0, x2, -x2)
    # 使用 'same' 模式进行卷积计算，保持结果长度与原始数据一致
    udvd_windowed = np.convolve(udvd, np.ones(N), 'same')
    udvd_windowed[x2 == 0] = 0
    return norm(udvd_windowed) 
# ======================= JZAL: add with gpquant: 33 =============== 
def ts_mean2(x1, x2):
    return norm((x1 + x2) / 2)

def ts_square1(x1):
    return norm(x1**2)

def ts_cube1(x1):
    return norm(x1**3)

def ts_cbrt1(x1):
    return norm(np.cbrt(x1))

def ts_sign1(x1):
    return norm(np.sign(x1))

def ts_clear_by_cond3(x1, x2, x3):
    """if x1 < x2 (keep NaN if and only if both x1 and x2 are NaN), then 0, else x3"""
    return np.where(x1 < x2, 0, np.where(~np.isnan(x1) | ~np.isnan(x2), x3, np.nan))


def ts_if_then_else3(x1, x2, x3):
    """if x1 is nonzero (keep NaN), then x2, else x3"""
    return np.where(x1, x2, np.where(~np.isnan(x1), x3, np.nan))


def ts_if_cond_then_else4(x1, x2, x3, x4):
    """if x1 < x2 (keep NaN if and only if both x1 and x2 are NaN), then x3, else x4"""
    return np.where(x1 < x2, x3, np.where(~np.isnan(x1) | ~np.isnan(x2), x4, np.nan))

def ts_mean_5(x1):
    """moving average"""
    d = 5
    return norm(pd.Series(x1).rolling(d, min_periods=int(d / 2)).mean().values)
def ts_mean_10(x1):
    """moving average"""
    d = 10
    return norm(pd.Series(x1).rolling(d, min_periods=int(d / 2)).mean().values)
def ts_mean_20(x1):
    """moving average"""
    d = 20
    return norm(pd.Series(x1).rolling(d, min_periods=int(d / 2)).mean().values)
def ts_mean_40(x1):
    """moving average"""
    d = 40
    return norm(pd.Series(x1).rolling(d, min_periods=int(d / 2)).mean().values)
	
def ts_median_5(x1):
    """moving median"""
    d = 5
    return norm(pd.Series(x1).rolling(d, min_periods=int(d / 2)).median().values)
def ts_median_10(x1):
    """moving median"""
    d = 10
    return norm(pd.Series(x1).rolling(d, min_periods=int(d / 2)).median().values)
def ts_median_20(x1):
    """moving median"""
    d = 20
    return norm(pd.Series(x1).rolling(d, min_periods=int(d / 2)).median().values)


def ts_midpoint_5(x1):
    """moving midpoint: (ts_max + ts_min) / 2"""
    return norm(ts_max_5(x1) + ts_min_5(x1))
def ts_midpoint_10(x1):
    """moving midpoint: (ts_max + ts_min) / 2"""
    return norm(ts_max_10(x1) + ts_min_10(x1))
def ts_midpoint_20(x1):
    """moving midpoint: (ts_max + ts_min) / 2"""
    return norm(ts_max_20(x1) + ts_min_20(x1))
	
def ts_inverse_cv_10(x1):
    """moving inverse of coefficient of variance"""
    return norm(_protected_division(ts_mean_10(x1), ts_std_10(x1)))
def ts_inverse_cv_20(x1):
    """moving inverse of coefficient of variance"""
    return norm(_protected_division(ts_mean_20(x1), ts_std_20(x1)))
def ts_inverse_cv_40(x1):
    """moving inverse of coefficient of variance"""
    return norm(_protected_division(ts_mean_40(x1), ts_std_40(x1)))
	
def ts_autocorr_20_3(x1):
    """moving autocorrelation coefficient between x and x lag i period"""
    d = 20
    i = 3
    return norm(pd.Series(x1).rolling(d, min_periods=int(d / 2)).corr(pd.Series(x1).shift(i)).values)
def ts_autocorr_40_3(x1):
    """moving autocorrelation coefficient between x and x lag i period"""
    d = 40
    i = 3
    return norm(pd.Series(x1).rolling(d, min_periods=int(d / 2)).corr(pd.Series(x1).shift(i)).values)
def ts_autocorr_20_8(x1):
    """moving autocorrelation coefficient between x and x lag i period"""
    d = 20
    i = 8
    return norm(pd.Series(x1).rolling(d, min_periods=int(d / 2)).corr(pd.Series(x1).shift(i)).values)
def ts_autocorr_40_8(x1):
    """moving autocorrelation coefficient between x and x lag i period"""
    d = 40
    i = 8
    return norm(pd.Series(x1).rolling(d, min_periods=int(d / 2)).corr(pd.Series(x1).shift(i)).values)
def ts_autocorr_20_17(x1):
    """moving autocorrelation coefficient between x and x lag i period"""
    d = 20
    i = 17
    return norm(pd.Series(x1).rolling(d, min_periods=int(d / 2)).corr(pd.Series(x1).shift(i)).values)
def ts_autocorr_40_17(x1):
    """moving autocorrelation coefficient between x and x lag i period"""
    d = 40
    i = 17
    return norm(pd.Series(x1).rolling(d, min_periods=int(d / 2)).corr(pd.Series(x1).shift(i)).values)
	
def ts_maxmin_5(x1):
    """moving maxmin normalization"""
    ts_max, ts_min = ts_max_5(x1), ts_min_5(x1)
    return norm(_protected_division(x1 - ts_min, ts_max - ts_min))
def ts_maxmin_10(x1):
    """moving maxmin normalization"""
    ts_max, ts_min = ts_max_10(x1), ts_min_10(x1)
    return norm(_protected_division(x1 - ts_min, ts_max - ts_min))
def ts_maxmin_20(x1):
    """moving maxmin normalization"""
    ts_max, ts_min = ts_max_20(x1), ts_min_20(x1)
    return norm(_protected_division(x1 - ts_min, ts_max - ts_min))


def ts_zscore_10(x1):
    """moving zscore standardization"""
    return norm(_protected_division(x1 - ts_mean_10(x1), ts_std_10(x1)))
def ts_zscore_20(x1):
    """moving zscore standardization"""
    return norm(_protected_division(x1 - ts_mean_20(x1), ts_std_20(x1)))
def ts_zscore_40(x1):
    """moving zscore standardization"""
    return norm(_protected_division(x1 - ts_mean_40(x1), ts_std_40(x1)))
# ======================= JZAL: end of gpquant: 33 ===============

def ts_add2(x1,x2):
    return norm(np.add(x1,x2))

def ts_sub2(x1,x2): # x1 - x2
    return norm(np.subtract(x1,x2))

def ts_mul2(x1,x2): 
    return norm(np.multiply(x1,x2))

def ts_div2(x1, x2):
    """Closure of division (x1/x2) for zero denominator."""
    with np.errstate(divide='ignore', invalid='ignore'):
        return norm(np.where(np.abs(x2) > 0.001, np.divide(x1, x2), 1.))

def ts_sqrt1(x1):
    """Closure of square root for negative arguments."""
    return norm(np.sqrt(np.abs(x1)))

def ts_log1(x1):
    """Closure of log for zero and negative arguments."""
    with np.errstate(divide='ignore', invalid='ignore'):
        return norm(np.where(np.abs(x1) > 0.001, np.log(np.abs(x1)), 0.))

def ts_inv1(x1):
    """Closure of inverse for zero arguments."""
    with np.errstate(divide='ignore', invalid='ignore'):
        return norm(np.where(np.abs(x1) > 0.001, 1. / x1, 0.))
    
def ts_neg1(x1):
    return norm(np.negative(x1))

def ts_abs1(x1):
    return norm(np.abs(x1))

def ts_max2(x1,x2):
    x1 = _to_numpy(x1)
    x2 = _to_numpy(x2)
    return norm(np.maximum(x1,x2))

def ts_min2(x1,x2):
    x1 = _to_numpy(x1)
    x2 = _to_numpy(x2)
    return norm(np.minimum(x1,x2))

def ts_sin1(x1):
    return norm(np.sin(x1))

def ts_cos1(x1):
    return norm(np.cos(x1))

def ts_tan1(x1):
    return norm(np.tan(x1))

def ts_sig1(x1):
    """Special case of logistic function to transform to probabilities."""
    with np.errstate(over='ignore', under='ignore'):
        return norm(1 / (1 + np.exp(-x1)))

def ts_tanh1(x1):
    with np.errstate(over='ignore', under='ignore'):
        return norm(np.tanh(x1))

def ts_elu1(x1):
    with np.errstate(over='ignore', under='ignore'):
        x = (np.where(x1 > 0, x1, 1 * (np.exp(x1) - 1)))
        return norm(x)

def ta_ht_trendline(x1):
    x1 = _flatten(x1)
    x = (np.nan_to_num(talib.HT_TRENDLINE(x1.astype(np.float64))))
    return norm(x)

def ta_ht_dcperiod(x1):
    x1 = _flatten(x1)
    x = (np.nan_to_num(talib.HT_DCPERIOD(x1.astype(np.float64))))
    return norm(x)


def ta_ht_dcphase(x1):
    x1 = _flatten(x1)
    x = (np.nan_to_num(talib.HT_DCPHASE(x1.astype(np.float64))))
    return norm(x)

def ta_sar(x1, x2):
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = (np.nan_to_num(talib.SAR(x1.astype(np.float64), x2.astype(np.float64))))
    return norm(x)


def ta_bop(x1, x2, x3, x4):
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x4 = _flatten(x4)
    x = (np.nan_to_num(talib.BOP(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), x4.astype(np.float64))))
    return norm(x)

def ta_bop_0(x1, x2, x3, x4):
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x4 = _flatten(x4)
    x = (np.nan_to_num(talib.BOP(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), x4.astype(np.float64))))
    return norm(x)

def ta_ad(x1, x2, x3, x4):
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x4 = _flatten(x4)
    x = (np.nan_to_num(talib.AD(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), x4.astype(np.float64))))
    return norm(x)

def ta_obv(x1, x2):
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = (np.nan_to_num(talib.OBV(x1.astype(np.float64), x2.astype(np.float64))))
    return norm(x)

def ta_trange(x1, x2, x3):
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = (np.nan_to_num(talib.TRANGE(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64))))
    return norm(x)

def ts_cov_20(x1, x2):
    t = 20
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).cov(pd.Series(x2)))
    return norm(x)

def ts_cov_40(x1, x2):
    t = 40
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = (np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).cov(pd.Series(x2))))
    return norm(x)

def ts_corr_20(x1, x2):
    t = 20
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = (np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).corr(pd.Series(x2))))
    return norm(x)

def ts_corr_40(x1, x2):
    t = 40
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = (np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).corr(pd.Series(x2))))
    return norm(x)

def ts_day_min_10(x1):
    t = 10
    x1 = _flatten(x1)
    if len(x1) < t + 1:
        return np.full(len(x1), 0.0)
    
    x = np.lib.stride_tricks.sliding_window_view(x1, window_shape=t+1)
    min_indices = np.argmin(x[:, :-1], axis=1) # 计算每个窗口的最小值位置
    intervals = t - min_indices
	
    result = np.full(len(x1), np.nan)      # 将结果放到预分配的数组中
    result[t:] = intervals
    
    return norm(np.nan_to_num(result))

def ts_day_min_20(x1):  # the i_th element is the interval between the min_value time point and t_i in the n-period time series from the past (t_i is not included)
    t = 20
    x1 = _flatten(x1)
    if len(x1) < t + 1:
        return np.full(len(x1), 0.0)
    
    x = np.lib.stride_tricks.sliding_window_view(x1, window_shape=t+1)
    min_indices = np.argmin(x[:, :-1], axis=1) # 计算每个窗口的最小值位置
    intervals = t - min_indices
	
    result = np.full(len(x1), np.nan)      # 将结果放到预分配的数组中
    result[t:] = intervals
    
    return norm(np.nan_to_num(result))

def ts_day_min_40(x1):  # the i_th element is the interval between the min_value time point and t_i in the n-period time series from the past (t_i is not included)
    t = 40
    x1 = _flatten(x1)
    if len(x1) < t + 1:
        return np.full(len(x1), 0.0)
    
    x = np.lib.stride_tricks.sliding_window_view(x1, window_shape=t+1)
    min_indices = np.argmin(x[:, :-1], axis=1) # 计算每个窗口的最小值位置
    intervals = t - min_indices
	
    result = np.full(len(x1), np.nan)      # 将结果放到预分配的数组中
    result[t:] = intervals
    
    return norm(np.nan_to_num(result))

def ts_day_max_10(x1):
    t = 10
    x1_flat = _flatten(x1)
    if len(x1_flat) < t + 1:
        return np.full(len(x1_flat), 0.0)
    
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t+1)
    max_indices = np.argmax(x[:, :-1], axis=1)
    intervals = t - max_indices

    result = np.full(len(x1_flat), np.nan)
    result[t:] = intervals
    
    return norm(np.nan_to_num(result))

def ts_day_max_20(x1):  # the i_th element is the interval between the max_value time point and t_i in the n-period time series from the past (t_i is not included)
    t = 20
    x1_flat = _flatten(x1)
    if len(x1_flat) < t + 1:
        return np.full(len(x1_flat), 0.0)
    
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t+1)
    max_indices = np.argmax(x[:, :-1], axis=1)
    intervals = t - max_indices

    result = np.full(len(x1_flat), np.nan)
    result[t:] = intervals
    
    return norm(np.nan_to_num(result))

def ts_day_max_40(x1):  # the i_th element is the interval between the max_value time point and t_i in the n-period time series from the past (t_i is not included)
    t = 40
    x1_flat = _flatten(x1)
    if len(x1_flat) < t + 1:
        return np.full(len(x1_flat), 0.0)
    
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t+1)
    max_indices = np.argmax(x[:, :-1], axis=1)
    intervals = t - max_indices

    result = np.full(len(x1_flat), np.nan)
    result[t:] = intervals
    
    return norm(np.nan_to_num(result))

def ts_sma_8(x1):  # the i_th element is the simple moving average of the elements in the n-period time series from the past
    t = 8
    x1 = _flatten(x1)
    x = (np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).mean()))
    return norm(x)

def ts_sma_21(x1):  # the i_th element is the simple moving average of the elements in the n-period time series from the past
    t = 21
    x1 = _flatten(x1)
    x = (np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).mean()))
    return norm(x)

def ts_sma_55(x1):  # the i_th element is the simple moving average of the elements in the n-period time series from the past
    t = 55
    x1 = _flatten(x1)
    x = (np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).mean()))
    return norm(x)

def ts_wma_8(x1): # the i_th element is the weighted moving average of the elements in the n-period time series from the past
    t = 8
    x1_flat = _flatten(x1)
    # 检查x1的长度是否足够
    if len(x1_flat) < t:
        return np.full(len(x1_flat), 0.0)
    
    weight_list = np.arange(1, t + 1) # 计算权重
    weight_list = weight_list / np.sum(weight_list)
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    wma = np.dot(x, weight_list) # 计算加权移动平均
    result = np.full(len(x1_flat), np.nan)
    result[t-1:] = wma
    
    return norm(np.nan_to_num(result))

def ts_wma_21(x1):  # the i_th element is the weighted moving average of the elements in the n-period time series from the past
    t = 21
    x1_flat = _flatten(x1)
    # 检查x1的长度是否足够
    if len(x1_flat) < t:
        return np.full(len(x1_flat), 0.0)
    
    weight_list = np.arange(1, t + 1) # 计算权重
    weight_list = weight_list / np.sum(weight_list)
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    wma = np.dot(x, weight_list) # 计算加权移动平均
    result = np.full(len(x1_flat), np.nan)
    result[t-1:] = wma
    
    return norm(np.nan_to_num(result))

def ts_wma_55(x1):  # the i_th element is the weighted moving average of the elements in the n-period time series from the past
    t = 55
    x1_flat = _flatten(x1)
    # 检查x1的长度是否足够
    if len(x1_flat) < t:
        return np.full(len(x1_flat), 0.0)
    
    weight_list = np.arange(1, t + 1) # 计算权重
    weight_list = weight_list / np.sum(weight_list)
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    wma = np.dot(x, weight_list) # 计算加权移动平均
    result = np.full(len(x1_flat), np.nan)
    result[t-1:] = wma
    
    return norm(np.nan_to_num(result))

def ts_lag_3(x1):
    t = 3
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).shift(periods=t))
    return norm(x)

def ts_lag_8(x1):
    t = 8
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).shift(periods=t))
    return norm(x)

def ts_lag_17(x1):
    t = 17
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).shift(periods=t))
    return norm(x)

def ts_delta_3(x1):
    t = 3
    x1 = _flatten(x1)
    x = np.nan_to_num(x1 - np.nan_to_num(pd.Series(x1).shift(periods=t)))
    return norm(x)

def ts_delta_8(x1):
    t = 8
    x1 = _flatten(x1)
    x = np.nan_to_num(x1 - np.nan_to_num(pd.Series(x1).shift(periods=t)))
    return norm(x)

def ts_delta_17(x1):
    t = 17
    x1 = _flatten(x1)
    x = np.nan_to_num(x1 - np.nan_to_num(pd.Series(x1).shift(periods=t)))
    return norm(x)

def ts_sum_3(x1):  # 性能提升1700倍!
    t = 3
    x1_flat = _flatten(x1)
    # 检查x1的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    x1_flat = np.nan_to_num(x1_flat) # for this func only...
    result = np.full(len(x1_flat), np.nan) # 预先分配一个全是NaN的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t): # min_periods处理
        x_min_periods = x1_flat[:i]
        sums = np.sum(x_min_periods) # for sum
        result[i-1] = sums        
        
    sums = np.sum(x, axis=1)
    result[t-1:] = sums
    
    return norm(np.nan_to_num(result))

def ts_sum_8(x1):  # the i_th element is the sum of the elements in the n-period time series from the past
    t = 8
    x1_flat = _flatten(x1)
    # 检查x1的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    x1_flat = np.nan_to_num(x1_flat) # for this func only...
    result = np.full(len(x1_flat), np.nan) # 预先分配一个全是NaN的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t): # min_periods处理
        x_min_periods = x1_flat[:i]
        sums = np.sum(x_min_periods) # for sum
        result[i-1] = sums        
        
    sums = np.sum(x, axis=1)
    result[t-1:] = sums
    
    return norm(np.nan_to_num(result))

def ts_sum_17(x1):  # the i_th element is the sum of the elements in the n-period time series from the past
    t = 17
    x1_flat = _flatten(x1)
    # 检查x1的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    x1_flat = np.nan_to_num(x1_flat) # for this func only...
    result = np.full(len(x1_flat), np.nan) # 预先分配一个全是NaN的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t): # min_periods处理
        x_min_periods = x1_flat[:i]
        sums = np.sum(x_min_periods) # for sum
        result[i-1] = sums        
        
    sums = np.sum(x, axis=1)
    result[t-1:] = sums
    
    return norm(np.nan_to_num(result))

def ts_prod_3(x1): # 提升1200倍!
    t = 3
    x1_flat = _flatten(x1)
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算乘积
        prods = np.prod(x_min_periods)
        result[i-1] = prods
    # 对滑动窗口内的元素计算乘积
    prods = np.prod(x, axis=1)
    result[t-1:] = prods
    
    return norm(np.nan_to_num(result))

def ts_prod_8(x1):  # the i_th element is the production of the elements in the n-period time series from the past
    t = 8
    x1_flat = _flatten(x1)
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算乘积
        prods = np.prod(x_min_periods)
        result[i-1] = prods
    # 对滑动窗口内的元素计算乘积
    prods = np.prod(x, axis=1)
    result[t-1:] = prods
    
    return norm(np.nan_to_num(result))

def ts_prod_17(x1):  # the i_th element is the production of the elements in the n-period time series from the past
    t = 17
    x1_flat = _flatten(x1)
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算乘积
        prods = np.prod(x_min_periods)
        result[i-1] = prods
    # 对滑动窗口内的元素计算乘积
    prods = np.prod(x, axis=1)
    result[t-1:] = prods
    
    return norm(np.nan_to_num(result))

def ts_std_10(x1):  # the i_th element is the standard deviation of the elements in the n-period time series from the past
    t = 10
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).std())
    return norm(x)

def ts_std_20(x1):  # the i_th element is the standard deviation of the elements in the n-period time series from the past
    t = 20
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).std())
    return norm(x)

def ts_std_40(x1):  # the i_th element is the standard deviation of the elements in the n-period time series from the past
    t = 40
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).std())
    return norm(x)

def ts_skew_10(x1):  # the i_th element is the skewness of the elements in the n-period time series from the past
    t = 10
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).skew())
    return norm(x)

def ts_skew_20(x1):  # the i_th element is the skewness of the elements in the n-period time series from the past
    t = 20
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).skew())
    return norm(x)

def ts_skew_40(x1):  # the i_th element is the skewness of the elements in the n-period time series from the past
    t = 40
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).skew())
    return norm(x)

def ts_kurt_10(x1):  # the i_th element is the kurtosis of the elements in the n-period time series from the past
    t = 10
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).kurt())
    return norm(x)

def ts_kurt_20(x1):  # the i_th element is the kurtosis of the elements in the n-period time series from the past
    t = 20
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).kurt())
    return norm(x)

def ts_kurt_40(x1):  # the i_th element is the kurtosis of the elements in the n-period time series from the past
    t = 40
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).kurt())
    return norm(x)

def ts_min_5(x1):  # the i_th element is the minimum value in the n-period time series from the past
    t = 5
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).min())
    return norm(x)

def ts_min_10(x1):  # the i_th element is the minimum value in the n-period time series from the past
    t = 10
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).min())
    return norm(x)

def ts_min_20(x1):  # the i_th element is the minimum value in the n-period time series from the past
    t = 20
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).min())
    return norm(x)

def ts_max_5(x1):  # the i_th element is the maximum value in the n-period time series from the past
    t = 5
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).max())
    return norm(x)

def ts_max_10(x1):  # the i_th element is the maximum value in the n-period time series from the past
    t = 10
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).max())
    return norm(x)

def ts_max_20(x1):  # the i_th element is the maximum value in the n-period time series from the past
    t = 20
    x1 = _flatten(x1)
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).max())
    return norm(x)

def ts_range_5(x1):
    t = 5
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).max()) - np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).min())
    return norm(x)

def ts_range_10(x1):
    t = 10
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).max()) - np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).min())
    return norm(x)

def ts_range_20(x1):
    t = 20
    x = np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).max()) - np.nan_to_num(pd.Series(x1).rolling(window=t, min_periods=int(t / 2)).min())
    return norm(x)

def ts_argmin_5(x1): # 性能提升1000倍!
    t = 5
    x1_flat = _flatten(x1)
    if len(x1_flat) < t: # 检查 x1 的长度是否足够
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最小值的位置
        argmins = np.argmin(x_min_periods) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
        result[i-1] = argmins
    
    # 对滑动窗口内的元素计算最小值的位置
    argmins = np.argmin(x, axis=1) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
    result[t-1:] = argmins
    
    return norm(np.nan_to_num(result))

def ts_argmin_10(x1):  # the i_th element is the location of the minimum value in the n-period time series from the past
    t = 10
    x1_flat = _flatten(x1)
    if len(x1_flat) < t: # 检查 x1 的长度是否足够
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最小值的位置
        argmins = np.argmin(x_min_periods) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
        result[i-1] = argmins
    
    # 对滑动窗口内的元素计算最小值的位置
    argmins = np.argmin(x, axis=1) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
    result[t-1:] = argmins
    
    return norm(np.nan_to_num(result))

def ts_argmin_20(x1):  # the i_th element is the location of the minimum value in the n-period time series from the past
    t = 20
    x1_flat = _flatten(x1)
    if len(x1_flat) < t: # 检查 x1 的长度是否足够
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最小值的位置
        argmins = np.argmin(x_min_periods) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
        result[i-1] = argmins
    
    # 对滑动窗口内的元素计算最小值的位置
    argmins = np.argmin(x, axis=1) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
    result[t-1:] = argmins
    
    return norm(np.nan_to_num(result))

def ts_argmax_5(x1): # 提升1000倍!
    t = 5
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最大值的位置
        argmaxs = np.argmax(x_min_periods) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
        result[i-1] = argmaxs
    
    # 对滑动窗口内的元素计算最大值的位置
    argmaxs = np.argmax(x, axis=1) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
    result[t-1:] = argmaxs
    
    return norm(np.nan_to_num(result))

def ts_argmax_10(x1):  # the i_th element is the location of the maximum value in the n-period time series from the past
    t = 10
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最大值的位置
        argmaxs = np.argmax(x_min_periods) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
        result[i-1] = argmaxs
    
    # 对滑动窗口内的元素计算最大值的位置
    argmaxs = np.argmax(x, axis=1) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
    result[t-1:] = argmaxs
    
    return norm(np.nan_to_num(result))

def ts_argmax_20(x1):  # the i_th element is the location of the maximum value in the n-period time series from the past
    t = 20
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最大值的位置
        argmaxs = np.argmax(x_min_periods) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
        result[i-1] = argmaxs
    
    # 对滑动窗口内的元素计算最大值的位置
    argmaxs = np.argmax(x, axis=1) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
    result[t-1:] = argmaxs
    
    return norm(np.nan_to_num(result))

def ts_argrange_5(x1): # 性能提升1000倍!
    t = 5
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最大值和最小值的位置
        argmaxs = np.argmax(x_min_periods) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
        argmins = np.argmin(x_min_periods) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
        result[i-1] = argmaxs - argmins
    
    # 对滑动窗口内的元素计算最大值和最小值的位置
    argmaxs = np.argmax(x, axis=1) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
    argmins = np.argmin(x, axis=1) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
    result[t-1:] = argmaxs - argmins
    
    return norm(np.nan_to_num(result))

def ts_argrange_10(x1):
    t = 10
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最大值和最小值的位置
        argmaxs = np.argmax(x_min_periods) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
        argmins = np.argmin(x_min_periods) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
        result[i-1] = argmaxs - argmins
    
    # 对滑动窗口内的元素计算最大值和最小值的位置
    argmaxs = np.argmax(x, axis=1) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
    argmins = np.argmin(x, axis=1) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
    result[t-1:] = argmaxs - argmins
    
    return norm(np.nan_to_num(result))

def ts_argrange_20(x1):
    t = 20
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最大值和最小值的位置
        argmaxs = np.argmax(x_min_periods) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
        argmins = np.argmin(x_min_periods) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
        result[i-1] = argmaxs - argmins
    
    # 对滑动窗口内的元素计算最大值和最小值的位置
    argmaxs = np.argmax(x, axis=1) + 1  # +1 是因为 argmax 返回的是基于 0 的索引
    argmins = np.argmin(x, axis=1) + 1  # +1 是因为 argmin 返回的是基于 0 的索引
    result[t-1:] = argmaxs - argmins
    
    return norm(np.nan_to_num(result))

def ts_rank_5(x1): # 性能提升20倍!
    t = 5
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最后一个元素的分位数
        ranks = np.sum(x_min_periods <= x_min_periods[-1]) / len(x_min_periods)
        result[i-1] = ranks
    
    # 对滑动窗口内的元素计算最后一个元素的分位数
    ranks = np.array([np.sum(window <= window[-1]) / len(window) for window in x])
    result[t-1:] = ranks
    
    return norm(np.nan_to_num(result))

def ts_rank_10(x1):  # the i_th element is the quantile of the last element in the n-period time series from the past
    t = 10
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最后一个元素的分位数
        ranks = np.sum(x_min_periods <= x_min_periods[-1]) / len(x_min_periods)
        result[i-1] = ranks
    
    # 对滑动窗口内的元素计算最后一个元素的分位数
    ranks = np.array([np.sum(window <= window[-1]) / len(window) for window in x])
    result[t-1:] = ranks
    
    return norm(np.nan_to_num(result))

def ts_rank_20(x1):  # the i_th element is the quantile of the last element in the n-period time series from the past
    t = 20
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    x1_flat = np.nan_to_num(x1_flat)  # 将 NaN 替换为 0
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        # 计算最后一个元素的分位数
        ranks = np.sum(x_min_periods <= x_min_periods[-1]) / len(x_min_periods)
        result[i-1] = ranks
    
    # 对滑动窗口内的元素计算最后一个元素的分位数
    ranks = np.array([np.sum(window <= window[-1]) / len(window) for window in x])
    result[t-1:] = ranks
    
    return norm(np.nan_to_num(result))

def ts_mean_return_5(x1): # 性能提升3000倍!
    t = 5
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        x_min_periods = x_min_periods[~np.isnan(x_min_periods)]
        # 计算百分比变化的均值
        pct_changes = np.diff(x_min_periods) / np.where(x_min_periods[:-1] != 0, x_min_periods[:-1], 1e-10)
        mean_returns = np.mean(pct_changes)
        result[i-1] = mean_returns
    
    # 对滑动窗口内的元素计算百分比变化的均值
    pct_changes = np.diff(x, axis=1) / np.where(x[:, :-1] != 0, x[:, :-1], 1e-10)
    mean_returns = np.nanmean(pct_changes, axis=1)
    result[t-1:] = mean_returns
    
    return norm(np.nan_to_num(result))

def ts_mean_return_10(x1):
    t = 10
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        x_min_periods = x_min_periods[~np.isnan(x_min_periods)]
        # 计算百分比变化的均值
        pct_changes = np.diff(x_min_periods) / np.where(x_min_periods[:-1] != 0, x_min_periods[:-1], 1e-10)
        mean_returns = np.mean(pct_changes)
        result[i-1] = mean_returns
    
    # 对滑动窗口内的元素计算百分比变化的均值
    pct_changes = np.diff(x, axis=1) / np.where(x[:, :-1] != 0, x[:, :-1], 1e-10)
    mean_returns = np.nanmean(pct_changes, axis=1)
    result[t-1:] = mean_returns
    
    return norm(np.nan_to_num(result))

def ts_mean_return_20(x1):
    t = 20
    x1_flat = _flatten(x1)
    
    # 检查 x1 的长度是否足够
    if len(x1_flat) < t:
        t = len(x1_flat)
    
    result = np.full(len(x1_flat), np.nan)  # 预先分配一个全是 NaN 的数组
    x = np.lib.stride_tricks.sliding_window_view(x1_flat, window_shape=t)
    
    for i in range(int(t / 2), t):  # min_periods 处理
        x_min_periods = x1_flat[:i]
        x_min_periods = x_min_periods[~np.isnan(x_min_periods)]
        # 计算百分比变化的均值
        pct_changes = np.diff(x_min_periods) / np.where(x_min_periods[:-1] != 0, x_min_periods[:-1], 1e-10)
        mean_returns = np.mean(pct_changes)
        result[i-1] = mean_returns
    
    # 对滑动窗口内的元素计算百分比变化的均值
    pct_changes = np.diff(x, axis=1) / np.where(x[:, :-1] != 0, x[:, :-1], 1e-10)
    mean_returns = np.nanmean(pct_changes, axis=1)
    result[t-1:] = mean_returns
    
    return norm(np.nan_to_num(result))

def ta_beta_5(x1, x2):
    t = 5
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = np.nan_to_num(talib.BETA(x1.astype(np.float64), x2.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_beta_10(x1, x2):
    t = 10
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = np.nan_to_num(talib.BETA(x1.astype(np.float64), x2.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_beta_20(x1, x2):
    t = 20
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = np.nan_to_num(talib.BETA(x1.astype(np.float64), x2.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_lr_slope_5(x1):
    t = 5
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.LINEARREG_SLOPE(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_lr_slope_10(x1):
    t = 10
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.LINEARREG_SLOPE(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_lr_slope_20(x1):
    t = 20
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.LINEARREG_SLOPE(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_lr_intercept_5(x1):
    t = 5
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.LINEARREG_INTERCEPT(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_lr_intercept_10(x1):
    t = 10
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.LINEARREG_INTERCEPT(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_lr_intercept_20(x1):
    t = 20
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.LINEARREG_INTERCEPT(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_lr_angle_5(x1):
    t = 5
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.LINEARREG_ANGLE(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_lr_angle_10(x1):
    t = 10
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.LINEARREG_ANGLE(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_lr_angle_20(x1):
    t = 20
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.LINEARREG_ANGLE(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_tsf_5(x1):
    t = 5
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TSF(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_tsf_10(x1):
    t = 10
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TSF(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_tsf_20(x1):
    t = 20
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TSF(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_ema_8(x1):
    t = 8
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.EMA(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_ema_21(x1):
    t = 21
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.EMA(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_ema_55(x1):
    t = 55
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.EMA(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_dema_8(x1):
    t = 8
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.DEMA(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_dema_21(x1):
    t = 21
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.DEMA(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_dema_55(x1):
    t = 55
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.DEMA(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_kama_8(x1):
    t = 8
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.KAMA(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_kama_21(x1):
    t = 21
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.KAMA(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_kama_55(x1):
    t = 55
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.KAMA(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_tema_8(x1):
    t = 8
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TEMA(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_tema_21(x1):
    t = 21
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TEMA(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_tema_55(x1):
    t = 55
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TEMA(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_trima_8(x1):
    t = 8
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TRIMA(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_trima_21(x1):
    t = 21
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TRIMA(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_trima_55(x1):
    t = 55
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TRIMA(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_rsi_6(x1):
    t = 6
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.RSI(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_rsi_12(x1):
    t = 12
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.RSI(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_rsi_24(x1):
    t = 24
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.RSI(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_cmo_14(x1):
    t = 14
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.CMO(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_cmo_25(x1):
    t = 25
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.CMO(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_mom_12(x1):
    t = 12
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.MOM(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_mom_25(x1):
    t = 25
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.MOM(x1.astype(np.float64), timeperiod=t))
    return norm(x)

# def ta_roc_14(x1):
#     t = 14
#     x1 = _flatten(x1)
#     return np.nan_to_num(talib.ROC(x1.astype(np.float64), timeperiod=t))

# def ta_roc_25(x1):
#     t = 25
#     x1 = _flatten(x1)
#     return np.nan_to_num(talib.ROC(x1.astype(np.float64), timeperiod=t))

def ta_rocp_14(x1):
    t = 14
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.ROCP(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_rocp_25(x1):
    t = 25
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.ROCP(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_rocr_14(x1):
    t = 14
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.ROCR(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_rocr_25(x1):
    t = 25
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.ROCR(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_trix_8(x1):
    t = 8
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TRIX(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_trix_21(x1):
    t = 21
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TRIX(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_trix_55(x1):
    t = 55
    x1 = _flatten(x1)
    x = np.nan_to_num(talib.TRIX(x1.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_adx_14(x1, x2, x3):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.ADX(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_adx_25(x1, x2, x3):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.ADX(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_adxr_14(x1, x2, x3):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.ADXR(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_adxr_25(x1, x2, x3):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.ADXR(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_aroonosc_14(x1, x2):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = np.nan_to_num(talib.AROONOSC(x1.astype(np.float64), x2.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_aroonosc_25(x1, x2):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = np.nan_to_num(talib.AROONOSC(x1.astype(np.float64), x2.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_cci_14(x1, x2, x3):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.CCI(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_cci_25(x1, x2, x3):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.CCI(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_dx_14(x1, x2, x3):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.DX(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_dx_25(x1, x2, x3):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.DX(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_mfi_14(x1, x2, x3, x4):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x4 = _flatten(x4)
    x = np.nan_to_num(talib.MFI(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), x4.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_mfi_25(x1, x2, x3, x4):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x4 = _flatten(x4)
    x = np.nan_to_num(talib.MFI(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), x4.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_minus_di_14(x1, x2, x3):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.MINUS_DI(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_minus_di_25(x1, x2, x3):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.MINUS_DI(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_minus_dm_14(x1, x2):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = np.nan_to_num(talib.MINUS_DM(x1.astype(np.float64), x2.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_minus_dm_25(x1, x2):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x = np.nan_to_num(talib.MINUS_DM(x1.astype(np.float64), x2.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_willr_14(x1, x2, x3):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.WILLR(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_willr_25(x1, x2, x3):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.WILLR(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_atr_14(x1, x2, x3):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.ATR(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_atr_25(x1, x2, x3):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.ATR(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_natr_14(x1, x2, x3):
    t = 14
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.NATR(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), timeperiod=t))
    return norm(x)

def ta_natr_25(x1, x2, x3):
    t = 25
    x1 = _flatten(x1)
    x2 = _flatten(x2)
    x3 = _flatten(x3)
    x = np.nan_to_num(talib.NATR(x1.astype(np.float64), x2.astype(np.float64), x3.astype(np.float64), timeperiod=t))
    return norm(x)

ts_cons_n30 = _ts_constant(-30.0)
ts_cons_n10 = _ts_constant(-10.0)
ts_cons_n5 = _ts_constant(-5.0)
ts_cons_n2 = _ts_constant(-2.0)
ts_cons_n1 = _ts_constant(-1.0)
ts_cons_n05 = _ts_constant(-0.5)
ts_cons_n001 = _ts_constant(-0.01)
ts_cons_30 = _ts_constant(30.0)
ts_cons_10 = _ts_constant(10.0)
ts_cons_5 = _ts_constant(5.0)
ts_cons_2 = _ts_constant(2.0)
ts_cons_1 = _ts_constant(1.0)
ts_cons_05 = _ts_constant(0.5) # 0.5
ts_cons_001 = _ts_constant(0.01) # 0.01