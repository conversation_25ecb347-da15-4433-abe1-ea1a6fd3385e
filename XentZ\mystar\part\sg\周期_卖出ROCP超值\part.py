
#!/usr/bin/env python
# -*- coding:utf-8 -*-

from hikyuu import *

author = "admin"
version = "20250417"

class ROCPSellCycleSignal(SignalBase):
    """周期信号, 支持轮内ROCP超过阀值卖出 (每天轮的意义不大)
    
    参数：
        rocp_days (int): ROCP计算天数
        take_profit (float): 止盈点,当ROCP大于此值时卖出
    """
    def __init__(self, rocp_period=5, take_profit=0.02, debug_if=False):
        super(ROCPSellCycleSignal, self).__init__("ROCPSellCycleSignal")
        # 设置参数
        self.set_param("rocp_period", rocp_period)  
        self.set_param("take_profit", take_profit)
        self.set_param("cycle", True)  # 标记为周期信号 --- 表示传入为切片数据
        self.set_param("alternate", False)  # 不要交替买卖
        self.set_param("debug_if",debug_if)
        
    def _clone(self):
        """复制函数，必须实现"""
        return ROCPSellCycleSignal(
            self.get_param("rocp_period"),
            self.get_param("take_profit"),
            self.get_param("debug_if")
        )
        
    def _calculate(self, k):
        """计算卖出信号"""
        if len(k) <= 0:
            return
                    
        # 获取参数
        rocp_period = self.get_param("rocp_period")
        take_profit = self.get_param("take_profit")
        debug_if = self.get_param("debug_if")
        
        # 获取股票和当前日期
        stock = k.get_stock()
        try:
            # 获取完整历史数据
            complete_k = self.to # 获得全量kdata
            # complete_k = stock.get_kdata(Query(-500))  # 获得足够长
            if complete_k.empty():
                return
            # 关键改进：遍历整个切片，确保所有交易日都被检查
            trace_head = f'[SG] {stock.code}_{stock.name}'
            hku_debug_if(debug_if,f'{trace_head} {k[0].datetime} - {k[-1].datetime}')
            for i in range(len(k)):
                # 获取当前交易日
                current_date = k[i].datetime
                # 在完整历史数据中找到对应位置
                current_pos = complete_k.get_pos(current_date)
                # 计算ROCP并判断
                if current_pos is not None and current_pos >= rocp_period:
                    current_rocp = (complete_k[current_pos].close - 
                                complete_k[current_pos-rocp_period].close) / complete_k[current_pos-rocp_period].close
                    
                    if i == 0: # 切片首个k
                        if current_rocp > take_profit: # 超过止盈点生成卖出信号
                            self._add_sell_signal(current_date) # TODO: 如果有持仓
                            hku_debug_if(debug_if,f'{trace_head} 切片首K ROCP:{current_rocp:.2f}>{take_profit:.2f} >>> {current_date}添加卖出sg后本轮休息')
                            # 本轮休息
                            return
                        else:
                            self._add_buy_signal(current_date)
                            hku_debug_if(debug_if,f'{trace_head} 切片首K ROCP:{current_rocp:.2f} >>> {current_date}首k添加买入sg')
                    else:
                        if current_rocp > take_profit: # 超过止盈点生成卖出信号
                            self._add_sell_signal(current_date) # TODO: 如果有持仓
                            hku_debug_if(debug_if,f'{trace_head} 切片内K ROCP:{current_rocp:.2f}>{take_profit:.2f} >>> {current_date}添加卖出sg后本轮休息')
                            return # 本轮休息
                        else:
                            hku_debug_if(debug_if,f'{trace_head} 切片内K ROCP:{current_rocp:.2f} >>> {current_date}无动作')
                else:
                    # 切片数据不足则直接买入
                    self._add_buy_signal(k[0].datetime)  # 周期开始日期
                    hku_debug_if(debug_if,f'{trace_head} 切片数据不足 >>> {current_date}首K添加买入sg')
        except Exception as e:
            print(f"计算ROCP卖出信号时发生错误: {e}")

def part(rocp_period: int = 5, take_profit: float = 0.02, debug_if: bool = False) -> SignalBase:
    """doc"""
    sg0 = ROCPSellCycleSignal(rocp_period=rocp_period, 
                              take_profit=take_profit,
                              debug_if=debug_if)
    sg0.name = "周期_卖出ROCP超值"
    return sg0
    

if __name__ == "__main__":
    # 执行 testall 命令时，会多传入一个参数，防止测试时间过长
    # 比如如果在测试代码中执行了绘图操作，可以打开下面的注释代码
    # 此时执行 testall 命令时，将直接返回
    if len(sys.argv) > 1:
        ind = part()
        print(ind)
        exit(0)

    # =============== 1. 策略参数设置 ===============
    # ETF列表
    etf_codes = [
        "sz159915",  # 创业板ETF(易方达)
        "sz159934",  # 主要消费ETF(嘉实)
        "sh510880",  # 上证红利ETF(华泰柏瑞)
        "sh513100",  # 纳斯达克100ETF(国泰)
        "sh513520",  # 日经etf
    ]
    # 
    start_date = Datetime(20200101)
    end_date = None
    initial_cash = 200000
    rebalance_days = 1
    rocp_period = 18
    take_profit = 0.15
    top_n = 1
    # cost_func = TC_FixedA2017()
    cost_func = TC_Zero()
    # ==================== 2. 系统参数配置 ===================
    options = {
        "stock_list": etf_codes,
        "ktype_list": ["day"],
        "load_history_finance": False,
        "load_weight": False,
        "start_spot": False,
        "spot_worker_num": 1,
    }
    load_hikyuu(**options)
    # =============== 3. 创建模拟交易账户/指标/交易系统 ===============
    tm = crtTM(date=start_date, init_cash=initial_cash, cost_func=cost_func)
    stks = tuple([sm[code] for code in options["stock_list"]])
    ind0 = get_part('mystar.ind.斜率年化乘R2',period=25)*0.4
    ind1 = (ROCP(CLOSE,5)+ROCP(CLOSE,10))*0.5*0.2
    ind2 = MA(VOL(),5) / MA(VOL(),20)*0.4
    ind = ind0 + ind1 + ind2
    
    sys = SYS_Simple()
    sys.set_param("buy_delay", False)
    sys.set_param("sell_delay", False)
    sys.tm = tm
    sys.mm = MM_FixedPercent(0.9)  # 使用90%的资金进行购买
    sg0 = part(rocp_period=rocp_period, 
                              take_profit=take_profit,
                              debug_if=True)
    sys.sg = sg0
    # sys.sg = SG_Cycle()
    # =============== 4. 创建选择器/资产分配器/投资组合 ===============
    se = SE_MultiFactor([ind], topn=top_n, 
                        ref_stk = sm['sz159915'],
                        mode="MF_EqualWeight")
    se.add_stock_list(stks,sys)
    af = AF_EqualWeight()
    pf = PF_Simple(tm=tm, af=af, se=se, adjust_cycle=rebalance_days,
                    adjust_mode="query", delay_to_trading_day=True)
    # pf.set_param("trace", True)
    # =============== 5. 指定测试区间并运行回测 ===============
    query = Query(start_date, end_date, 
                  ktype=Query.DAY, recover_type=Query.BACKWARD)
    query = Query(start_date, end_date)
    pf.run(query)
    # =============== 6. 结果保存与展示 ======================
    pf.tm.tocsv(os.path.dirname(os.path.abspath(__file__)))
    pf.performance()
    import matplotlib.pyplot as plt
    plt.show()  
