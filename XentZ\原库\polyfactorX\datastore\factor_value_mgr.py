import pandas as pd
from pandas.testing import assert_frame_equal
from typing import List
import duckdb
import os
import re
import json
import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
    
from jzal_pro.cls_common import BaseObj

class FactorValueMgr(BaseObj):
    def __init__(self, base_path: str, max_columns: int = 200):
        self.base_path = base_path
        self.max_columns = max_columns
        self.meta_file = os.path.join(base_path, "metadata.json")
        self._load_metadata()
        
    def _load_metadata(self):
        if os.path.exists(self.meta_file):
            with open(self.meta_file, 'r') as f:
                self.metadata = json.load(f)
        else:
            self.metadata = {}
            
    def _save_metadata(self):
        with open(self.meta_file, 'w') as f:
            json.dump(self.metadata, f)

    def _get_task_files(self, task_uid: str):
        ''' 获取某个任务ID下所有相关且文件名中带三位数字编号的Parquet文件 '''
        regex = re.compile(f"^{re.escape(task_uid)}_(\\d{{3}}).parquet$")
        all_files = os.listdir(self.base_path)
        files = sorted(f for f in all_files if regex.match(f))
        return [os.path.join(self.base_path, f) for f in files]

    def _get_new_filename(self, task_uid: str):
        ''' 生成新的Parquet文件名, 后缀为3位数字 '''
        existing_files = self._get_task_files(task_uid)
        if not existing_files:
            return os.path.join(self.base_path, f"{task_uid}_001.parquet")
        latest_file = existing_files[-1]
        file_number = int(latest_file.split('_')[-1].split('.')[0])
        return os.path.join(self.base_path, f"{task_uid}_{file_number + 1:03d}.parquet")
    
    def _get_current_file(self, task_uid: str):
        ''' 获取当前应存储数据的Parquet文件, 如果已满则生成新的文件名 '''
        existing_files = self._get_task_files(task_uid)
        if not existing_files:
            return self._get_new_filename(task_uid)
        # 确保metadata正确记录了文件列数
        for file in existing_files:
            if file not in self.metadata:
                df = pd.read_parquet(file)
                self.metadata[file] = df.shape[1]
                self._save_metadata()
    
        latest_file = existing_files[-1]
        file_cols = self.metadata.get(latest_file, 0)
        if file_cols < self.max_columns:
            return latest_file
        else:
            return self._get_new_filename(task_uid)
        
    def _initialize_new_file(self, file_path: str, base_data: pd.DataFrame):
        # 使用基础数据创建 DataFrame
        base_data.to_parquet(file_path)
        self.metadata[file_path] = base_data.shape[1]
        self._save_metadata()
        
    def add_base_data(self, task_uid: str, base_data: pd.DataFrame):
        ''' 挖掘之前 这步要先做! 
            保存基础数据到专门的文件,如date, OHLCV, ret, ret_open, ..其他labels
        '''
        base_data_file = os.path.join(self.base_path, f"{task_uid}_base.parquet")
        # 检查文件是否存在
        if os.path.exists(base_data_file):
            # 读取现有文件内容
            existing_data = pd.read_parquet(base_data_file)
            # 比较新数据与现有数据是否一致
            try:
                assert_frame_equal(existing_data, base_data, check_like=True)
                self.log("挖掘之前的基础数据与现有parquet文件一致,无需重新保存。")
            except AssertionError:
                self.log("挖掘之前的基础数据与现有parquet文件不一致, 有逻辑问题! 需退出检查...")
                exit()  # 如果内容不同，则覆盖保存
        else:
            # 如果文件不存在，则直接保存
            base_data.to_parquet(base_data_file)
        
    def add_factor_values(self, task_uid: str, new_data: pd.DataFrame):
        new_data = new_data.copy()
        base_data_file = os.path.join(self.base_path, f"{task_uid}_base.parquet")
        if not os.path.exists(base_data_file):
            raise ValueError("请先添加基础数据")

        current_file = self._get_current_file(task_uid)   # win7这句报错
        base_data = pd.read_parquet(base_data_file)
        
        if not os.path.exists(current_file):
            # 只有第一个文件包含 base_data
            if current_file.endswith('_001.parquet'):
                self._initialize_new_file(current_file, base_data)
            else:
                # 创建一个空的 DataFrame 只保留索引
                empty_df = pd.DataFrame(index=base_data.index)
                empty_df.to_parquet(current_file)
                self.metadata[current_file] = 0
                self._save_metadata()
        df = pd.read_parquet(current_file)
        # 确保索引对齐
        if not new_data.index.equals(df.index):
            raise ValueError("新数据的索引与现有数据的索引不匹配")

        # 去重: 新数据中去掉已经存在的列
        new_columns = [col for col in new_data.columns if col not in df.columns]
        # 打印去重后的列数量
        self.log(f"批量插入{current_file}的列数(去重后): {len(new_columns)}")  # 新增部分
        if new_columns:
            df = pd.concat([df, new_data[new_columns]], axis=1)
            df.to_parquet(current_file)
            self.metadata[current_file] = df.shape[1]
            self._save_metadata()
        else:
            self.log(f"所有新因子列已经存在于文件 {current_file} 中，未添加新的列。")
            
    def query_factor_values(self, task_uid: str) -> pd.DataFrame:
        files = self._get_task_files(task_uid)
        if not files:
            return pd.DataFrame()
        dfs = [pd.read_parquet(file) for file in files]
        return pd.concat(dfs, axis=1)
    
    def delete_factor_value(self, task_uid: str, expr: str) -> None:
        files = self._get_task_files(task_uid)
        for file in files:
            df = pd.read_parquet(file)
            if expr in df.columns:
                df.drop(columns=[expr], inplace=True)
                if df.empty:
                    os.remove(file)
                    del self.metadata[file]
                else:
                    df.to_parquet(file)
                    self.metadata[file] = df.shape[1]
                self._save_metadata()
    
    def load_factor_values(self, task_uid: str, excl_cols: List[str] = []) -> pd.DataFrame:
        files = self._get_task_files(task_uid)
        if not files:
            return pd.DataFrame()
        # 过滤掉以 '_base.parquet' 结尾的文件
        files = [file for file in files if not file.endswith('_base.parquet')]
        if not files:
            return pd.DataFrame()

        # 生成 DuckDB 查询语句以读取 Parquet 文件并进行全外连接
        query_parts = []
        for i, file in enumerate(files):
            query_parts.append("read_parquet('{}') AS t{}".format(file.replace('\\', '/'), i+1))
            
        if len(files) > 1:
            # 当有多个文件时，使用FULL OUTER JOIN
            query = "SELECT * FROM {}".format(query_parts[0])
            for i in range(1, len(files)):
                query += " FULL OUTER JOIN {} USING (date)".format(query_parts[i])
        else:
            # 当只有一个文件时，直接查询该文件
            query = "SELECT * FROM {}".format(query_parts[0])
        # 使用 DuckDB 执行查询
        conn = duckdb.connect()
        df = conn.execute(query).fetchdf()
        conn.close()
        ''' 解决内存不足 '''
        ''' 解决内存不足 '''
        df.set_index('date', inplace=True)
        
        if excl_cols:
            df = df.drop(columns=excl_cols)
        
        df.sort_index(inplace=True)  #  NOTE: 很关键, 不然duckdb机制FULL OUTER JOIN会乱序
            
        return df 

    def load_factor_values_by_cols(self, task_uid: str, incl_cols: List[str]) -> pd.DataFrame:
        
        files = self._get_task_files(task_uid)
        
        if not files:
            print("DEBUG: No files found")
            return pd.DataFrame()
        
        # 过滤掉以 '_base.parquet' 结尾的文件
        files = [file for file in files if not file.endswith('_base.parquet')]
        if not files:
            print("DEBUG: No non-base files found")
            return pd.DataFrame()
        
        # 检查每个文件的列
        all_columns = []
        for file in files:
            df = pd.read_parquet(file)
            all_columns.extend(df.columns.tolist())
        
        # 验证所有请求的列都存在
        missing_cols = [col for col in incl_cols if col not in all_columns]

        if missing_cols:
            raise ValueError(f"Requested columns not found: {missing_cols}")
        
        # 确保包含 'date' 列
        if 'date' not in incl_cols:
            incl_cols.append('date')
        # 生成 DuckDB 查询语句以读取 Parquet 文件并进行全外连接
        query_parts = []
        for i, file in enumerate(files):
            query_parts.append("read_parquet('{}') AS t{}".format(file.replace('\\', '/'), i+1))
        
        # 构建查询
        if len(files) > 1:
            # 当有多个文件时，使用FULL OUTER JOIN
            query = "SELECT * FROM {}".format(query_parts[0])
            for i in range(1, len(files)):
                query += " FULL OUTER JOIN {} USING (date)".format(query_parts[i])
            query = "SELECT {} FROM ({})".format(", ".join([f'"{col}"' for col in incl_cols]), query)
        else:
            # 当只有一个文件时，直接查询该文件
            query = "SELECT {} FROM {}".format(", ".join([f'"{col}"' for col in incl_cols]), query_parts[0])
        
        # 执行查询
        conn = duckdb.connect()
        try:
            df = conn.execute(query).fetchdf()
        except Exception as e:
            print(f"DEBUG: Query failed with error: {str(e)}")
            raise
        finally:
            conn.close()
        
        df.set_index('date', inplace=True)
        df.sort_index(inplace=True)  #  NOTE: 很关键, 不然duckdb机制FULL OUTER JOIN会乱序
    
        return df
          
if __name__ == "__main__":
    base_path = "D:\\myquant\\btq_jzal\\datastore\\dbs\\factor_values"
    mgr = FactorValueMgr(base_path)
    # df = mgr.load_factor_values_by_cols('cf73abd075ea446d',['ret'])
    df = mgr.load_factor_values_by_cols('cf73abd075ea446d_pj',['ret_open','ts_mean_40(Constant(-5.0))'])
    print(df.shape)
    print(df.index)
    print(df)
    df.to_csv('test.csv')
    
