from audioop import avg
from matplotlib import axis
from typing import Union
import pandas as pd
import numpy as np
from datetime import datetime
import math
from icecream import ic
import numba as nb

def year_frac(start: datetime, end: datetime):
    """
    start/end: datetime
    year fraction between two dates (i.e. 1.53 years).
    """
    if start > end:
        raise ValueError("start cannot be larger than end")
    return (end - start).total_seconds() / (31557600)

def calc_stats(df_all_nav: pd.DataFrame):
    """
    df_all_nav: 策略净值/价格净值, 可以多资产(多列) df(每日,行数相同)
                    nav1    nav2
    2014-01-03   0.992221   0.989171
    """
    df_all_nav.copy()
    # df_rates = df_all_nav.pct_change()
    df_rates = (df_all_nav / df_all_nav.shift(1)) - 1 # 前一个值 
    df_equity = df_all_nav
    df_equity.dropna(inplace=True)
    df_rates.dropna(inplace=True)
    
    count = len(df_all_nav)
    start = df_all_nav.index[0]
    end = df_all_nav.index[-1]

    accu_return = round(df_equity.iloc[-1] - 1, 2)
    accu_return.name = '累计收益'
    # annu_ret = round((accu_return + 1) ** (252 / count) - 1, 2)
    annu_ret = round(df_rates.mean(axis=0) * 252 , 2)
    annu_ret.name = '年化收益'
    annu_ret2 = round((accu_return+1) ** (1 / year_frac(start, end)) - 1,2)
    annu_ret2.name = '复合CAGR'
    # 标准差
    std = round(df_rates.std() * (252 ** 0.5), 2)
    std.name = '年化波动'
    # 夏普比
    sharpe = round(annu_ret / std, 2)
    sharpe.name = '夏普比率'
    # 最大回撤
    mdd = round((df_equity / df_equity.expanding(min_periods=1).max()).min() - 1, 2)
    mdd.name = '最大回撤'
    ret_2_mdd = round(annu_ret / abs(mdd), 2)
    ret_2_mdd.name = '卡玛比率'
    df_ratios = pd.concat([annu_ret, annu_ret2, mdd, ret_2_mdd, sharpe, accu_return, std], axis=1)
    df_ratios['开始时间'] = start.strftime('%Y-%m-%d')
    df_ratios['结束时间'] = end.strftime('%Y-%m-%d')
    
    ''' 逐年nav表现'''
    df_all_nav['year'] = df_all_nav.index.year
    nav_peryear = df_all_nav['nav'].groupby(df_all_nav['year']).last()/df_all_nav['nav'].groupby(df_all_nav['year']).first() - 1
    if not df_all_nav.iloc[:,1].empty:
        bech_peryear = df_all_nav.iloc[:,1].groupby(df_all_nav['year']).last()/df_all_nav.iloc[:,1].groupby(df_all_nav['year']).first() - 1
    excess_ret = nav_peryear - bech_peryear
    df_nav_peryear = pd.concat([nav_peryear,bech_peryear,excess_ret],axis = 1)
    df_nav_peryear.columns = ['strategy_ret','bench_ret','excess_ret']
    df_nav_peryear = round(df_nav_peryear,3)

    # 净值可视化...
    
    return df_ratios.T, df_nav_peryear.T

def calc_stats2(df: pd.DataFrame) -> pd.DataFrame: # 支持非日线级别bar nav
    """
                        date       nav
    0    2015-12-23 09:45:00  1.000000
    return: pd.DataFrame (11 * 1)
    """
    # 0：设置无风险利率
    fixed_return = 0.0
    df.set_index('date', inplace=True)
    # 1：总收益
    total_return = round(df['nav'].iloc[-1] / df['nav'].iloc[0] - 1, 3)
    total_return = pd.Series(total_return)
    total_return.name = '总收益'
    # 2：年化收益率
    date_list = [i for i in pd.Series(df.index).dt.date.unique()]
    run_day_length = len(date_list)  # 计算策略运行天数
    annual_return = pd.Series(np.round(np.power(1 + total_return, 252 / run_day_length) - 1, 3))
    annual_return.name = '年化收益率' # series
    # 3：夏普比率、年化波动率
    net_asset_value_list = []  # 初始化累计持仓净值列表（日度）
    # Timestamp('2006-01-04 15:00:00')  为一天结束bar
    net_asset_value_index = [i[-1] for i in df.index.groupby(pd.Series(df.index).dt.date).values()]
    for date_index in net_asset_value_index:
        net_asset_value = df.loc[date_index, 'nav']
        net_asset_value_list.append(net_asset_value)  # 附加每日结束时对应的累计持仓净值
    net = pd.DataFrame({'date': date_list, 'nav': net_asset_value_list})  # 构建日度累计持仓净值表格
    net.set_index('date', inplace=True)
    ''' net >>
                    nav
    date
    2015-12-23  0.998339
    '''
    net['log_ret_d'] = np.diff(np.log(net['nav']),prepend=np.log(net['nav'].iloc[0])) 
    
    # net.dropna(inplace=True)
    annual_volatility = round((math.sqrt(252) * net['log_ret_d'].std(axis=0)),3)  # 计算年化波动率
    sharpe_ratio = round((annual_return[0] - fixed_return) / annual_volatility,3)  # 计算夏普比率
    annual_volatility = pd.Series(annual_volatility)
    annual_volatility.name = '年化波动率'
    sharpe_ratio = pd.Series(sharpe_ratio)
    sharpe_ratio.name = '夏普比率'
    # 4：最大回撤率及其对应的起止日（需要利用计算夏普比率过程中构建的日度累计持仓净值表格）
    mdd_end_index = np.argmax((np.maximum.accumulate(net_asset_value_list) - net_asset_value_list) / (
        np.maximum.accumulate(net_asset_value_list)))
    if mdd_end_index == 0:
        ic('mdd_end_index == 0')
        return pd.DataFrame()
    mdd_end_date = net.index[mdd_end_index]  # 最大回撤结束日
    mdd_start_index = np.argmax(net_asset_value_list[: mdd_end_index])
    mdd_start_date = net.index[mdd_start_index]  # 最大回撤起始日

    maximum_drawdown = round((net_asset_value_list[mdd_start_index] - net_asset_value_list[mdd_end_index]) / (
        net_asset_value_list[mdd_start_index]),3)  # 计算最大回撤率
    maximum_drawdown = pd.Series(maximum_drawdown)  
    maximum_drawdown.name = '最大回撤率'
    mdd_start_date = pd.Series(mdd_start_date) 
    mdd_start_date.name = '最大回撤起始日'
    mdd_end_date = pd.Series(mdd_end_date) 
    mdd_end_date.name = '最大回撤结束日'

    # 5：卡尔玛比率（基于夏普比率以及最大回撤率）
    calmar_ratio = round((annual_return - fixed_return) / maximum_drawdown ,3) # 计算卡尔玛比率
    calmar_ratio = pd.Series(calmar_ratio)
    calmar_ratio.name = '卡尔玛比率'

    # 6：总交易次数、交易胜率、交易盈亏比
    total_trading_times = len(df)  # 计算总交易次数
    win_times = 0  # 初始化盈利次数
    win_lose_frame = pd.DataFrame()  # 初始化盈亏表格
    win_lose_frame['delta_value'] = df['nav'].diff()
    win_times = (win_lose_frame['delta_value'] > 0).sum()
    gain_amount = abs(win_lose_frame[win_lose_frame['delta_value'] > 0]['delta_value'].sum())  # 计算总盈利额
    loss_amount = abs(win_lose_frame[win_lose_frame['delta_value'] < 0]['delta_value'].sum())  # 计算总亏损额
    winning_rate = round(win_times / total_trading_times,3)  # 计算胜率
    gain_loss_ratio = round(gain_amount / loss_amount, 3)  # 盈利因子
    total_trading_times = pd.Series(total_trading_times)
    total_trading_times.name = '总交易次数'
    winning_rate = pd.Series(winning_rate)
    winning_rate.name = '胜率'
    gain_loss_ratio = pd.Series(gain_loss_ratio)
    gain_loss_ratio.name = '盈亏因子'
    df_ratios = pd.concat([total_return, annual_return,annual_volatility, sharpe_ratio, 
                           maximum_drawdown, mdd_start_date, mdd_end_date, calmar_ratio, 
                           total_trading_times,winning_rate, gain_loss_ratio], axis=1)
    return df_ratios.T

def calc_sr_RC(bar_pct_ret: Union[pd.DataFrame, pd.Series], 
                  bar_pct_cost: Union[pd.DataFrame, pd.Series], # 为了兼容 calc_stats_RC函数
                  day_bars: int = 16, ann_days: int = 252, 
                  fixed_return = 0.03) -> pd.DataFrame: # 支持非日线级别bar_pct_ret 和 矩阵/se        
    """
        bar_pct_ret: 扣除cost后的bar百分比收益率df或se [date(index), a_ret, b_ret, ...]
    """
    bar_pct_ret = bar_pct_ret.copy()
    if isinstance(bar_pct_ret, pd.Series):
        bar_pct_ret = pd.DataFrame(bar_pct_ret)
    bar_pct_ret.replace([np.inf, -np.inf, np.nan], 0, inplace=True)
    # 1：年化百分比收益率: bar_pct_ret_mean * N = ann_ret
    bar_pct_ret_mean = np.mean(bar_pct_ret, axis=0)
    # ann_pct_ret = np.power(bar_pct_ret_mean + 1, 252) - 1 # 复利年化
    ann_pct_ret = bar_pct_ret_mean * ann_days * day_bars
    ann_pct_ret.name = 'ann_ret'
    # 3：年化波动率std
    bar_pct_std = bar_pct_ret.std(axis=0) # np.std(ret,ddof=1) 速度接近且结果一致
    ann_pct_std = np.sqrt(day_bars * ann_days) * bar_pct_std # 每日16个15min bar
    ann_pct_std.name = 'ann_std'
    # 4：夏普比率
    cond_0 = np.isclose(ann_pct_std, 0)
    with np.errstate(divide='ignore', invalid='ignore'):
        ann_sr = np.where(cond_0, 0, (ann_pct_ret - fixed_return) / ann_pct_std)
    ann_sr = pd.Series(ann_sr, index=bar_pct_ret.columns, name='sr')
    df_sr = pd.DataFrame(ann_sr)
    return df_sr

def calc_rolling_sr_RC(bar_pct_ret: Union[pd.DataFrame, pd.Series], 
                       bar_pct_cost: Union[pd.DataFrame, pd.Series], # 为了兼容 calc_stats_RC函数
                       rolling_days: int, min_period: int, 
                       day_bars: int = 16,ann_days: int = 252,
                       fixed_return = 0.03) -> pd.DataFrame: # 支持非日线级别bar_pct_ret 和 矩阵/se
    '''
        bar_pct_ret: 扣除cost后的bar百分比收益率df或se [date(index), a_ret, b_ret, ...],支持日度以及日度以下
        roll_days: 滚动的天数
        返回: 滚动sharpe矩阵, 结构类似bar_pct_ret(列为不同的因子表达式名或不同策略名)
    '''
    bar_pct_ret = bar_pct_ret.copy()
    if isinstance(bar_pct_ret, pd.Series):
        bar_pct_ret = pd.DataFrame(bar_pct_ret)
    bar_pct_ret.replace([np.inf, -np.inf, np.nan], 0, inplace=True)
    # 计算滚动窗口大小
    rolling_window = rolling_days * day_bars
    # 计算滚动年化收益率
    rolling_mean = bar_pct_ret.rolling(window=rolling_window, min_periods=min_period).mean()
    rolling_ann_ret = rolling_mean * ann_days * day_bars
    # 计算滚动年化波动率
    rolling_std = bar_pct_ret.rolling(window=rolling_window, min_periods=min_period).std()
    rolling_ann_std = np.sqrt(day_bars * ann_days) * rolling_std
    # 计算滚动夏普比率
    cond_0 = np.isclose(rolling_ann_std, 0)
    with np.errstate(divide='ignore', invalid='ignore'):
        rolling_sr = np.where(cond_0, 0, (rolling_ann_ret - fixed_return) / rolling_ann_std)

    rolling_sr_df = pd.DataFrame(rolling_sr, index=bar_pct_ret.index, columns=bar_pct_ret.columns)
    rolling_sr_df = rolling_sr_df.replace([np.nan, np.inf, -np.inf],0)

    return rolling_sr_df

def calc_stats_RC(bar_pct_ret: Union[pd.DataFrame, pd.Series], 
                  bar_pct_cost: Union[pd.DataFrame, pd.Series],
                  day_bars: int = 16, ann_days: int = 252, 
                  fixed_return = 0.03) -> pd.DataFrame: # 支持非日线级别bar_pct_ret 和 矩阵/se        
    """
        bar_pct_ret: 扣除cost后的bar百分比收益率df或se [date(index), a_ret, b_ret, ...]
        bar_pct_cost: bar百分比成本df或se [date(index), a_cost, b_cost, ...]
    """
    bar_pct_ret = bar_pct_ret.copy()
    bar_pct_cost = bar_pct_cost.copy()
    if isinstance(bar_pct_ret, pd.Series):
        bar_pct_ret = pd.DataFrame(bar_pct_ret)
    if isinstance(bar_pct_cost, pd.Series):
        bar_pct_cost = pd.DataFrame(bar_pct_cost)
    # bar_pct_ret.set_index('date', inplace=True)
    bar_pct_ret.replace([np.inf, -np.inf, np.nan], 0, inplace=True)
    bar_pct_cost.replace([np.inf, -np.inf, np.nan], 0, inplace=True)
    # 0: 总百分比收益率
    tot_pct_ret = (bar_pct_ret + 1).prod() - 1
    tot_pct_ret.name = 'tot_ret'
    # 1：年化百分比收益率: bar_pct_ret_mean * N = ann_ret
    bar_pct_ret_mean = np.mean(bar_pct_ret, axis=0)
    # ann_pct_ret = np.power(bar_pct_ret_mean + 1, 252) - 1 # 复利年化
    ann_pct_ret = bar_pct_ret_mean * ann_days * day_bars
    ann_pct_ret.name = 'ann_ret'
    # 2：年化百分比cost: bar_pct_cost_mean * N = ann_cost
    bar_pct_cost_mean = np.mean(bar_pct_cost, axis=0)
    ann_pct_cost = bar_pct_cost_mean * ann_days * day_bars
    ann_pct_cost.index = ann_pct_ret.index
    ann_pct_cost.name = 'ann_cost'
    # 3：年化波动率std
    bar_pct_std = bar_pct_ret.std(axis=0) # np.std(ret,ddof=1) 速度接近且结果一致
    ann_pct_std = np.sqrt(day_bars * ann_days) * bar_pct_std # 每日16个15min bar
    ann_pct_std.name = 'ann_std'
    # 4：夏普比率
    cond_0 = np.isclose(ann_pct_std, 0)
    with np.errstate(divide='ignore', invalid='ignore'):
        ann_sr = np.where(cond_0, 0, (ann_pct_ret - fixed_return) / ann_pct_std)
    ann_sr = pd.Series(ann_sr, index=bar_pct_ret.columns, name='sr')
    # 4_2：cost夏普
    cond_0 = np.isclose(ann_pct_std, 0)
    with np.errstate(divide='ignore', invalid='ignore'):
        ann_cost_sr = np.where(cond_0, 0, (ann_pct_cost) / ann_pct_std)
    ann_cost_sr = pd.Series(ann_cost_sr, index=bar_pct_ret.columns, name='cost_sr')
    # 计算日度净值
    bar_net = (bar_pct_ret + 1).cumprod()
    day_net_index = [i[-1] for i in bar_pct_ret.index.groupby(pd.Series(bar_pct_ret.index).dt.date).values()]
    day_net = bar_net.loc[day_net_index]
    # 5：回撤 mdd & avg_dd
    day_net_rolling_max = day_net.cummax()   # 净值的滚动最大值
    day_dd = day_net / day_net_rolling_max - 1  # 净值相对于滚动最大值的回撤百分比
    mdd = abs(day_dd.min())  # 为了不用修改打分函数 此处以正数记录mdd
    mdd.name = 'mdd'
    # 5-1 最大回撤mdd结束日/开始日
    bgn_mdd = {}
    end_mdd = {}
    for col in day_net.columns:
        mdd_date = day_dd[col].idxmin()
        bgn_mdd[col] = day_net_rolling_max[col].loc[:mdd_date].idxmax()
        end_mdd[col] = day_net_rolling_max[col][(day_net_rolling_max[col] >= day_net_rolling_max[col][mdd_date])
                                                & (day_net_rolling_max[col].index >= mdd_date)].index[0]
    bgn_mdd = pd.Series(bgn_mdd, name='bgn_mdd')
    end_mdd = pd.Series(end_mdd, name='end_mdd')
    # 5. avg_dd
    mask = day_net < day_net_rolling_max
    avg_dd = abs(day_dd[mask].mean())  # 为了不用修改打分函数 此处以正数记录mdd
    avg_dd.name = 'avg_dd'
    # 6: 月skew
    monthly_net = day_net.resample('ME').last()
    monthly_pct_ret = monthly_net/monthly_net.shift(1) - 1
    monthly_skew = monthly_pct_ret.skew()
    monthly_skew.name = 'monthly_skew'
    # 7. tail -- kurt变形
    day_ret = day_net/day_net.shift(1) - 1
    day_ret = day_ret.fillna(0)
    day_ret_mean = day_ret.mean()
    day_ret_demean = day_ret - day_ret_mean
    perc_1 = np.percentile(day_ret_demean,1)
    perc_30 = np.percentile(day_ret_demean,30)
    perc_70 = np.percentile(day_ret_demean,70)
    perc_99 = np.percentile(day_ret_demean,99)
    if perc_30 == 0 or np.isnan(perc_30) or np.isnan(perc_1):
        lower_tail = 1.0
    lower_tail = (perc_1/perc_30)/4.43 # 对高斯分布的相对倍数(高斯分布是异常值为正常范围的4.43倍)
    if perc_70 == 0 or np.isnan(perc_70) or np.isnan(perc_99):
        upper_tail = 1.0
    upper_tail = (perc_99/perc_70)/4.43
    lower_tail = pd.Series(lower_tail, index=bar_pct_ret.columns, name='lower_tail')
    upper_tail = pd.Series(upper_tail, index=bar_pct_ret.columns, name='upper_tail')
    # 8. 日度盈利因子
    delta_values = day_net.diff()
    is_positive = delta_values > 0
    is_negative = delta_values < 0
    gain_amounts = (delta_values * is_positive).sum()
    loss_amounts = abs((delta_values * is_negative).sum())
    profit_factor = np.where(loss_amounts == 0, 0, abs(gain_amounts / loss_amounts))
    profit_factor = pd.Series(profit_factor, index=bar_pct_ret.columns, name='profit_factor')
    # 9. calmar
    calmar = (ann_pct_ret - fixed_return) / mdd
    calmar.name = 'calmar'
    # 10. 交易次数
    trade_times = bar_pct_ret.notna().sum()
    trade_times.name = 'trade_times'
    # 11. 胜率
    win_rate = (bar_pct_ret > 0).mean()
    win_rate.name = 'win_rate'
    df_ratios = pd.concat([ann_sr,ann_cost_sr,tot_pct_ret,ann_pct_ret,ann_pct_cost,ann_pct_std,mdd,bgn_mdd,end_mdd,
                           avg_dd,monthly_skew,lower_tail,upper_tail,profit_factor,calmar,trade_times,win_rate],axis=1)

    return df_ratios

def stats_saw_score(df_ratios: pd.DataFrame, weights: Union[dict, None] = None):
    ''' df_ratios先标准化, 然后基于权重对每行策略的指标打分, 构成新的一列'saw_score'(该策略的评分)
                sr   ann_ret  ann_cost   ann_std       mdd    avg_dd  monthly_skew  lower_tail  upper_tail  profit_factor
    a_ret  0.662892  0.450996  0.028020  0.635090 0.591366 0.266921      1.400281    0.945756    1.314639       1.161056
    b_ret -1.494991 -0.907855  0.060663  0.627332 0.909886 0.686748      1.718615    0.945756    1.314639       0.522447
    返回: 增加'saw_score'列的df_ratios    
    '''
    if weights is None:
        weights = {
            'sr': 0.27,           # 年化夏普比率 (越大越好)
            'ann_ret': 0.18,      # 年收益率 (越大越好)
            'cost_sr': -0.10,    # 年化成本夏普率 (越小越好)
            'ann_std': -0.10,     # 年化波动率 (越小越好)
            'mdd': -0.02,         # 最大回撤率 (越小越好)
            'avg_dd': -0.05,      # 平均回撤率 (越小越好)
            'monthly_skew': -0.05,# 月偏度 (负偏为好)
            'lower_tail': -0.05,  # 下尾比率 (越小越好)
            'upper_tail': 0.05,   # 上尾比率 (越大越好)
            'profit_factor': 0.13 # 盈利因子 (越大越好)
        }
    df_ratios = df_ratios.copy()
    # row_means = df_ratios.mean(axis=1)
    # row_stds = df_ratios.std(axis=1)
    
    # 确保权重字典的键与 df_ratios 的列名匹配
    valid_weights = {k: weights[k] for k in weights if k in df_ratios.columns}
    total_weight = sum(abs(weight) for weight in valid_weights.values())
    normalized_weights = {key: weight / total_weight for key, weight in valid_weights.items()}
    valid_columns = list(valid_weights.keys())

    # 按行进行zscore -- 有些牵强
    standardized_df = df_ratios[valid_columns].sub(
                        df_ratios[valid_columns].mean(axis=1), axis=0).div(
                        df_ratios[valid_columns].std(axis=1), axis=0)
    # print(standardized_df)
    # 计算归一化权重下的加权总得分
    standardized_df['saw_score'] = standardized_df.apply(
        lambda row: sum(row[col] * normalized_weights[col] for col in normalized_weights), axis=1)

    df_ratios['saw_score'] = standardized_df['saw_score']
    return df_ratios

def stats_topsis_score(df_ratios: pd.DataFrame, col_prefix:str = 'in_', strat_weights: Union[list, None] = None):
    ''' df_ratios先标准化, 然后基于权重计算得到新矩阵, TOPSIS算法总体大排名, 构成新的一列'TOPSIS_score'(该策略的评分)
                sr   ann_ret   cost_sr   ann_std       mdd    avg_dd  monthly_skew  lower_tail  upper_tail  profit_factor
    a_ret  0.662892  0.450996  0.028020  0.635090 0.591366 0.266921      1.400281    0.945756    1.314639       1.161056
    b_ret -1.494991 -0.907855  0.060663  0.627332 0.909886 0.686748      1.718615    0.945756    1.314639       0.522447
    strat_weights: 每个策略的权重(对应每行一个weight), 更新optresult表的in_TOPSIS字段时 通常为None
    返回: 增加'TOPSIS_score'列的df_ratios    
    '''
    # 指定每个指标的“越大越好”还是“越小越好”
    benefit_criteria = [col_prefix + crit for crit in ['sr', 'ann_ret', 
                                                       'upper_tail', 
                                                       'profit_factor']]
    
    cost_criteria = [col_prefix + crit for crit in ['cost_sr', 'ann_std',
                                                    'mdd', 'avg_dd', 
                                                    'monthly_skew',
                                                    'lower_tail']]

    numeric_columns = df_ratios.select_dtypes(include=[np.number]).columns
    
    # Step 1: 标准化 (向量归一化)
    norm_data = df_ratios[numeric_columns].copy()
    for column in norm_data.columns:
        norm_data[column] = df_ratios[column] / np.sqrt((df_ratios[column]**2).sum())
    
    # Step 2: 基于权重计算
    if strat_weights is not None:
        weighted_data = norm_data.multiply(strat_weights, axis=0)
    else:
        weighted_data = norm_data
    
    # Step 3: Determine ideal and negative ideal solutions
    ideal_solution = weighted_data.copy()
    negative_ideal_solution = weighted_data.copy()
    
    # 对于“越大越好”的指标，理想解是最大值，负理想解是最小值
    ideal_solution[benefit_criteria] = weighted_data[benefit_criteria].max(axis=0)
    negative_ideal_solution[benefit_criteria] = weighted_data[benefit_criteria].min(axis=0)
    
    # 对于“越小越好”的指标，理想解是最小值，负理想解是最大值
    ideal_solution[cost_criteria] = weighted_data[cost_criteria].min(axis=0)
    negative_ideal_solution[cost_criteria] = weighted_data[cost_criteria].max(axis=0)
    
    # Step 4: Calculate distance to ideal and negative ideal solutions
    dist_to_ideal = np.sqrt(((weighted_data - ideal_solution) ** 2).sum(axis=1))
    dist_to_negative_ideal = np.sqrt(((weighted_data - negative_ideal_solution) ** 2).sum(axis=1))
    
    # Step 5: Calculate relative closeness to the negative ideal solution
    relative_closeness = dist_to_negative_ideal / (dist_to_ideal + dist_to_negative_ideal)
    
    # 增加'TOPSIS_score'列到df_ratios
    if len(df_ratios) == 1:
        df_ratios[col_prefix+'topsis_score'] = 1.0
    else:
        df_ratios[col_prefix+'topsis_score'] = relative_closeness
    
    return df_ratios 

def calc_nav_FP2(df, y_hat, position_size):
    """计算持仓净值、累计持仓净值"""
    df = df.copy()
    # 计算仓位，仓位由预测出来的y_hat映射
    # position_size = 0.2
    df['position'] = [(i / 0.0005) * position_size for i in y_hat]
    df['position'] = df['position'].clip(-1, 1)
    # 分周期测算持仓净值
    df['持仓净值'] = 1 + df['position'].shift(1) * df['ret'].shift(1)
    df['持仓净值'] = df['持仓净值'].replace(0, 1e-10)
    # 测算累计持仓净值
    df['持仓净值(累计)'] = 1. * df['持仓净值'].cumprod()

    return df
def calc_nav_FP(a_df: pd.DataFrame, pos_hat: list, pos_size: float): # 基于预测(forcast)的仓位(pos) 算 单品种策略净值
    ''' 是计算单品种df的策略净值 非价格净值!! '''
    df = a_df.copy() # 避免视图
    ''' check起始位置是否正确
    bgn_dt = df['date'].iloc[0].strftime('%Y-%m-%d %H:%M:%S')
    end_dt = df['date'].iloc[-1].strftime('%Y-%m-%d %H:%M:%S')
    print(bgn_dt)
    print(end_dt)
    print(df)
    '''
    df['position'] = [(i / 0.0005) * pos_size for i in pos_hat] # 映射仓位, 否则 [1 for i in pos_hat]
    # 滚动标准化
    df['mean'] = df['position'].rolling(window=2000).mean()  # 15min设置2000或以上!!
    df['std'] = df['position'].rolling(window=2000).std()
    df['pos_norm'] = (df['position'] - df['mean']) / df['std']
    df.drop(columns=['mean', 'std'], inplace=True)
    df['pos_norm'] = df['pos_norm'].clip(-2.5, 2.5) # norm时千万别是1, 否则30%信息会丢!!
       
    df['position'] = df['position'].clip(-1, 1)  
    ''' df >>
                    date       tdate   close    factor      ret     position    pos_norm
    0  2005-02-23 09:45:00  2005-02-23  0.6433  0.000000   0.002332  0.068513    NaN
    '''
    df['change_pct'] = df.close.pct_change(1).fillna(0)
    df['position'] = df['position'].shift(1) 
    df['last_pos'] = df['position'].shift(1)
    df['inc_pos'] = df['position'] - df['last_pos']
    df = df.replace([np.inf, -np.inf, np.nan], 0.0)
    fee_rate = 0.001
    df['nav'] = 1 + ((df.last_pos + df.inc_pos) * df.change_pct - fee_rate * df.inc_pos.abs()).cumsum()
    df['nav_change_pct'] = df['nav'].pct_change(1).fillna(0)
    ''' df >>
                        date       tdate   close    factor       ret  ...  change_pct  last_pos   inc_pos       nav  nav_change_pct
    0     2005-02-23 09:45:00  2005-02-23  0.6433  0.000000  0.002332  ...    0.000000  0.000000  0.000000  1.000000    0.000000e+00
    '''
    return df

def calc_nav_FP3(a_df: pd.DataFrame, pos_hat: list, pos_size: float): # 基于预测(forcast)的仓位(pos) 算 单品种策略净值
    ''' 基于预测(forcast)的仓位(pos)计算单品种df的策略净值 非价格净值!! '''
    df = a_df.copy() # 避免视图
    df['position'] = [(i / 0.0005) * pos_size for i in pos_hat] # 映射仓位, 否则 [1 for i in pos_hat]
    # # 滚动标准化
    # df['mean'] = df['position'].rolling(window=2000).mean()  # 15min设置2000或以上!!
    # df['std'] = df['position'].rolling(window=2000).std()
    # df['pos_norm'] = (df['position'] - df['mean']) / df['std']
    # df.drop(columns=['mean', 'std'], inplace=True)
    # df['pos_norm'] = df['pos_norm'].clip(-2.5, 2.5) # 千万别是1, 否则30%信息会丢!!
       
    df['position'] = df['position'].clip(-2, 2) # 千万别是1, 否则30%信息会丢!!  
    ''' df >>
                    date       tdate   close    factor      ret     position    pos_norm
    0  2005-02-23 09:45:00  2005-02-23  0.6433  0.000000   0.002332  0.068513    NaN
    '''
    # df['position'] = df['position'].shift(1)
    # df['nav'] = 1
    # mv = df['close'].iloc[0] * 1
    # for i in range(1, len(df), 1):
    #     lots = abs(df['position'].iloc[i])
    #     if df['position'].iloc[i] > 0 : # 多单
    #         mv = (df['close'].iloc[i] - df['close'].iloc[i-1]) * lots + mv
    #     elif df['position'].iloc[i] < 0 : # 空单
    #         mv = (df['close'].iloc[i-1] - df['close'].iloc[i]) * lots + mv
    #     df['nav'].iloc[i] = mv/df['close'].iloc[0] 
    fee_rate = 0.001
    df['position'] = df['position'].shift(1)
    df = df.replace([np.inf, -np.inf, np.nan], 0.0)
    df['nav'] = 1
    df['fee'] =0
    df['mv'] =0
    mv = df['close'].iloc[0] * 1
    for i in range(1, len(df), 1):
        this_klots = abs(df['position'].iloc[i])
        last_klots = abs(df['position'].iloc[i-1])
        incr_klots = abs(this_klots - last_klots)
        if df['position'].iloc[i-1] > 0 : # 上个bar是多单
            mv = (df['open'].iloc[i] - df['close'].iloc[i-1]) * last_klots + mv
            if df['position'].iloc[i] > 0: # 这个bar是多单
                mv = (df['close'].iloc[i] - df['open'].iloc[i]) * this_klots + mv
            elif df['position'].iloc[i] < 0: # 这个bar是空单
                mv = (df['open'].iloc[i] - df['close'].iloc[i]) * this_klots + mv  
        elif df['position'].iloc[i-1] < 0 : # 上个bar是空单
            mv = (df['close'].iloc[i-1] - df['open'].iloc[i]) * last_klots + mv
            if df['position'].iloc[i] > 0: # 这个bar是多单
                mv = (df['close'].iloc[i] - df['open'].iloc[i]) * this_klots + mv
            elif df['position'].iloc[i] < 0: # 这个bar是空单
                mv = (df['open'].iloc[i] - df['close'].iloc[i]) * this_klots + mv
        df['fee'].iloc[i] = fee_rate * incr_klots * df['open'].iloc[i] + df['fee'].iloc[i-1]  
        df['mv'].iloc[i] = mv
    df['nav'] = (df['mv']-df['fee'])/df['close'].iloc[0]
    df['nav'].iloc[0] = 1
    return df

def calc_daily_ret(bar_ln_ret:np.ndarray)-> np.ndarray:
    '''
        输入: 列名因子值, 矩阵值是每bar的对数收益率
        返回: 日对数收益率(每日的bar对数收益率求和) -- 二维数组
    '''
    how_many_bars_daily =16 # 这个品种一天有多少个15分钟的bar?
    rows_to_use = bar_ln_ret.shape[0]- bar_ln_ret.shape[0] % how_many_bars_daily # 控制长度,被一天的bar的个数整除
    reshaped_array = bar_ln_ret[:rows_to_use].reshape(-1,16,bar_ln_ret.shape[1])# 重要,重塑了矩阵形状
    daily_ln_ret = np.exp(reshaped_array.sum(axis=1))-1 # 返回矩阵的列是因子值,行是每一日的收益率
    return daily_ln_ret

def to_day_pct_ret_np(bar_pct_ret: np.ndarray, day_bars: int = 16) -> np.ndarray:
    '''
        输入: 矩阵值是每bar的百分比收益率
        返回: 日百分比收益率 -- 二维数组
    '''
    # 控制长度，使数据长度能被一天的bar数量整除
    rows_to_use = bar_pct_ret.shape[0] - (bar_pct_ret.shape[0] % day_bars)
    # 重塑矩阵，使其具有每天的bar数量的维度
    reshaped_array = bar_pct_ret[:rows_to_use].reshape(-1, day_bars, bar_pct_ret.shape[1])
    # 对每组bar的百分比收益率加1后进行乘积，再减去1得到日百分比收益率
    daily_pct_ret = (reshaped_array + 1).prod(axis=1) - 1

    return daily_pct_ret

# def to_day_pct_ret(bar_pct_ret: pd.DataFrame, day_bars: int = 16) -> pd.DataFrame:
#     '''
#     输入: DataFrame类型, 矩阵值是每bar的百分比收益率, 含date索引(datetime类型)
#     返回: DataFrame类型, 对应的日频索引, 日百分比收益率
#     '''
#     bar_pct_ret = bar_pct_ret[~bar_pct_ret.index.duplicated(keep='first')]
#     daily_pct_ret = bar_pct_ret.resample('D', closed='left', label='left').apply(
#                         lambda x: (x + 1).prod() - 1 if x.count() >= 1 else float('nan'))
#     daily_pct_ret = daily_pct_ret.dropna()
    
#     return daily_pct_ret
def to_day_pct_ret(bar_pct_ret: pd.DataFrame, day_bars: int = 16) -> pd.DataFrame:  # 性能提升35倍!
    '''
    NOTE: day_bars不起作用, 支持每日交易的bar数不完全一致的真实交易情况!
    输入: DataFrame类型, 矩阵值是每bar的百分比收益率, 含date索引(datetime类型)
    返回: DataFrame类型, 对应的日频索引, 日百分比收益率
    '''
    # 确保索引是日期时间类型
    if not isinstance(bar_pct_ret.index, pd.DatetimeIndex):
        bar_pct_ret.index = pd.to_datetime(bar_pct_ret.index)
    
    normalized_dates = bar_pct_ret.index.normalize()  # 根据日期归一化（提取日期）以计算日期变化
    # 找出每一天的起点（日期发生变化的位置）
    date_change = normalized_dates[1:] != normalized_dates[:-1]
    date_change = np.insert(date_change, 0, True)  # 第一天也是日期变化点    
    day_end_indices = np.where(date_change)[0] # 找到每天的结束索引

    daily_returns = []
    for i in range(1, len(day_end_indices)):
        day_start_idx = day_end_indices[i - 1]
        day_end_idx = day_end_indices[i]
        # 计算从 day_start_idx 到 day_end_idx-1 的日收益率
        day_ret = np.prod(1 + bar_pct_ret.values[day_start_idx:day_end_idx], axis=0) - 1
        daily_returns.append(day_ret)
        
    # 处理最后一天的数据
    last_day_start_idx = day_end_indices[-1]
    day_ret = np.prod(1 + bar_pct_ret.values[last_day_start_idx:], axis=0) - 1
    daily_returns.append(day_ret)

    result = pd.DataFrame(daily_returns, 
                          index=normalized_dates[day_end_indices].unique(), 
                          columns=bar_pct_ret.columns)
    
    return result
def to_day_close(bar_close: pd.DataFrame, day_bars: int = 16) -> pd.DataFrame:
    '''
    输入: DataFrame类型, 矩阵值是每bar的close值, 含date索引(datetime类型, 因有非交易日存在而不连续)
    返回: DataFrame类型, 对应的日频索引, 日结束时的收盘价
    '''
    # 确保索引是唯一的，并且只包含实际存在的交易日
    bar_close = bar_close[~bar_close.index.duplicated(keep='first')]
    # 使用resample函数按日重采样，并从每个交易日的最后一个bar中提取收盘价
    # 'D'表示按日重采样，closed='right'表示包含右边界（即每个交易日的最后一个bar）
    # label='right'表示标签（即日期）是每个交易日的最后一个bar的日期
    daily_close = bar_close.resample('D', closed='left', label='left').last()
    
    return daily_close
    
def calc_sr_ln(bar_ln_ret:np.ndarray, trading_days:int = 252)-> float:
    # TODO: 此函数错误待排查
    # puppy: 返回的sr是个一维数组,shape[0]为因子的个数
    daily_ln_ret = calc_daily_ret(bar_ln_ret) #返回的daily_return是一个列为因子值,行为每一日收益的矩阵
    total_ret = np.exp(np.sum(daily_ln_ret, axis=0)) - 1 # -- 一维数组
    ann_comp_ret = np.sign(total_ret)* np.abs(np.abs(1 + total_ret)**(trading_days / (daily_ln_ret.shape[0]))- 1) # 年化收益率
    daily_pct_ret = np.exp(daily_ln_ret) - 1 # NOTE: 对数收益率转为百分比收益率! 重要! puppy
    annual_std = np.std(daily_pct_ret, axis=0) * np.sqrt(trading_days)
    sr = (ann_comp_ret-0.03) / (annual_std)
    return sr

def calc_sr(bar_ret:np.ndarray, day_bars: int = 16, trading_days:int = 252, free_rate:float = 0.03)-> np.ndarray:
    '''
        输入: 列名因子值, 矩阵值是每bar的算数收益率
        返回: 各因子值对应的sr -- 一维数组
    '''
    bar_ret = np.nan_to_num(bar_ret, nan=0.0, posinf=0.0, neginf=0.0, copy=False)
    # NOTE: 结论: 百分比收益率ret, ret.mean() * N = ann_ret
    ret_mean = np.mean(bar_ret,axis=0)
    ann_ret = ret_mean * day_bars * trading_days
    ret_std = bar_ret.std(axis=0) # np.std(ret,ddof=1) 速度接近且结果一致
    ann_vol = np.sqrt(day_bars * trading_days) * ret_std # 每日16个15min bar
    cond_0 = np.isclose(ann_vol, 0)
    with np.errstate(divide='ignore', invalid='ignore'):
        sr = np.where(cond_0, 0, (ann_ret - free_rate) / ann_vol)
    return sr

def warmup_jit_functions():
    """预热所有jit函数，避免第一次运行时的编译开销"""
    # 生成小规模测试数据
    n_samples, n_factors = 300, 2
    fct_values = np.random.randn(n_samples, n_factors).astype(np.float32)
    returns = np.random.randn(n_samples).astype(np.float32)
    # 预热所有jit函数
    _ = calc_rolling_rankic(fct_values, returns)

@nb.jit(nopython=True)
def rank_1d(arr):
    """计算1D数组的排名，与numpy双重argsort结果一致"""
    arr = arr.copy()
    n = len(arr)
    # 第一次argsort：获取值从小到大的索引位置
    # 注意：np.argsort默认是稳定排序，相同值的相对顺序保持不变
    first_sort = np.argsort(arr)
    ranks = np.zeros(n, dtype=np.int32)
    # 第二次：将排序位置赋值给原始位置
    for i in range(n):
        ranks[first_sort[i]] = i
    return ranks

@nb.jit(nopython=True, parallel=True)
def fast_spearman(x: np.ndarray, y: np.ndarray) -> np.ndarray:
    """使用numba加速的Spearman相关系数计算"""
    x = x.copy()
    y = y.copy()
    n = x.shape[1]
    rho = np.empty(x.shape[0])
    
    if n <= 1:
        rho.fill(np.nan)
        return rho
    
    for i in nb.prange(x.shape[0]):
        d_squared = (x[i] - y[i]) ** 2
        sum_d_squared = np.sum(d_squared)
        rho[i] = 1 - (6 * sum_d_squared) / (n * (n**2 - 1))
    
    return rho

@nb.jit(nopython=True, parallel=True)
def calc_rolling_rankic(fct_values: np.ndarray, returns: np.ndarray, 
                           window: int = 240, min_periods: int = 120) -> np.ndarray:
    """
    计算滚动 RankIC 矩阵（每个时间窗口对应一个 RankIC 值）
    
    Args:
        fct_values: 因子值序列, shape (n_samples,) 或 (n_samples, n_factors)
        returns: 未来 t 期收益率序列（与因子序列对齐）, shape (n_samples,)
        window: 滚动窗口大小
        min_periods: 每个窗口要求的最小有效样本数（这里以因子值非 0 的数量判断）
        
    Returns:
        滚动 RankIC 矩阵，形状为 (n_samples, n_factors) —— 前 window-1 行为 NaN，
        后续每一行对应一个窗口计算得到的 RankIC 值
    """
    """优化后实现"""
    # 数据预处理：处理NaN和无穷值
    fct_values_clean = fct_values.copy().astype(np.float32)
    returns_clean = returns.copy().astype(np.float32)
    
    if fct_values.ndim == 1:
        fct_values = fct_values.reshape(-1, 1)
    n_samples, n_factors = fct_values.shape
    
    rolling_rankic = np.full((n_samples, n_factors), np.nan)
        
    # 计算收益率的滑动窗口
    ret_roll = np.lib.stride_tricks.sliding_window_view(returns_clean, window)
    n_windows = ret_roll.shape[0]
    
    # 预计算收益率排名
    ret_ranks = np.empty_like(ret_roll, dtype=np.int32)
    for i in range(n_windows):
        ret_ranks[i] = rank_1d(ret_roll[i])
    
    # 并行处理每个因子
    for j in nb.prange(n_factors):
        fct_roll = np.lib.stride_tricks.sliding_window_view(fct_values_clean[:, j], window)
        fct_ranks = np.empty_like(fct_roll, dtype=np.int32)
        
        # 计算因子排名
        for i in range(n_windows):
            fct_ranks[i] = rank_1d(fct_roll[i])
        
        # 使用简单的比较操作替代isclose
        valid_mask = np.zeros(n_windows, dtype=np.bool_)
        for i in range(n_windows):
            valid_count = 0
            for k in range(window):
                if abs(fct_roll[i, k]) > 1e-8:  # 使用小阈值替代isclose
                    valid_count += 1
            valid_mask[i] = (valid_count >= min_periods)
        
        corr = fast_spearman(fct_ranks, ret_ranks)
        corr[~valid_mask] = np.nan
        rolling_rankic[window-1:, j] = corr
    
    return rolling_rankic

def generate_test_data():
    # 生成随机的bar_pct_ret数据
    num_bars = 100
    num_symbols = 5
    bar_pct_ret = np.random.rand(num_bars, num_symbols)
    # 添加日期索引
    dates = pd.date_range(start='2022-01-01', periods=num_bars, freq='15min')
    bar_pct_ret = pd.DataFrame(bar_pct_ret, index=dates)
    return bar_pct_ret

def test_to_day_pct_ret():
    # 生成测试数据
    bar_pct_ret = generate_test_data()
    # 调用to_day_pct_ret函数
    daily_pct_ret = to_day_pct_ret(bar_pct_ret)
    # 检查结果是否正确
    assert daily_pct_ret.index.freq == 'D'
    assert daily_pct_ret.shape[0] == (len(bar_pct_ret) // 96) + 1
    assert daily_pct_ret.shape[1] == bar_pct_ret.shape[1]
    # 输出结果
    print(bar_pct_ret)
    print(daily_pct_ret)

# if __name__ == '__main__':
    # test_to_day_pct_ret()

# if __name__ == '__main__':
    # # 创建跨3个月的测试数据
    # dates = pd.date_range(start='2023-01-01', end='2023-04-01', freq='15T')  # 确保有3个月的15分钟数据
    # bar_pct_ret_df = pd.DataFrame(np.random.randn(len(dates), 3) / 100, index=dates, columns=['a_ret', 'b_ret', 'c_ret'])
    # bar_pct_cost_df = pd.DataFrame(np.random.randn(len(dates), 3) / 1000, index=dates, columns=['a_cost', 'b_cost', 'c_cost'])

    # bar_pct_ret_se = bar_pct_ret_df['a_ret']
    # bar_pct_cost_se = bar_pct_cost_df['a_cost']

    # # 测试 DataFrame 情况
    # df_result = calc_stats_RC(bar_pct_ret_df, bar_pct_cost_df)
    # print("DataFrame 测试结果:")
    # print(df_result)

    # # 测试 Series 情况
    # se_result = calc_stats_RC(bar_pct_ret_se, bar_pct_cost_se)
    # print("Series 测试结果:")
    # print(se_result)
    ''' stats_saw_score 
    data = {
        'sr': [0.662892, -1.494991],
        'ann_ret': [0.450996, -0.907855],
        'ann_cost': [0.028020, 0.060663],
        'ann_std': [0.635090, 0.627332],
        'mdd': [-0.591366, -0.909886],
        'avg_dd': [-0.266921, -0.686748],
        'monthly_skew': [1.400281, 1.718615],
        'lower_tail': [0.945756, 0.945756],
        'upper_tail': [1.314639, 1.314639],
        'profit_factor': [1.161056, 0.522447]
    }
    
    df_ratios = pd.DataFrame(data, index=['a_ret', 'b_ret'])

    # 计算 SAW 分数
    df_ratios_with_scores = stats_saw_score(df_ratios)
    print(df_ratios_with_scores)
    '''
    ''' stats_TOPSIS_score 
    data = {
        'sr': [0.662892, -1.494991, 0.512345, 0.123456, -0.987654, 0.333333, -0.444444, 0.777777],
        'ann_ret': [0.450996, -0.907855, 0.223344, 0.567890, -0.234567, 0.678912, -0.543210, 0.123456],
        'ann_cost': [0.028020, 0.060663, 0.012345, 0.045678, 0.034567, 0.023456, 0.067890, 0.019876],
        'ann_std': [0.635090, 0.627332, 0.543210, 0.678912, 0.456789, 0.512345, 0.623456, 0.567890],
        'mdd': [0.591366, 0.909886, 0.512345, 0.678912, 0.456789, 0.334567, 0.234567, 0.543210],
        'avg_dd': [0.266921, 0.686748, 0.234567, 0.456789, 0.345678, 0.212345, 0.323456, 0.198765],
        'monthly_skew': [1.400281, 1.718615, 1.234567, 1.567890, 1.345678, 1.123456, 1.678912, 1.212345],
        'lower_tail': [0.945756, 0.945756, 0.900000, 0.850000, 0.870000, 0.880000, 0.860000, 0.910000],
        'upper_tail': [1.314639, 1.314639, 1.300000, 1.320000, 1.310000, 1.330000, 1.340000, 1.310000],
        'profit_factor': [1.161056, 0.522447, 1.100000, 0.900000, 1.200000, 1.300000, 1.400000, 1.500000]
    }

    df_ratios = pd.DataFrame(data, index=['a_ret', 'b_ret', 'c_ret', 'd_ret', 'e_ret', 'f_ret', 'g_ret', 'h_ret'])
    strat_weights = None  # 可以设置权重，例如：[0.5, 0.5]

    result_df = stats_TOPSIS_score(df_ratios, col_prefix='', strat_weights=strat_weights)
    print(result_df)
    '''
if __name__ == '__main__':
    # 示例数据
    dates = pd.date_range(start='2022-01-01', periods=100, freq='D')
    data = np.random.randn(100, 3)  # 3列代表不同的资产或策略
    bar_pct_ret = pd.DataFrame(data, index=dates, columns=['Asset1', 'Asset2', 'Asset3'])

    # 测试参数
    roll_days = 20
    min_periods = 3
    day_bars = 1
    ann_days = 252
    fixed_return = 0.03

    # 调用 calc_rolling_sr_RC 函数进行测试
    rolling_sharpe_df = calc_rolling_sr_RC(bar_pct_ret, None, roll_days, min_periods, day_bars, ann_days, fixed_return)

    # 打印结果
    print(bar_pct_ret)
    print(rolling_sharpe_df)