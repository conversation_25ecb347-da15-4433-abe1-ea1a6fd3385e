import pandas as pd
from pathlib import Path
from datetime import datetime
# from autogluon.tabular import TabularPredictor
from loguru import logger
from .algo_base import Algo
from config import DATA_DIR_OTHER
from quantlab.config import DATA_DIR_CACHE
from quantlab.datafeed.expr_funcs import roc

h5_file = DATA_DIR_CACHE.joinpath('cache.h5')

class SelectCap(Algo): 
    '''
    1.选择当日流通盘靠前的品种
    2.加入task.capK字段, 需要市场基本面快照数据支持
    '''
    def __init__(self):
        super(SelectCap, self).__init__()
        self.df = pd.read_csv(DATA_DIR_OTHER.joinpath('mkt_cap_snapshot.csv'))
        self.df.index = self.df.date # type: ignore
    
    def __call__(self, target):        
        df_bar = target.df_bar
        current_date = str(df_bar['date'][0])[0:10]
        df0 = self.df.loc[self.df.date==current_date]
        if len(df0) == 0:
            return True
        else:
            df0 = df0.sort_values(by='circulation_market_cap')[0:target.task.capK] 
            keys = [item for item in df_bar.index if item not in df0.index]
            df_bar.drop(keys, inplace=True)
            target.df_bar = df_bar
            return True

class SelectConcept(Algo):
    def __init__(self):
        super(SelectConcept, self).__init__()
        self.df = pd.read_csv(DATA_DIR_OTHER.joinpath('mkt_gn.csv'))
        self.df.index = self.df.date # type: ignore
        string = self.df.code
        new_string = []
        self.pre_close = {}
        for i in string:
            temp = '0' * (6 - len(str(i)))
            new_string.append(temp + str(i))
            self.pre_close[temp + str(i)] = 0
        self.df.code = new_string
        
    def __call__(self, target):
        df_bar = target.df_bar
        current_date = str(df_bar['date'][0])[0:10]
        df0 = self.df.loc[self.df.date==current_date]
        #print('------------------' + current_date)
        if len(df0) == 0:
            return True
        else:
            keys = []
            for item in df_bar.index:
                #print(df_bar.loc[df_bar.index=='002173'])
                x = list(set(list(df0.code)))
                if item not in x:
                    keys.append(item)
                else:
                    up = 0                    
                    y = df_bar.loc[df_bar.index==item]['close'].values[0]
                    #print(y, self.pre_close[item])
                    if self.pre_close[item] > 0:
                        up = y/self.pre_close[item]-1
                        print(up)
                    if up > 0.07 or up < -0.03:
                        keys.append(item)
                    self.pre_close[item] = y
            df_bar.drop(keys, inplace=True)
            target.df_bar = df_bar
            #print(df_bar)
            return True

class SignTime(Algo):
    def __init__(self, rule_time):
        super(SignTime, self).__init__()
        self.rule_list = []
        rule_time0 = pd.to_datetime(rule_time)
        start_time = rule_time0 - pd.Timedelta(days=120)
        with pd.HDFStore(h5_file.resolve()) as f:
            for key in f.keys():
                if len(key)==7:   #股票代码             
                    df_sign = f[key]
                    df_sign['date'] = pd.to_datetime(df_sign['date'])
                    sorted_df0 = df_sign.loc[(df_sign['date']<=rule_time0) & (df_sign['date']>=start_time)].sort_values(by='close')
                    # 获取最小值对应的日期
                    min_dates0 = sorted_df0.head(1).index[0]
                    if min_dates0 < rule_time0 - pd.Timedelta(days=100):
                        self.rule_list.append(key[1:])
    
    def __call__(self, target):
        df_bar = target.df_bar
        keys = [item for item in df_bar.index if item not in self.rule_list]
        df_bar.drop(keys, inplace=True)
        target.df_bar = df_bar
        return True

class SignCYB(Algo):
    '''
    板块指数择时 
    '''
    def __init__(self):
        super(SignCYB, self).__init__()
        with pd.HDFStore(h5_file.resolve()) as f:
            self.df_sign = f['sh399300']
            self.df_sign['roc_20'] = roc(self.df_sign.close, 20)
            self.status = True

    def __call__(self, target):
        df_bar = target.df_bar        
        current_date = str(df_bar['date'][0])[0:10]
        #df0 = self.df_sign.loc[self.df_sign.date==current_date]
        
        if current_date not in self.df_sign.index:
            return True # 无数据
        if self.df_sign.at[current_date, 'roc_20'] > 0.08:
            self.status = True
        if self.df_sign.at[current_date, 'roc_20'] < 0:
            self.status = False
        
        if not self.status:
            target.temp['selected'] = []            
            return False
        else:
            return True

class SignMarket(Algo):
    '''研判市场上涨下跌家数情况，是否可选股'''
    def __init__(self):
        super(SignMarket, self).__init__()
        self.df = pd.read_csv(DATA_DIR_OTHER.joinpath('mkt_sign.csv'))
        self.df.index = self.df.date
        self.status = True
    
    def __call__(self, target):        
        df_bar = target.df_bar
        #print(df_bar)
        current_date = str(df_bar['date'][0])[0:10]
        df0 = self.df.loc[self.df.date==current_date]
        if len(df0) == 0:
            return True # 无数据
        if df0.at[current_date, 'diff'] >= 200:
            self.status = False
        if df0.at[current_date, 'diff'] <= -700:
            self.status = True
        
        if not self.status:
            target.temp['selected'] = []            
            return False
        else:
            return True
        