# 混合面板数据模型 (Hybrid Panel Data Model) 任务配置示例
# 对应于 'factor/TODO-多因子.md' 中描述的最佳实践

dynaconf_include = ["_datasets/ts_multi_etf_pool.toml"]

[task]
name = "Hybrid_Panel_Model_on_Multi_ETF"
desc = "使用通用模型+特异性模型，对多品种ETF池进行预测"

# ============================== 模型定义 ============================== #
[model]
type = "hybrid_panel"

# 核心模型: 在所有品种的面板数据上训练，捕捉共性
[model.core]
name = "LGBM_Core"
# 因子类型A: 通用型因子，适用于所有品种
features = [
    "ROC5",
    "ROC20",
    "ROC60",
    "VOL20"
]
hyperparams = { learning_rate = 0.05, num_leaves = 31, n_estimators = 500 }

# 调整模型: 针对每个品种单独训练，捕捉其个性
# 'asset_specific' 表下的键是品种代码，值是该品种的特异性配置
[model.adjustment.asset_specific]

[model.adjustment.asset_specific."510050.SH"] # 上证50ETF
name = "Ridge_50ETF"
# 因子类型B: '510050.SH' 的特异性因子
features = ["size_factor", "liquidity_factor_50"]
hyperparams = { alpha = 1.0 }

[model.adjustment.asset_specific."159915.SZ"] # 创业板50ETF
name = "Ridge_CYB50"
# 因子类型B: '159915.SZ' 的特异性因子
features = ["growth_factor", "tech_momentum"]
hyperparams = { alpha = 0.8 }

# (其他品种的特异性配置...)

# ============================== 信号与仓位 ============================== #
[positioning]
# 使用 tanh S型曲线 将最终预测信号映射到仓位
mapping_function = "tanh"
tanh_scale = 1.5 # 曲线的陡峭程度 