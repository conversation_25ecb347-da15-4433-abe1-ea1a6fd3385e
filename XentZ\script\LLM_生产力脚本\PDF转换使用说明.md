# PDF转Markdown脚本使用说明

## 📋 功能概述

这个脚本能够智能地将PDF文档转换为Markdown格式，支持**多种文档类型**的差异化处理，有效避免内容误伤。

## 🎯 支持的文档类型

### 1. **研究报告** (`research_report`)
- **适用场景**: 券商研报、投资分析报告、行业研究等
- **特点**: 严格去重，过滤页眉页脚，保留核心图表
- **示例文件**: 华泰证券研报、中信证券分析等

### 2. **法律文档** (`legal_document`) 
- **适用场景**: 合同、协议、法律条文、合规文件等
- **特点**: 保留重复条款，避免过度去重
- **保护**: "第X条"、"甲方乙方"等重要法律术语

### 3. **学术论文** (`academic_paper`)
- **适用场景**: 期刊论文、学位论文、研究报告等  
- **特点**: 保留摘要、参考文献等重复性内容
- **保护**: "Abstract"、"References"等学术关键词

### 4. **通用文档** (`general`)
- **适用场景**: 产品说明书、用户手册、通用报告等
- **特点**: 最小化过滤，最大程度保留原始内容

## 🔧 使用方法

### 方式1: 处理单个文件

```python
# 修改 main() 函数中的配置
SINGLE_FILE_MODE = True

# 指定文件路径
input_dir = Path(r"你的PDF文件目录")
pdf_filename = "文件名.pdf"

# 运行脚本，自动检测文档类型
python 研报pdf转md.py
```

### 方式2: 批量处理

```python
# 修改 main() 函数中的配置  
SINGLE_FILE_MODE = False

# 指定包含多个PDF的目录
input_dir = Path(r"包含PDF文件的目录")

# 运行脚本，自动检测每个文件的类型
python 研报pdf转md.py
```

### 方式3: 手动指定文档类型

```python
# 直接调用函数，手动指定类型
pdf_to_markdown(
    pdf_path=Path("文件路径.pdf"),
    out_dir=Path("输出目录"),
    doc_type="legal_document"  # 强制指定为法律文档
)
```

## ⚙️ 自定义配置

### 创建专用配置

```python
# 为特殊需求创建自定义配置
custom_config = PDFProcessConfig(doc_type="research_report")

# 关闭去重功能（适用于需要保留所有重复内容的场景）
custom_config.enable_duplicate_filter = False

# 调整图片过滤阈值
custom_config.min_image_size = 5000  # 降低图片大小要求
custom_config.min_image_width = 100  # 降低图片宽度要求

# 自定义页眉页脚关键词
custom_config.header_footer_keywords = ["自定义关键词1", "自定义关键词2"]

# 使用自定义配置
pdf_to_markdown(pdf_path, out_dir, custom_config=custom_config)
```

## 🛡️ 误伤防护机制

### 1. **保护性关键词**
- 包含重要关键词的行即使重复也会保留
- 研报: "风险提示", "投资建议", "目标价", "评级"
- 法律: "第", "条", "款", "项", "协议", "合同"
- 学术: "abstract", "conclusion", "reference", "摘要"

### 2. **上下文检查**
- 重复行之间距离较远时会保留（避免误删表格标题）
- 包含数字的行优先保留
- 长度超过50字符的行通常保留

### 3. **智能判断**
- 包含句号的完整句子优先保留
- 包含特殊字符的行（如数学公式）保留
- 文档类型自动检测，应用对应的保护策略

## 📊 处理效果对比

| 文档类型 | 去重强度 | 页眉页脚过滤 | 图片过滤 | 典型用途 |
|---------|---------|-------------|---------|---------|
| 研究报告 | 严格 | 是 | 智能 | 券商研报、投资分析 |
| 法律文档 | 宽松 | 否 | 保守 | 合同协议、法律条文 |
| 学术论文 | 中等 | 是 | 智能 | 期刊论文、学位论文 |
| 通用文档 | 最小 | 否 | 保守 | 说明书、手册 |

## 🚀 最佳实践

### 1. **文档类型识别**
```python
# 让脚本自动识别（推荐）
doc_type = detect_document_type(pdf_path)

# 或根据文件名规律手动判断
if "研报" in filename or "证券" in filename:
    doc_type = "research_report"
elif "合同" in filename or "协议" in filename:
    doc_type = "legal_document"
```

### 2. **质量检查**
```python
# 处理完成后检查关键内容是否完整
# 1. 检查总行数是否合理
# 2. 查看是否有重要章节标题丢失
# 3. 验证图片数量是否符合预期
```

### 3. **异常处理**
```python
# 如果遇到内容丢失，可以临时调整配置
config = PDFProcessConfig(doc_type="general")
config.enable_duplicate_filter = False  # 关闭去重
config.filter_standalone_numbers = False  # 保留所有数字
```

## ⚠️ 注意事项

1. **备份原文件**: 建议在处理前备份原始PDF文件
2. **检查输出**: 处理完成后请检查Markdown文件是否完整
3. **调整配置**: 如发现内容丢失，可关闭相关过滤功能
4. **文档类型**: 自动检测可能不准确，必要时手动指定

## 🔍 故障排除

### 问题1: 重要内容被误删
**解决方案**: 
- 检查是否触发了页眉页脚过滤
- 将重要关键词添加到`protected_keywords`
- 或临时设置`enable_duplicate_filter = False`

### 问题2: 图片丢失
**解决方案**:
- 降低`min_image_size`阈值
- 调整`min_image_width`和`min_image_height`
- 设置`doc_type = "general"`使用保守的图片过滤

### 问题3: 文件过大
**解决方案**:
- 提高图片过滤阈值
- 启用更严格的去重
- 使用`research_report`模式

---

**💡 小贴士**: 如果处理的是全新类型的文档，建议先用`doc_type="general"`进行测试，确保内容完整性，然后再根据需要调整过滤参数。 