# -*- coding: utf-8 -*-


"""
ʹ��ǰ��Ҫ�޸�·�����ʽ��˺� �˺�����

"""

import csv
import os
import sys
from datetime import datetime
import time
import re
from threading import Timer

import pandas as pd
import numpy as np
import json
from xtquant import xtconstant
from xtquant import xtdata
# ������
from xtquant.xttype import StockAccount
from xtquant import xtconstant
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback


def print_attr(obj):
    attr_dict = {}
    for attr in dir(obj):
        try:
            if attr[:2] == 'm_':
                attr_dict[attr] = getattr(obj, attr)
        except:
            pass
    return attr_dict


class MyXtQuantTraderCallback(XtQuantTraderCallback):
    def on_disconnected(self):
        """
        ���ӶϿ�
        :return:
        """
        print(datetime.datetime.now(), '���ӶϿ��ص�')

    def on_stock_order(self, order):
        """
        ί�лر�����
        :param order: XtOrder����
        :return:
        """
        # print(dir(order))
        print(order.order_id)
        if order.offset_flag == xtconstant.OFFSET_FLAG_OPEN:
            print(print_attr(order))
            print(order.order_id,  order.stock_code, '����')
            print('===========')
            print(xt_trader.query_stock_orders(acc, ))

        # print('ί�лص�', print_attr(order))
        if order.offset_flag == xtconstant.OFFSET_FLAG_CLOSE:
            print(order.order_id,  order.stock_code, '����')
            
    def on_order_error(self, order_error):
        """
        委托失败推送
        :param order_error:XtOrderError 对象
        :return:
        """
        print("on order_error callback")
        print(order_error.order_id, order_error.error_id, order_error.error_msg)
        
    def on_stock_trade(self, trade):
        """
        �ɽ��䶯����
        :param trade: XtTrade����
        :return:
        """
        print('�ɽ��䶯����', trade.order_id, trade.offset_flag)
        # print('�ɽ��ص�', print_attr(trade))

    def on_order_stock_async_response(self, response):
        """
        �첽�µ��ر�����
        :param response: XtOrderResponse ����
        :return:
        """

        print(f"�첽ί�лص� {response.order_remark}")



if __name__ == '__main__':

    from random import randint
    xtdata.reconnect()
    print(xtdata.data_dir)

    path = r'I:\qmtͶ��\34969\ѸͶ���ٽ����ն�����ڿư�\userdata'
    path= r'D:/p-program/gjQMT_sim/userdata_mini'
    session_id = randint(100000, 999999)
    stock_account_id = '********'  # �ʽ��˺�
    xt_trader = XtQuantTrader(path, session_id)
    xt_trader.set_relaxed_response_order_enabled(True)
    acc = StockAccount(stock_account_id, 'STOCK')
    callback = MyXtQuantTraderCallback()
    xt_trader.register_callback(callback)
    xt_trader.start()
    connect_result = xt_trader.connect()
    # print('�����������ӣ�����0��ʾ���ӳɹ�', connect_result)
    subscribe_result = xt_trader.subscribe(acc)
    # print('�Խ��׻ص����ж��ģ����ĺ�����յ��������ƣ�����0��ʾ���ĳɹ�', subscribe_result)
    pos = xt_trader.query_stock_positions(acc)
    print(pos)
    # todo ��ѯί��
    # for obj in xt_trader.query_stock_orders(acc):
    #     print(print_attr(obj))
    # todo ��ѯ�ֲ�
    # positions = xt_trader.query_position_statistics(acc)
    # for obj in positions:
    #     print(print_attr(obj))
    # todo �µ�
    # xt_trader.order_stock(acc, '159001.SZ', xtconstant.ETF_PURCHASE, 1, 5, -1, '�����µ�', '�����µ�')
    # xt_trader.order_stock(acc, '000628.SZ', 23, 100, 11,87.56, '�����µ�', '�����µ�')
    # todo �µ����ض������
    # order_id =xt_trader.order_stock(acc, '600066.SH', 23, 200, 45,23.95,  '�����µ�', '�����µ�')
    # order_id = xt_trader.order_stock(acc, 'SM409.ZF', 23, 1, 5, -1, '�����µ�', '�����µ�')
    # todo �첽�µ�
    # xt_trader.order_stock_async(acc, '600066.SH', 23, 200, 45, 23.95, '�����µ�', '�����µ�')

    print('end')

    try:
        xt_trader.run_forever()

    except:
        pass






