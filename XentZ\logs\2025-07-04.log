2025-07-04 12:46 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 12:46 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 100
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 100
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 3
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 0
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 50
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 40
2025-07-04 12:46 | DEBUG    | WFAValidator.validate_wfa_inputs: 输入验证通过: 因子数据1000点, 价格数据1000点
2025-07-04 12:46 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252
2025-07-04 12:46 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.759
2025-07-04 12:46 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252
2025-07-04 12:46 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.759
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.1080
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 1.0000
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -1.0000
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | WARNING  | WFAValidator.calculate_spearman_correlation: 有效数据点不足: 2
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | WARNING  | WFAValidator.calculate_spearman_correlation: 有效数据点不足: 3
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=5.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.964, 0.964]
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=5.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.964, 0.964]
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=5.0, direction=-1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.964, 0.964]
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=1.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.380, 0.380]
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=10.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.999, 0.999]
2025-07-04 12:46 | DEBUG    | WFAValidator.check_wfa_criteria: 开始检查WFA通过标准
2025-07-04 12:46 | INFO     | WFAValidator.check_wfa_criteria: WFA验证未通过，失败原因数: 3
2025-07-04 12:46 | DEBUG    | WFAValidator.check_wfa_criteria: 开始检查WFA通过标准
2025-07-04 12:46 | INFO     | WFAValidator.check_wfa_criteria: WFA验证通过所有标准
2025-07-04 12:46 | INFO     | WFAValidator.run_wfa_validation: 开始WFA验证: 训练窗口=200, 测试窗口=50
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 执行数据预处理和对齐
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 数据对齐完成，有效数据点: 999
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 开始滚动窗口验证
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 1: 训练期 2020-01-02 00:00:00 - 2020-07-19 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0555
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 1 成功，方向=-1, 相关性=-0.0555
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 2: 训练期 2020-02-21 00:00:00 - 2020-09-07 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0636
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.887, 0.905]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 2 成功，方向=-1, 相关性=-0.0636
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 3: 训练期 2020-04-11 00:00:00 - 2020-10-27 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0171
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.881, 0.874]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 3 成功，方向=-1, 相关性=-0.0171
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 4: 训练期 2020-05-31 00:00:00 - 2020-12-16 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0069
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.897, 0.902]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 4 成功，方向=-1, 相关性=-0.0069
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 5: 训练期 2020-07-20 00:00:00 - 2021-02-04 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1145
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.881]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 5 成功，方向=1, 相关性=0.1145
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 6: 训练期 2020-09-08 00:00:00 - 2021-03-26 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1924
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.905]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 6 成功，方向=1, 相关性=0.1924
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 7: 训练期 2020-10-28 00:00:00 - 2021-05-15 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.2273
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.887]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 7 成功，方向=1, 相关性=0.2273
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 8: 训练期 2020-12-17 00:00:00 - 2021-07-04 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1673
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.902]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 8 成功，方向=1, 相关性=0.1673
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 9: 训练期 2021-02-05 00:00:00 - 2021-08-23 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0899
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 9 成功，方向=1, 相关性=0.0899
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 10: 训练期 2021-03-27 00:00:00 - 2021-10-12 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0089
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.902]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 10 成功，方向=1, 相关性=0.0089
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 11: 训练期 2021-05-16 00:00:00 - 2021-12-01 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0200
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.881]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 11 成功，方向=1, 相关性=0.0200
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 12: 训练期 2021-07-05 00:00:00 - 2022-01-20 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0134
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.863]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 12 成功，方向=-1, 相关性=-0.0134
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 13: 训练期 2021-08-24 00:00:00 - 2022-03-11 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0145
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.859, 0.897]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 13 成功，方向=1, 相关性=0.0145
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 14: 训练期 2021-10-13 00:00:00 - 2022-04-30 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0215
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.878, 0.897]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 14 成功，方向=1, 相关性=0.0215
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 15: 训练期 2021-12-02 00:00:00 - 2022-06-19 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0165
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.905]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 15 成功，方向=-1, 相关性=-0.0165
2025-07-04 12:46 | INFO     | WFAValidator.run_wfa_validation: 滚动窗口验证完成: 总窗口=15, 成功窗口=15
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 计算整体绩效指标
2025-07-04 12:46 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 750
2025-07-04 12:46 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.125
2025-07-04 12:46 | INFO     | WFAValidator.run_wfa_validation: WFA验证完成: 耗时=0.20秒, PnL长度=750, 夏普比率=-0.125
2025-07-04 12:46 | INFO     | WFAValidator.run_wfa_validation: 开始WFA验证: 训练窗口=200, 测试窗口=50
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 执行数据预处理和对齐
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 数据对齐完成，有效数据点: 999
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 开始滚动窗口验证
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 1: 训练期 2020-01-02 00:00:00 - 2020-07-19 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0555
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 1 成功，方向=-1, 相关性=-0.0555
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 2: 训练期 2020-02-21 00:00:00 - 2020-09-07 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0636
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.887, 0.905]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 2 成功，方向=-1, 相关性=-0.0636
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 3: 训练期 2020-04-11 00:00:00 - 2020-10-27 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0171
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.881, 0.874]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 3 成功，方向=-1, 相关性=-0.0171
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 4: 训练期 2020-05-31 00:00:00 - 2020-12-16 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0069
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.897, 0.902]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 4 成功，方向=-1, 相关性=-0.0069
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 5: 训练期 2020-07-20 00:00:00 - 2021-02-04 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1145
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.881]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 5 成功，方向=1, 相关性=0.1145
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 6: 训练期 2020-09-08 00:00:00 - 2021-03-26 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1924
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.905]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 6 成功，方向=1, 相关性=0.1924
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 7: 训练期 2020-10-28 00:00:00 - 2021-05-15 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.2273
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.887]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 7 成功，方向=1, 相关性=0.2273
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 8: 训练期 2020-12-17 00:00:00 - 2021-07-04 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1673
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.902]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 8 成功，方向=1, 相关性=0.1673
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 9: 训练期 2021-02-05 00:00:00 - 2021-08-23 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0899
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 9 成功，方向=1, 相关性=0.0899
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 10: 训练期 2021-03-27 00:00:00 - 2021-10-12 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0089
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.902]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 10 成功，方向=1, 相关性=0.0089
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 11: 训练期 2021-05-16 00:00:00 - 2021-12-01 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0200
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.881]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 11 成功，方向=1, 相关性=0.0200
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 12: 训练期 2021-07-05 00:00:00 - 2022-01-20 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0134
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.863]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 12 成功，方向=-1, 相关性=-0.0134
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 13: 训练期 2021-08-24 00:00:00 - 2022-03-11 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0145
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.859, 0.897]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 13 成功，方向=1, 相关性=0.0145
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 14: 训练期 2021-10-13 00:00:00 - 2022-04-30 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0215
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.878, 0.897]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 14 成功，方向=1, 相关性=0.0215
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 15: 训练期 2021-12-02 00:00:00 - 2022-06-19 00:00:00
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:46 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0165
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:46 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.905]
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 15 成功，方向=-1, 相关性=-0.0165
2025-07-04 12:46 | INFO     | WFAValidator.run_wfa_validation: 滚动窗口验证完成: 总窗口=15, 成功窗口=15
2025-07-04 12:46 | DEBUG    | WFAValidator.run_wfa_validation: 计算整体绩效指标
2025-07-04 12:46 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 750
2025-07-04 12:46 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.125
2025-07-04 12:46 | INFO     | WFAValidator.run_wfa_validation: WFA验证完成: 耗时=0.18秒, PnL长度=750, 夏普比率=-0.125
2025-07-04 12:47 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 12:47 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 100
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 100
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 3
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 0
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 50
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 40
2025-07-04 12:47 | DEBUG    | WFAValidator.validate_wfa_inputs: 输入验证通过: 因子数据1000点, 价格数据1000点
2025-07-04 12:47 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252
2025-07-04 12:47 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.759
2025-07-04 12:47 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252
2025-07-04 12:47 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.759
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.1080
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 1.0000
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -1.0000
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | WARNING  | WFAValidator.calculate_spearman_correlation: 有效数据点不足: 2
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | WARNING  | WFAValidator.calculate_spearman_correlation: 有效数据点不足: 3
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=5.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.964, 0.964]
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=5.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.964, 0.964]
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=5.0, direction=-1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.964, 0.964]
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=1.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.380, 0.380]
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=10.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.999, 0.999]
2025-07-04 12:47 | DEBUG    | WFAValidator.check_wfa_criteria: 开始检查WFA通过标准
2025-07-04 12:47 | INFO     | WFAValidator.check_wfa_criteria: WFA验证未通过，失败原因数: 3
2025-07-04 12:47 | DEBUG    | WFAValidator.check_wfa_criteria: 开始检查WFA通过标准
2025-07-04 12:47 | INFO     | WFAValidator.check_wfa_criteria: WFA验证通过所有标准
2025-07-04 12:47 | INFO     | WFAValidator.run_wfa_validation: 开始WFA验证: 训练窗口=200, 测试窗口=50
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 执行数据预处理和对齐
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 数据对齐完成，有效数据点: 999
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 开始滚动窗口验证
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 1: 训练期 2020-01-02 00:00:00 - 2020-07-19 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0555
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 1 成功，方向=-1, 相关性=-0.0555
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 2: 训练期 2020-02-21 00:00:00 - 2020-09-07 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0636
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.887, 0.905]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 2 成功，方向=-1, 相关性=-0.0636
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 3: 训练期 2020-04-11 00:00:00 - 2020-10-27 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0171
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.881, 0.874]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 3 成功，方向=-1, 相关性=-0.0171
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 4: 训练期 2020-05-31 00:00:00 - 2020-12-16 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0069
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.897, 0.902]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 4 成功，方向=-1, 相关性=-0.0069
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 5: 训练期 2020-07-20 00:00:00 - 2021-02-04 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1145
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.881]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 5 成功，方向=1, 相关性=0.1145
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 6: 训练期 2020-09-08 00:00:00 - 2021-03-26 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1924
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.905]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 6 成功，方向=1, 相关性=0.1924
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 7: 训练期 2020-10-28 00:00:00 - 2021-05-15 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.2273
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.887]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 7 成功，方向=1, 相关性=0.2273
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 8: 训练期 2020-12-17 00:00:00 - 2021-07-04 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1673
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.902]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 8 成功，方向=1, 相关性=0.1673
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 9: 训练期 2021-02-05 00:00:00 - 2021-08-23 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0899
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 9 成功，方向=1, 相关性=0.0899
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 10: 训练期 2021-03-27 00:00:00 - 2021-10-12 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0089
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.902]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 10 成功，方向=1, 相关性=0.0089
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 11: 训练期 2021-05-16 00:00:00 - 2021-12-01 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0200
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.881]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 11 成功，方向=1, 相关性=0.0200
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 12: 训练期 2021-07-05 00:00:00 - 2022-01-20 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0134
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.863]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 12 成功，方向=-1, 相关性=-0.0134
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 13: 训练期 2021-08-24 00:00:00 - 2022-03-11 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0145
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.859, 0.897]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 13 成功，方向=1, 相关性=0.0145
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 14: 训练期 2021-10-13 00:00:00 - 2022-04-30 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0215
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.878, 0.897]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 14 成功，方向=1, 相关性=0.0215
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 15: 训练期 2021-12-02 00:00:00 - 2022-06-19 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0165
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.905]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 15 成功，方向=-1, 相关性=-0.0165
2025-07-04 12:47 | INFO     | WFAValidator.run_wfa_validation: 滚动窗口验证完成: 总窗口=15, 成功窗口=15
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 计算整体绩效指标
2025-07-04 12:47 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 750
2025-07-04 12:47 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.125
2025-07-04 12:47 | INFO     | WFAValidator.run_wfa_validation: WFA验证完成: 耗时=0.18秒, PnL长度=750, 夏普比率=-0.125
2025-07-04 12:47 | INFO     | WFAValidator.run_wfa_validation: 开始WFA验证: 训练窗口=200, 测试窗口=50
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 执行数据预处理和对齐
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 数据对齐完成，有效数据点: 999
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 开始滚动窗口验证
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 1: 训练期 2020-01-02 00:00:00 - 2020-07-19 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0555
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 1 成功，方向=-1, 相关性=-0.0555
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 2: 训练期 2020-02-21 00:00:00 - 2020-09-07 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0636
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.887, 0.905]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 2 成功，方向=-1, 相关性=-0.0636
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 3: 训练期 2020-04-11 00:00:00 - 2020-10-27 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0171
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.881, 0.874]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 3 成功，方向=-1, 相关性=-0.0171
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 4: 训练期 2020-05-31 00:00:00 - 2020-12-16 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0069
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.897, 0.902]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 4 成功，方向=-1, 相关性=-0.0069
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 5: 训练期 2020-07-20 00:00:00 - 2021-02-04 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1145
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.881]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 5 成功，方向=1, 相关性=0.1145
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 6: 训练期 2020-09-08 00:00:00 - 2021-03-26 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1924
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.905]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 6 成功，方向=1, 相关性=0.1924
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 7: 训练期 2020-10-28 00:00:00 - 2021-05-15 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.2273
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.887]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 7 成功，方向=1, 相关性=0.2273
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 8: 训练期 2020-12-17 00:00:00 - 2021-07-04 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1673
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.902]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 8 成功，方向=1, 相关性=0.1673
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 9: 训练期 2021-02-05 00:00:00 - 2021-08-23 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0899
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 9 成功，方向=1, 相关性=0.0899
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 10: 训练期 2021-03-27 00:00:00 - 2021-10-12 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0089
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.902]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 10 成功，方向=1, 相关性=0.0089
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 11: 训练期 2021-05-16 00:00:00 - 2021-12-01 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0200
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.881]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 11 成功，方向=1, 相关性=0.0200
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 12: 训练期 2021-07-05 00:00:00 - 2022-01-20 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0134
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.863]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 12 成功，方向=-1, 相关性=-0.0134
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 13: 训练期 2021-08-24 00:00:00 - 2022-03-11 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0145
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.859, 0.897]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 13 成功，方向=1, 相关性=0.0145
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 14: 训练期 2021-10-13 00:00:00 - 2022-04-30 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0215
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.878, 0.897]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 14 成功，方向=1, 相关性=0.0215
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 15: 训练期 2021-12-02 00:00:00 - 2022-06-19 00:00:00
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 12:47 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0165
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 12:47 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.905]
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 15 成功，方向=-1, 相关性=-0.0165
2025-07-04 12:47 | INFO     | WFAValidator.run_wfa_validation: 滚动窗口验证完成: 总窗口=15, 成功窗口=15
2025-07-04 12:47 | DEBUG    | WFAValidator.run_wfa_validation: 计算整体绩效指标
2025-07-04 12:47 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 750
2025-07-04 12:47 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.125
2025-07-04 12:47 | INFO     | WFAValidator.run_wfa_validation: WFA验证完成: 耗时=0.21秒, PnL长度=750, 夏普比率=-0.125
2025-07-04 13:04 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:04 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 100
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 100
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 3
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 0
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 50
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 40
2025-07-04 13:04 | DEBUG    | WFAValidator.validate_wfa_inputs: 输入验证通过: 因子数据1000点, 价格数据1000点
2025-07-04 13:04 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:04 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.759
2025-07-04 13:04 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:04 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.759
2025-07-04 13:04 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 50, 年化因子: 252
2025-07-04 13:04 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 8.145
2025-07-04 13:04 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 50, 年化因子: 52
2025-07-04 13:04 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 3.700
2025-07-04 13:04 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 50, 年化因子: 12
2025-07-04 13:04 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 1.777
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.1080
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 1.0000
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -1.0000
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | WARNING  | WFAValidator.calculate_spearman_correlation: 有效数据点不足: 2
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | WARNING  | WFAValidator.calculate_spearman_correlation: 有效数据点不足: 3
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=5.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.964, 0.964]
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=5.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.964, 0.964]
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=5.0, direction=-1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.964, 0.964]
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=1.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.380, 0.380]
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=10.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.999, 0.999]
2025-07-04 13:04 | DEBUG    | WFAValidator.check_wfa_criteria: 开始检查WFA通过标准
2025-07-04 13:04 | INFO     | WFAValidator.check_wfa_criteria: WFA验证未通过，失败原因数: 3
2025-07-04 13:04 | DEBUG    | WFAValidator.check_wfa_criteria: 开始检查WFA通过标准
2025-07-04 13:04 | INFO     | WFAValidator.check_wfa_criteria: WFA验证通过所有标准
2025-07-04 13:04 | INFO     | WFAValidator.run_wfa_validation: 开始WFA验证: 训练窗口=200, 测试窗口=50
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 执行数据预处理和对齐
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 数据对齐完成，有效数据点: 999
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 开始滚动窗口验证
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 1: 训练期 2020-01-02 00:00:00 - 2020-07-19 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0555
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 1 成功，方向=-1, 相关性=-0.0555
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 2: 训练期 2020-02-21 00:00:00 - 2020-09-07 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0636
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.887, 0.905]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 2 成功，方向=-1, 相关性=-0.0636
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 3: 训练期 2020-04-11 00:00:00 - 2020-10-27 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0171
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.881, 0.874]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 3 成功，方向=-1, 相关性=-0.0171
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 4: 训练期 2020-05-31 00:00:00 - 2020-12-16 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0069
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.897, 0.902]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 4 成功，方向=-1, 相关性=-0.0069
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 5: 训练期 2020-07-20 00:00:00 - 2021-02-04 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1145
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.881]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 5 成功，方向=1, 相关性=0.1145
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 6: 训练期 2020-09-08 00:00:00 - 2021-03-26 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1924
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.905]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 6 成功，方向=1, 相关性=0.1924
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 7: 训练期 2020-10-28 00:00:00 - 2021-05-15 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.2273
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.887]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 7 成功，方向=1, 相关性=0.2273
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 8: 训练期 2020-12-17 00:00:00 - 2021-07-04 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1673
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.902]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 8 成功，方向=1, 相关性=0.1673
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 9: 训练期 2021-02-05 00:00:00 - 2021-08-23 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0899
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 9 成功，方向=1, 相关性=0.0899
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 10: 训练期 2021-03-27 00:00:00 - 2021-10-12 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0089
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.902]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 10 成功，方向=1, 相关性=0.0089
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 11: 训练期 2021-05-16 00:00:00 - 2021-12-01 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0200
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.881]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 11 成功，方向=1, 相关性=0.0200
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 12: 训练期 2021-07-05 00:00:00 - 2022-01-20 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0134
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.863]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 12 成功，方向=-1, 相关性=-0.0134
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 13: 训练期 2021-08-24 00:00:00 - 2022-03-11 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0145
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.859, 0.897]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 13 成功，方向=1, 相关性=0.0145
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 14: 训练期 2021-10-13 00:00:00 - 2022-04-30 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0215
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.878, 0.897]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 14 成功，方向=1, 相关性=0.0215
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 15: 训练期 2021-12-02 00:00:00 - 2022-06-19 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0165
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.905]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 15 成功，方向=-1, 相关性=-0.0165
2025-07-04 13:04 | INFO     | WFAValidator.run_wfa_validation: 滚动窗口验证完成: 总窗口=15, 成功窗口=15
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 计算整体绩效指标
2025-07-04 13:04 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 750, 年化因子: 252
2025-07-04 13:04 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.125
2025-07-04 13:04 | INFO     | WFAValidator.run_wfa_validation: WFA验证完成: 耗时=0.21秒, PnL长度=750, 夏普比率=-0.125
2025-07-04 13:04 | INFO     | WFAValidator.run_wfa_validation: 开始WFA验证: 训练窗口=200, 测试窗口=50
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 执行数据预处理和对齐
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 数据对齐完成，有效数据点: 999
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 开始滚动窗口验证
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 1: 训练期 2020-01-02 00:00:00 - 2020-07-19 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0555
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 1 成功，方向=-1, 相关性=-0.0555
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 2: 训练期 2020-02-21 00:00:00 - 2020-09-07 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0636
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.887, 0.905]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 2 成功，方向=-1, 相关性=-0.0636
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 3: 训练期 2020-04-11 00:00:00 - 2020-10-27 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0171
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.881, 0.874]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 3 成功，方向=-1, 相关性=-0.0171
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 4: 训练期 2020-05-31 00:00:00 - 2020-12-16 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0069
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.897, 0.902]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 4 成功，方向=-1, 相关性=-0.0069
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 5: 训练期 2020-07-20 00:00:00 - 2021-02-04 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1145
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.881]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 5 成功，方向=1, 相关性=0.1145
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 6: 训练期 2020-09-08 00:00:00 - 2021-03-26 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1924
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.905]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 6 成功，方向=1, 相关性=0.1924
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 7: 训练期 2020-10-28 00:00:00 - 2021-05-15 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.2273
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.887]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 7 成功，方向=1, 相关性=0.2273
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 8: 训练期 2020-12-17 00:00:00 - 2021-07-04 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1673
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.902]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 8 成功，方向=1, 相关性=0.1673
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 9: 训练期 2021-02-05 00:00:00 - 2021-08-23 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0899
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 9 成功，方向=1, 相关性=0.0899
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 10: 训练期 2021-03-27 00:00:00 - 2021-10-12 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0089
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.902]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 10 成功，方向=1, 相关性=0.0089
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 11: 训练期 2021-05-16 00:00:00 - 2021-12-01 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0200
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.881]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 11 成功，方向=1, 相关性=0.0200
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 12: 训练期 2021-07-05 00:00:00 - 2022-01-20 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0134
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.863]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 12 成功，方向=-1, 相关性=-0.0134
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 13: 训练期 2021-08-24 00:00:00 - 2022-03-11 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0145
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.859, 0.897]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 13 成功，方向=1, 相关性=0.0145
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 14: 训练期 2021-10-13 00:00:00 - 2022-04-30 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0215
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.878, 0.897]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 14 成功，方向=1, 相关性=0.0215
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 15: 训练期 2021-12-02 00:00:00 - 2022-06-19 00:00:00
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:04 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0165
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:04 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.905]
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 15 成功，方向=-1, 相关性=-0.0165
2025-07-04 13:04 | INFO     | WFAValidator.run_wfa_validation: 滚动窗口验证完成: 总窗口=15, 成功窗口=15
2025-07-04 13:04 | DEBUG    | WFAValidator.run_wfa_validation: 计算整体绩效指标
2025-07-04 13:04 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 750, 年化因子: 252
2025-07-04 13:04 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.125
2025-07-04 13:04 | INFO     | WFAValidator.run_wfa_validation: WFA验证完成: 耗时=0.19秒, PnL长度=750, 夏普比率=-0.125
2025-07-04 13:05 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:05 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 100
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 100
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 3
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 0
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 50
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 40
2025-07-04 13:05 | DEBUG    | WFAValidator.validate_wfa_inputs: 输入验证通过: 因子数据1000点, 价格数据1000点
2025-07-04 13:05 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:05 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.759
2025-07-04 13:05 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:05 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.759
2025-07-04 13:05 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 50, 年化因子: 252
2025-07-04 13:05 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 8.145
2025-07-04 13:05 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 50, 年化因子: 52
2025-07-04 13:05 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 3.700
2025-07-04 13:05 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 50, 年化因子: 12
2025-07-04 13:05 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 1.777
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.1080
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 1.0000
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -1.0000
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | WARNING  | WFAValidator.calculate_spearman_correlation: 有效数据点不足: 2
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | WARNING  | WFAValidator.calculate_spearman_correlation: 有效数据点不足: 3
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=5.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.964, 0.964]
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=5.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.964, 0.964]
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=5.0, direction=-1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.964, 0.964]
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=1.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.380, 0.380]
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=10.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.999, 0.999]
2025-07-04 13:05 | DEBUG    | WFAValidator.check_wfa_criteria: 开始检查WFA通过标准
2025-07-04 13:05 | INFO     | WFAValidator.check_wfa_criteria: WFA验证未通过，失败原因数: 3
2025-07-04 13:05 | DEBUG    | WFAValidator.check_wfa_criteria: 开始检查WFA通过标准
2025-07-04 13:05 | INFO     | WFAValidator.check_wfa_criteria: WFA验证通过所有标准
2025-07-04 13:05 | INFO     | WFAValidator.run_wfa_validation: 开始WFA验证: 训练窗口=200, 测试窗口=50
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 执行数据预处理和对齐
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 数据对齐完成，有效数据点: 999
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 开始滚动窗口验证
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 1: 训练期 2020-01-02 00:00:00 - 2020-07-19 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0555
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 1 成功，方向=-1, 相关性=-0.0555
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 2: 训练期 2020-02-21 00:00:00 - 2020-09-07 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0636
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.887, 0.905]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 2 成功，方向=-1, 相关性=-0.0636
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 3: 训练期 2020-04-11 00:00:00 - 2020-10-27 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0171
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.881, 0.874]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 3 成功，方向=-1, 相关性=-0.0171
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 4: 训练期 2020-05-31 00:00:00 - 2020-12-16 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0069
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.897, 0.902]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 4 成功，方向=-1, 相关性=-0.0069
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 5: 训练期 2020-07-20 00:00:00 - 2021-02-04 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1145
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.881]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 5 成功，方向=1, 相关性=0.1145
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 6: 训练期 2020-09-08 00:00:00 - 2021-03-26 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1924
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.905]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 6 成功，方向=1, 相关性=0.1924
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 7: 训练期 2020-10-28 00:00:00 - 2021-05-15 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.2273
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.887]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 7 成功，方向=1, 相关性=0.2273
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 8: 训练期 2020-12-17 00:00:00 - 2021-07-04 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1673
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.902]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 8 成功，方向=1, 相关性=0.1673
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 9: 训练期 2021-02-05 00:00:00 - 2021-08-23 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0899
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 9 成功，方向=1, 相关性=0.0899
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 10: 训练期 2021-03-27 00:00:00 - 2021-10-12 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0089
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.902]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 10 成功，方向=1, 相关性=0.0089
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 11: 训练期 2021-05-16 00:00:00 - 2021-12-01 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0200
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.881]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 11 成功，方向=1, 相关性=0.0200
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 12: 训练期 2021-07-05 00:00:00 - 2022-01-20 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0134
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.863]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 12 成功，方向=-1, 相关性=-0.0134
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 13: 训练期 2021-08-24 00:00:00 - 2022-03-11 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0145
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.859, 0.897]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 13 成功，方向=1, 相关性=0.0145
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 14: 训练期 2021-10-13 00:00:00 - 2022-04-30 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0215
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.878, 0.897]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 14 成功，方向=1, 相关性=0.0215
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 15: 训练期 2021-12-02 00:00:00 - 2022-06-19 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0165
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.905]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 15 成功，方向=-1, 相关性=-0.0165
2025-07-04 13:05 | INFO     | WFAValidator.run_wfa_validation: 滚动窗口验证完成: 总窗口=15, 成功窗口=15
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 计算整体绩效指标
2025-07-04 13:05 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 750, 年化因子: 252
2025-07-04 13:05 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.125
2025-07-04 13:05 | INFO     | WFAValidator.run_wfa_validation: WFA验证完成: 耗时=0.18秒, PnL长度=750, 夏普比率=-0.125
2025-07-04 13:05 | INFO     | WFAValidator.run_wfa_validation: 开始WFA验证: 训练窗口=200, 测试窗口=50
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 执行数据预处理和对齐
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 数据对齐完成，有效数据点: 999
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 开始滚动窗口验证
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 1: 训练期 2020-01-02 00:00:00 - 2020-07-19 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0555
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 1 成功，方向=-1, 相关性=-0.0555
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 2: 训练期 2020-02-21 00:00:00 - 2020-09-07 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0636
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.887, 0.905]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 2 成功，方向=-1, 相关性=-0.0636
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 3: 训练期 2020-04-11 00:00:00 - 2020-10-27 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0171
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.881, 0.874]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 3 成功，方向=-1, 相关性=-0.0171
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 4: 训练期 2020-05-31 00:00:00 - 2020-12-16 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0069
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.897, 0.902]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 4 成功，方向=-1, 相关性=-0.0069
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 5: 训练期 2020-07-20 00:00:00 - 2021-02-04 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1145
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.881]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 5 成功，方向=1, 相关性=0.1145
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 6: 训练期 2020-09-08 00:00:00 - 2021-03-26 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1924
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.905]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 6 成功，方向=1, 相关性=0.1924
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 7: 训练期 2020-10-28 00:00:00 - 2021-05-15 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.2273
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.887]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 7 成功，方向=1, 相关性=0.2273
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 8: 训练期 2020-12-17 00:00:00 - 2021-07-04 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1673
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.902]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 8 成功，方向=1, 相关性=0.1673
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 9: 训练期 2021-02-05 00:00:00 - 2021-08-23 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0899
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 9 成功，方向=1, 相关性=0.0899
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 10: 训练期 2021-03-27 00:00:00 - 2021-10-12 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0089
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.902]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 10 成功，方向=1, 相关性=0.0089
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 11: 训练期 2021-05-16 00:00:00 - 2021-12-01 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0200
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.881]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 11 成功，方向=1, 相关性=0.0200
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 12: 训练期 2021-07-05 00:00:00 - 2022-01-20 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0134
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.863]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 12 成功，方向=-1, 相关性=-0.0134
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 13: 训练期 2021-08-24 00:00:00 - 2022-03-11 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0145
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.859, 0.897]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 13 成功，方向=1, 相关性=0.0145
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 14: 训练期 2021-10-13 00:00:00 - 2022-04-30 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0215
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.878, 0.897]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 14 成功，方向=1, 相关性=0.0215
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 15: 训练期 2021-12-02 00:00:00 - 2022-06-19 00:00:00
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:05 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0165
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:05 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.905]
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 15 成功，方向=-1, 相关性=-0.0165
2025-07-04 13:05 | INFO     | WFAValidator.run_wfa_validation: 滚动窗口验证完成: 总窗口=15, 成功窗口=15
2025-07-04 13:05 | DEBUG    | WFAValidator.run_wfa_validation: 计算整体绩效指标
2025-07-04 13:05 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 750, 年化因子: 252
2025-07-04 13:05 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.125
2025-07-04 13:05 | INFO     | WFAValidator.run_wfa_validation: WFA验证完成: 耗时=0.20秒, PnL长度=750, 夏普比率=-0.125
2025-07-04 13:35 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:35 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 13:35 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:35 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:35 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.2047
2025-07-04 13:35 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.2551
2025-07-04 13:35 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0289, CVaR(95%): -0.0360
2025-07-04 13:35 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.759, 最大回撤: 0.255
2025-07-04 13:36 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:36 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 13:36 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 50, 年化因子: 252
2025-07-04 13:36 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 50
2025-07-04 13:36 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.3442
2025-07-04 13:36 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0100
2025-07-04 13:36 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0100, CVaR(95%): -0.0100
2025-07-04 13:36 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 8.145, 最大回撤: 0.010
2025-07-04 13:36 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 50, 年化因子: 52
2025-07-04 13:36 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 50
2025-07-04 13:36 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.3442
2025-07-04 13:36 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0100
2025-07-04 13:36 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0100, CVaR(95%): -0.0100
2025-07-04 13:36 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 3.700, 最大回撤: 0.010
2025-07-04 13:36 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 50, 年化因子: 12
2025-07-04 13:36 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 50
2025-07-04 13:36 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.3442
2025-07-04 13:36 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0100
2025-07-04 13:36 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0100, CVaR(95%): -0.0100
2025-07-04 13:36 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 1.777, 最大回撤: 0.010
2025-07-04 13:37 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:37 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 10, 年化因子: 252
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 10
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.0000
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0000
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): 0.0000, CVaR(95%): 0.0000
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.000, 最大回撤: 0.000
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 1, 年化因子: 252
2025-07-04 13:37 | WARNING  | PerformanceCalculator.calculate_basic_metrics: PnL序列数据点过少: 1，至少需要2个数据点
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 5, 年化因子: 252
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 3
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.0611
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0000
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): 0.0110, CVaR(95%): 0.0100
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 31.749, 最大回撤: 0.000
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 开始计算增强绩效指标，净值序列长度: 6, 年化因子: 252
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 收益率序列计算完成，长度: 5
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 核心指标计算完成，CAGR: 275.4428, 夏普比率: 8.4791
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 开始计算逐年表现
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 年度 2020 收益率: 0.0800
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 逐年表现计算完成，覆盖年份: 1年
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 增强绩效指标计算完成，CAGR: 275.4428, 年化夏普: 8.4791
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 开始计算增强绩效指标，净值序列长度: 6, 年化因子: 52
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 收益率序列计算完成，长度: 5
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 核心指标计算完成，CAGR: 275.4428, 夏普比率: 3.8517
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 开始计算逐年表现
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 年度 2020 收益率: 0.0800
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 逐年表现计算完成，覆盖年份: 1年
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 增强绩效指标计算完成，CAGR: 275.4428, 年化夏普: 3.8517
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 5, 年化因子: 252
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 5
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 0.9563
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.2800
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.1800, CVaR(95%): -0.2000
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.000, 最大回撤: 0.280
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 5, 年化因子: 252
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 5
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.0715
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0100
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0060, CVaR(95%): -0.0100
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 14.654, 最大回撤: 0.010
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 5, 年化因子: 252
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 5
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.0715
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0100
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0060, CVaR(95%): -0.0100
2025-07-04 13:37 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 14.571, 最大回撤: 0.010
2025-07-04 13:37 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:37 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 13:37 | ERROR    | PerformanceCalculator._validate_input_series: PnL序列为None
2025-07-04 13:37 | WARNING  | PerformanceCalculator._validate_input_series: PnL序列为空
2025-07-04 13:37 | ERROR    | PerformanceCalculator._validate_input_series: PnL序列全为NaN
2025-07-04 13:37 | WARNING  | PerformanceCalculator._validate_input_series: PnL序列包含无穷值
2025-07-04 13:38 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:38 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 100
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 100
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 3
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 0
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 50
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 40
2025-07-04 13:38 | DEBUG    | WFAValidator.validate_wfa_inputs: 输入验证通过: 因子数据1000点, 价格数据1000点
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.2047
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.2551
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0289, CVaR(95%): -0.0360
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.759, 最大回撤: 0.255
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.2047
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.2551
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0289, CVaR(95%): -0.0360
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.759, 最大回撤: 0.255
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 50, 年化因子: 252
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 50
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.3442
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0100
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0100, CVaR(95%): -0.0100
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 8.145, 最大回撤: 0.010
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 50, 年化因子: 52
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 50
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.3442
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0100
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0100, CVaR(95%): -0.0100
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 3.700, 最大回撤: 0.010
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 50, 年化因子: 12
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 50
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.3442
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0100
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0100, CVaR(95%): -0.0100
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 1.777, 最大回撤: 0.010
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 10, 年化因子: 252
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 10
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.0000
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0000
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): 0.0000, CVaR(95%): 0.0000
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.000, 最大回撤: 0.000
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 1, 年化因子: 252
2025-07-04 13:38 | WARNING  | PerformanceCalculator.calculate_basic_metrics: PnL序列数据点过少: 1，至少需要2个数据点
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 5, 年化因子: 252
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 3
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.0611
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0000
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): 0.0110, CVaR(95%): 0.0100
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 31.749, 最大回撤: 0.000
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 开始计算增强绩效指标，净值序列长度: 6, 年化因子: 252
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 收益率序列计算完成，长度: 5
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 核心指标计算完成，CAGR: 275.4428, 夏普比率: 8.4791
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 开始计算逐年表现
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 年度 2020 收益率: 0.0800
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 逐年表现计算完成，覆盖年份: 1年
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 增强绩效指标计算完成，CAGR: 275.4428, 年化夏普: 8.4791
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 开始计算增强绩效指标，净值序列长度: 6, 年化因子: 52
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 收益率序列计算完成，长度: 5
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 核心指标计算完成，CAGR: 275.4428, 夏普比率: 3.8517
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 开始计算逐年表现
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 年度 2020 收益率: 0.0800
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 逐年表现计算完成，覆盖年份: 1年
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 增强绩效指标计算完成，CAGR: 275.4428, 年化夏普: 3.8517
2025-07-04 13:38 | ERROR    | PerformanceCalculator._validate_input_series: PnL序列为None
2025-07-04 13:38 | WARNING  | PerformanceCalculator._validate_input_series: PnL序列为空
2025-07-04 13:38 | ERROR    | PerformanceCalculator._validate_input_series: PnL序列全为NaN
2025-07-04 13:38 | WARNING  | PerformanceCalculator._validate_input_series: PnL序列包含无穷值
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 5, 年化因子: 252
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 5
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 0.9563
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.2800
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.1800, CVaR(95%): -0.2000
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.000, 最大回撤: 0.280
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 5, 年化因子: 252
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 5
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.0715
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0100
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0060, CVaR(95%): -0.0100
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 14.654, 最大回撤: 0.010
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 5, 年化因子: 252
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 5
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.0715
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0100
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0060, CVaR(95%): -0.0100
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 14.571, 最大回撤: 0.010
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.1080
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 1.0000
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -1.0000
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | WARNING  | WFAValidator.calculate_spearman_correlation: 有效数据点不足: 2
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | WARNING  | WFAValidator.calculate_spearman_correlation: 有效数据点不足: 3
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=5.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.964, 0.964]
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=5.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.964, 0.964]
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=5.0, direction=-1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.964, 0.964]
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=1.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.380, 0.380]
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=10.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.999, 0.999]
2025-07-04 13:38 | DEBUG    | WFAValidator.check_wfa_criteria: 开始检查WFA通过标准
2025-07-04 13:38 | INFO     | WFAValidator.check_wfa_criteria: WFA验证未通过，失败原因数: 3
2025-07-04 13:38 | DEBUG    | WFAValidator.check_wfa_criteria: 开始检查WFA通过标准
2025-07-04 13:38 | INFO     | WFAValidator.check_wfa_criteria: WFA验证通过所有标准
2025-07-04 13:38 | INFO     | WFAValidator.run_wfa_validation: 开始WFA验证: 训练窗口=200, 测试窗口=50
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 执行数据预处理和对齐
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 数据对齐完成，有效数据点: 999
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 开始滚动窗口验证
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 1: 训练期 2020-01-02 00:00:00 - 2020-07-19 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0555
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 1 成功，方向=-1, 相关性=-0.0555
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 2: 训练期 2020-02-21 00:00:00 - 2020-09-07 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0636
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.887, 0.905]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 2 成功，方向=-1, 相关性=-0.0636
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 3: 训练期 2020-04-11 00:00:00 - 2020-10-27 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0171
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.881, 0.874]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 3 成功，方向=-1, 相关性=-0.0171
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 4: 训练期 2020-05-31 00:00:00 - 2020-12-16 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0069
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.897, 0.902]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 4 成功，方向=-1, 相关性=-0.0069
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 5: 训练期 2020-07-20 00:00:00 - 2021-02-04 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1145
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.881]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 5 成功，方向=1, 相关性=0.1145
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 6: 训练期 2020-09-08 00:00:00 - 2021-03-26 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1924
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.905]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 6 成功，方向=1, 相关性=0.1924
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 7: 训练期 2020-10-28 00:00:00 - 2021-05-15 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.2273
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.887]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 7 成功，方向=1, 相关性=0.2273
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 8: 训练期 2020-12-17 00:00:00 - 2021-07-04 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1673
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.902]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 8 成功，方向=1, 相关性=0.1673
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 9: 训练期 2021-02-05 00:00:00 - 2021-08-23 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0899
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 9 成功，方向=1, 相关性=0.0899
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 10: 训练期 2021-03-27 00:00:00 - 2021-10-12 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0089
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.902]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 10 成功，方向=1, 相关性=0.0089
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 11: 训练期 2021-05-16 00:00:00 - 2021-12-01 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0200
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.881]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 11 成功，方向=1, 相关性=0.0200
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 12: 训练期 2021-07-05 00:00:00 - 2022-01-20 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0134
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.863]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 12 成功，方向=-1, 相关性=-0.0134
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 13: 训练期 2021-08-24 00:00:00 - 2022-03-11 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0145
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.859, 0.897]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 13 成功，方向=1, 相关性=0.0145
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 14: 训练期 2021-10-13 00:00:00 - 2022-04-30 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0215
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.878, 0.897]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 14 成功，方向=1, 相关性=0.0215
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 15: 训练期 2021-12-02 00:00:00 - 2022-06-19 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0165
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.905]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 15 成功，方向=-1, 相关性=-0.0165
2025-07-04 13:38 | INFO     | WFAValidator.run_wfa_validation: 滚动窗口验证完成: 总窗口=15, 成功窗口=15
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 计算整体绩效指标
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 750, 年化因子: 252
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 750
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 0.8781
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.3386
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0210, CVaR(95%): -0.0292
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.125, 最大回撤: 0.339
2025-07-04 13:38 | INFO     | WFAValidator.run_wfa_validation: WFA验证完成: 耗时=0.20秒, PnL长度=750, 夏普比率=-0.125
2025-07-04 13:38 | INFO     | WFAValidator.run_wfa_validation: 开始WFA验证: 训练窗口=200, 测试窗口=50
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 执行数据预处理和对齐
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 数据对齐完成，有效数据点: 999
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 开始滚动窗口验证
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 1: 训练期 2020-01-02 00:00:00 - 2020-07-19 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0555
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 1 成功，方向=-1, 相关性=-0.0555
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 2: 训练期 2020-02-21 00:00:00 - 2020-09-07 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0636
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.887, 0.905]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 2 成功，方向=-1, 相关性=-0.0636
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 3: 训练期 2020-04-11 00:00:00 - 2020-10-27 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0171
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.881, 0.874]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 3 成功，方向=-1, 相关性=-0.0171
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 4: 训练期 2020-05-31 00:00:00 - 2020-12-16 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0069
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.897, 0.902]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 4 成功，方向=-1, 相关性=-0.0069
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 5: 训练期 2020-07-20 00:00:00 - 2021-02-04 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1145
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.881]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 5 成功，方向=1, 相关性=0.1145
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 6: 训练期 2020-09-08 00:00:00 - 2021-03-26 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1924
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.905]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 6 成功，方向=1, 相关性=0.1924
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 7: 训练期 2020-10-28 00:00:00 - 2021-05-15 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.2273
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.887]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 7 成功，方向=1, 相关性=0.2273
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 8: 训练期 2020-12-17 00:00:00 - 2021-07-04 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1673
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.902]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 8 成功，方向=1, 相关性=0.1673
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 9: 训练期 2021-02-05 00:00:00 - 2021-08-23 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0899
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 9 成功，方向=1, 相关性=0.0899
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 10: 训练期 2021-03-27 00:00:00 - 2021-10-12 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0089
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.902]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 10 成功，方向=1, 相关性=0.0089
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 11: 训练期 2021-05-16 00:00:00 - 2021-12-01 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0200
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.881]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 11 成功，方向=1, 相关性=0.0200
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 12: 训练期 2021-07-05 00:00:00 - 2022-01-20 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0134
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.863]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 12 成功，方向=-1, 相关性=-0.0134
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 13: 训练期 2021-08-24 00:00:00 - 2022-03-11 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0145
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.859, 0.897]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 13 成功，方向=1, 相关性=0.0145
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 14: 训练期 2021-10-13 00:00:00 - 2022-04-30 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0215
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.878, 0.897]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 14 成功，方向=1, 相关性=0.0215
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 15: 训练期 2021-12-02 00:00:00 - 2022-06-19 00:00:00
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:38 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0165
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:38 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.905]
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 15 成功，方向=-1, 相关性=-0.0165
2025-07-04 13:38 | INFO     | WFAValidator.run_wfa_validation: 滚动窗口验证完成: 总窗口=15, 成功窗口=15
2025-07-04 13:38 | DEBUG    | WFAValidator.run_wfa_validation: 计算整体绩效指标
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 750, 年化因子: 252
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 750
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 0.8781
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.3386
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0210, CVaR(95%): -0.0292
2025-07-04 13:38 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.125, 最大回撤: 0.339
2025-07-04 13:38 | INFO     | WFAValidator.run_wfa_validation: WFA验证完成: 耗时=0.29秒, PnL长度=750, 夏普比率=-0.125
2025-07-04 13:39 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:39 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 100
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 100
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 3
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 0
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 50
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 40
2025-07-04 13:39 | DEBUG    | WFAValidator.validate_wfa_inputs: 输入验证通过: 因子数据1000点, 价格数据1000点
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.2047
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.2551
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0289, CVaR(95%): -0.0360
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.759, 最大回撤: 0.255
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.2047
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.2551
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0289, CVaR(95%): -0.0360
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.759, 最大回撤: 0.255
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 50, 年化因子: 252
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 50
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.3442
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0100
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0100, CVaR(95%): -0.0100
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 8.145, 最大回撤: 0.010
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 50, 年化因子: 52
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 50
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.3442
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0100
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0100, CVaR(95%): -0.0100
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 3.700, 最大回撤: 0.010
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 50, 年化因子: 12
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 50
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.3442
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0100
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0100, CVaR(95%): -0.0100
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 1.777, 最大回撤: 0.010
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 10, 年化因子: 252
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 10
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.0000
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0000
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): 0.0000, CVaR(95%): 0.0000
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.000, 最大回撤: 0.000
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 1, 年化因子: 252
2025-07-04 13:39 | WARNING  | PerformanceCalculator.calculate_basic_metrics: PnL序列数据点过少: 1，至少需要2个数据点
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 5, 年化因子: 252
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 3
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.0611
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0000
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): 0.0110, CVaR(95%): 0.0100
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 31.749, 最大回撤: 0.000
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 开始计算增强绩效指标，净值序列长度: 6, 年化因子: 252
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 收益率序列计算完成，长度: 5
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 核心指标计算完成，CAGR: 275.4428, 夏普比率: 8.4791
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 开始计算逐年表现
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 年度 2020 收益率: 0.0800
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 逐年表现计算完成，覆盖年份: 1年
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 增强绩效指标计算完成，CAGR: 275.4428, 年化夏普: 8.4791
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 开始计算增强绩效指标，净值序列长度: 6, 年化因子: 52
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 收益率序列计算完成，长度: 5
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 核心指标计算完成，CAGR: 275.4428, 夏普比率: 3.8517
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 开始计算逐年表现
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 年度 2020 收益率: 0.0800
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 逐年表现计算完成，覆盖年份: 1年
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_enhanced_metrics: 增强绩效指标计算完成，CAGR: 275.4428, 年化夏普: 3.8517
2025-07-04 13:39 | ERROR    | PerformanceCalculator._validate_input_series: PnL序列为None
2025-07-04 13:39 | WARNING  | PerformanceCalculator._validate_input_series: PnL序列为空
2025-07-04 13:39 | ERROR    | PerformanceCalculator._validate_input_series: PnL序列全为NaN
2025-07-04 13:39 | WARNING  | PerformanceCalculator._validate_input_series: PnL序列包含无穷值
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 5, 年化因子: 252
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 5
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 0.9563
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.2800
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.1800, CVaR(95%): -0.2000
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.000, 最大回撤: 0.280
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 5, 年化因子: 252
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 5
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.0715
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0100
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0060, CVaR(95%): -0.0100
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 14.654, 最大回撤: 0.010
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 5, 年化因子: 252
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 5
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 1.0715
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.0100
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0060, CVaR(95%): -0.0100
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 14.571, 最大回撤: 0.010
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.1080
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 1.0000
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -1.0000
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | WARNING  | WFAValidator.calculate_spearman_correlation: 有效数据点不足: 2
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | WARNING  | WFAValidator.calculate_spearman_correlation: 有效数据点不足: 3
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=5.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.964, 0.964]
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=5.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.964, 0.964]
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=5.0, direction=-1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.964, 0.964]
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=1.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.380, 0.380]
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=10.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.999, 0.999]
2025-07-04 13:39 | DEBUG    | WFAValidator.check_wfa_criteria: 开始检查WFA通过标准
2025-07-04 13:39 | INFO     | WFAValidator.check_wfa_criteria: WFA验证未通过，失败原因数: 3
2025-07-04 13:39 | DEBUG    | WFAValidator.check_wfa_criteria: 开始检查WFA通过标准
2025-07-04 13:39 | INFO     | WFAValidator.check_wfa_criteria: WFA验证通过所有标准
2025-07-04 13:39 | INFO     | WFAValidator.run_wfa_validation: 开始WFA验证: 训练窗口=200, 测试窗口=50
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 执行数据预处理和对齐
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 数据对齐完成，有效数据点: 999
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 开始滚动窗口验证
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 1: 训练期 2020-01-02 00:00:00 - 2020-07-19 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0555
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 1 成功，方向=-1, 相关性=-0.0555
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 2: 训练期 2020-02-21 00:00:00 - 2020-09-07 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0636
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.887, 0.905]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 2 成功，方向=-1, 相关性=-0.0636
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 3: 训练期 2020-04-11 00:00:00 - 2020-10-27 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0171
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.881, 0.874]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 3 成功，方向=-1, 相关性=-0.0171
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 4: 训练期 2020-05-31 00:00:00 - 2020-12-16 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0069
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.897, 0.902]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 4 成功，方向=-1, 相关性=-0.0069
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 5: 训练期 2020-07-20 00:00:00 - 2021-02-04 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1145
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.881]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 5 成功，方向=1, 相关性=0.1145
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 6: 训练期 2020-09-08 00:00:00 - 2021-03-26 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1924
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.905]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 6 成功，方向=1, 相关性=0.1924
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 7: 训练期 2020-10-28 00:00:00 - 2021-05-15 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.2273
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.887]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 7 成功，方向=1, 相关性=0.2273
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 8: 训练期 2020-12-17 00:00:00 - 2021-07-04 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1673
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.902]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 8 成功，方向=1, 相关性=0.1673
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 9: 训练期 2021-02-05 00:00:00 - 2021-08-23 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0899
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 9 成功，方向=1, 相关性=0.0899
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 10: 训练期 2021-03-27 00:00:00 - 2021-10-12 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0089
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.902]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 10 成功，方向=1, 相关性=0.0089
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 11: 训练期 2021-05-16 00:00:00 - 2021-12-01 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0200
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.881]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 11 成功，方向=1, 相关性=0.0200
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 12: 训练期 2021-07-05 00:00:00 - 2022-01-20 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0134
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.863]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 12 成功，方向=-1, 相关性=-0.0134
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 13: 训练期 2021-08-24 00:00:00 - 2022-03-11 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0145
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.859, 0.897]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 13 成功，方向=1, 相关性=0.0145
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 14: 训练期 2021-10-13 00:00:00 - 2022-04-30 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0215
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.878, 0.897]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 14 成功，方向=1, 相关性=0.0215
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 15: 训练期 2021-12-02 00:00:00 - 2022-06-19 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0165
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.905]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 15 成功，方向=-1, 相关性=-0.0165
2025-07-04 13:39 | INFO     | WFAValidator.run_wfa_validation: 滚动窗口验证完成: 总窗口=15, 成功窗口=15
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 计算整体绩效指标
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 750, 年化因子: 252
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 750
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 0.8781
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.3386
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0210, CVaR(95%): -0.0292
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.125, 最大回撤: 0.339
2025-07-04 13:39 | INFO     | WFAValidator.run_wfa_validation: WFA验证完成: 耗时=0.21秒, PnL长度=750, 夏普比率=-0.125
2025-07-04 13:39 | INFO     | WFAValidator.run_wfa_validation: 开始WFA验证: 训练窗口=200, 测试窗口=50
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 执行数据预处理和对齐
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 数据对齐完成，有效数据点: 999
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 开始滚动窗口验证
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 1: 训练期 2020-01-02 00:00:00 - 2020-07-19 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0555
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 1 成功，方向=-1, 相关性=-0.0555
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 2: 训练期 2020-02-21 00:00:00 - 2020-09-07 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0636
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.887, 0.905]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 2 成功，方向=-1, 相关性=-0.0636
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 3: 训练期 2020-04-11 00:00:00 - 2020-10-27 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0171
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.881, 0.874]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 3 成功，方向=-1, 相关性=-0.0171
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 4: 训练期 2020-05-31 00:00:00 - 2020-12-16 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0069
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.897, 0.902]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 4 成功，方向=-1, 相关性=-0.0069
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 5: 训练期 2020-07-20 00:00:00 - 2021-02-04 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1145
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.881]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 5 成功，方向=1, 相关性=0.1145
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 6: 训练期 2020-09-08 00:00:00 - 2021-03-26 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1924
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.905]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 6 成功，方向=1, 相关性=0.1924
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 7: 训练期 2020-10-28 00:00:00 - 2021-05-15 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.2273
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.887]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 7 成功，方向=1, 相关性=0.2273
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 8: 训练期 2020-12-17 00:00:00 - 2021-07-04 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.1673
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.900, 0.902]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 8 成功，方向=1, 相关性=0.1673
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 9: 训练期 2021-02-05 00:00:00 - 2021-08-23 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0899
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.902]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 9 成功，方向=1, 相关性=0.0899
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 10: 训练期 2021-03-27 00:00:00 - 2021-10-12 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0089
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.902, 0.902]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 10 成功，方向=1, 相关性=0.0089
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 11: 训练期 2021-05-16 00:00:00 - 2021-12-01 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0200
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.881]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 11 成功，方向=1, 相关性=0.0200
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 12: 训练期 2021-07-05 00:00:00 - 2022-01-20 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0134
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.905, 0.863]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 12 成功，方向=-1, 相关性=-0.0134
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 13: 训练期 2021-08-24 00:00:00 - 2022-03-11 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0145
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.859, 0.897]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 13 成功，方向=1, 相关性=0.0145
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 14: 训练期 2021-10-13 00:00:00 - 2022-04-30 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: 0.0215
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.878, 0.897]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 14 成功，方向=1, 相关性=0.0215
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 处理窗口 15: 训练期 2021-12-02 00:00:00 - 2022-06-19 00:00:00
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: 开始学习ECDF，训练样本数: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_ecdf_mapping: ECDF构建完成，分位点数量: 200
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: 开始计算Spearman相关性
2025-07-04 13:39 | DEBUG    | WFAValidator.calculate_spearman_correlation: Spearman相关性: -0.0165
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 执行S型仓位映射，tanh_k=3.0, direction=-1
2025-07-04 13:39 | DEBUG    | WFAValidator.apply_tanh_position_mapping: 仓位映射完成，范围: [-0.891, 0.905]
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 窗口 15 成功，方向=-1, 相关性=-0.0165
2025-07-04 13:39 | INFO     | WFAValidator.run_wfa_validation: 滚动窗口验证完成: 总窗口=15, 成功窗口=15
2025-07-04 13:39 | DEBUG    | WFAValidator.run_wfa_validation: 计算整体绩效指标
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 750, 年化因子: 252
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 750
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 累积收益计算完成，最终累积收益: 0.8781
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 最大回撤计算完成: 0.3386
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0210, CVaR(95%): -0.0292
2025-07-04 13:39 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.125, 最大回撤: 0.339
2025-07-04 13:39 | INFO     | WFAValidator.run_wfa_validation: WFA验证完成: 耗时=0.60秒, PnL长度=750, 夏普比率=-0.125
2025-07-04 13:46 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:46 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 13:46 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:46 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:47 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:47 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 13:47 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:47 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:47 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0289, CVaR(95%): -0.0360
2025-07-04 13:47 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.760, 最大回撤: 0.255
2025-07-04 13:48 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:48 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:48 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0299, CVaR(95%): -0.0370
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.127, 最大回撤: 0.315
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0299, CVaR(95%): -0.0370
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.062, 最大回撤: 0.315
2025-07-04 13:48 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0299, CVaR(95%): -0.0370
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.062, 最大回撤: 0.315
2025-07-04 13:48 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:48 | WARNING  | PerformanceCalculator._validate_input_series: PnL序列为空
2025-07-04 13:48 | ERROR    | PerformanceCalculator._validate_input_series: PnL序列全为NaN
2025-07-04 13:48 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:48 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0299, CVaR(95%): -0.0370
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.062, 最大回撤: 0.315
2025-07-04 13:48 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0299, CVaR(95%): -0.0370
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.062, 最大回撤: 0.315
2025-07-04 13:48 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 2520, 年化因子: 252
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 2520
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0313, CVaR(95%): -0.0397
2025-07-04 13:48 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.694, 最大回撤: 0.510
2025-07-04 13:48 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:48 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:49 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:49 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 13:49 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:49 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:49 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0289, CVaR(95%): -0.0360
2025-07-04 13:49 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.760, 最大回撤: 0.255
2025-07-04 13:49 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:49 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:49 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0289, CVaR(95%): -0.0360
2025-07-04 13:49 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.760, 最大回撤: 0.255
2025-07-04 13:50 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:50 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:50 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0299, CVaR(95%): -0.0370
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.127, 最大回撤: 0.315
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0299, CVaR(95%): -0.0370
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.062, 最大回撤: 0.315
2025-07-04 13:50 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0299, CVaR(95%): -0.0370
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.062, 最大回撤: 0.315
2025-07-04 13:50 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:50 | WARNING  | PerformanceCalculator._validate_input_series: PnL序列为空
2025-07-04 13:50 | ERROR    | PerformanceCalculator._validate_input_series: PnL序列全为NaN
2025-07-04 13:50 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:50 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0299, CVaR(95%): -0.0370
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.062, 最大回撤: 0.315
2025-07-04 13:50 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 252, 年化因子: 252
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 252
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0299, CVaR(95%): -0.0370
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: -0.062, 最大回撤: 0.315
2025-07-04 13:50 | INFO     | PerformanceCalculator.__init__: 绩效计算器初始化完成
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 开始计算基础绩效指标，PnL序列长度: 2520, 年化因子: 252
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 数据清洗完成，有效数据点: 2520
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 风险指标计算完成，VaR(95%): -0.0313, CVaR(95%): -0.0397
2025-07-04 13:50 | DEBUG    | PerformanceCalculator.calculate_basic_metrics: 绩效指标计算完成，夏普比率: 0.694, 最大回撤: 0.510
2025-07-04 14:20 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 14:23 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 14:38 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 14:38 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 14:39 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 14:43 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 14:43 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 14:44 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 14:44 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 14:44 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-04 14:44 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=10
2025-07-04 14:44 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-04 14:44 | INFO     | FactorQueryManager.query_l2_passed_factors: 开始查询L2阶段通过的因子
2025-07-04 14:44 | INFO     | FactorQueryManager.query_l2_passed_factors: 查询完成: 找到3个因子，耗时0.00秒
2025-07-04 14:44 | DEBUG    | FactorQueryManager.query_l2_passed_factors: 品种分布: {'510050.SH': 1, '510300.SH': 1, '159915.SZ': 1}
2025-07-04 14:44 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 14:44 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 14:44 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-04 14:44 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=10
2025-07-04 14:44 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=2, chunk_size=3
2025-07-04 14:44 | INFO     | BatchProcessor.progress_callback: 处理进度: 50.0%, 当前因子: test_factor
2025-07-04 14:44 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=2, chunk_size=3
2025-07-04 14:44 | WARNING  | BatchProcessor.process_factors_batch: 因子列表为空
2025-07-04 14:44 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=2, chunk_size=3
2025-07-04 14:44 | INFO     | BatchProcessor.process_factors_batch: 开始批量处理: 5个因子
2025-07-04 14:44 | DEBUG    | BatchProcessor.process_factors_batch: 块处理完成: 3个因子, 耗时0.00秒
2025-07-04 14:44 | DEBUG    | BatchProcessor.process_factors_batch: 块处理完成: 2个因子, 耗时0.00秒
2025-07-04 14:44 | INFO     | BatchProcessor.process_factors_batch: 批量处理完成: 成功5/5, 总耗时0.00秒
2025-07-04 14:44 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=2, chunk_size=3
2025-07-04 14:44 | INFO     | BatchProcessor.process_factors_batch: 开始批量处理: 3个因子
2025-07-04 14:44 | ERROR    | BatchProcessor.process_factors_batch: 处理因子失败: unknown, 错误: Mock processing error
2025-07-04 14:44 | DEBUG    | BatchProcessor.process_factors_batch: 块处理完成: 3个因子, 耗时0.00秒
2025-07-04 14:44 | INFO     | BatchProcessor.process_factors_batch: 批量处理完成: 成功2/3, 总耗时0.00秒
2025-07-04 14:44 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=2, chunk_size=3
2025-07-04 14:44 | INFO     | BatchProcessor.process_factors_batch: 开始批量处理: 5个因子
2025-07-04 14:44 | DEBUG    | BatchProcessor.process_factors_batch: 块处理完成: 3个因子, 耗时0.00秒
2025-07-04 14:44 | DEBUG    | BatchProcessor.process_factors_batch: 块处理完成: 2个因子, 耗时0.00秒
2025-07-04 14:44 | INFO     | BatchProcessor.process_factors_batch: 批量处理完成: 成功5/5, 总耗时0.00秒
2025-07-04 14:45 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 14:46 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 14:46 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 14:46 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 14:46 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-04 14:46 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=10
2025-07-04 14:46 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 14:46 | INFO     | L3WFAValidator.__init__: L3动态稳健性检验器初始化完成
2025-07-04 14:58 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 14:58 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 14:58 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 14:58 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-04 14:58 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=10
2025-07-04 14:58 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 14:58 | INFO     | L3WFAValidator.__init__: L3动态稳健性检验器初始化完成
2025-07-04 14:58 | INFO     | L3WFAValidator.run_validation_pipeline: 开始L3动态稳健性检验流程
2025-07-04 14:58 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤1: 加载配置参数
2025-07-04 14:58 | ERROR    | L3WFAValidator._load_and_validate_config: 缺少必要配置参数: factor_query.source_pipeline_step
2025-07-04 15:04 | WARNING  | QuantStatsReportGenerator.__init__: quantstats库未安装，将跳过HTML报告生成
2025-07-04 15:04 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-04 15:04 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: D:\myquant\XentZ\reports
2025-07-04 15:04 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(8, 6), dpi=100
2025-07-04 15:04 | INFO     | CustomChartGenerator.generate_wfa_analysis_chart: 开始生成WFA分析图表
2025-07-04 15:04 | INFO     | CustomChartGenerator.generate_wfa_analysis_chart: WFA分析图表生成完成: C:\Users\<USER>\AppData\Local\Temp\tmpwbt8kbqm\wfa_analysis.png, 耗时1.95秒
2025-07-04 15:04 | WARNING  | QuantStatsReportGenerator.__init__: quantstats库未安装，将跳过HTML报告生成
2025-07-04 15:04 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-04 15:04 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: D:\myquant\XentZ\reports
2025-07-04 15:04 | WARNING  | QuantStatsReportGenerator.__init__: quantstats库未安装，将跳过HTML报告生成
2025-07-04 15:04 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-04 15:04 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: D:\myquant\XentZ\reports
2025-07-04 15:04 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-04 15:04 | INFO     | CustomChartGenerator.generate_wfa_analysis_chart: 开始生成WFA分析图表
2025-07-04 15:04 | INFO     | CustomChartGenerator.generate_wfa_analysis_chart: WFA分析图表生成完成: test_chart.png, 耗时2.10秒
2025-07-04 15:05 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 15:05 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 15:05 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 15:05 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-04 15:05 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=10
2025-07-04 15:05 | WARNING  | QuantStatsReportGenerator.__init__: quantstats库未安装，将跳过HTML报告生成
2025-07-04 15:05 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-04 15:05 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: D:\myquant\XentZ\reports
2025-07-04 15:05 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 15:05 | INFO     | L3WFAValidator.__init__: L3动态稳健性检验器初始化完成
2025-07-04 18:23 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 18:23 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 18:23 | INFO     | WFAQueryManager.__init__: WFA查询管理器初始化完成
2025-07-04 18:23 | INFO     | PriceDataManager.__init__: 价格数据管理器初始化完成
2025-07-04 18:23 | INFO     | WFADataLoader.__init__: WFA数据加载器初始化完成
2025-07-04 18:23 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 18:23 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 18:23 | INFO     | WFAQueryManager.__init__: WFA查询管理器初始化完成
2025-07-04 18:23 | INFO     | PriceDataManager.__init__: 价格数据管理器初始化完成
2025-07-04 18:23 | INFO     | WFADataLoader.__init__: WFA数据加载器初始化完成
2025-07-04 18:23 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成（FactorZoo集成版）
2025-07-04 18:23 | INFO     | BatchProcessor.__init__: WFA批量处理器初始化完成: max_workers=4, chunk_size=10
2025-07-04 18:32 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 18:32 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 18:32 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-04 18:32 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=10
2025-07-04 18:32 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-04 18:32 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=10
2025-07-04 18:32 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 18:32 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 18:32 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 18:32 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-04 18:32 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=10
2025-07-04 18:32 | WARNING  | QuantStatsReportGenerator.__init__: quantstats库未安装，将跳过HTML报告生成
2025-07-04 18:32 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-04 18:32 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: D:\myquant\XentZ\reports
2025-07-04 18:32 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 18:32 | INFO     | L3WFAValidator.__init__: L3动态稳健性检验器初始化完成
2025-07-04 18:35 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 18:35 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 18:35 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 18:35 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-04 18:35 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=10
2025-07-04 18:35 | WARNING  | QuantStatsReportGenerator.__init__: quantstats库未安装，将跳过HTML报告生成
2025-07-04 18:35 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-04 18:35 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: d:\myquant\XentZ\reports
2025-07-04 18:35 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 18:35 | INFO     | L3WFAValidator.__init__: L3动态稳健性检验器初始化完成
2025-07-04 18:35 | INFO     | L3WFAValidator.run_validation_pipeline: 开始L3动态稳健性检验流程
2025-07-04 18:35 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤1: 加载配置参数
2025-07-04 18:35 | ERROR    | L3WFAValidator._load_and_validate_config: 缺少必要配置参数: factor_query.source_pipeline_step
2025-07-04 18:43 | INFO     | QuantStatsReportGenerator.__init__: quantstats_lumi库导入成功
2025-07-04 18:43 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-04 18:43 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: D:\myquant\XentZ\reports
2025-07-04 18:43 | INFO     | QuantStatsReportGenerator.__init__: quantstats_lumi库导入成功
2025-07-04 18:43 | INFO     | QuantStatsReportGenerator.__init__: quantstats_lumi库导入成功
2025-07-04 18:43 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-04 18:43 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: D:\myquant\XentZ\reports
2025-07-04 18:43 | INFO     | QuantStatsReportGenerator.__init__: quantstats_lumi库导入成功
2025-07-04 18:44 | INFO     | QuantStatsReportGenerator.__init__: quantstats_lumi库导入成功
2025-07-04 18:44 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-04 18:44 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: D:\myquant\XentZ\reports
2025-07-04 18:44 | INFO     | QuantStatsReportGenerator.__init__: quantstats_lumi库导入成功
2025-07-04 18:44 | INFO     | QuantStatsReportGenerator.generate_html_report: 开始生成quantstats_lumi HTML报告: quantstats_lumi Test Report
2025-07-04 18:44 | INFO     | QuantStatsReportGenerator.generate_html_report: HTML报告生成完成: test_quantstats_lumi_report.html, 耗时3.20秒
2025-07-04 18:46 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 18:46 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 18:46 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 18:46 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-04 18:46 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=10
2025-07-04 18:46 | INFO     | QuantStatsReportGenerator.__init__: quantstats_lumi库导入成功
2025-07-04 18:46 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-04 18:46 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: D:\myquant\XentZ\reports
2025-07-04 18:46 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 18:46 | INFO     | L3WFAValidator.__init__: L3动态稳健性检验器初始化完成
2025-07-04 18:47 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 18:47 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 18:47 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 18:47 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-04 18:47 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=10
2025-07-04 18:47 | INFO     | QuantStatsReportGenerator.__init__: quantstats_lumi库导入成功
2025-07-04 18:47 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-04 18:47 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: d:\myquant\XentZ\reports
2025-07-04 18:47 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 18:47 | INFO     | L3WFAValidator.__init__: L3动态稳健性检验器初始化完成
2025-07-04 18:47 | INFO     | L3WFAValidator.run_validation_pipeline: 开始L3动态稳健性检验流程
2025-07-04 18:47 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤1: 加载配置参数
2025-07-04 18:47 | ERROR    | L3WFAValidator._load_and_validate_config: 缺少必要配置参数: factor_query.source_pipeline_step
2025-07-04 21:26 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 21:26 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 21:26 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 21:26 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-04 21:26 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=10
2025-07-04 21:26 | INFO     | QuantStatsReportGenerator.__init__: quantstats_lumi库导入成功
2025-07-04 21:26 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-04 21:26 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: d:\myquant\XentZ\reports
2025-07-04 21:26 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 21:26 | INFO     | L3WFAValidator.__init__: L3动态稳健性检验器初始化完成
2025-07-04 21:26 | INFO     | L3WFAValidator.run_validation_pipeline: 开始L3动态稳健性检验流程
2025-07-04 21:26 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤1: 加载配置参数
2025-07-04 21:26 | ERROR    | L3WFAValidator._load_and_validate_config: 缺少必要配置参数: factor_query.source_pipeline_step
2025-07-04 22:04 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 22:04 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 22:04 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 22:04 | INFO     | FactorQueryManager.__init__: 因子查询管理器初始化完成
2025-07-04 22:04 | INFO     | BatchProcessor.__init__: 批量处理器初始化完成: max_workers=4, chunk_size=10
2025-07-04 22:04 | INFO     | QuantStatsReportGenerator.__init__: quantstats_lumi库导入成功
2025-07-04 22:04 | INFO     | CustomChartGenerator.__init__: 自定义图表生成器初始化完成: figsize=(12, 8), dpi=300
2025-07-04 22:04 | INFO     | BatchReportManager.__init__: 批量报告管理器初始化完成: d:\myquant\XentZ\reports
2025-07-04 22:04 | INFO     | WFAValidator.__init__: WFA验证器初始化完成
2025-07-04 22:04 | INFO     | L3WFAValidator.__init__: L3动态稳健性检验器初始化完成
2025-07-04 22:04 | INFO     | L3WFAValidator.run_validation_pipeline: 开始L3动态稳健性检验流程
2025-07-04 22:04 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤1: 加载配置参数
2025-07-04 22:04 | DEBUG    | L3WFAValidator._load_and_validate_config: 配置参数验证通过
2025-07-04 22:04 | INFO     | L3WFAValidator.run_validation_pipeline: 步骤2: 查询L2阶段通过的因子
2025-07-04 22:04 | INFO     | FactorQueryManager.query_l2_passed_factors: 开始查询L2阶段通过的因子
2025-07-04 22:04 | WARNING  | FactorQueryManager.query_l2_passed_factors: 未找到符合条件的因子
2025-07-04 22:33 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 22:33 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 22:33 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:33 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_211730/L0
2025-07-04 22:33 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_211730/L0 耗时113.0ms
2025-07-04 22:33 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_277461/L0
2025-07-04 22:33 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_277461/L0 耗时61.0ms
2025-07-04 22:33 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_366378/L0
2025-07-04 22:33 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_366378/L0 耗时54.2ms
2025-07-04 22:33 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-07-04 22:33 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时28.0ms
2025-07-04 22:33 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:33 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-04 22:33 | INFO     | ResMonitor.end_timer: L1_CORR_SH510050_label_1 took 0.01 seconds (00:00:00)
2025-07-04 22:33 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-04 22:33 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_SH510050_label_1 memory delta: 0.22MB
2025-07-04 22:33 | INFO     | FactorMonitorContext.__exit__: L1_CORR_SH510050_label_1 completed successfully
2025-07-04 22:33 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:33 | INFO     | ResMonitor.end_timer: PERSIST_L1_SH510050_label_1 took 0.00 seconds (00:00:00)
2025-07-04 22:33 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L1_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-04 22:33 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L1_SH510050_label_1 memory delta: 0.00MB
2025-07-04 22:33 | ERROR    | FactorMonitorContext.__exit__: PERSIST_L1_SH510050_label_1 failed with error: "'DynaBox' object has no attribute 'corr'"
2025-07-04 22:33 | INFO     | ResMonitor.end_timer: L1_CORR_FILTER_1751639596826748 took 0.45 seconds (00:00:00)
2025-07-04 22:33 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_FILTER_1751639596826748 CPU usage: 0.11s user, 0.02s system
2025-07-04 22:33 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_FILTER_1751639596826748 memory delta: 19.56MB
2025-07-04 22:33 | INFO     | MonitorContext.__exit__: L1_CORR_FILTER_1751639596826748 completed successfully
2025-07-04 22:41 | INFO     | TestLogger.test_normal_log: 这是一条普通的INFO日志
2025-07-04 22:41 | DEBUG    | TestLogger.test_normal_log: 这是一条DEBUG日志
2025-07-04 22:41 | WARNING  | TestLogger.test_normal_log: 这是一条WARNING日志
2025-07-04 22:41 | ERROR    | TestLogger.test_error_log [test_enhanced_log.py:15]: 这是一条ERROR日志，应该显示文件位置
    📍 点击定位: file:///D:/myquant/XentZ/test_enhanced_log.py:15
    📚 调用栈:
      1. <module>() at test_enhanced_log.py:52
         📍 file:///D:/myquant/XentZ/test_enhanced_log.py:52
         💻 tester.test_error_log()
2025-07-04 22:41 | CRITICAL | TestLogger.test_error_log [test_enhanced_log.py:16]: 这是一条CRITICAL日志，应该显示详细信息
    📍 点击定位: file:///D:/myquant/XentZ/test_enhanced_log.py:16
    📚 调用栈:
      1. <module>() at test_enhanced_log.py:52
         📍 file:///D:/myquant/XentZ/test_enhanced_log.py:52
         💻 tester.test_error_log()
2025-07-04 22:41 | ERROR    | TestLogger._deep_method [test_enhanced_log.py:28]: 深层嵌套调用中的错误
    📍 点击定位: file:///D:/myquant/XentZ/test_enhanced_log.py:28
    📚 调用栈:
      1. _inner_method() at test_enhanced_log.py:24
         📍 file:///D:/myquant/XentZ/test_enhanced_log.py:24
         💻 self._deep_method()
      2. test_nested_call() at test_enhanced_log.py:20
         📍 file:///D:/myquant/XentZ/test_enhanced_log.py:20
         💻 self._inner_method()
      3. <module>() at test_enhanced_log.py:55
         📍 file:///D:/myquant/XentZ/test_enhanced_log.py:55
         💻 tester.test_nested_call()
2025-07-04 22:41 | ERROR    | TestLogger.test_exception_handling [test_enhanced_log.py:36]: 捕获到除零错误: division by zero
    📍 点击定位: file:///D:/myquant/XentZ/test_enhanced_log.py:36
    📚 调用栈:
      1. <module>() at test_enhanced_log.py:58
         📍 file:///D:/myquant/XentZ/test_enhanced_log.py:58
         💻 tester.test_exception_handling()
2025-07-04 22:41 | INFO     | TestLogger.test_manual_traceback [test_enhanced_log.py:40]: 手动启用详细信息的INFO日志
    📍 点击定位: file:///D:/myquant/XentZ/test_enhanced_log.py:40
    📚 调用栈:
      1. <module>() at test_enhanced_log.py:61
         📍 file:///D:/myquant/XentZ/test_enhanced_log.py:61
         💻 tester.test_manual_traceback()
2025-07-04 22:42 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 22:42 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 22:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:42 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_211730/L0
2025-07-04 22:42 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_211730/L0 耗时24.0ms
2025-07-04 22:42 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_277461/L0
2025-07-04 22:42 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_277461/L0 耗时5.0ms
2025-07-04 22:42 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_366378/L0
2025-07-04 22:42 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_366378/L0 耗时3.0ms
2025-07-04 22:42 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-07-04 22:42 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时3.6ms
2025-07-04 22:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:42 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-04 22:42 | INFO     | ResMonitor.end_timer: L1_CORR_SH510050_label_1 took 0.00 seconds (00:00:00)
2025-07-04 22:42 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-04 22:42 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_SH510050_label_1 memory delta: 0.23MB
2025-07-04 22:42 | INFO     | FactorMonitorContext.__exit__: L1_CORR_SH510050_label_1 completed successfully
2025-07-04 22:42 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:42 | INFO     | ResMonitor.end_timer: PERSIST_L1_SH510050_label_1 took 0.00 seconds (00:00:00)
2025-07-04 22:42 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L1_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-04 22:42 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L1_SH510050_label_1 memory delta: 0.01MB
2025-07-04 22:42 | ERROR    | FactorMonitorContext.__exit__ [common\cls_base.py:319]: PERSIST_L1_SH510050_label_1 failed with error: "'DynaBox' object has no attribute 'corr'"
    📍 点击定位: file:///D:/myquant/XentZ/common/cls_base.py:319
    📚 调用栈:
      1. __exit__() at factorzoo\monitor.py:51
         📍 file:///D:/myquant/XentZ/factorzoo/monitor.py:51
         💻 super().__exit__(exc_type, exc_val, exc_tb)
      2. persist_and_register_factors() at script\投研_因子挖掘集成\L1总体相关筛选.py:220
         📍 file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:220
         💻 with FactorMonitorContext(f"PERSIST_L1_{symbol}_{target_label}",
      3. run_l1_corr_filter_pipeline() at script\投研_因子挖掘集成\L1总体相关筛选.py:501
         📍 file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:501
         💻 persistence_success = persist_and_register_factors(
      4. <module>() at script\投研_因子挖掘集成\L1总体相关筛选.py:563
         📍 file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:563
         💻 results = run_l1_corr_filter_pipeline()
2025-07-04 22:42 | INFO     | ResMonitor.end_timer: L1_CORR_FILTER_1751640141196516 took 0.16 seconds (00:00:00)
2025-07-04 22:42 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_FILTER_1751640141196516 CPU usage: 0.03s user, 0.03s system
2025-07-04 22:42 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_FILTER_1751640141196516 memory delta: 20.17MB
2025-07-04 22:42 | INFO     | MonitorContext.__exit__: L1_CORR_FILTER_1751640141196516 completed successfully
2025-07-04 22:43 | INFO     | WFADemo.simulate_wfa_validation: 开始WFA验证流程
2025-07-04 22:43 | DEBUG    | WFADemo._load_config: 配置加载成功
2025-07-04 22:43 | INFO     | WFADemo._validate_factor: 验证因子: F_TEST_001
2025-07-04 22:43 | WARNING  | WFADemo._validate_factor: 因子 F_TEST_001 的夏普比率较低: 0.3
2025-07-04 22:43 | ERROR    | WFADemo._validation_error [demo_enhanced_log.py:42]: 因子 F_TEST_001 验证失败: 数据不足
    📍 点击定位: file:///D:/myquant/XentZ/demo_enhanced_log.py:42
    📚 调用栈:
      1. _validate_factor() at demo_enhanced_log.py:38
         📍 file:///D:/myquant/XentZ/demo_enhanced_log.py:38
         💻 self._validation_error(factor_id)
      2. simulate_wfa_validation() at demo_enhanced_log.py:18
         📍 file:///D:/myquant/XentZ/demo_enhanced_log.py:18
         💻 self._validate_factor("F_TEST_001")
      3. <module>() at demo_enhanced_log.py:61
         📍 file:///D:/myquant/XentZ/demo_enhanced_log.py:61
         💻 demo.simulate_wfa_validation()
2025-07-04 22:43 | ERROR    | WFADemo.simulate_wfa_validation [demo_enhanced_log.py:24]: WFA验证流程异常: 'nonexistent_key'
    📍 点击定位: file:///D:/myquant/XentZ/demo_enhanced_log.py:24
    📚 调用栈:
      1. <module>() at demo_enhanced_log.py:61
         📍 file:///D:/myquant/XentZ/demo_enhanced_log.py:61
         💻 demo.simulate_wfa_validation()
2025-07-04 22:44 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 22:44 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 22:44 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:44 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_211730/L0
2025-07-04 22:44 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_211730/L0 耗时26.8ms
2025-07-04 22:44 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_277461/L0
2025-07-04 22:44 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_277461/L0 耗时31.6ms
2025-07-04 22:44 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_366378/L0
2025-07-04 22:44 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_366378/L0 耗时4.7ms
2025-07-04 22:44 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-07-04 22:44 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时4.3ms
2025-07-04 22:44 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:44 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-04 22:44 | INFO     | ResMonitor.end_timer: L1_CORR_SH510050_label_1 took 0.00 seconds (00:00:00)
2025-07-04 22:44 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_SH510050_label_1 CPU usage: 0.02s user, 0.00s system
2025-07-04 22:44 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_SH510050_label_1 memory delta: 0.24MB
2025-07-04 22:44 | INFO     | FactorMonitorContext.__exit__: L1_CORR_SH510050_label_1 completed successfully
2025-07-04 22:44 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:44 | INFO     | ResMonitor.end_timer: PERSIST_L1_SH510050_label_1 took 0.00 seconds (00:00:00)
2025-07-04 22:44 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L1_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-04 22:44 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L1_SH510050_label_1 memory delta: 0.00MB
2025-07-04 22:44 | ERROR    | FactorMonitorContext.__exit__ [common\cls_base.py:319]: PERSIST_L1_SH510050_label_1 failed with error: "'DynaBox' object has no attribute 'corr'"
    📍 点击定位: file:///D:/myquant/XentZ/common/cls_base.py:319
    📚 调用栈:
      1. __exit__() at factorzoo\monitor.py:51
         📍 file:///D:/myquant/XentZ/factorzoo/monitor.py:51
         💻 super().__exit__(exc_type, exc_val, exc_tb)
      2. persist_and_register_factors() at script\投研_因子挖掘集成\L1总体相关筛选.py:220
         📍 file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:220
         💻 with FactorMonitorContext(f"PERSIST_L1_{symbol}_{target_label}",
      3. run_l1_corr_filter_pipeline() at script\投研_因子挖掘集成\L1总体相关筛选.py:501
         📍 file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:501
         💻 persistence_success = persist_and_register_factors(
      4. <module>() at script\投研_因子挖掘集成\L1总体相关筛选.py:563
         📍 file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:563
         💻 results = run_l1_corr_filter_pipeline()
2025-07-04 22:44 | INFO     | ResMonitor.end_timer: L1_CORR_FILTER_1751640290131648 took 0.18 seconds (00:00:00)
2025-07-04 22:44 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_FILTER_1751640290131648 CPU usage: 0.06s user, 0.03s system
2025-07-04 22:44 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_FILTER_1751640290131648 memory delta: 19.65MB
2025-07-04 22:44 | INFO     | MonitorContext.__exit__: L1_CORR_FILTER_1751640290131648 completed successfully
2025-07-04 22:46 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 22:46 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 22:46 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:46 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_211730/L0
2025-07-04 22:46 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_211730/L0 耗时26.0ms
2025-07-04 22:46 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_277461/L0
2025-07-04 22:46 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_277461/L0 耗时4.6ms
2025-07-04 22:46 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_366378/L0
2025-07-04 22:46 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_366378/L0 耗时4.1ms
2025-07-04 22:46 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-07-04 22:46 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时4.2ms
2025-07-04 22:46 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:46 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-04 22:46 | INFO     | ResMonitor.end_timer: L1_CORR_SH510050_label_1 took 0.00 seconds (00:00:00)
2025-07-04 22:46 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-04 22:46 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_SH510050_label_1 memory delta: 0.23MB
2025-07-04 22:46 | INFO     | FactorMonitorContext.__exit__: L1_CORR_SH510050_label_1 completed successfully
2025-07-04 22:46 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:46 | INFO     | ResMonitor.end_timer: PERSIST_L1_SH510050_label_1 took 0.00 seconds (00:00:00)
2025-07-04 22:46 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L1_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-04 22:46 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L1_SH510050_label_1 memory delta: 0.00MB
2025-07-04 22:46 | ERROR    | FactorMonitorContext.__exit__ [common\cls_base.py:318]: PERSIST_L1_SH510050_label_1 failed with error: "'DynaBox' object has no attribute 'corr'"
    📍 点击定位: file:///D:/myquant/XentZ/common/cls_base.py:318
    📚 调用栈:
      1. __exit__() at file:///D:/myquant/XentZ/factorzoo/monitor.py:51:51
         📍 file:///D:/myquant/XentZ/factorzoo/monitor.py:51

      2. persist_and_register_factors() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:220:220
         📍 file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:220

      3. run_l1_corr_filter_pipeline() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:501:501
         📍 file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:501

      4. <module>() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:563:563
         📍 file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:563

2025-07-04 22:46 | INFO     | ResMonitor.end_timer: L1_CORR_FILTER_1751640374269692 took 0.17 seconds (00:00:00)
2025-07-04 22:46 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_FILTER_1751640374269692 CPU usage: 0.05s user, 0.05s system
2025-07-04 22:46 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_FILTER_1751640374269692 memory delta: 19.90MB
2025-07-04 22:46 | INFO     | MonitorContext.__exit__: L1_CORR_FILTER_1751640374269692 completed successfully
2025-07-04 22:49 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 22:49 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 22:49 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:49 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_211730/L0
2025-07-04 22:49 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_211730/L0 耗时25.8ms
2025-07-04 22:49 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_277461/L0
2025-07-04 22:49 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_277461/L0 耗时7.0ms
2025-07-04 22:49 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_366378/L0
2025-07-04 22:49 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_366378/L0 耗时5.4ms
2025-07-04 22:49 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-07-04 22:49 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时6.6ms
2025-07-04 22:49 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:49 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-04 22:49 | INFO     | ResMonitor.end_timer: L1_CORR_SH510050_label_1 took 0.00 seconds (00:00:00)
2025-07-04 22:49 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-04 22:49 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_SH510050_label_1 memory delta: 0.23MB
2025-07-04 22:49 | INFO     | FactorMonitorContext.__exit__: L1_CORR_SH510050_label_1 completed successfully
2025-07-04 22:49 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:49 | INFO     | ResMonitor.end_timer: PERSIST_L1_SH510050_label_1 took 0.00 seconds (00:00:00)
2025-07-04 22:49 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L1_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-04 22:49 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L1_SH510050_label_1 memory delta: 0.00MB
2025-07-04 22:49 | ERROR    | FactorMonitorContext.__exit__ [common\cls_base.py:317]: PERSIST_L1_SH510050_label_1 failed with error: "'DynaBox' object has no attribute 'corr'"
    📍 file:///D:/myquant/XentZ/common/cls_base.py:317
    📚 调用栈:
      1. __exit__() at file:///D:/myquant/XentZ/factorzoo/monitor.py:51

      2. persist_and_register_factors() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:220

      3. run_l1_corr_filter_pipeline() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:501

      4. <module>() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:563

2025-07-04 22:49 | INFO     | ResMonitor.end_timer: L1_CORR_FILTER_1751640594651289 took 0.16 seconds (00:00:00)
2025-07-04 22:49 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_FILTER_1751640594651289 CPU usage: 0.08s user, 0.03s system
2025-07-04 22:49 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_FILTER_1751640594651289 memory delta: 19.67MB
2025-07-04 22:49 | INFO     | MonitorContext.__exit__: L1_CORR_FILTER_1751640594651289 completed successfully
2025-07-04 22:51 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 22:51 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 22:51 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:51 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_211730/L0
2025-07-04 22:51 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_211730/L0 耗时23.6ms
2025-07-04 22:51 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_277461/L0
2025-07-04 22:51 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_277461/L0 耗时33.4ms
2025-07-04 22:51 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_366378/L0
2025-07-04 22:51 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_366378/L0 耗时3.8ms
2025-07-04 22:51 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-07-04 22:51 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时5.0ms
2025-07-04 22:51 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:51 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-04 22:51 | INFO     | ResMonitor.end_timer: L1_CORR_SH510050_label_1 took 0.01 seconds (00:00:00)
2025-07-04 22:51 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_SH510050_label_1 CPU usage: 0.02s user, 0.00s system
2025-07-04 22:51 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_SH510050_label_1 memory delta: 0.24MB
2025-07-04 22:51 | INFO     | FactorMonitorContext.__exit__: L1_CORR_SH510050_label_1 completed successfully
2025-07-04 22:51 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:51 | INFO     | ResMonitor.end_timer: PERSIST_L1_SH510050_label_1 took 0.00 seconds (00:00:00)
2025-07-04 22:51 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L1_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-04 22:51 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L1_SH510050_label_1 memory delta: 0.00MB
2025-07-04 22:51 | ERROR    | FactorMonitorContext.__exit__ [common\cls_base.py:311]: PERSIST_L1_SH510050_label_1 failed with error: "'DynaBox' object has no attribute 'corr'"
    📍 file:///D:/myquant/XentZ/common/cls_base.py:311
    📚 调用栈:
      1. __exit__() at file:///D:/myquant/XentZ/factorzoo/monitor.py:51
      2. persist_and_register_factors() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:220
      3. run_l1_corr_filter_pipeline() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:501
      4. <module>() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:563
2025-07-04 22:51 | INFO     | ResMonitor.end_timer: L1_CORR_FILTER_1751640713361680 took 0.18 seconds (00:00:00)
2025-07-04 22:51 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_FILTER_1751640713361680 CPU usage: 0.09s user, 0.00s system
2025-07-04 22:51 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_FILTER_1751640713361680 memory delta: 19.71MB
2025-07-04 22:51 | INFO     | MonitorContext.__exit__: L1_CORR_FILTER_1751640713361680 completed successfully
2025-07-04 22:52 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 22:52 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:52 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_211730/L0
2025-07-04 22:52 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_211730/L0 耗时21.8ms
2025-07-04 22:52 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_277461/L0
2025-07-04 22:52 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_277461/L0 耗时5.1ms
2025-07-04 22:52 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_366378/L0
2025-07-04 22:52 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_366378/L0 耗时4.0ms
2025-07-04 22:52 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-07-04 22:52 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时6.0ms
2025-07-04 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:52 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-04 22:52 | INFO     | ResMonitor.end_timer: L1_CORR_SH510050_label_1 took 0.01 seconds (00:00:00)
2025-07-04 22:52 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-04 22:52 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_SH510050_label_1 memory delta: 0.23MB
2025-07-04 22:52 | INFO     | FactorMonitorContext.__exit__: L1_CORR_SH510050_label_1 completed successfully
2025-07-04 22:52 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:52 | INFO     | ResMonitor.end_timer: PERSIST_L1_SH510050_label_1 took 0.00 seconds (00:00:00)
2025-07-04 22:52 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L1_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-04 22:52 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L1_SH510050_label_1 memory delta: 0.00MB
2025-07-04 22:52 | ERROR    | FactorMonitorContext.__exit__ [common\cls_base.py:311]: PERSIST_L1_SH510050_label_1 failed with error: "'DynaBox' object has no attribute 'corr'"
    📍 file:///D:/myquant/XentZ/common/cls_base.py:311
    📚 调用栈:
      1. __exit__() at file:///D:/myquant/XentZ/factorzoo/monitor.py:51
      2. persist_and_register_factors() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:220
      3. run_l1_corr_filter_pipeline() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:501
      4. <module>() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:563

2025-07-04 22:52 | INFO     | ResMonitor.end_timer: L1_CORR_FILTER_1751640748599587 took 0.16 seconds (00:00:00)
2025-07-04 22:52 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_FILTER_1751640748599587 CPU usage: 0.06s user, 0.03s system
2025-07-04 22:52 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_FILTER_1751640748599587 memory delta: 19.81MB
2025-07-04 22:52 | INFO     | MonitorContext.__exit__: L1_CORR_FILTER_1751640748599587 completed successfully
2025-07-04 22:56 | INFO     | FactorValueManager._init_performance_components: 性能优化组件初始化成功
2025-07-04 22:56 | INFO     | FactorValueManager.__init__: FactorValueManager 高性能版本初始化完成
2025-07-04 22:56 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:56 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_211730/L0
2025-07-04 22:56 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_211730/L0 耗时28.5ms
2025-07-04 22:56 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_277461/L0
2025-07-04 22:56 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_277461/L0 耗时4.0ms
2025-07-04 22:56 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250629_L0_366378/L0
2025-07-04 22:56 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250629_L0_366378/L0 耗时3.9ms
2025-07-04 22:56 | DEBUG    | FactorValueManager._cache_loaded_data: 数据缓存完成: GP_SH510050_20250628_L0_607982/L0
2025-07-04 22:56 | INFO     | FactorValueManager.load_batch_data: 数据加载完成: GP_SH510050_20250628_L0_607982/L0 耗时4.0ms
2025-07-04 22:56 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:56 | INFO     | FctsGPMiner.select_by_corr: start compute corr...
2025-07-04 22:56 | INFO     | ResMonitor.end_timer: L1_CORR_SH510050_label_1 took 0.01 seconds (00:00:00)
2025-07-04 22:56 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-04 22:56 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_SH510050_label_1 memory delta: 0.23MB
2025-07-04 22:56 | INFO     | FactorMonitorContext.__exit__: L1_CORR_SH510050_label_1 completed successfully
2025-07-04 22:56 | INFO     | ResMonitor.__init__: Resource monitor started
2025-07-04 22:56 | INFO     | ResMonitor.end_timer: PERSIST_L1_SH510050_label_1 took 0.00 seconds (00:00:00)
2025-07-04 22:56 | INFO     | ResMonitor.end_timer_cpu: PERSIST_L1_SH510050_label_1 CPU usage: 0.00s user, 0.00s system
2025-07-04 22:56 | INFO     | ResMonitor.end_memory_monitor: PERSIST_L1_SH510050_label_1 memory delta: 0.00MB
2025-07-04 22:56 | ERROR    | FactorMonitorContext.__exit__: PERSIST_L1_SH510050_label_1 failed with error: "'DynaBox' object has no attribute 'corr'"
    📍 file:///D:/myquant/XentZ/common/cls_base.py:311
    📚 调用栈:
      1. __exit__() at file:///D:/myquant/XentZ/factorzoo/monitor.py:51
      2. persist_and_register_factors() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:220
      3. run_l1_corr_filter_pipeline() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:501
      4. <module>() at file:///d:/myquant/XentZ/script/投研_因子挖掘集成/L1总体相关筛选.py:563

2025-07-04 22:56 | INFO     | ResMonitor.end_timer: L1_CORR_FILTER_1751640969945334 took 0.16 seconds (00:00:00)
2025-07-04 22:56 | INFO     | ResMonitor.end_timer_cpu: L1_CORR_FILTER_1751640969945334 CPU usage: 0.06s user, 0.02s system
2025-07-04 22:56 | INFO     | ResMonitor.end_memory_monitor: L1_CORR_FILTER_1751640969945334 memory delta: 19.36MB
2025-07-04 22:56 | INFO     | MonitorContext.__exit__: L1_CORR_FILTER_1751640969945334 completed successfully
