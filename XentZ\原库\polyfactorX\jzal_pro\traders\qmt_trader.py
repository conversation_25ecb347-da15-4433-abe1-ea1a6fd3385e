import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent.parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
    
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import StockAccount, XtOrder, XtAsset
from xtquant import xtconstant, xtdata
import time
import pandas as pd
import random
from loguru import logger
import datetime
from config import SETTING_TZ, DATA_DIR_OTHER

class QMTTrader(XtQuantTraderCallback):
# class MyXtQuantTraderCallback(XtQuantTraderCallback):
    def on_disconnected(self):
        """
        连接断开
        """
        self.xt_trader = None # 很关键,用来判断是否断开
        self.log("连接已断开...即将重连...")                   
    def on_stock_asset(self, asset):
        """
        资金变动推送
        :param asset: XtAsset对象
        :return:
        """
        print("on asset callback")
        print(asset.account_id, asset.cash, asset.total_asset)
    def on_stock_position(self, position):
        """
        持仓变动推送
        :param position: XtPosition对象
        :return:
        """
        print("on position callback")
        print(position.stock_code, position.volume)
    def on_cancel_error(self, cancel_error):
        """
        撤单失败推送
        :param cancel_error: XtCancelError 对象
        :return:
        """
        print("on cancel_error callback")
        print(cancel_error.order_id, cancel_error.error_id, cancel_error.error_msg)
    def on_order_stock_async_response(self, response):
        """
        异步下单回报推送
        :param response: XtOrderResponse 对象
        :return:
        """
        print("on_order_stock_async_response")
        print(response.account_id, response.order_id, response.seq)

# class QMTTrader():
    def __init__(self, path= r'D:/p-program/gjQMT_sim/userdata_mini',
                  account='********',account_type='STOCK',
                  is_slippage=True,slippage=0.01) -> None:
        self.xt_trader=None
        self.acc=''
        self.path=path
        self.session_id = int(random.randint(100000, 999999))
        self.account=account
        self.account_type=account_type
        if is_slippage==True:
            self.slippage=slippage
        else:
            self.slippage=0
    
    def log(self, txt: str):
        logger.info('%s: %s' % (self.__class__.__name__, txt))           
    def connect(self) -> int:
        ''' 返回: 0 成功 1 连接失败 2 订阅acc失败'''
        self.xt_trader = XtQuantTrader(self.path, self.session_id)
        # xt_trader.set_relaxed_response_order_enabled(True)
        acc = StockAccount(account_id=self.account,account_type=self.account_type)
        # callback = MyXtQuantTraderCallback()
        # self.xt_trader.register_callback(callback)
        self.xt_trader.register_callback(self)
        self.xt_trader.start()
        self.log('----  启动qmt的API接口    ----')
        connect_result = self.xt_trader.connect()
        if connect_result==0:
            self.log('qmt连接成功')
            # 对交易回调进行订阅，订阅后可以收到交易主推，返回0表示订阅成功
            subscribe_result = self.xt_trader.subscribe(acc)
            if subscribe_result != 0:
                self.log('账号订阅失败 %d' % subscribe_result)
                return 2
            # self.xt_trader=xt_trader
            self.acc=acc
            self.log('qmt账号订阅成功')
            return connect_result
        else:
            self.log('qmt连接失败')
            return 1
    
    def is_trade_date(self) -> bool:
        # 交易日期判断, 不判断时间
        dt_now = datetime.datetime.now(SETTING_TZ).date()
        df_astock_calendar = pd.read_csv(DATA_DIR_OTHER.joinpath('astock_calendar.csv'))
        se = pd.to_datetime(df_astock_calendar['trade_date']).dt.date
        if (se == dt_now).any():
            return True
        return False
    def is_trade_time(self) -> bool:
        # return True
        # 交易时间判断
        now = datetime.datetime.now(SETTING_TZ)
        # astock交易时段
        morning_bgn = datetime.time(9, 30)
        morning_end = datetime.time(11, 30)
        afternoon_bgn = datetime.time(13, 0)
        afternoon_end = datetime.time(15, 0)    
        # 判断是否在上午交易时间
        if morning_bgn <= now.time() <= morning_end:
            return True
        # 判断是否在下午交易时间
        elif afternoon_bgn <= now.time() <= afternoon_end:
            return True
        return False
    def query_stock_asset(self):
        ret = XtAsset(0,0,0,0,0)
        try:
            asset = self.xt_trader.query_stock_asset(account=self.acc)
            if asset:
                return asset  # XtAsset数据对象
            else:
                self.log('query_stock_asset()返回空!')
                return ret
        except:
            self.log('query_stock_asset()异常!')
            return ret
    def cancel_order_by_id(self, order_id) -> int:
        ''' 根据order_id撤单, 0成功, -1失败'''
        # TODO: 有bug, 收盘时撤掉已成交的order, 返回成功?
        ret = -1
        try:
            ret = self.xt_trader.cancel_order_stock(account=self.acc, order_id=order_id)
        except:
            self.log('cancel_order_by_id()异常!')
        return ret
    def query_a_symbol_order(self, symbol, cancelable_only = True) -> list:
        ''' 根据symbol查询某个股票的委托单'''
        order_id_list = []
        try:
            orders = self.xt_trader.query_stock_orders(account=self.acc, cancelable_only=cancelable_only)
            if orders:
                for o in orders:
                    if o.stock_code == symbol:
                        order_id_list.append(o.order_id)
            else:
                self.log('query_stock_order()返回空!')
        except:
            self.log('query_stock_order()异常!')
        return order_id_list    
    def _symbol_type(self,symbol='600031.SH') -> str:
        ''' return: stock, fund, bond'''
        if symbol[:3] in ['110','113','123','127','128','111','118']:
            return 'bond'
        elif symbol[:3] in ['510','511','512','513','514','515','516','517','518','588','159','501']:
            return 'fund'
        else:
            return 'stock'
    def _set_slippage(self, symbol = '159915.SZ',price=15.01,trader_type='buy'):
        '''
        按价格来滑点, 比如0.01就是一块; etf3位数,股票可转债2位数
        '''
        symbol_type=self._symbol_type(symbol=symbol)
        if symbol_type=='fund' or symbol_type=='bond': # 可转债和etf基金
            slippage=self.slippage/10
            if trader_type=='buy' or trader_type==23:
                price=price+slippage
            else:
                price=price-slippage
        else:
            slippage=self.slippage
            if trader_type=='buy' or trader_type==23:
                price=price+slippage
            else:
                price=price-slippage
        return price
    
    def buy_a_symbol(self, symbol='159915.SZ', amount=100,
                     price_type=xtconstant.FIX_PRICE, price:float=20,
                     strategy_name='',order_remark='') -> int: # 返回order_id, -1(QMT约定)为委托失败, 0为不用发指令了
        ''' TODO: 判断是否在交易时段'''
        price=self._set_slippage(symbol=symbol, price=price, trader_type='buy')
        order_volume=amount
        fix_result_order_id = -1
        # 使用指定价下单，接口返回订单编号，后续可以用于撤单操作以及查询委托状态
        try:
            if order_volume>0:
                fix_result_order_id = self.xt_trader.order_stock(account=self.acc,stock_code=symbol, order_type=xtconstant.STOCK_BUY,
                                                                    order_volume=order_volume, price_type=price_type,
                                                                    price=price, strategy_name=strategy_name, order_remark=order_remark)
                time.sleep(2)
                # self.xt_trader.run_forever()
            else:
                self.log('买入 标的{} 价格{} 委托数量{} 小于等于0有问题'.format(symbol,price,order_volume))
                return 0 # 不用发指令了 也算一种"完成"
        except:
            print('买入 标的{} 价格{} 委托数量{} 异常'.format(symbol,price,order_volume))
            return -1
        return fix_result_order_id               
    def sell_a_symbol(self, symbol='159915.SZ', amount=100,
                      price_type=xtconstant.FIX_PRICE, price:float=20, 
                      strategy_name='',order_remark='') -> int: # 返回order_id, -1(QMT约定)为委托失败, 0为不用发指令了
        ''' TODO: 判断是否在交易时段'''
        price=self._set_slippage(symbol=symbol,price=price,trader_type='sell')
        order_volume=amount
        fix_result_order_id = -1
        # 使用指定价下单，接口返回订单编号，后续可以用于撤单操作以及查询委托状态
        try:
            if order_volume>0:
                fix_result_order_id = self.xt_trader.order_stock(account=self.acc,stock_code=symbol, order_type=xtconstant.STOCK_SELL,
                                                                    order_volume=order_volume, price_type=price_type,
                                                                    price=price, strategy_name=strategy_name, order_remark=order_remark)
                time.sleep(2)
            else:
                self.log('卖出 标的{} 价格{} 委托数量{} 小于等于0有问题'.format(symbol,price,order_volume))
                return 0 # 不用发指令也算一种"完成"
        except:
            self.log('卖出 标的{} 价格{} 委托数量{} 异常'.format(symbol,price,order_volume))
            return -1
        return fix_result_order_id
    def get_bidPrice1(self,a_symbol:str) -> float:
        try:
            price = xtdata.get_full_tick(code_list=[a_symbol])
            bidPrice = price[a_symbol]['bidPrice']
            bidPrice1=bidPrice[0]
            return float(bidPrice1) 
        except:
            self.log('get bidPrice1 error!')
            return -0.0         
    def query_a_symbol_position(self, a_symbol:str):
        # TODO: query_stock_position模拟盘不返回,需确认是否为bug(客服)
        if self.xt_trader:
            pos_all = self.xt_trader.query_stock_positions(account=self.acc)
            position = next((p for p in pos_all if p.stock_code == a_symbol), None)
            # position = self.xt_trader.query_stock_position(account=self.acc, stock_code='600031.SH')
            return position
        else:
            return None
    def stop(self):
        if self.xt_trader:
            self.xt_trader.stop() # 对应启动线程start()
            self.log('----  主动停止qmt的API接口    ----')
        return
if __name__ == '__main__':
    t=QMTTrader()
    # if t.xt_trader is None:
    t.connect()
    # print(t.query_a_symbol_position('000628.SZ'))
    print(t.get_bidPrice1('159915.SZ'))
    # print(t.query_a_symbol_order('159915.SZ', False))
    # print(t.cancel_order_by_id(**********))
    # orders:list = t.xt_trader.query_stock_orders(t.acc)
    # this_order = next((o for o in orders if o.order_id == **********), None)
    # price = t.get_bidPrice1('159915.SZ')
    # # print(this_order.traded_price)
    # t.buy_a_symbol(price=price,symbol='510880.SH',amount=100)
    # t.sell_a_symbol(price=price,symbol='159915.SZ',amount=100)
    t.stop()
    '''
    问题:
    1. 有时候on_order_error不推送(15:00多)
    2. on_stock_order不推送(15:00多)
    '''