import sqlite3
from sqlite3 import Error
from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional
import sys
from pathlib import Path
path_root = Path(__file__).parent.absolute().parent
if str(path_root) not in sys.path:
    sys.path.append(str(path_root))
from jzal_pro.cls_common import BaseObj
from config import DB_DIR_FACTOR

@dataclass
class FactorL2Data:
    expr: str
    task_uid: str
    opt_uid: str
    scope: Optional[str] = None
    sr: Optional[float] = None
    cost_sr: Optional[float] = None
    tot_ret: Optional[float] = None
    ann_ret: Optional[float] = None
    ann_cost: Optional[float] = None
    ann_std: Optional[float] = None
    mdd: Optional[float] = None
    avg_dd: Optional[float] = None
    monthly_skew: Optional[float] = None
    lower_tail: Optional[float] = None
    upper_tail: Optional[float] = None
    profit_factor: Optional[float] = None
    calmar: Optional[float] = None
    trade_times: Optional[float] = None
    win_rate: Optional[float] = None
    saw_score: Optional[float] = None
    topsis_score: Optional[float] = None
    bgn_mdd: Optional[datetime] = None
    end_mdd: Optional[datetime] = None
    metric: Optional[float] = None # 暂时没用!  TODO: 重新设置
    def __post_init__(self):
        if self.scope is not None and self.scope not in ['in', 'out', 'all', 'all_r']:
            raise ValueError("scope字段必须是: 'in', 'out', 'all', or 'all_r'")

class FactorL2DataMgr(BaseObj):
    ''' TODO: 批量插入时可能遇到同一个因子同一套数据 先去重再批量插入; bug在于metric相差很大的情况发现过,未解决 '''
    def __init__(self, db_path: str, table_name: str):
        super().__init__()
        self.db_path = db_path
        self.tbl_name = table_name
        self._create_table() # 表不存在 则创建table

    def _create_table(self):
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    CREATE TABLE IF NOT EXISTS [{self.tbl_name}] (
                        expr TEXT NOT NULL, 
                        task_uid TEXT NOT NULL,
                        opt_uid TEXT NOT NULL,
                        scope TEXT,
                        sr REAL,
                        cost_sr REAL,
                        tot_ret REAL,
                        ann_ret REAL,
                        ann_cost REAL,
                        ann_std REAL,
                        mdd REAL,
                        avg_dd REAL,
                        monthly_skew REAL,
                        lower_tail REAL,
                        upper_tail REAL,
                        profit_factor REAL,
                        calmar REAL,
                        trade_times REAL,
                        win_rate REAL,
                        saw_score REAL,
                        topsis_score REAL,
                        bgn_mdd DATETIME,
                        end_mdd DATETIME,
                        metric REAL,
                        UNIQUE (expr, task_uid, scope), -- 去重约束
                        FOREIGN KEY (task_uid) REFERENCES gp_tasks(task_uid)
                    )
                ''')
                conn.commit()
        except Error as e:
            self.log(f"创建{self.tbl_name}表时发生错误: {e}")
            
    def _is_close(self, value1, value2, tolerance=1e-5):
        return abs(value1 - value2) <= tolerance

    # 将 datetime 对象转换为 ISO 格式字符串
    def _datetime_to_str(self, dt: Optional[datetime]) -> Optional[str]:
        return dt.isoformat() if dt else None

    def add_factor(self, factor_data: FactorL2Data) -> None:
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                # 检查是否有相同expr和task_uid的记录
                cursor.execute(f'''
                    SELECT sr FROM [{self.tbl_name}]
                    WHERE expr = ? AND task_uid = ? AND scope = ?
                ''', (factor_data.expr, factor_data.task_uid, factor_data.scope))
                
                existing_records = cursor.fetchall()
                if existing_records:
                    for record in existing_records:
                        if self._is_close(factor_data.sr, record[0]): # 如果sr值接近，则不插入
                            self.log(f"同一套数据的同一个因子已存在表中, 本次插入失败!: {factor_data.expr}, {factor_data.task_uid}")
                            return
                    # 如果sr值不接近，打印警告
                    self.log(f"同一套数据的同一个因子出现非常不同的metric值, 本次插入失败!: {factor_data.expr}, {factor_data.task_uid}")
                cursor.execute(f'''
                    INSERT OR IGNORE INTO [{self.tbl_name}] (
                        expr, task_uid, opt_uid, scope, sr, cost_sr, tot_ret, ann_ret, ann_cost, 
                        ann_std, mdd, bgn_mdd, end_mdd, avg_dd, monthly_skew, 
                        lower_tail, upper_tail, profit_factor, calmar, trade_times, 
                        win_rate, saw_score, topsis_score, metric
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    factor_data.expr, factor_data.task_uid, factor_data.opt_uid, factor_data.scope,
                    factor_data.sr, factor_data.cost_sr, factor_data.tot_ret, factor_data.ann_ret, 
                    factor_data.ann_cost, factor_data.ann_std, factor_data.mdd, 
                    factor_data.bgn_mdd, factor_data.end_mdd, factor_data.avg_dd, 
                    factor_data.monthly_skew, factor_data.lower_tail, factor_data.upper_tail, 
                    factor_data.profit_factor, factor_data.calmar, factor_data.trade_times, 
                    factor_data.win_rate, factor_data.saw_score, factor_data.topsis_score, factor_data.metric
                ))
        except Error as e:
            self.log(f"插入因子数据时发生错误: {e}")
            
    def load_all_factors(self, scope: str = 'in') -> List[str]:
        """返回所有expr字段的值的列表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f"SELECT expr FROM [{self.tbl_name}] WHERE scope=?", (scope,))
                rows = cursor.fetchall()
                return [row[0] for row in rows]
        except Error as e:
            self.log(f"加载所有expr字段的值时发生错误: {e}")
            return []
        
    def load_factors_by_optuid(self, opt_uid: str, scope: str = 'in') -> List[str]:
        """根据opt_uid返回所有expr字段的值的列表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f"SELECT expr FROM [{self.tbl_name}] WHERE opt_uid=? AND scope=?", (opt_uid,scope))
                rows = cursor.fetchall()
                return [row[0] for row in rows]
        except Error as e:
            self.log(f"根据opt_uid加载expr字段的值时发生错误: {e}")
            return []
                        
    def query_all_factors(self) -> List[FactorL2Data]:
        """查询表中的所有因子数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f"SELECT * FROM [{self.tbl_name}]")
                rows = cursor.fetchall()
                return [FactorL2Data(*row) for row in rows]
        except Error as e:
            self.log(f"查询所有因子数据时发生错误: {e}")
            return []
        
    def query_by_task_uid(self, task_uid: str) -> List[FactorL2Data]:
        """根据任务ID查询因子数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f"SELECT * FROM [{self.tbl_name}] WHERE task_uid=?",(task_uid,))
                rows = cursor.fetchall()
                return [FactorL2Data(*row) for row in rows]
        except Error as e:
            self.log(f"根据任务ID查询因子数据时发生错误: {e}")
            return []
        
    def update_field_value(self, expr: str, task_uid: str, opt_uid: str, scope: str,
                           field_name: str, new_value: float) -> bool:
        """更新指定因子数据的metric值"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    UPDATE [{self.tbl_name}]
                    SET {field_name} = ?
                    WHERE expr = ? AND task_uid = ? AND opt_uid = ? AND scope = ?
                ''', (new_value, expr, task_uid, opt_uid, scope))
                if cursor.rowcount > 0:
                    conn.commit()
                    return True
                else:
                    self.log("没有找到匹配的记录进行更新")
                    return False
        except Error as e:
            self.log(f"更新因子数据metric值时发生错误: {e}")
            return False

    def update_by_factor_list(self, factor_list: List[FactorL2Data]) -> bool:
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                update_query = f'''
                    UPDATE [{self.tbl_name}]
                    SET sr = ?, cost_sr = ?, tot_ret = ?, ann_ret = ?, ann_cost = ?, ann_std = ?, 
                        mdd = ?, bgn_mdd = ?, end_mdd = ?, avg_dd = ?, monthly_skew = ?, 
                        lower_tail = ?, upper_tail = ?, profit_factor = ?, calmar = ?, 
                        trade_times = ?, win_rate = ?, saw_score = ?, topsis_score = ?, metric = ?
                    WHERE expr = ? AND task_uid = ? AND opt_uid = ? AND scope = ?
                '''
                update_data = [
                    (
                        factor.sr, factor.cost_sr, factor.tot_ret, factor.ann_ret, 
                        factor.ann_cost, factor.ann_std,
                        factor.mdd, self._datetime_to_str(factor.bgn_mdd), 
                        self._datetime_to_str(factor.end_mdd), factor.avg_dd, factor.monthly_skew,
                        factor.lower_tail, factor.upper_tail, factor.profit_factor, factor.calmar,
                        factor.trade_times, factor.win_rate, factor.saw_score, factor.topsis_score, 
                        factor.metric, factor.expr, factor.task_uid, factor.opt_uid, factor.scope
                    )
                    for factor in factor_list
                ]
                # 批量执行更新
                cursor.executemany(update_query, update_data)
                conn.commit()
                
                self.log(f"更新了 {cursor.rowcount} 条记录")
                return True
        except sqlite3.Error as e:
            self.log(f"批量更新因子数据时发生错误: {e}")
            return False
        
    def delete_factor(self, expr: str, task_uid: str, opt_uid: str) -> bool:
        """删除特定的因子数据记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    DELETE FROM [{self.tbl_name}]
                    WHERE expr = ? AND task_uid = ? AND opt_uid = ?
                ''', (expr, task_uid, opt_uid))
                if cursor.rowcount > 0:
                    conn.commit()
                    return True
                else:
                    self.log("没有找到匹配的记录进行删除")
                    return False
        except Error as e:
            self.log(f"删除因子数据记录时发生错误: {e}")
            return False

    def add_factor_list(self, factor_list: List[FactorL2Data]) -> None:
        """
        批量添加因子数据到数据库中。
        :param factor_list: 包含多个FactorL2Data对象的列表
        """
        try:
            # 获取数据库中已存在的记录
            existing_records = set()
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    SELECT expr, task_uid, scope FROM [{self.tbl_name}]
                ''')
                existing_records = {(row[0], row[1], row[2]) for row in cursor.fetchall()}
            
            # 从factor_list中移除已存在的记录
            filtered_list = [
                factor for factor in factor_list
                if (factor.expr, factor.task_uid, factor.scope) not in existing_records
            ]
            
            # 准备SQL和参数                                                                                          
            sql = f'''
                INSERT OR IGNORE INTO [{self.tbl_name}] (expr, task_uid, opt_uid, scope, sr, cost_sr, tot_ret, 
                ann_ret, ann_cost, ann_std, mdd, bgn_mdd, end_mdd, avg_dd, monthly_skew,
                lower_tail, upper_tail, profit_factor, calmar, trade_times, win_rate, saw_score, topsis_score, metric)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            params = [(factor.expr, factor.task_uid, factor.opt_uid, factor.scope, factor.sr, factor.cost_sr,
                       factor.tot_ret, factor.ann_ret, factor.ann_cost, factor.ann_std, 
                       factor.mdd, self._datetime_to_str(factor.bgn_mdd), 
                       self._datetime_to_str(factor.end_mdd), factor.avg_dd, 
                       factor.monthly_skew, factor.lower_tail, factor.upper_tail, 
                       factor.profit_factor, factor.calmar, factor.trade_times, 
                       factor.win_rate, factor.saw_score, factor.topsis_score, factor.metric) 
                      for factor in filtered_list]
      
            # 执行批量插入
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.executemany(sql, params)
                conn.commit()
        
        except Error as e:
            self.log(f"批量插入因子数据时发生错误: {e}")
                    
    def query_by_factor_list(self, factor_list: List[str]) -> List[FactorL2Data]:
        """
        根据传入的因子表达式列表，查询并返回所有相关记录。
        :param factor_list: 包含多个表达式的列表
        :return: 包含查询结果的 FactorL2Data 对象列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                placeholders = ', '.join('?' for _ in factor_list)
                query = f"SELECT * FROM [{self.tbl_name}] WHERE expr IN ({placeholders})"
                cursor.execute(query, factor_list)
                rows = cursor.fetchall()
                return [FactorL2Data(*row) for row in rows]
        except Error as e:
            self.log(f"根据表达式列表查询因子数据时发生错误: {e}")
            return []
        
def main():
    # 定义数据库路径和表名，这里使用内存数据库以简化测试
    # db_path = ":memory:"
    db_path = str(DB_DIR_FACTOR.joinpath('test.sqlite'))
    tbl_name = "factor_data_test"
    
    # 初始化FactorL2DataMgr实例
    factor_mgr = FactorL2DataMgr(db_path, tbl_name)
    
    # 测试创建表
    factor_mgr._create_table()
    print("表已创建或确认存在。")
    
    # 插入测试数据
    test_factor = FactorL2Data(expr="Close > Open", task_uid="TST001", opt_uid="RUN001", metric=0.75)
    factor_mgr.add_factor(test_factor)
    print("测试数据插入成功。")
    
    # 查询所有数据并打印
    all_factors = factor_mgr.query_all_factors()
    print("查询所有因子数据:")
    for factor in all_factors:
        print(factor)
    
    # 根据task_uid查询并打印
    factors_by_task = factor_mgr.query_by_task_uid("TST001")
    print(f"根据任务ID 'TST001' 查询到的因子数据:")
    for factor in factors_by_task:
        print(factor)
    
    # 更新数据
    new_metric = 0.80
    factor_mgr.update_metric_value(test_factor.expr, test_factor.task_uid, test_factor.opt_uid, new_metric)
    print(f"数据更新成功，新的metric值为: {new_metric}")
    
    # 验证更新
    updated_factor = factor_mgr.query_by_task_uid("TST001")[0]
    print(f"验证更新结果: {updated_factor}")
    
    # 删除数据
    factor_mgr.delete_factor(test_factor.expr, test_factor.task_uid, test_factor.opt_uid)
    print("数据删除成功。")
    
    # 再次查询所有数据，验证删除操作
    remaining_factors = factor_mgr.query_all_factors()
    print("删除后查询所有因子数据:")
    if remaining_factors:
        for factor in remaining_factors:
            print(factor)
    else:
        print("无数据，删除验证成功。")

def test_load_factors_by_optuid():
    # 定义数据库路径和表名，这里使用内存数据库以简化测试
    db_path = str(DB_DIR_FACTOR.joinpath('test.sqlite'))
    tbl_name = "factor_data_test"
    
    # 初始化FactorL2DataMgr实例
    factor_mgr = FactorL2DataMgr(db_path, tbl_name)
    
    # 测试创建表
    factor_mgr._create_table()
    print("表已创建或确认存在。")
    
    # 插入测试数据
    test_factors = [
        FactorL2Data(expr="Close > Open", task_uid="TST001", opt_uid="RUN001", metric=0.75),
        FactorL2Data(expr="Open > Close", task_uid="TST002", opt_uid="RUN001", metric=0.80),
        FactorL2Data(expr="High > Low", task_uid="TST003", opt_uid="RUN002", metric=0.85)
    ]
    for factor in test_factors:
        factor_mgr.add_factor(factor)
    print("测试数据插入成功。")
    
    # 根据opt_uid查询并打印
    factors_by_optuid = factor_mgr.load_factors_by_optuid("RUN001")
    print(f"根据opt_uid 'RUN001' 查询到的因子表达式:")
    for expr in factors_by_optuid:
        print(expr)

if __name__ == "__main__":
    test_load_factors_by_optuid()
    