#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FactorZoo 高性能缓存系统
实现多层级缓存架构，提升因子数据访问性能
"""

import threading
import time
import hashlib
import pandas as pd
import numpy as np
from typing import Dict, Optional, Any, List, Tuple, Union
from collections import OrderedDict, defaultdict
from dataclasses import dataclass
import psutil
import weakref
from .config_cache import get_performance_config


@dataclass
class CacheEntry:
    """缓存条目"""
    data: Any
    created_time: float
    last_access_time: float
    access_count: int
    size_bytes: int
    key: str


class LRUCache:
    """线程安全的LRU缓存"""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 3600):
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self._cache = OrderedDict()
        self._lock = threading.RLock()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'size_bytes': 0
        }
    
    def get(self, key: str) -> Optional[CacheEntry]:
        """获取缓存项"""
        with self._lock:
            if key not in self._cache:
                self._stats['misses'] += 1
                return None
            
            entry = self._cache[key]
            current_time = time.time()
            
            # 检查TTL
            if current_time - entry.created_time > self.ttl_seconds:
                del self._cache[key]
                self._stats['size_bytes'] -= entry.size_bytes
                self._stats['misses'] += 1
                return None
            
            # 更新访问信息
            entry.last_access_time = current_time
            entry.access_count += 1
            
            # 移到末尾（最近使用）
            self._cache.move_to_end(key)
            
            self._stats['hits'] += 1
            return entry
    
    def put(self, key: str, data: Any, size_bytes: int = 0) -> bool:
        """添加缓存项"""
        with self._lock:
            current_time = time.time()
            
            # 如果已存在，更新
            if key in self._cache:
                old_entry = self._cache[key]
                self._stats['size_bytes'] -= old_entry.size_bytes
            
            # 创建新条目
            entry = CacheEntry(
                data=data,
                created_time=current_time,
                last_access_time=current_time,
                access_count=1,
                size_bytes=size_bytes,
                key=key
            )
            
            self._cache[key] = entry
            self._stats['size_bytes'] += size_bytes
            
            # 检查大小限制
            while len(self._cache) > self.max_size:
                # 移除最久未使用的项
                oldest_key, oldest_entry = self._cache.popitem(last=False)
                self._stats['size_bytes'] -= oldest_entry.size_bytes
                self._stats['evictions'] += 1
            
            return True
    
    def remove(self, key: str) -> bool:
        """移除缓存项"""
        with self._lock:
            if key in self._cache:
                entry = self._cache.pop(key)
                self._stats['size_bytes'] -= entry.size_bytes
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._stats['size_bytes'] = 0
    
    def get_stats(self) -> Dict:
        """获取缓存统计"""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0.0
            
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'size_bytes': self._stats['size_bytes'],
                'hits': self._stats['hits'],
                'misses': self._stats['misses'],
                'evictions': self._stats['evictions'],
                'hit_rate': hit_rate
            }


class ExpressionHashCache:
    """表达式哈希缓存，避免重复计算相同表达式"""
    
    def __init__(self, max_size: int = 10000):
        self.max_size = max_size
        self._hash_cache = LRUCache(max_size=max_size, ttl_seconds=7200)  # 2小时TTL
        self._expression_map = {}  # 表达式 -> 哈希值映射
        self._lock = threading.RLock()
    
    def get_expression_hash(self, expression: str) -> str:
        """获取表达式哈希值"""
        with self._lock:
            if expression in self._expression_map:
                return self._expression_map[expression]
            
            # 计算SHA256哈希
            hash_value = hashlib.sha256(expression.encode('utf-8')).hexdigest()[:16]
            self._expression_map[expression] = hash_value
            
            return hash_value
    
    def get_cached_result(self, expression: str, data_signature: str) -> Optional[Any]:
        """获取表达式计算结果缓存"""
        expr_hash = self.get_expression_hash(expression)
        cache_key = f"{expr_hash}_{data_signature}"
        
        entry = self._hash_cache.get(cache_key)
        return entry.data if entry else None
    
    def cache_result(self, expression: str, data_signature: str, result: Any):
        """缓存表达式计算结果"""
        expr_hash = self.get_expression_hash(expression)
        cache_key = f"{expr_hash}_{data_signature}"
        
        # 估算结果大小
        if isinstance(result, pd.DataFrame):
            size_bytes = result.memory_usage(deep=True).sum()
        elif isinstance(result, pd.Series):
            size_bytes = result.memory_usage(deep=True)
        elif isinstance(result, np.ndarray):
            size_bytes = result.nbytes
        else:
            size_bytes = 1024  # 默认1KB
        
        self._hash_cache.put(cache_key, result, size_bytes)
    
    def get_data_signature(self, data: pd.DataFrame) -> str:
        """生成数据签名"""
        # 使用形状、列名、索引范围等生成签名
        shape_str = f"{data.shape[0]}x{data.shape[1]}"
        cols_hash = hashlib.md5(str(sorted(data.columns)).encode()).hexdigest()[:8]
        
        if len(data.index) > 0:
            index_range = f"{data.index[0]}_{data.index[-1]}"
        else:
            index_range = "empty"
        
        return f"{shape_str}_{cols_hash}_{index_range}"
    
    def get_stats(self) -> Dict:
        """获取缓存统计"""
        return {
            'expression_count': len(self._expression_map),
            'cache_stats': self._hash_cache.get_stats()
        }


class HotFactorCache:
    """热点因子缓存，自动识别和缓存高频访问的因子"""
    
    def __init__(self, threshold: int = 5, max_factors: int = 100):
        self.threshold = threshold
        self.max_factors = max_factors
        self._access_counts = defaultdict(int)
        self._hot_factors = LRUCache(max_size=max_factors, ttl_seconds=3600)
        self._lock = threading.RLock()
    
    def record_access(self, factor_key: str):
        """记录因子访问"""
        with self._lock:
            self._access_counts[factor_key] += 1
            
            # 如果达到热点阈值，添加到热点缓存
            if (self._access_counts[factor_key] >= self.threshold and 
                not self._is_cached(factor_key)):
                self._promote_to_hot_cache(factor_key)
    
    def get_hot_factor(self, factor_key: str) -> Optional[Any]:
        """获取热点因子数据"""
        entry = self._hot_factors.get(factor_key)
        return entry.data if entry else None
    
    def cache_hot_factor(self, factor_key: str, data: Any):
        """缓存热点因子数据"""
        if isinstance(data, pd.DataFrame):
            size_bytes = data.memory_usage(deep=True).sum()
        elif isinstance(data, pd.Series):
            size_bytes = data.memory_usage(deep=True)
        else:
            size_bytes = 1024
        
        self._hot_factors.put(factor_key, data, size_bytes)
    
    def _is_cached(self, factor_key: str) -> bool:
        """检查因子是否已缓存"""
        return self._hot_factors.get(factor_key) is not None
    
    def _promote_to_hot_cache(self, factor_key: str):
        """提升因子到热点缓存"""
        # 这里可以添加从磁盘加载因子数据的逻辑
        # 暂时只记录提升动作
        print(f"🔥 因子 {factor_key} 被提升为热点因子")
    
    def get_hot_factors_list(self) -> List[str]:
        """获取当前热点因子列表"""
        with self._lock:
            return [key for key in self._access_counts.keys() 
                   if self._access_counts[key] >= self.threshold]
    
    def get_stats(self) -> Dict:
        """获取热点缓存统计"""
        with self._lock:
            hot_count = len(self.get_hot_factors_list())
            return {
                'total_factors_accessed': len(self._access_counts),
                'hot_factors_count': hot_count,
                'cache_stats': self._hot_factors.get_stats(),
                'top_factors': sorted(
                    self._access_counts.items(), 
                    key=lambda x: x[1], 
                    reverse=True
                )[:10]  # 前10个最热的因子
            }


class PerformanceCache:
    """高性能缓存系统主类"""
    
    def __init__(self):
        self.config = get_performance_config()
        
        # 初始化各级缓存
        self.memory_cache = LRUCache(
            max_size=1000,
            ttl_seconds=self.config.cache.cache_ttl_seconds
        )
        
        self.expression_cache = ExpressionHashCache(
            max_size=self.config.cache.max_expression_cache
        )
        
        self.hot_factor_cache = HotFactorCache(
            threshold=self.config.cache.hot_factor_threshold,
            max_factors=self.config.cache.max_hot_factors
        )
        
        # 性能监控
        self._performance_stats = {
            'total_memory_usage_mb': 0,
            'cache_efficiency_score': 0.0,
            'last_cleanup_time': time.time()
        }
        
        # 启动后台清理线程
        if self.config.cache.enable_lru_cleanup:
            self._start_cleanup_thread()
    
    def get_factor_data(self, batch_id: str, factor_names: List[str], 
                       pipeline_step: str = 'L2') -> Optional[pd.DataFrame]:
        """获取因子数据（优先从缓存）"""
        cache_key = f"{batch_id}_{pipeline_step}_{hash(tuple(sorted(factor_names)))}"
        
        # 1. 检查内存缓存
        if self.config.cache.enable_memory_cache:
            entry = self.memory_cache.get(cache_key)
            if entry:
                return entry.data
        
        # 2. 检查热点因子缓存
        if self.config.cache.enable_hot_factor_cache:
            for factor_name in factor_names:
                self.hot_factor_cache.record_access(f"{batch_id}_{factor_name}")
                
            hot_data = self.hot_factor_cache.get_hot_factor(cache_key)
            if hot_data is not None:
                return hot_data
        
        # 3. 如果缓存未命中，返回None让调用者从磁盘加载
        return None
    
    def cache_factor_data(self, batch_id: str, factor_names: List[str],
                         pipeline_step: str, data: pd.DataFrame):
        """缓存因子数据"""
        cache_key = f"{batch_id}_{pipeline_step}_{hash(tuple(sorted(factor_names)))}"
        
        # 计算数据大小
        size_bytes = data.memory_usage(deep=True).sum()
        
        # 检查内存使用情况
        current_memory_mb = self._get_current_memory_usage()
        max_memory_mb = self.config.cache.max_memory_cache_size_mb
        
        if current_memory_mb + size_bytes / (1024*1024) < max_memory_mb:
            # 内存充足，添加到内存缓存
            if self.config.cache.enable_memory_cache:
                self.memory_cache.put(cache_key, data, size_bytes)
            
            # 如果是热点因子，也添加到热点缓存
            if self.config.cache.enable_hot_factor_cache:
                hot_factors = self.hot_factor_cache.get_hot_factors_list()
                if any(f"{batch_id}_{name}" in hot_factors for name in factor_names):
                    self.hot_factor_cache.cache_hot_factor(cache_key, data)
    
    def cache_expression_result(self, expression: str, base_data: pd.DataFrame, 
                               result: Any):
        """缓存表达式计算结果"""
        if not self.config.cache.enable_expression_hash:
            return
        
        data_signature = self.expression_cache.get_data_signature(base_data)
        self.expression_cache.cache_result(expression, data_signature, result)
    
    def get_cached_expression_result(self, expression: str, 
                                   base_data: pd.DataFrame) -> Optional[Any]:
        """获取缓存的表达式计算结果"""
        if not self.config.cache.enable_expression_hash:
            return None
        
        data_signature = self.expression_cache.get_data_signature(base_data)
        return self.expression_cache.get_cached_result(expression, data_signature)
    
    def _get_current_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / (1024 * 1024)
        except:
            return 0.0
    
    def _start_cleanup_thread(self):
        """启动后台清理线程"""
        def cleanup_worker():
            while True:
                time.sleep(self.config.cache.cleanup_interval_seconds)
                self._perform_cleanup()
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
    
    def _perform_cleanup(self):
        """执行缓存清理"""
        current_time = time.time()
        self._performance_stats['last_cleanup_time'] = current_time
        
        # 清理过期的缓存项（LRU缓存会自动处理TTL）
        
        # 更新内存使用统计
        self._performance_stats['total_memory_usage_mb'] = self._get_current_memory_usage()
        
        # 计算缓存效率得分
        memory_stats = self.memory_cache.get_stats()
        hot_stats = self.hot_factor_cache.get_stats()
        
        if memory_stats['hits'] + memory_stats['misses'] > 0:
            cache_efficiency = (
                memory_stats['hit_rate'] * 0.6 +  # 内存缓存命中率权重60%
                (hot_stats['hot_factors_count'] / self.config.cache.max_hot_factors) * 0.4  # 热点覆盖率权重40%
            )
            self._performance_stats['cache_efficiency_score'] = cache_efficiency
    
    def get_comprehensive_stats(self) -> Dict:
        """获取综合缓存统计"""
        return {
            'memory_cache': self.memory_cache.get_stats(),
            'expression_cache': self.expression_cache.get_stats(),
            'hot_factor_cache': self.hot_factor_cache.get_stats(),
            'performance': self._performance_stats,
            'config_summary': self.config.get_config_summary()['cache']
        }
    
    def optimize_cache_sizes(self):
        """根据使用情况动态优化缓存大小"""
        stats = self.get_comprehensive_stats()
        
        # 如果内存缓存命中率低，减少缓存大小
        memory_hit_rate = stats['memory_cache']['hit_rate']
        if memory_hit_rate < 0.3:
            new_size = max(500, int(self.memory_cache.max_size * 0.8))
            self.memory_cache.max_size = new_size
            print(f"🔧 调整内存缓存大小到 {new_size} (命中率: {memory_hit_rate:.2f})")
        
        # 如果热点因子很少，调整阈值
        hot_count = stats['hot_factor_cache']['hot_factors_count']
        if hot_count < 10:
            new_threshold = max(3, self.hot_factor_cache.threshold - 1)
            self.hot_factor_cache.threshold = new_threshold
            print(f"🔧 调整热点阈值到 {new_threshold} (当前热点: {hot_count})")
    
    def clear_all_caches(self):
        """清空所有缓存"""
        self.memory_cache.clear()
        self.hot_factor_cache._hot_factors.clear()
        self.expression_cache._hash_cache.clear()
        print("🧹 所有缓存已清空")


# 全局缓存实例
_global_cache = None
_cache_lock = threading.Lock()


def get_performance_cache() -> PerformanceCache:
    """获取全局性能缓存实例（单例模式）"""
    global _global_cache
    
    if _global_cache is None:
        with _cache_lock:
            if _global_cache is None:
                _global_cache = PerformanceCache()
    
    return _global_cache


if __name__ == "__main__":
    # 缓存系统演示
    cache = get_performance_cache()
    
    print("🚀 高性能缓存系统演示")
    print("=" * 50)
    
    # 模拟因子数据
    test_data = pd.DataFrame({
        'factor1': np.random.randn(100),
        'factor2': np.random.randn(100),
        'factor3': np.random.randn(100)
    })
    
    # 测试缓存功能
    print("\n📊 测试缓存功能:")
    
    # 缓存数据
    cache.cache_factor_data('test_batch', ['factor1', 'factor2'], 'L2', test_data)
    print("✅ 数据已缓存")
    
    # 从缓存读取
    cached_data = cache.get_factor_data('test_batch', ['factor1', 'factor2'], 'L2')
    if cached_data is not None:
        print("✅ 从缓存成功读取数据")
    else:
        print("❌ 缓存未命中")
    
    # 表达式缓存测试
    print("\n🧮 测试表达式缓存:")
    expression = "factor1 + factor2 * 2"
    
    # 第一次计算
    result1 = test_data.eval(expression)
    cache.cache_expression_result(expression, test_data, result1)
    print("✅ 表达式结果已缓存")
    
    # 从缓存获取
    cached_result = cache.get_cached_expression_result(expression, test_data)
    if cached_result is not None:
        print("✅ 表达式结果缓存命中")
    else:
        print("❌ 表达式缓存未命中")
    
    # 显示统计信息
    print("\n📈 缓存统计信息:")
    stats = cache.get_comprehensive_stats()
    
    for cache_type, cache_stats in stats.items():
        if cache_type != 'config_summary':
            print(f"\n{cache_type.upper()}:")
            if isinstance(cache_stats, dict):
                for key, value in cache_stats.items():
                    if isinstance(value, float):
                        print(f"  {key}: {value:.3f}")
                    else:
                        print(f"  {key}: {value}")
    
    print("\n🔧 执行缓存优化:")
    cache.optimize_cache_sizes() 