#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键批次管理
管理FactorZoo中的批次数据
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from factorzoo.utils.batch_tools import BatchTools

def main():
    print("🛠️ FactorZoo批次管理")
    print("=" * 50)
    
    tools = BatchTools()
    
    # 显示总体统计
    all_batches = tools.manager.get_available_batches()
    
    if not all_batches:
        print("❌ 暂无批次数据")
        return
    
    # 统计品种分布
    symbol_stats = {}
    for batch_id in all_batches:
        try:
            parts = batch_id.split('_')
            if len(parts) >= 2:
                symbol = parts[1]
                if symbol not in symbol_stats:
                    symbol_stats[symbol] = 0
                symbol_stats[symbol] += 1
        except:
            pass
    
    print(f"📊 当前状态: {len(all_batches)} 个批次, {len(symbol_stats)} 个品种")
    print("品种分布:", dict(sorted(symbol_stats.items(), key=lambda x: x[1], reverse=True)))
    print()
    
    # 操作菜单
    while True:
        print("💡 管理选项:")
        print("1. 查看所有批次概览")
        print("2. 按品种查看批次")
        print("3. 按阶段查看批次")
        print("4. 查看批次详细文件")
        print("5. 清理旧批次数据")
        print("6. 存储空间分析")
        print("0. 退出")
        
        try:
            choice = input("\n请选择操作 (0-6): ").strip()
            print()
            
            if choice == '0':
                print("👋 退出管理")
                break
            
            elif choice == '1':
                print("选择显示模式:")
                print("1. 简要模式")
                print("2. 详细模式")
                mode_choice = input("请选择 (1-2): ").strip()
                detailed = mode_choice == '2'
                tools.list_all_batches(detailed=detailed)
            
            elif choice == '2':
                if symbol_stats:
                    print("可用品种:")
                    for i, symbol in enumerate(sorted(symbol_stats.keys()), 1):
                        print(f"{i}. {symbol} ({symbol_stats[symbol]} 个批次)")
                    
                    symbol = input("\n请输入品种代码: ").strip().upper()
                    if symbol:
                        tools.list_by_symbol(symbol)
                else:
                    print("❌ 无可用品种")
            
            elif choice == '3':
                print("可用阶段: L0, L1, L2, L3")
                stage = input("请输入阶段名: ").strip()
                if stage:
                    tools.list_by_stage(stage)
            
            elif choice == '4':
                batch_id = input("请输入批次ID: ").strip()
                if batch_id:
                    file_list = tools.show_batch_files(batch_id)
                    
                    if file_list:
                        print("\n💡 查看文件内容:")
                        print("输入文件编号查看内容，输入0退出")
                        
                        while True:
                            try:
                                file_choice = input(f"\n请选择文件 (1-{len(file_list)}, 0=退出): ").strip()
                                
                                if file_choice == '0':
                                    break
                                
                                file_idx = int(file_choice) - 1
                                if 0 <= file_idx < len(file_list):
                                    # 询问显示行数
                                    rows_input = input("显示多少行? (默认10行): ").strip()
                                    head_rows = int(rows_input) if rows_input.isdigit() else 10
                                    
                                    # 显示文件内容
                                    result = tools.show_file_content(file_list[file_idx], head_rows)
                                    
                                    # 如果是DataFrame，提供更多选项
                                    if hasattr(result, 'shape'):  # 是DataFrame
                                        print("\n🔧 更多操作:")
                                        print("1. 查看更多行")
                                        print("2. 查看列信息")
                                        print("3. 查看数据范围")
                                        print("0. 返回文件列表")
                                        
                                        sub_choice = input("请选择操作 (0-3): ").strip()
                                        
                                        if sub_choice == '1':
                                            more_rows = input("显示多少行? ").strip()
                                            if more_rows.isdigit():
                                                print(f"\n📋 数据内容 (前{more_rows}行):")
                                                print("-" * 80)
                                                print(result.head(int(more_rows)).to_string())
                                        
                                        elif sub_choice == '2':
                                            print(f"\n📊 详细列信息:")
                                            print("-" * 50)
                                            print(result.info())
                                        
                                        elif sub_choice == '3':
                                            print(f"\n📈 数据范围:")
                                            print("-" * 50)
                                            numeric_cols = result.select_dtypes(include=['number']).columns
                                            if len(numeric_cols) > 0:
                                                print("数值列最小值、最大值:")
                                                for col in numeric_cols[:8]:  # 显示前8列
                                                    min_val = result[col].min()
                                                    max_val = result[col].max()
                                                    print(f"  {col:<20}: [{min_val:>10.4f}, {max_val:>10.4f}]")
                                            else:
                                                print("无数值列")
                                    
                                else:
                                    print("❌ 无效的文件编号")
                                
                            except ValueError:
                                print("❌ 请输入有效数字")
                            except KeyboardInterrupt:
                                print("\n退出文件查看")
                                break
                            except Exception as e:
                                print(f"❌ 操作失败: {str(e)}")
            
            elif choice == '5':
                print("清理旧批次数据")
                print("⚠️  注意: 此操作不可逆!")
                
                days = input("清理多少天前的数据? (默认30天): ").strip()
                try:
                    days = int(days) if days else 30
                except:
                    days = 30
                
                # 先预览
                print(f"\n🔍 预览清理 {days} 天前的数据:")
                tools.cleanup_old_batches(days=days, dry_run=True)
                
                # 确认执行
                confirm = input("\n确认执行清理? (输入 'YES' 确认): ").strip()
                if confirm == 'YES':
                    tools.cleanup_old_batches(days=days, dry_run=False)
                else:
                    print("❌ 取消清理操作")
            
            elif choice == '6':
                # 存储空间分析
                print("📊 存储空间分析")
                print("-" * 40)
                
                batch_dir = Path(tools.manager.config['by_batch_dir'])
                if batch_dir.exists():
                    total_size = 0
                    batch_sizes = {}
                    
                    for batch_id in all_batches[:10]:  # 分析前10个批次
                        batch_path = batch_dir / batch_id
                        if batch_path.exists():
                            size = sum(f.stat().st_size for f in batch_path.rglob('*') if f.is_file())
                            size_mb = size / 1024 / 1024
                            batch_sizes[batch_id] = size_mb
                            total_size += size_mb
                    
                    print(f"总存储空间: {total_size:.2f} MB")
                    print("\n批次大小排行 (前10个):")
                    for batch_id, size in sorted(batch_sizes.items(), key=lambda x: x[1], reverse=True):
                        print(f"  {batch_id}: {size:.2f} MB")
                else:
                    print("❌ 批次目录不存在")
            
            else:
                print("❌ 无效选择")
            
            input("\n按回车键继续...")
            print("\n" + "="*50)
            
        except KeyboardInterrupt:
            print("\n👋 退出管理")
            break
        except Exception as e:
            print(f"❌ 操作失败: {str(e)}")
            input("按回车键继续...")

if __name__ == "__main__":
    main() 