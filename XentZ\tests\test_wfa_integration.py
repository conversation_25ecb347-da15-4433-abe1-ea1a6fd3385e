#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WFA验证系统集成测试
测试与现有系统的兼容性和集成能力
"""

import unittest
import numpy as np
import pandas as pd
import sys
import os
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from factor.wfa_eval_utils import (
    WFAParams, WFAResult, run_wfa_validation, 
    calculate_wfa_metrics_with_reuse, check_wfa_criteria
)


class TestSystemIntegration(unittest.TestCase):
    """系统集成测试"""
    
    def setUp(self):
        """设置测试数据"""
        np.random.seed(42)
        
        # 创建模拟数据
        n_days = 1200
        dates = pd.date_range('2020-01-01', periods=n_days, freq='D')
        
        # 创建有预测能力的因子
        noise = np.random.randn(n_days)
        trend = np.cumsum(np.random.randn(n_days) * 0.01)
        self.factor_data = pd.Series(noise + trend * 0.1, index=dates, name='test_factor')
        
        # 创建价格数据
        price_changes = np.random.randn(n_days) * 0.02 + self.factor_data.shift(1) * 0.001
        self.price_data = pd.Series(100 * np.exp(np.cumsum(price_changes)), index=dates, name='price')
        
        self.wfa_params = WFAParams()
    
    def test_factor_utils_integration(self):
        """测试与factor_utils的集成"""
        try:
            from factor.factor_perf import rank_1d, fast_spearman
            
            # 测试rank_1d函数集成
            test_data = np.random.randn(100).astype(np.float32)
            ranks = rank_1d(test_data)
            self.assertEqual(len(ranks), len(test_data))
            
            # 测试在WFA中的使用
            result = run_wfa_validation(self.factor_data, self.price_data, self.wfa_params)
            self.assertIsInstance(result, WFAResult)
            
            print("✅ factor_utils集成测试通过")
            
        except ImportError as e:
            print(f"⚠️ factor_utils导入失败: {e}")
            self.skipTest("factor_utils不可用")
    
    def test_original_library_integration(self):
        """测试与原库绩效计算的集成"""
        # 创建测试PnL序列
        pnl_series = pd.Series(np.random.normal(0.001, 0.02, 252))
        
        # 测试绩效计算集成
        metrics = calculate_wfa_metrics_with_reuse(pnl_series)
        
        # 验证基础指标存在
        required_metrics = ['sharpe_ratio', 'max_drawdown', 'win_rate']
        for metric in required_metrics:
            self.assertIn(metric, metrics)
        
        print("✅ 原库绩效计算集成测试通过")
    
    def test_config_system_compatibility(self):
        """测试配置系统兼容性"""
        try:
            # 测试dynaconf配置加载
            from dynaconf import Dynaconf
            
            # 创建临时配置
            temp_config = {
                'wfa': {
                    'training_window': 500,
                    'testing_window': 50,
                    'tanh_k': 3.0
                },
                'criteria': {
                    'min_sharpe': 0.5,
                    'max_mdd': 0.30
                }
            }
            
            # 验证WFAParams可以从配置创建
            wfa_params = WFAParams(
                training_window=temp_config['wfa']['training_window'],
                testing_window=temp_config['wfa']['testing_window'],
                tanh_k=temp_config['wfa']['tanh_k']
            )
            
            self.assertEqual(wfa_params.training_window, 500)
            self.assertEqual(wfa_params.tanh_k, 3.0)
            
            print("✅ 配置系统兼容性测试通过")
            
        except ImportError as e:
            print(f"⚠️ dynaconf导入失败: {e}")
            self.skipTest("dynaconf不可用")
    
    def test_pandas_compatibility(self):
        """测试pandas兼容性"""
        # 测试不同pandas数据类型
        
        # 测试Series with different dtypes
        int_factor = pd.Series(np.random.randint(-100, 100, 1000), name='int_factor')
        float_factor = pd.Series(np.random.randn(1000), name='float_factor')
        
        # 测试不同索引类型
        date_index = pd.date_range('2020-01-01', periods=1000, freq='D')
        int_index = pd.RangeIndex(1000)
        
        factor_date = pd.Series(np.random.randn(1000), index=date_index)
        factor_int = pd.Series(np.random.randn(1000), index=int_index)
        
        # 验证都能正常处理
        for factor in [int_factor, float_factor, factor_date, factor_int]:
            try:
                price = pd.Series(np.random.randn(1000), index=factor.index)
                wfa_params = WFAParams(training_window=200, testing_window=50)
                result = run_wfa_validation(factor, price, wfa_params)
                self.assertIsInstance(result, WFAResult)
            except Exception as e:
                self.fail(f"pandas兼容性测试失败: {e}")
        
        print("✅ pandas兼容性测试通过")
    
    def test_numpy_compatibility(self):
        """测试numpy兼容性"""
        # 测试不同numpy数据类型
        dtypes = [np.float32, np.float64, np.int32, np.int64]
        
        for dtype in dtypes:
            try:
                factor_array = np.random.randn(1000).astype(dtype)
                factor_series = pd.Series(factor_array, name=f'factor_{dtype.__name__}')
                price_series = pd.Series(np.random.randn(1000), name='price')
                
                wfa_params = WFAParams(training_window=200, testing_window=50)
                result = run_wfa_validation(factor_series, price_series, wfa_params)
                self.assertIsInstance(result, WFAResult)
                
            except Exception as e:
                self.fail(f"numpy {dtype} 兼容性测试失败: {e}")
        
        print("✅ numpy兼容性测试通过")


class TestDataFormatCompatibility(unittest.TestCase):
    """数据格式兼容性测试"""
    
    def test_missing_data_handling(self):
        """测试缺失数据处理"""
        np.random.seed(42)
        
        # 创建包含缺失值的数据
        n = 1000
        factor_data = pd.Series(np.random.randn(n))
        price_data = pd.Series(np.random.randn(n))
        
        # 随机插入缺失值
        missing_indices = np.random.choice(n, size=50, replace=False)
        factor_data.iloc[missing_indices] = np.nan
        
        missing_indices = np.random.choice(n, size=30, replace=False)
        price_data.iloc[missing_indices] = np.nan
        
        # 验证能正常处理
        wfa_params = WFAParams(training_window=200, testing_window=50)
        result = run_wfa_validation(factor_data, price_data, wfa_params)
        
        self.assertIsInstance(result, WFAResult)
        self.assertGreater(len(result.pnl_series), 0)
        
        print("✅ 缺失数据处理测试通过")
    
    def test_extreme_values_handling(self):
        """测试极端值处理"""
        np.random.seed(42)
        
        # 创建包含极端值的数据
        factor_data = pd.Series(np.random.randn(1000))
        price_data = pd.Series(np.random.randn(1000))
        
        # 插入极端值
        factor_data.iloc[100] = 1000  # 极大值
        factor_data.iloc[200] = -1000  # 极小值
        price_data.iloc[150] = 100  # 极大价格变化
        
        # 验证能正常处理
        wfa_params = WFAParams(training_window=200, testing_window=50)
        result = run_wfa_validation(factor_data, price_data, wfa_params)
        
        self.assertIsInstance(result, WFAResult)
        
        # 验证结果指标在合理范围内
        for key, value in result.metrics.items():
            self.assertTrue(np.isfinite(value), f"指标 {key} 值异常: {value}")
        
        print("✅ 极端值处理测试通过")
    
    def test_different_frequencies(self):
        """测试不同频率数据"""
        np.random.seed(42)
        
        # 测试不同频率的时间序列
        frequencies = ['D', 'B', 'W', 'M']
        
        for freq in frequencies:
            try:
                # 创建对应频率的数据
                if freq == 'M':
                    n_periods = 60  # 5年月度数据
                    min_window = 24
                    test_window = 6
                elif freq == 'W':
                    n_periods = 260  # 5年周度数据
                    min_window = 100
                    test_window = 20
                else:
                    n_periods = 1300  # 5年日度数据
                    min_window = 500
                    test_window = 50
                
                dates = pd.date_range('2020-01-01', periods=n_periods, freq=freq)
                factor_data = pd.Series(np.random.randn(n_periods), index=dates)
                price_data = pd.Series(np.random.randn(n_periods), index=dates)
                
                wfa_params = WFAParams(
                    training_window=min_window,
                    testing_window=test_window,
                    step_size=test_window
                )
                
                result = run_wfa_validation(factor_data, price_data, wfa_params)
                self.assertIsInstance(result, WFAResult)
                
            except Exception as e:
                print(f"⚠️ 频率 {freq} 测试失败: {e}")
                continue
        
        print("✅ 不同频率数据测试通过")


class TestErrorHandling(unittest.TestCase):
    """错误处理测试"""
    
    def test_invalid_input_handling(self):
        """测试无效输入处理"""
        wfa_params = WFAParams()
        
        # 测试各种无效输入
        invalid_inputs = [
            (None, pd.Series([1, 2, 3])),  # None因子
            (pd.Series([1, 2, 3]), None),  # None价格
            (pd.Series([]), pd.Series([1, 2, 3])),  # 空因子
            (pd.Series([1, 2, 3]), pd.Series([])),  # 空价格
        ]
        
        for factor, price in invalid_inputs:
            with self.assertRaises((ValueError, TypeError)):
                run_wfa_validation(factor, price, wfa_params)
        
        print("✅ 无效输入处理测试通过")
    
    def test_insufficient_data_handling(self):
        """测试数据不足处理"""
        # 创建数据长度不足的情况
        short_factor = pd.Series(np.random.randn(100))
        short_price = pd.Series(np.random.randn(100))
        
        wfa_params = WFAParams()  # 默认需要810个数据点
        
        with self.assertRaises(ValueError):
            run_wfa_validation(short_factor, short_price, wfa_params)
        
        print("✅ 数据不足处理测试通过")
    
    def test_criteria_edge_cases(self):
        """测试通过标准边界情况"""
        # 测试边界值情况
        edge_metrics = {
            'sharpe_ratio': 0.5,  # 刚好达到最小值
            'max_drawdown': 0.30,  # 刚好达到最大值
            'win_rate': 0.55,  # 刚好达到最小值
        }
        
        criteria = {
            'min_sharpe': 0.5,
            'max_mdd': 0.30,
            'min_win_rate': 0.55
        }
        
        status, reasons = check_wfa_criteria(edge_metrics, criteria)
        self.assertEqual(status, 'L3_PASSED')
        self.assertEqual(len(reasons), 0)
        
        # 测试刚好不达标的情况
        failing_metrics = {
            'sharpe_ratio': 0.499,  # 略低于最小值
            'max_drawdown': 0.301,  # 略高于最大值
            'win_rate': 0.549,  # 略低于最小值
        }
        
        status, reasons = check_wfa_criteria(failing_metrics, criteria)
        self.assertEqual(status, 'L3_FAILED')
        self.assertEqual(len(reasons), 3)
        
        print("✅ 通过标准边界情况测试通过")


class TestConcurrencyCompatibility(unittest.TestCase):
    """并发兼容性测试"""
    
    def test_thread_safety(self):
        """测试线程安全性"""
        import threading
        import time
        
        np.random.seed(42)
        
        # 创建测试数据
        factor_data = pd.Series(np.random.randn(1000))
        price_data = pd.Series(np.random.randn(1000))
        wfa_params = WFAParams(training_window=200, testing_window=50)
        
        results = []
        errors = []
        
        def run_wfa_test():
            try:
                result = run_wfa_validation(factor_data, price_data, wfa_params)
                results.append(result)
            except Exception as e:
                errors.append(e)
        
        # 创建多个线程同时运行
        threads = []
        for i in range(3):
            thread = threading.Thread(target=run_wfa_test)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        self.assertEqual(len(errors), 0, f"线程安全测试出现错误: {errors}")
        self.assertEqual(len(results), 3, "应该有3个结果")
        
        # 验证结果一致性（相同输入应该产生相同结果）
        first_result = results[0]
        for result in results[1:]:
            np.testing.assert_array_almost_equal(
                first_result.pnl_series.values, 
                result.pnl_series.values,
                decimal=10
            )
        
        print("✅ 线程安全性测试通过")


if __name__ == '__main__':
    print("=" * 60)
    print("WFA验证系统集成测试")
    print("=" * 60)
    
    # 运行集成测试
    unittest.main(verbosity=2)
