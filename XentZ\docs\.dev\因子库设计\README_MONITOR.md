# FactorZoo 监控系统使用指南

## 概述

FactorZoo监控系统采用分层设计：
- **基础层（common）**：通用的ResMonitor和MonitorContext
- **专业层（factor）**：因子专用的FactorMonitorContext和数据管理

这种设计既保持了监控功能的通用性，又提供了因子领域的专业功能。

## 核心功能

### 1. 基础监控（common层）
- ⏱️ **时间消耗**：精确记录计算耗时
- 🧠 **CPU使用**：监控CPU时间（用户时间 + 系统时间）
- 💾 **内存使用**：跟踪内存消耗和增量
- 📊 **系统信息**：获取系统资源状态

### 2. 专业监控（factor层）
- 🔄 **自动记录**：性能数据自动保存到FactorZoo数据库
- 📊 **统计分析**：提供因子级别和批次级别的性能统计
- 🔗 **无缝集成**：自动更新到FactorZoo主表

## 主要组件

### ResMonitor（基础监控器）
来自`common.cls_base`，提供通用的监控功能

```python
from common.cls_base import ResMonitor

monitor = ResMonitor()

# 时间监控
monitor.start_timer('calculation')
# ... 执行计算
elapsed_time = monitor.end_timer('calculation')  # 返回 "HH:MM:SS" 格式

# CPU监控
monitor.start_timer_cpu('cpu_task')
# ... 执行计算
cpu_usage = monitor.end_timer_cpu('cpu_task')  # 返回 "X.XXs user, Y.YYs system"

# 内存监控
monitor.start_memory_monitor('memory_task')
# ... 执行计算
memory_delta = monitor.end_memory_monitor('memory_task')  # 返回增量MB

# 系统信息
system_info = monitor.get_system_info()
```

### MonitorContext（通用上下文管理器）
来自`common.cls_base`，适用于所有类型的监控需求

```python
from common.cls_base import MonitorContext

# 通用监控
with MonitorContext('data_processing') as ctx:
    # 执行任意计算任务
    result = process_data(data)
    
    # 监控结果自动记录在ctx属性中
    print(f"耗时: {ctx.elapsed_time}")
    print(f"CPU: {ctx.cpu_usage}")
    print(f"内存增量: {ctx.memory_delta}MB")
```

### FactorMonitorContext（因子专用上下文管理器）
来自`factor.factorzoo_monitor`，专门为因子计算优化

```python
from factor.factorzoo_monitor import FactorMonitorContext

# 因子专用监控（推荐使用方式）
with FactorMonitorContext(
    factor_id="F_001", 
    operation_type='calculation',
    batch_id="BATCH_001",
    data_size=10000
) as ctx:
    # 执行因子计算
    result = calculate_factor(data)
    
    # 可选：添加自定义备注
    ctx.run_data.notes = "特殊处理：移除异常值"
    
    # 性能数据自动保存到FactorZoo数据库
    # 并更新factors表的性能字段
```

### FactorZooRunManager（因子数据管理器）
专门处理因子性能数据的存储和查询

```python
from factor.factorzoo_monitor import factorzoo_run_manager

# 查询因子性能统计
stats = factorzoo_run_manager.get_factor_performance_stats('F_001')
print(f"平均耗时: {stats.get('avg_time_ms')}ms")
print(f"最大内存: {stats.get('max_memory_mb')}MB")
print(f"运行次数: {stats.get('total_runs')}")

# 查询批次性能汇总
batch_stats = factorzoo_run_manager.get_batch_performance_summary('BATCH_001')
```

## 典型使用场景

### 场景1：通用任务监控

```python
from common.cls_base import MonitorContext

def data_preprocessing(data):
    with MonitorContext('data_preprocessing') as ctx:
        # 数据清洗
        cleaned_data = clean_data(data)
        
        # 特征工程
        features = extract_features(cleaned_data)
        
        return features
```

### 场景2：因子计算监控

```python
from factor.factorzoo_monitor import FactorMonitorContext

def calculate_momentum_factor(data, factor_id):
    with FactorMonitorContext(factor_id, 'calculation') as ctx:
        # 计算动量因子
        momentum = data['close'] / data['close'].shift(20) - 1
        ctx.run_data.notes = "20日动量因子"
        return momentum
```

### 场景3：批量任务监控

```python
from common.cls_base import ResMonitor
from factor.factorzoo_monitor import FactorMonitorContext

def batch_calculate_factors(data, expressions, batch_id):
    # 批次级别监控（通用）
    with MonitorContext('batch_total') as batch_ctx:
        results = {}
        
        for i, expr in enumerate(expressions):
            factor_id = f"F_{batch_id}_{i:03d}"
            
            # 单因子监控（专业）
            with FactorMonitorContext(factor_id, 'calculation', batch_id) as factor_ctx:
                result = eval_expression(expr, data)
                results[factor_id] = result
        
        print(f"批次总耗时: {batch_ctx.elapsed_time}")
        return results
```

### 场景4：混合监控策略

```python
from common.cls_base import ResMonitor, MonitorContext
from factor.factorzoo_monitor import FactorMonitorContext

class HybridCalculator:
    def __init__(self):
        self.monitor = ResMonitor()  # 全局监控器
    
    def complex_calculation(self, data, factor_id):
        # 使用全局监控器进行整体计时
        self.monitor.start_timer('total_process')
        
        try:
            # 数据预处理（通用监控）
            with MonitorContext('preprocessing', self.monitor) as prep_ctx:
                processed_data = self.preprocess(data)
            
            # 因子计算（专业监控）
            with FactorMonitorContext(factor_id, 'calculation', monitor=self.monitor) as calc_ctx:
                factor_values = self.calculate_factor(processed_data)
                calc_ctx.run_data.notes = f"预处理耗时: {prep_ctx.elapsed_time}"
            
            return factor_values
            
        finally:
            total_time = self.monitor.end_timer('total_process')
            print(f"整体流程耗时: {total_time}")
```

## 架构优势

### 🏗️ 分层设计
- **通用性**：基础监控功能可用于任何计算任务
- **专业性**：因子专用功能针对量化投资需求优化
- **可扩展**：其他模块可以基于基础监控构建专业功能

### 🔄 兼容性
- **向后兼容**：保留原有的`FactorZooResMonitor`别名
- **渐进迁移**：可以逐步从专用监控迁移到分层监控
- **灵活组合**：可以混合使用通用和专业监控

### 📊 功能完整性
- **基础监控**：时间、CPU、内存、系统信息
- **专业存储**：FactorZoo数据库集成
- **统计分析**：性能趋势和资源使用分析
- **自动更新**：主表性能字段同步

## 最佳实践

1. **分层使用**：
   - 通用计算任务使用`MonitorContext`
   - 因子计算使用`FactorMonitorContext`
   - 批量任务使用混合策略

2. **监控器复用**：
   - 使用共享的`ResMonitor`实例避免重复初始化
   - 全局监控器：`from factor.factorzoo_monitor import factorzoo_monitor`

3. **合理分类**：
   - 正确设置`operation_type`（calculation/evaluation/optimization）
   - 添加有意义的`notes`记录重要信息

4. **性能分析**：
   - 定期查看因子性能统计
   - 利用批次汇总优化流程
   - 基于资源强度调整计算策略

## 迁移指南

### 从原有监控迁移

```python
# 旧方式（仍然支持）
from factor.factorzoo_monitor import FactorZooResMonitor, FactorZooMonitorContext

# 新方式（推荐）
from common.cls_base import ResMonitor, MonitorContext
from factor.factorzoo_monitor import FactorMonitorContext

# 直接替换
FactorZooResMonitor -> ResMonitor
FactorZooMonitorContext -> FactorMonitorContext
```

### 导入建议

```python
# 基础功能
from common.cls_base import ResMonitor, MonitorContext

# 因子专用功能
from factor.factorzoo_monitor import (
    FactorMonitorContext,
    FactorZooRunManager,
    factorzoo_monitor,
    factorzoo_run_manager
)
```

这样就完成了监控系统的架构优化，既保持了通用性，又提供了专业功能！🚀 