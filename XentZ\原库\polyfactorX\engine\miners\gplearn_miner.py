from typing import Tuple
import numpy as np
import pandas as pd
from scipy.stats import skew,kurtosis
from datetime import datetime
from typing import Optional
from sklearnex import patch_sklearn # intel加速
from sklearn.linear_model import LinearRegression
# from gplearn.genetic import SymbolicTransformer
from datafeed.mining.gplearn import SymbolicTransformer
from task_defs.task_fctsmine import TaskFctsMining
from jzal_pro.cls_common import BaseObj
from config import DATA_DIR_FACTOR
from enums import NORM_PARAMS_POS, METRIC_PARAMS_DEFAULT, TRADE_ON_OPEN, GP_PARAMS
from datafeed.mining.gplearn import _fitness_map
import jzal_pro.utils.expr_utils as eu
import jzal_pro.utils.perf_utils as pu

patch_sklearn() # intel加速

class FctsGPMiner(BaseObj):
    ''' 适用: 单品种+单周期 的多因子 挖掘+筛选 '''
    def __init__(self, symbol:str, x_train: pd.DataFrame, y_train: pd.Series, y_raw:pd.Series, 
                 task: TaskFctsMining, base_features:list):
        self.symbol = symbol
        self.x_train = x_train
        self.y_train = y_train
        self.y_raw = y_raw
        self.func_set = task.func_set
        self.base_features = base_features  # 基础特征列, [OHLC,volume_norm, R_0, V_0, sma5,...] 除了OHLC都normed
        self.job_num = task.job_num
        self.fee_rate = task.fee_rate
        self.free_rate = task.free_rate
        self.best_gp_frame = pd.DataFrame()
        self.sr_selected = []
        self.pjsr_selected = []
        self.corr_selected = []
        self.skew_selected = []
        self.kurt_selected = []
        self.rolling_rankic_selected = []
        self.pj_rolling_rankic_selected = []
    
    def mine(self, g_uid: Optional[str] = None) -> list:
        x_train = self.x_train.copy()
        y_train = self.y_train.copy()
        y_raw = self.y_raw.copy()
        
        ST_gplearn = SymbolicTransformer(
            **GP_PARAMS, # 解包所有参数
            function_set=self.func_set,  # 需要动态设定的参数
            feature_names=self.base_features,
            n_jobs=self.job_num,
            # metric=_fitness_map['sr_intercept_filtered']
            metric=_fitness_map['sr_rolling_segmented']
        )
        self.log('start mining on {}...'.format(self.symbol))
        # NOTE: x_close最终传入raw_fitness(**kwargs) 用来计算sharpe metric函数的百分比收益率
        X = np.nan_to_num(x_train, nan=0.0, posinf=0.0, neginf=0.0, copy=False)
        y = np.nan_to_num(y_train, nan=0.0, posinf=0.0, neginf=0.0, copy=False) # 一维数组
        y = y.reshape(-1, )
        y_raw =  np.nan_to_num(y_raw, nan=0.0, posinf=0.0, neginf=0.0, copy=False)
        # x_close = np.nan_to_num(x_train.loc[:, 'close'])
        # ST_gplearn.fit(X, y, **{'y_raw': y_raw, 'x_close': x_close, 'symbol': self.symbol})
        ST_gplearn.fit(X, y, **{'y_raw': y_raw, 'symbol': self.symbol})
        best_programs = ST_gplearn._best_programs
        best_programs_dict = {}

        for bp in best_programs:
            factor_name = 'alpha_' + str(best_programs.index(bp) + 1)
            best_programs_dict[factor_name] = {'fitness': bp.fitness_, 'expression': str(bp), 'depth': bp.depth_, 'length': bp.length_}

        best_programs_frame = pd.DataFrame(best_programs_dict).T
        best_programs_frame = best_programs_frame.sort_values(by='fitness', axis=0, ascending=False)
        best_programs_frame = best_programs_frame.drop_duplicates(subset=['expression'], keep='first')
        if g_uid is not None:
            file_name = '{}_{}_fcts_gp_{}.csv'.format(self.symbol, g_uid, datetime.now().strftime('%y%m%d_%H'))
            path_name = DATA_DIR_FACTOR.joinpath(file_name)
            best_programs_frame.to_csv(path_name)
            
        self.best_gp_frame = best_programs_frame
        return best_programs_frame['expression'].tolist() # init factor list
    
    
    def select_by_sharp(self, a_fcts_df: pd.DataFrame, y_raw_se: pd.Series, sr_threshold: float = 0.8, 
                        pjsr_threshold: float = 0.2, g_uid: Optional[str] = None) -> Tuple[pd.DataFrame,pd.DataFrame]:
        '''
             a_fcts_df      : 训练集, [index(date), fct1, fct2, ret, ret_open],值为各单因子值 -- 不含任何基础数据,如OHLC, volume, volume_norm, rsi_6,...
             y_raw          : 1期未来收益率(真实,未经norm的)
             gp_result_list : gp初筛得到的因子列表, a_fcts_df的因子列可以多于它
             sr_threshold   : threshold of the sharpe ratio (e.g., 0.8)
             pjsr_threshold : threshold of the pj tucker sharpe ratio (e.g., 0.2~0.8)
             返回: (best_factors_list, best_pjsr_factors_list)
        '''
        day_bars, ann_days, fixed_return, fee_rate, weights, is_ret_open = METRIC_PARAMS_DEFAULT[self.symbol]
        if a_fcts_df.empty:
           return [], []
        a_fcts_df = a_fcts_df.copy()
        y = np.nan_to_num(self.y_train, nan=0.0, posinf=0.0, neginf=0.0, copy=False)
        y_raw_se = y_raw_se.replace([np.inf, -np.inf, np.nan],0.0) # 这里是series
        assert a_fcts_df.index.equals(y_raw_se.index)
        y_raw = y_raw_se.values
        
        a_fcts_np = np.array(a_fcts_df)
        a_fcts_np = a_fcts_np.reshape(-1,a_fcts_df.shape[1])
        a_rets_np = np.zeros((a_fcts_np.shape[0],a_fcts_np.shape[1]),dtype=float)
        
        for i in range(a_fcts_np.shape[1]):
            X = a_fcts_np[:, i].reshape(-1, 1)
            model = LinearRegression()
            model.fit(X, y) # X/y都是norm过的!!
            pos_np = model.predict(X)
            pos_se = pd.Series(eu.norm(pos_np, params=NORM_PARAMS_POS))
            if is_ret_open: # 下一个bar的open买, NOTE: prepare_df时已考虑过y_raw的不同处理
                cost = pos_se.shift(1).fillna(0).diff().abs() * fee_rate
                ret = pos_se.shift(1).fillna(0) * y_raw - cost.fillna(0)
            else:
                ret = pos_se * y_raw - pos_se.diff(1).abs() * fee_rate
            ret = ret.replace([np.inf, np.nan, -np.inf],0.0)
            a_rets_np[:,i] = ret   # 收益率矩阵
        sr_np = pu.calc_sr(a_rets_np, day_bars, ann_days, fixed_return)  # sharp矩阵
        fcts_list = a_fcts_df.columns.tolist()
        sr_dict = {fct: sr for fct, sr in zip(fcts_list, sr_np)}
        sr_df = pd.DataFrame(sr_dict, index=[None])
        sr_df_T = sr_df[[col for col in sr_df.columns if col not in ('ret', 'ret_open')]].T
        sr_df_T = sr_df_T.rename(columns={None: 'sr'})
        sr_df_T = sr_df_T.sort_values(by='sr', ascending=False).dropna()
        self.sr_selected = sr_df_T[sr_df_T['sr'] >= sr_threshold].index.tolist()
        self.pjsr_selected = sr_df_T[(sr_df_T['sr'] >= pjsr_threshold) & (sr_df_T['sr'] < sr_threshold)].index.tolist()
        sr_selected_df = sr_df_T[sr_df_T['sr'] >= sr_threshold].copy()
        srpj_selected_df = sr_df_T[(sr_df_T['sr'] >= pjsr_threshold) & (sr_df_T['sr'] < sr_threshold)].copy()
        if g_uid is not None:
            file_name = '{}_{}_fcts_sr_{}.csv'.format(self.symbol, g_uid, datetime.now().strftime('%y%m%d_%H'))
            path_name = DATA_DIR_FACTOR.joinpath(file_name)
            sr_df_T[sr_df_T['sr'] >= sr_threshold].to_csv(path_name)
            file_name = '{}_{}_fcts_srpj_{}.csv'.format(self.symbol, g_uid, datetime.now().strftime('%y%m%d_%H'))
            path_name = DATA_DIR_FACTOR.joinpath(file_name)
            sr_df_T[(sr_df_T['sr'] >= pjsr_threshold) & (sr_df_T['sr'] < sr_threshold)].to_csv(path_name)
        return sr_selected_df, srpj_selected_df
    
    
    def select_by_skew(self, a_fcts_df: pd.DataFrame, skew_threshold: float = 0.5, 
                       g_uid: Optional[str] = None) ->list:
        skew_selected = []
        if a_fcts_df.empty:
            return skew_selected
        a_fcts_df = a_fcts_df.copy()
        for col in a_fcts_df.columns:
            sk = skew(a_fcts_df[col])
            if abs(sk)<=skew_threshold:
                skew_selected.append({col:sk})
        self.skew_selected = [list(a_dict.keys())[0] for a_dict in skew_selected]
        if g_uid is not None:
            file_name = '{}_{}_fcts_skew_{}.csv'.format(self.symbol, g_uid, datetime.now().strftime('%y%m%d_%H'))
            path_name = DATA_DIR_FACTOR.joinpath(file_name)
            skew_df = pd.DataFrame([(key, value) for d in skew_selected for key, value in d.items()])
            if not skew_df.empty:
                skew_df.columns = ['Factor', 'skew']
            skew_df.to_csv(path_name)
            
        return self.skew_selected
    
    def select_by_kurt(self, a_fcts_df: pd.DataFrame, kurt_threshold: float = 5, 
                       g_uid: Optional[str] = None) ->list:
        kurt_selected = []
        if a_fcts_df.empty:
            return kurt_selected
        a_fcts_df = a_fcts_df.copy()
        for col in a_fcts_df.columns:
            kt = kurtosis(a_fcts_df[col])
            if kt<=kurt_threshold:
                kurt_selected.append({col:kt})
        self.kurt_selected = [list(a_dict.keys())[0] for a_dict in kurt_selected]
        if g_uid is not None:
            file_name = '{}_{}_fcts_kurt_{}.csv'.format(self.symbol, g_uid, datetime.now().strftime('%y%m%d_%H'))
            path_name = DATA_DIR_FACTOR.joinpath(file_name)
            kurt_df = pd.DataFrame([(key, value) for d in kurt_selected for key, value in d.items()])
            if not kurt_df.empty:
                kurt_df.columns = ['Factor', 'kurt']
            kurt_df.to_csv(path_name)
            
        return self.kurt_selected
    
    @classmethod
    def select_by_corr(cls, a_fcts_df: pd.DataFrame, corr_threshold: float = 0.3, 
                       g_uid: Optional[str] = None) -> list: # Xv经验值0.3
        ''' L2corr调用 - 定义为类方法
            a_fcts_df        : 训练集, [index(date), fct1, fct2, ret] -- 不含基础数据,如OHLC, volume, volume_norm, rsi_6,...
            corr_threshold   : threshold of the absolute value of correlation between factors (e.g., 0.75)
        '''
        super().log("start compute corr...")
        if a_fcts_df.empty:
            return []
        a_fcts_df = a_fcts_df.copy()
        ret_col = 'ret_open' if TRADE_ON_OPEN else 'ret'
        X: pd.DataFrame = a_fcts_df[a_fcts_df.columns.difference([ret_col])]
        y: pd.Series = a_fcts_df[ret_col]
        # 确保所有特征均为数值类型
        num_features = X.select_dtypes(include=['number']).columns
        if len(num_features) != X.shape[1]:
            X = X[num_features]
            super().log(f"包含非数值列,仅用数值列计算corr...被忽略的列有：{set(X.columns) - set(num_features)}",level='ERROR')
        # 计算协方差矩阵
        X_corr_matrix = X.corr().where(np.triu(np.ones(X.corr().shape), k=1).astype(bool))
        factor_list_2 = num_features.tolist()
        # 遍历上三角相关系数矩阵，移除高度相关的因子
        for fct_1, fct_2 in zip(*np.triu_indices_from(X_corr_matrix.values, k=1)):
            corr_value = X_corr_matrix.iloc[fct_1, fct_2]
            if pd.notna(corr_value): # 检查 corr_value 是否为 NaN
                corr_value = float(corr_value)  # 将 corr_value 转换为浮点数
                if abs(corr_value) > corr_threshold: # corr_value大于阈值的两个因子,进一步判断
                    corr_with_y_1 = X[num_features[fct_1]].corr(y)
                    corr_with_y_2 = X[num_features[fct_2]].corr(y)
                    # 保留与y相关性较大的因子
                    factor_to_remove = num_features[fct_2] if abs(corr_with_y_1) >= abs(corr_with_y_2) else num_features[fct_1]
                    if factor_to_remove in factor_list_2:
                        factor_list_2.remove(factor_to_remove)
        # 赋值self.corr_selected
        cls.corr_selected = factor_list_2      
        if g_uid is not None:
            file_name = '{}_fcts_corr_{}.csv'.format(g_uid, datetime.now().strftime('%y%m%d_%H'))
            path_name = DATA_DIR_FACTOR.joinpath(file_name)
            a_fcts_df[factor_list_2].corr().to_csv(path_name)

        return factor_list_2

    @classmethod
    def select_by_rolling_rankic(cls, a_fcts_df: pd.DataFrame,
                                 y_norm_df: pd.DataFrame, # LABEL
                                 y_raw_t_return_se: pd.Series, 
                                 window: int = 240, min_period: int = 120,
                                 top_pct: float = 0.2, pj_pct: float = 0.2,
                                 symbol: str = '',
                                 g_uid: Optional[str] = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """ L2group调用 - 定义为类方法
        根据因子值的 滚动RankIC平均值 选择优质因子
        
        Args:
            a_fcts_df: 训练集, [index(date), fct1, fct2, ret, ret_open], 值为各单因子值
            y_raw_t_return_se: T_Delay期未来收益率(真实,未经norm的,已TRADE_ON_OPEN逻辑处理过)
            window: 滚动窗口天数
            min_period: 最小所需样本数
            top_pct: RankIC均值筛选前pct(如20%)
            pj_pct: PJ池RankIC均值筛选剩余的前pct(如20%)
            g_uid: 可选的唯一标识符,用于保存结果
            
        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: (优质因子DataFrame, PJ池因子DataFrame)
            每个DataFrame包含因子名称和对应的r1_score, r2_score, final_score
        """
        if a_fcts_df.empty:
            return pd.DataFrame(), pd.DataFrame()
        
        a_fcts_df = a_fcts_df.copy()
        ''' ====    实现r1: 滚动rankic绝对值的平均数    ==== '''    
        # 1. 获取每个因子的滚动RankIC序列        
        # 1. 获取因子列名并确保索引一致性
        fct_cols = [col for col in a_fcts_df.columns if col not in ['ret', 'ret_open']]
        assert a_fcts_df.index.equals(y_norm_df.index)
        assert a_fcts_df.index.equals(y_raw_t_return_se.index)
        
        # 2. 数据预处理
        y_raw_t_return_se = y_raw_t_return_se.replace([np.inf, -np.inf, np.nan], 0.0)
        y_raw_t = y_raw_t_return_se.values.ravel()
        y = np.nan_to_num(y_norm_df, nan=0.0, posinf=0.0, neginf=0.0, copy=False)
        
        # 2. 对每个因子进行模型预测和仓位映射
        pos_matrix = np.zeros((len(a_fcts_df), len(fct_cols)))
        for i, fct in enumerate(fct_cols):
            X = np.array(a_fcts_df[fct]).reshape(-1, 1)
            model = LinearRegression()
            model.fit(X, y)  # y是标准化后的label
            y_pred = model.predict(X)
            pos_np = eu.norm(y_pred, params=NORM_PARAMS_POS)
            pos_matrix[:, i] = pos_np.ravel()
        # 3. 计算滚动RankIC
        # NOTE: !! 用了JIT,先预热
        pu.warmup_jit_functions()
        rolling_rankic_matrix = pu.calc_rolling_rankic(
            pos_matrix,
            y_raw_t,
            window=window,
            min_periods=min_period
        )

        # 4. 计算r1分数：滚动RankIC绝对值的平均数（已在[0,1]范围内）
        r1_scores = {}
        for i, fct in enumerate(fct_cols):
            r1_score = np.nanmean(np.abs(rolling_rankic_matrix[:, i]))
            r1_scores[fct] = r1_score
        
        ''' ====    实现r2: 时序因子稳定性    ==== '''
        # 1. 计算未来收益的符号（label），注意对齐长度
        ret_signs = np.sign(np.array(y_raw_t))  # 明确转换为numpy数组
        ret_signs = ret_signs[window-1:]  # 从window-1开始截取，与rolling_rankic_matrix对齐
        
        # 2. 计算每个因子的时序稳定性得分
        r2_scores = {}
        for i, fct in enumerate(fct_cols):
            # 获取当前因子的滚动RankIC序列
            fct_rankic = rolling_rankic_matrix[:, i]
            rankic_signs = np.sign(fct_rankic)
            
            # 确保valid_mask与ret_signs长度一致
            valid_mask = ~np.isnan(fct_rankic)[:len(ret_signs)]
            rankic_signs = rankic_signs[:len(ret_signs)]
            
            if np.sum(valid_mask) > 0:
                # 计算同向的比例
                valid_rankic_signs = rankic_signs[valid_mask]
                valid_ret_signs = ret_signs[valid_mask]
                
                same_direction = np.sum((valid_rankic_signs * valid_ret_signs) > 0)
                total_valid = len(valid_rankic_signs)
                r2_score = same_direction / total_valid
            else:
                r2_score = 0.0
                
            r2_scores[fct] = r2_score
        
        # 合并r1和r2得分
        final_scores = {}
        for fct in fct_cols:
            final_scores[fct] = r1_scores[fct] * r2_scores[fct]
        # 转换为DataFrame并排序
        rankic_df = pd.DataFrame({
            'r1_score': r1_scores,
            'r2_score': r2_scores,
            'sic': final_scores
        })
        rankic_df = rankic_df.sort_values('sic', ascending=False)

        # 选择优质因子和PJ池因子
        n_total = len(rankic_df)
        n_top = int(n_total * top_pct)
        n_pj = int((n_total - n_top) * pj_pct)
        
        top_factors = rankic_df.head(n_top)
        pj_factors = rankic_df.iloc[n_top:n_top + n_pj]
        # 赋值类属性
        cls.rolling_rankic_selected = top_factors.index.tolist()
        cls.pj_rolling_rankic_selected = pj_factors.index.tolist()

        # 保存结果(如果需要)
        if g_uid is not None:
            file_name = '{}_{}_fcts_rankic_{}.csv'.format(
                symbol, g_uid, datetime.now().strftime('%y%m%d_%H')
            )

            path_name = DATA_DIR_FACTOR.joinpath(file_name)
            rankic_df.to_csv(path_name)
            
        return top_factors, pj_factors